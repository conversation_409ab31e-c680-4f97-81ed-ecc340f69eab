# Firebase Emulator Data Management

This document explains how to manage persistent data in the Firebase emulators for the Oryn project.

## 🔄 **Auto-Import Setup**

The Firebase emulators are configured to automatically import data from `./firebase-data/` when they start. This ensures that:

- ✅ **Privileged user emails** are always available
- ✅ **Test user accounts** are pre-loaded
- ✅ **Community strategies** persist between sessions
- ✅ **Development data** is consistent across team members

## 📁 **Data Structure**

```
firebase-data/
├── auth_export/           # User authentication data
│   ├── accounts.json      # User accounts and profiles
│   └── config.json        # Auth configuration
├── firestore_export/      # Firestore database data
│   └── all_namespaces/    # All collections and documents
└── firebase-export-metadata.json  # Export metadata
```

## 🔧 **Common Operations**

### **Export Current Data**
```bash
# Export all current emulator data
firebase emulators:export ./firebase-data
```

### **Import Data Manually**
```bash
# Import data when starting emulators
firebase emulators:start --import=./firebase-data
```

### **Clear All Data**
```bash
# Remove all exported data
rm -rf ./firebase-data
```

## 👤 **Privileged Users**

The following users have community strategy privileges:

- **<EMAIL>** - Admin with full permissions

### **Adding New Privileged Users**

1. **Start the emulators**: `npm run dev`
2. **Open Firestore UI**: http://127.0.0.1:4000/
3. **Navigate to**: `privilegedEmails` collection
4. **Add document** with structure:
   ```json
   {
     "email": "<EMAIL>",
     "active": true,
     "role": "admin",
     "permissions": ["community_strategies"]
   }
   ```
5. **Export data**: `firebase emulators:export ./firebase-data`

## 🚀 **Development Workflow**

### **Starting Fresh**
```bash
# Start with clean data
rm -rf ./firebase-data
npm run dev
```

### **Preserving Changes**
```bash
# Save current state
firebase emulators:export ./firebase-data
```

### **Team Sync**
```bash
# After pulling changes that include firebase-data updates
npm run dev  # Data will auto-import
```

## ⚠️ **Important Notes**

- **Auto-import is enabled** in `firebase.json`
- **Data persists** between emulator restarts
- **Export regularly** to save important test data
- **Commit firebase-data** to share with team (optional)
- **Firestore rules** still apply to imported data

## 🔍 **Troubleshooting**

### **Privileges Not Working**
1. Check `privilegedEmails` collection exists
2. Verify `active: true` (boolean, not string)
3. Ensure email matches exactly
4. Re-export data after changes

### **Data Not Loading**
1. Check `firebase.json` has import paths
2. Verify `./firebase-data` directory exists
3. Restart emulators completely
4. Check console for import errors

### **Performance Issues**
1. Large datasets slow emulator startup
2. Consider selective exports for development
3. Clear unnecessary test data periodically

## 📝 **Configuration**

Current emulator configuration in `firebase.json`:

```json
{
  "emulators": {
    "auth": {
      "port": 9099,
      "import": "./firebase-data"
    },
    "firestore": {
      "port": 8082,
      "import": "./firebase-data"
    },
    "ui": {
      "enabled": true,
      "port": 4000
    }
  }
}
```
