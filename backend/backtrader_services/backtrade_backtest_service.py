import os
# Force the Agg backend before any matplotlib import
os.environ['MPLBACKEND'] = 'Agg'
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import io
import base64
import backtrader as bt
import pandas as pd
import datetime
import os
from fastapi import APIRouter, HTTPException
from algebra.mathematical_calculations import MathematicalCalculations
from backtrader_services.backtrader_indicators import INDICATOR_MAPPING
import traceback
import re
import backtrader.analyzers as btanalyzer
import copy
import random
import time
from backtrader_services.equity_analyzer import EquityAnalyzer  # import our custom analyzer


router = APIRouter()

# Helper to generate unique IDs.
def generateId():
    return str(int(time.time() * 1000)) + ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=8))

PROJECT_ROOT = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
DATA_DIRECTORY = os.path.join(PROJECT_ROOT, "forex_data")
CSV_FILENAME = "eur_usd_hourly.csv"  

def load_forex_data_from_csv():
    file_path = os.path.join(DATA_DIRECTORY, CSV_FILENAME)
    if not os.path.exists(file_path):
        print(f"❌ CSV file not found: {file_path}")
        raise HTTPException(status_code=500, detail=f"CSV file not found: {file_path}")
    df = pd.read_csv(file_path, sep="\t", names=["datetime", "open", "high", "low", "close", "volume"])
    df["datetime"] = pd.to_datetime(df["datetime"], errors="coerce")
    df.set_index("datetime", inplace=True)
    df = df.sort_index()
    if df.index.isnull().any():
        print(f"⚠️ Warning: Some rows have invalid datetime values! Fixing...")
        df = df.dropna(subset=["open", "high", "low", "close"])
    if "volume" not in df.columns:
        df["volume"] = 0  # Backtrader requires a volume column
    print(f"✅ Loaded {len(df)} rows of historical Forex data from {file_path}")
    print(f"✅ Data Sample:\n{df.head()}")
    return df


class DynamicStrategy(bt.Strategy):
    params = (
        ("indicators", []),
        ("entry_rules", []),
        ("exit_rules", []),
        ("stop_loss", None),    # e.g. "1%"
        ("take_profit", None),  # e.g. "2%"
    )

    MOVAV_MAPPING = {
        "MovAv.Simple": bt.indicators.MovingAverageSimple,
        "MovAv.Exponential": bt.indicators.ExponentialMovingAverage,
        "MovAv.Weighted": bt.indicators.WeightedMovingAverage,
        "MovAv.Hull": bt.indicators.HullMovingAverage,
    }

    def __init__(self):
        self.indicators = {}
        self.open_trades = []  # Track individual trades

        # Parse risk management percentages.
        if self.p.stop_loss:
            self.stop_loss_pct = float(self.p.stop_loss.strip('%')) / 100.0
        else:
            self.stop_loss_pct = None
        if self.p.take_profit:
            self.take_profit_pct = float(self.p.take_profit.strip('%')) / 100.0
        else:
            self.take_profit_pct = None

        # Dynamically add indicators.
        for indicator in self.p.indicators:
            class_name = indicator["indicator_class"]
            params = indicator["parameters"]
            # Ensure each indicator has an id – use the provided id or generate one.
            ind_id = indicator.get("id", None)
            if not ind_id:
                ind_id = generateId()
                indicator["id"] = ind_id  # Update the JSON so that rules can refer to this id.
            print(f" Adding indicator {class_name} with id: {ind_id} and params: {params}")
            if class_name in INDICATOR_MAPPING:
                bt_class = INDICATOR_MAPPING[class_name]
                bt_params = copy.deepcopy(params)
                if "movav" in params and isinstance(params["movav"], str):
                    movav_name = params["movav"]
                    if movav_name in self.MOVAV_MAPPING:
                        bt_params["movav"] = self.MOVAV_MAPPING[movav_name]
                    else:
                        print(f"⚠️ Warning: Unsupported movav type '{movav_name}', defaulting to MovingAverageSimple.")
                        bt_params["movav"] = bt.indicators.MovingAverageSimple
                print(f" Indicator {bt_class} is supported.")
                try:
                    indicator_instance = bt_class(self.data, **bt_params)
                except Exception as e:
                    print(f"Error initializing indicator {class_name}: {e}")
                    raise HTTPException(status_code=500, detail=f"Error initializing indicator {class_name}")
                setattr(self, ind_id, indicator_instance)
                self.indicators[ind_id] = indicator_instance
                print(f" Indicator {ind_id} added successfully with params: {params}")
            else:
                print(f"⚠️ Warning: Indicator '{class_name}' is not supported.")
                raise HTTPException(status_code=500, detail=f"Error: Indicator '{class_name}' is not supported.")

    def build_rule_condition(self, rule):
        """
        Constructs a condition string from a rule.
        Expected rule fields: tradeType, indicator1, operator, compareType, indicator2, value.
        """
        op = rule["operator"].lower()
        op = op.replace("crossing above", "crosses above")
        op = op.replace("crossing below", "crosses below")
        if rule["compareType"] == "indicator":
            return f"{rule['indicator1']} {op} {rule['indicator2']}"
        else:
            return f"{rule['indicator1']} {op} {rule['value']}"

    def next(self):
        current_date = self.datetime.date(0)
        current_time = self.datetime.time(0)
        print(f" Processing next() for {current_date} {current_time}")

        # Warm-up check.
        warmup_period = 0
        for name, ind in self.indicators.items():
            period = getattr(ind, 'period', 12)
            warmup_period = max(warmup_period, period)
            print(f" Indicator {name} has period: {period} (Warmup: {warmup_period})")
        if len(self) < warmup_period:
            print(f" ⏳ Warming up: {len(self)} bars processed, need {warmup_period} bars. Skipping logic.")
            return

        # Prepare indicator values.
        indicator_values = {}
        for name, ind in self.indicators.items():
            indicator_values[name] = ind[0]
            indicator_values[f"{name}[0]"] = ind[0]
            indicator_values[f"{name}(0)"] = ind[0]
            indicator_values[f"{name}(-1)"] = ind[-1]
            indicator_values[f"{name}[-1]"] = ind[-1]
            print(f" {name}: {ind[0]:.4f}, Previous: {ind[-1]:.4f}")

        # Add reserved key "price" to represent the current close price.
        indicator_values["price"] = self.data.close[0]
        indicator_values["price(0)"] = self.data.close[0]
        indicator_values["price[0]"] = self.data.close[0]

        print(f"📊 Indicator Values: {indicator_values}")

        # Count current open trades.
        current_buy_trades = len([t for t in self.open_trades if t["type"] == "buy"])
        current_sell_trades = len([t for t in self.open_trades if t["type"] == "sell"])

        # --- Entry Logic ---
        for rule in self.p.entry_rules:
            condition_str = self.build_rule_condition(rule)
            print(f" Evaluating Entry Rule for {rule['tradeType'].upper()}: {condition_str}")
            if MathematicalCalculations.evaluate_condition(condition_str, indicator_values):
                entry_trade_type = rule["tradeType"].lower()
                entry_price = self.data.close[0]
                portfolio_value = self.broker.getvalue()
                size = (portfolio_value * 0.01) / entry_price
                print(f" Calculated position size for {entry_trade_type.upper()}: {size} (Portfolio: {portfolio_value}, Price: {entry_price})")
                if entry_trade_type == "buy" and current_buy_trades == 0:
                    sl = entry_price * (1 - self.stop_loss_pct) if self.stop_loss_pct is not None else None
                    tp = entry_price * (1 + self.take_profit_pct) if self.take_profit_pct is not None else None
                    self.buy(size=size)
                    trade_record = {
                        "type": entry_trade_type,
                        "entry_price": entry_price,
                        "size": size,
                        "stop_loss": sl,
                        "take_profit": tp,
                        "entry_bar": len(self)
                    }
                    self.open_trades.append(trade_record)
                    print(f"✅ {entry_trade_type.upper()} trade entered at {entry_price:.4f} with size {size}, SL {sl} and TP {tp}")
                elif entry_trade_type == "sell" and current_sell_trades == 0:
                    sl = entry_price * (1 + self.stop_loss_pct) if self.stop_loss_pct is not None else None
                    tp = entry_price * (1 - self.take_profit_pct) if self.take_profit_pct is not None else None
                    self.sell(size=size)
                    trade_record = {
                        "type": entry_trade_type,
                        "entry_price": entry_price,
                        "size": size,
                        "stop_loss": sl,
                        "take_profit": tp,
                        "entry_bar": len(self)
                    }
                    self.open_trades.append(trade_record)
                    print(f"✅ {entry_trade_type.upper()} trade entered at {entry_price:.4f} with size {size}, SL {sl} and TP {tp}")



        # --- Exit Logic ---
        for trade in self.open_trades.copy():
            if len(self) == trade["entry_bar"]:
                continue
            trade_type = trade["type"]
            exit_triggered = False
            reason = ""
            if trade_type == "buy":
                if trade["stop_loss"] is not None and self.data.low[0] <= trade["stop_loss"]:
                    exit_triggered = True
                    reason = f"Stop Loss hit: {self.data.low[0]:.4f} <= {trade['stop_loss']:.4f}"
                elif trade["take_profit"] is not None and self.data.high[0] >= trade["take_profit"]:
                    exit_triggered = True
                    reason = f"Take Profit hit: {self.data.high[0]:.4f} >= {trade['take_profit']:.4f}"
            elif trade_type == "sell":
                if trade["stop_loss"] is not None and self.data.high[0] >= trade["stop_loss"]:
                    exit_triggered = True
                    reason = f"Stop Loss hit: {self.data.high[0]:.4f} >= {trade['stop_loss']:.4f}"
                elif trade["take_profit"] is not None and self.data.low[0] <= trade["take_profit"]:
                    exit_triggered = True
                    reason = f"Take Profit hit: {self.data.low[0]:.4f} <= {trade['take_profit']:.4f}"

            for rule in self.p.exit_rules:
                if rule.get("tradeType", "buy/sell").lower() in [trade_type, "buy/sell"]:
                    condition_str = self.build_rule_condition(rule)
                    print(f" Evaluating Exit Rule for {trade_type.upper()} trade: {condition_str}")
                    if MathematicalCalculations.evaluate_condition(condition_str, indicator_values):
                        exit_triggered = True
                        reason = f"Exit rule triggered: {condition_str}"
                        break

            if exit_triggered:
                self.close(size=trade["size"])
                print(f"🚨 {trade_type.upper()} trade exited on {current_date} | Reason: {reason}")
                self.open_trades.remove(trade)

def safe_round(value, decimals=2):
    if value is None or value == "N/A":
        return "N/A"
    return round(value, decimals)

def run_backtest(strategy_json):
    print("Got the following strategy JSON:" + str(strategy_json)) 
    strategy = strategy_json["strategy"]["strategy"]
    print("Got the following strategy:" + str(strategy))
    indicators = strategy["indicators"]
    entry_rules = strategy["entryRules"]
    exit_rules = strategy["exitRules"]
    risk_management = strategy["riskManagement"]

    try:
        print("📥 Loading Forex Data from CSV...")
        data = load_forex_data_from_csv()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"❌ Error loading CSV: {str(e)}")

    print(f"✅ Data Loaded Successfully. First 5 rows:\n{data.head()}")

    try:
        if not {"open", "high", "low", "close", "volume"}.issubset(data.columns):
            raise HTTPException(status_code=500, detail=f"❌ Missing required columns in data: {data.columns}")

        data_feed = bt.feeds.PandasData(dataname=data, timeframe=bt.TimeFrame.Minutes, compression=60)

        cerebro = bt.Cerebro()
        cerebro.addstrategy(
            DynamicStrategy,
            indicators=indicators,
            entry_rules=entry_rules,
            exit_rules=exit_rules,
            stop_loss=risk_management["stopLoss"],
            take_profit=risk_management["takeProfit"],
        )

        cerebro.adddata(data_feed)
        cerebro.broker.set_cash(100000)
        cerebro.broker.setcommission(commission=0.001)

        # Add standard analyzers
        cerebro.addanalyzer(btanalyzer.TradeAnalyzer, _name="trade_analyzer")
        cerebro.addanalyzer(btanalyzer.SharpeRatio, _name="sharpe_ratio")
        cerebro.addanalyzer(btanalyzer.DrawDown, _name="drawdown")
        # Add our custom equity analyzer
        cerebro.addanalyzer(EquityAnalyzer, _name="equity_curve")

        print("🚀 Running Backtest...")
        results = cerebro.run()
        print("✅ Backtest completed successfully!")

        # Retrieve analyzers
        strategy_instance = results[0]
        trade_analysis = strategy_instance.analyzers.trade_analyzer.get_analysis()
        sharpe_ratio = strategy_instance.analyzers.sharpe_ratio.get_analysis()
        drawdown = strategy_instance.analyzers.drawdown.get_analysis()
        equity_curve = strategy_instance.analyzers.equity_curve.get_analysis()

        total_trades = trade_analysis.get("total", {}).get("closed", 0)
        wins = trade_analysis.get("won", {}).get("total", 0)
        losses = trade_analysis.get("lost", {}).get("total", 0)
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
        lose_rate = (losses / total_trades * 100) if total_trades > 0 else 0
        avg_win = trade_analysis.get("won", {}).get("pnl", {}).get("average", None)
        avg_loss = trade_analysis.get("lost", {}).get("pnl", {}).get("average", None)
        max_drawdown = drawdown.get("max", {}).get("drawdown", None)
        sharpe = sharpe_ratio.get("sharperatio", None)
        profit_factor = (
            trade_analysis.get("won", {}).get("pnl", {}).get("total", 1) /
            abs(trade_analysis.get("lost", {}).get("pnl", {}).get("total", 1))
        ) if trade_analysis.get("lost", {}).get("pnl", {}).get("total", 0) != 0 else None
        best_trade = trade_analysis.get("won", {}).get("pnl", {}).get("max", None)
        worst_trade = trade_analysis.get("lost", {}).get("pnl", {}).get("min", None)

        final_value = cerebro.broker.getvalue()
        print(f"Final Portfolio Value: {final_value:.2f}")

        # --- Generate the Plot Manually ---
        # Plot the equity curve using matplotlib
        fig, ax = plt.subplots(figsize=(10, 5))
        ax.plot(equity_curve, label="Equity Curve")
        ax.set_title("Equity Curve")
        ax.set_xlabel("Time")
        ax.set_ylabel("Portfolio Value")
        ax.legend()

        buf = io.BytesIO()
        fig.savefig(buf, format="png")
        buf.seek(0)
        img_base64 = base64.b64encode(buf.read()).decode("utf-8")
        plt.close(fig)

        performance = {
            "final_portfolio_value": safe_round(final_value),
            "total_trades": total_trades,
            "win_rate": safe_round(win_rate),
            "lose_rate": safe_round(lose_rate),
            "average_win": safe_round(avg_win),
            "average_loss": safe_round(avg_loss),
            "max_drawdown": safe_round(max_drawdown),
            "sharpe_ratio": safe_round(sharpe),
            "profit_factor": safe_round(profit_factor),
            "best_trade": safe_round(best_trade),
            "worst_trade": safe_round(worst_trade),
        }

        return {
            "success": True,
            "message": "✅ Backtest completed successfully!",
            "performance": performance,
            "plot": img_base64,  # Base64 encoded chart image
        }

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"❌ Backtest Error: {error_trace}")
        raise HTTPException(status_code=500, detail=f"❌ Backtest Error: {str(e)}")

def safe_round(value, decimals=2):
    if value is None or value == "N/A":
        return "N/A"
    return round(value, decimals)

@router.post("/backtest")
def run_backtest_endpoint(strategy: dict):
    try:
        return run_backtest(strategy)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
