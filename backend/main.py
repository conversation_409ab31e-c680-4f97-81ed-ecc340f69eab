from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
# from account_service import router as account_router
from backtrader_services.backtrade_backtest_service import router as backtrade_backtest_service
from oanda_service import router as oanda_router
from forex_service import router as forex_router 
from ai_chatbot import router as ai_router
from backtest_service import router as backtest_router
from strategy_service import router as strategy_router

app = FastAPI()

#   Enable CORS to allow frontend requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins (change in production)
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

#   Include the account service router
# app.include_router(account_router)

#   Include Forex API Router
app.include_router(forex_router)

app.include_router(oanda_router)

app.include_router(ai_router, prefix="/ai")

app.include_router(backtest_router, prefix="/backtest")

app.include_router(backtrade_backtest_service, prefix="/backtrader")

app.include_router(strategy_router, prefix="/strategy")

@app.get("/")
def read_root():
    return {"message": "Welcome to Oryn Trading Platform"}

@app.route("/handle_feature_vote", methods=["POST"])
def handle_feature_vote():
    try:
        request_json = request.get_json()
        if not request_json:
            return jsonify({"error": "No JSON data received"}), 400

        feature_id = request_json.get("featureId")
        feature_name = request_json.get("featureName")
        feature_description = request_json.get("featureDescription")

        if not all([feature_id, feature_name, feature_description]):
            return jsonify({"error": "Missing required fields"}), 400

        # Get client IP address
        client_ip = request.remote_addr
        if request.headers.get('X-Forwarded-For'):
            client_ip = request.headers.get('X-Forwarded-For').split(',')[0]

        # Check if this IP has already voted for this feature
        votes_ref = db.collection('feature_votes')
        ip_votes = votes_ref.where('ip_address', '==', client_ip).where('feature_id', '==', feature_id).get()
        
        if len(list(ip_votes)) > 0:
            return jsonify({"error": "You have already voted for this feature"}), 400

        # Create a new vote document
        vote_data = {
            'feature_id': feature_id,
            'feature_name': feature_name,
            'feature_description': feature_description,
            'ip_address': client_ip,
            'timestamp': firestore.SERVER_TIMESTAMP
        }
        
        # Add the vote to Firestore
        votes_ref.add(vote_data)

        # Update the feature's vote count
        feature_ref = db.collection('features').document(feature_id)
        feature_ref.update({
            'votes': firestore.Increment(1)
        })

        return jsonify({"message": "Vote recorded successfully"}), 200

    except Exception as e:
        print(f"Error in handle_feature_vote: {str(e)}")
        return jsonify({"error": str(e)}), 500

