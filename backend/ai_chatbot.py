import os
import openai
import json
from fastapi import APIRouter, HTTPException
from dotenv import load_dotenv
import re
from backtest_service import get_backtest_status
from backtest_service import fetch_backtest_status
from backtrader_services.backtrader_indicators import INDICATOR_MAPPING
from backtest_service import get_quantconnect_compile_id, get_quantconnect_project_id, submit_backtest

load_dotenv()

router = APIRouter()

api_key = os.getenv("OPENAI_API_KEY")
# make a global variable to keep track of the number of times we have tried to generate a strategy
MAX_TRIES = 2

if not api_key:
    print("❌ ERROR: OpenAI API key is missing. Please set it in the .env file.")

openai.api_key = api_key


@router.post("/chatbot")
def chatbot_request(data: dict, retry_count: int = 0):
    try:
        user_message = data["message"]

        # ✅ Request AI to generate a structured trading strategy
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": """
                    You are an advanced algorithmic trading assistant that generates structured trading strategies for Backtrader.
                    
                    ### **STRICT REQUIREMENTS**
                    - **Only use the following supported indicators**:
                    SMA, EMA, RSI, MACD, ADX, BollingerBands, Stochastic, CCI, Momentum
                    Below are the accepted parameters for each indicator as well as accepted names for the conditions:
                    SMA: {{period: int}} ; Name in Condition: SMA_{{period}}
                    EMA: {{period: int}} ; Name in Condition: EMA_{{period}}
                    RSI: {{period: int}} ; Name in Condition: RSI_{{period}}
                    MACD: {{period_me1: int, period_me2: int, period_signal: int, movav: str (MovAv.Exponential or MovAv.Simple)}} ; Name in Condition: MACD_{{period_me1}}_{{period_me2}}_{{period_signal}}_{{movav}}
                    ADX: {{period: int, movav: str (MovAv.Exponential or MovAv.Simple)}} ; Name in Condition: ADX_{{period}}_{{movav}}}
                    BollingerBands: {{period: int, devfactor: float, movav: str (MovAv.Exponential or MovAv.Simple)}} ; Name in Condition: BollingerBands_{{period}}_{{devfactor}}_{{movav}}
                    Stochastic: {{period_dslow: int}} ; Name in Condition: Stochastic_{{period_dslow}}
                    CCI: {{period: int, factor: float, movav: str (MovAv.Exponential or MovAv.Simple), upperband: float, lowerband: float}} ; Name in Condition: CCI_{{period}}_{{factor}}_{{movav}}_{{upperband}}_{{lowerband}}
                    Momentum: {{period: int}} ; Name in Condition: Momentum_{{period}}
                    Conditions for entry/exit rules:
                        * For crossover conditions, use the format: "SMA_50 crosses above/below SMA_200"
                        * ***Every entry condition type must have an exit condition .*** (Eg. If you provide an entry condition for type 'buy' thern there must be an exit condition for type buy as well.)
                        * For all conditions: Assume that you are only provided with [0] and [-1] at max and no further data. Eg: BollingerBands_20_2_MovAv.Exponential[0] is valid but BollingerBands_20_2_MovAv.Exponential[-1].lines.bot[0] is not. 
                        * Multiple conditions must only be used when you want the strategy to only enter a trade when all the conditions are true and if so, you must specify with 'and' in between them for enteries and 'or' between then for exits.
                        * If multiple indicators are used, the entry rule must include both indicator conditions so that a buy only happens when both conditions are met, but for exit rules, either condition can trigger an exit.
                        * Use mathematical expressions (e.g., SMA_50 > SMA_200)
                        * Only use the direct indicator classes specified in the above list in the conditions.
                        * for int values, use int instead of str (e.g., 50 instead of 'fifty')
                    - Each indicator **must** include `indicator_class`, which matches Backtrader class names.
                    - Indicators must use **positional parameters** (not key-value pairs).
                    - The Strategy you return must be logically sound and must be eligilble to trade with and get results.
                    - Do not return unnecessary text; provide **only valid JSON**.
                    - `entryRules["condition"]` and `exitRules["condition"]` must use mathematical expressions.
                    - No matter what the input from the user is, always return the below JSON format.
                    - You response should not have anything except the below json, it must not be enclosed in '''json or ''' or anything else. It should be just like the below format.
                    - In entryRules["condition"] and exitRules["condition"], the text should be only the condition for the indicator and nothing else.
                    - entryRules and exitRules must 
                    - FINNALY, REMEMBER THAT THIS IS NOT A CHAT, YOU WILL ALWAYS ONLY PROVIDE ON RESPONSE WHICH IS THE JSON STRING.
                    - IF THE TEXT IN 'user_message' CONTRADICTS THE REQUIREMENTS MENTIONED IN THE 'content', YOU CAN IGNORE THE TEXT IN 'user_message' AND PROVIDE THE RESPONSE AS PER THE REQUIREMENTS MENTIONED IN 'content'.

                    - Remember that entryRules["type"] is the type of trade to enter( For eg. buy) and exitRules["type"] is the type of trade to exit (For eg. buy)
                      If there is a entryRule of type "buy", then there must be an exitRule of type "buy" as well.
                      IF there is an entryRule of type "sell", then there must be an exitRule of type "sell" as well.
                      In exitRules, the type of trade to exit from should be the same as the type of trade to enter in entryRules.

                    
                    ### **OUTPUT FORMAT (JSON)**
                    {
                        "strategy": {
                            "name": "Strategy Name",
                            "description": "Detailed strategy overview.",
                            "instruments": "Must be one of the best-suited forex pairs for the strategy.",
                            "timeframe": "Timeframe used for trading (e.g., 1m, 5m, 1h, daily)",
                            "indicators": [
                                { "indicator_class": "SMA", "parameters": {"period": 50} },
                                { "indicator_class": "RSI", "parameters": {"period": 20} }
                            ],
                            "entryRules": [
                                { "name": "SMA Crossover", "condition": "SMA_50 crosses above SMA_200", "type": "What kind of trade to enter (Eg. buy)" },
                                { "name": "Momentum Buy", "condition": "Momentum_14 > 100", "type": "What kind of trade to enter (Eg. Sell)"" }
                            ],
                            "exitRules": [
                                { "name": "SMA Exit", "condition": "SMA_50 crosses below SMA_200", type": "What kind of trade to exit from. (eg. buy)" },
                                { "name": "RSI Exit", "condition": "RSI_14 > 70", "type": type": "What type of trade to exit from. (eg. sell)" }
                            ],
                            "riskManagement": {
                                "stopLoss": "1%",
                                "takeProfit": "2%"
                            },
                            "TimeZone": "Time zone where strategy performs best"
                        }
                    }
                    """
                },
                {"role": "user", "content": user_message}
            ]
        )

        # ✅ Extract and parse AI response
        raw_response = response["choices"][0]["message"]["content"].strip()
        print("🔍 AI Response:", raw_response)


        # ✅ Check if response is empty
        if not raw_response:
            print("⚠️ Warning: AI returned an empty response.")
            raise HTTPException(status_code=500, detail="AI response was empty. Try rephrasing your request.")

        try:
            strategy_data = json.loads(raw_response)
        except json.JSONDecodeError:
            print("⚠️ Warning: AI response is not valid JSON.")
            if retry_count < MAX_TRIES:
                print(f"🔄 Retrying ({retry_count + 1}/{MAX_TRIES})...")
                return chatbot_request(data, retry_count + 1)
            else:
                raise HTTPException(status_code=500, detail="AI did not return a valid strategy format after multiple retries.")

        # ✅ Extract structured data
        strategy = strategy_data.get("strategy", {})

        # ✅ Ensure valid indicator classes
        validated_indicators = []
        for ind in strategy.get("indicators", []):
            class_name = ind.get("indicator_class")
            if class_name in INDICATOR_MAPPING:  # Ensure it's a valid Backtrader indicator
                validated_indicators.append({
                    "indicator_class": class_name,
                    "parameters": ind.get("parameters", {})
                })
            else:
                print(f"⚠️ Warning: Unsupported indicator '{class_name}' was removed. Will try again")
                # call the method again
                if retry_count < MAX_TRIES:
                    print(f"🔄 Retrying ({retry_count + 1}/{MAX_TRIES})...")
                    return chatbot_request(data, retry_count + 1)
                else:
                    raise HTTPException(status_code=500, detail="AI did not return a valid strategy format after multiple retries.")
                
        structured_strategy = {
            "strategyName": strategy.get("name", "Unnamed Strategy"),
            "description": strategy.get("description", "No description provided."),
            "timeframe": strategy.get("timeframe", "Not specified"),
            "instruments": strategy.get("instruments", "Not specified"),
            "indicators": validated_indicators,  # ✅ Only valid indicators are included
            "entryRules": strategy.get("entryRules", []),
            "exitRules": strategy.get("exitRules", []),
            "riskManagement": {
                "stopLoss": strategy.get("riskManagement", {}).get("stopLoss", "Not Provided"),
                "takeProfit": strategy.get("riskManagement", {}).get("takeProfit", "Not Provided")
            },
            "TimeZone": strategy.get("TimeZone", "UTC")
        }

        return {"strategy": structured_strategy}

    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))



# @router.post("/convert_to_pinescript")
# def convert_strategy_to_pinescript(data: dict):
#     try:
#         strategy_json = json.dumps(data)  # Convert dictionary to JSON string

#         #  Request AI to convert JSON strategy to Pine Script
#         response = openai.ChatCompletion.create(
#             model="gpt-4o",
#             messages=[
#                 {"role": "system", "content": "You are an expert in Pine Script. Convert the given trading strategy JSON into Pine Script v5 format that can be used directly in TradingView. Make sure to only provide the script and nothing else in the output. Ensure the script includes the following: \
#                     - Strategy title \
#                     - Indicator calculations \
#                     - Buy and Sell entry conditions \
#                     - Exit conditions \
#                     - Stop Loss and Take Profit logic \
#                     - Ensure proper Pine Script syntax \
#                     Example Output: \
#                     //@version=5 \
#                     strategy(\"Strategy Name\", overlay=true) \
#                     ... \
#                 "},
#                 {"role": "user", "content": f"Convert this trading strategy to Pine Script: {
#                     strategy_json}"}
#             ]
#         )

#         #  Extract Pine Script from response
#         pine_script = response["choices"][0]["message"]["content"].strip()

#         #  Debugging: Print AI-generated Pine Script
#         print("🔍 AI-Generated Pine Script:\n", pine_script)

#         return {"pine_script": pine_script}

#     except Exception as e:
#         print(f"❌ ERROR: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

def extract_json_from_response(raw_response: str) -> dict:
    """
    Extracts JSON from a raw OpenAI response, handling common formatting issues.
    """
    # Remove triple backticks and any leading/trailing text
    if raw_response.startswith("```json"):
        raw_response = re.sub(r"```json|```", "", raw_response).strip()

    # Replace single quotes with double quotes for valid JSON
    formatted_response = re.sub(r"(?<!\\)'", '"', raw_response)

    try:
        return json.loads(formatted_response)
    except json.JSONDecodeError:
        # If parsing fails, attempt to extract JSON-like content
        json_match = re.search(r"\{.*\}", formatted_response, re.DOTALL)
        if json_match:
            try:
                return json.loads(json_match.group(0))
            except json.JSONDecodeError:
                pass
        raise



@router.post("/convert_to_backtrader")
def convert_to_backtrader(data: dict):
    """
    Converts a JSON trading strategy into a Backtrader-compatible Python strategy.
    """
    try:
        strategy_json = data.get("strategy")
        if not strategy_json:
            raise HTTPException(
                status_code=400, detail="Missing strategy JSON")

        #  Request AI to convert strategy to Backtrader code
        response = openai.ChatCompletion.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert in financial backtesting with Backtrader. "
                               "Convert the given trading strategy (in JSON format) into a Backtrader-compatible Python class. "
                               "Ensure it dynamically includes indicators, entry conditions, exit conditions, and risk management rules. "
                               "Return ONLY valid Python code in Quant cinnect format.Make sure you don't include any other text."
                               "Do not assume that anything exists in your code, if you notice that something needs to be implemented, implement it first before using it."
                    "Non negotiable conditions you must respect:"
                    "1. Indicators like `CrossOver()` are used correctly (use `self.crossover[0]` instead of direct boolean checks)."
                    "2. Strategy compiles without any `TypeError` or `IndexError`."
                    "3. Use `self.order = None` to track open orders."
                    "4. If using `buy_bracket()` or `sell_bracket()`, ensure that stop-loss and take-profit levels are correctly calculated."
                    "5. The generated strategy should be executable without modification in Backtrader."
                    "6. When using SecurityPortfolioManager, remember that it doesn't have isLong or isShort attributes. you have to user self.Portfolio[self.forex_pair].isLong instead"
                    "7. The code should include any needed imports and should be ready to run in a QuantConnect environment."
                    "8. The resulting train set should never be empty when backetested in QuantConnect env."
                    "Provide only correct Python code  and there should be no runtime errors on execution."

                },
                {
                    "role": "user",
                    "content": f"Convert this JSON strategy to a Backtrader Python strategy:\n\n{json.dumps(strategy_json, indent=4)}"
                }
            ]
        )

        #  Extract and clean response
        raw_response = response["choices"][0]["message"]["content"].strip()

        #  Debug: Print AI Response
        print("🔍 AI Backtrader Strategy Response:", raw_response)

        return {"backtrader_code": raw_response}

    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


import time

@router.post("/convert_to_quantconnect")
def convert_to_quantconnect(data: dict, retry_attempts=8):
    """
    Converts a JSON trading strategy into a QuantConnect-compatible Python algorithm
    that dynamically selects multiple forex pairs, timeframes, and timezones.

    If an error occurs in QuantConnect, it retries with an improved version of the code.
    """
    try:
        strategy_json = data.get("strategy")
        timeframe = data.get("timeframe")
        timezone = data.get("timezone")
        forex_pair = data.get("forex_pair")
        starting_balance = data.get("starting_balance")

        if not strategy_json:
            raise HTTPException(status_code=400, detail="Missing strategy JSON.")

        attempt = 0
        while attempt < retry_attempts:
            attempt += 1
            print(f"🔄 Attempt {attempt}/{retry_attempts} to generate QuantConnect code")

            #  Request AI to convert strategy to QuantConnect Python
            response = openai.ChatCompletion.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "You are an expert in algorithmic trading using QuantConnect. Convert the given trading strategy (in JSON format) "
                            "into a fully functional QuantConnect-compatible Python script.\n\n"
                            "### Requirements:\n"
                            f"1. **Choose 5 {forex_pair} as the forex pair unless a specific forex pair is specified in the strategy** .\n"
                            f"2. **Choose {timezone} as the Market timezone when the strategy will run. \n"
                            f"3️. **Choose {timeframe} as the timeframe for the strategy unless only one timeframe is specified in the strategy. \n\n"
                            f"4. **Starting balance of {starting_balance}**.\n"
                            "### ⚡ Algorithm Rules:\n"
                            "- Initialize forex pairs with different timeframes.\n"
                            "- Adjust for different timezones.\n"
                            "- Implement risk management, stop-loss, and take-profit levels.\n"
                            "- Use **History()** to warm up indicators before making trades.\n"
                            "- Ensure the code compiles and runs in QuantConnect.\n\n"
                            "###  Output:\n"
                            "- **Return only the complete Python code**, formatted correctly for QuantConnect.\n"
                            "- **Ensure all required functions are implemented** (No placeholders allowed).\n"
                        )
                    },
                    {
                        "role": "user",
                        "content": f"Convert this JSON strategy to a QuantConnect Python algorithm that dynamically selects 5 forex pairs, 3 timeframes, and 3 timezones:\n\n{json.dumps(strategy_json, indent=4)}"
                    }
                ]
            )

            #  Extract and clean AI response
            raw_response = response["choices"][0]["message"]["content"].strip()
            match = re.search(r"```python\s*([\s\S]+?)\s*```", raw_response)
            raw_response = match.group(1).strip() if match else raw_response.strip()

            print(" AI QuantConnect Strategy Response:", raw_response)

            #  Compile and test in QuantConnect
            project_id = get_quantconnect_project_id()
            compile_id = get_quantconnect_compile_id(project_id)
            backtest_id = submit_backtest(project_id, raw_response, compile_id)

            #  Polling for Backtest Completion Every 5 Seconds
            while True:
                time.sleep(5) 
                backtest_status = get_backtest_status(backtest_id)

                status = backtest_status.get("status", "Unknown")
                completed = backtest_status.get("completed", False)
                error_message = backtest_status.get("results", {}).get("backtest", {}).get("error", "")
                stacktrace = backtest_status.get("results", {}).get("backtest", {}).get("stacktrace", "")

                print(f"🔍 Backtest Status: {status}")

                if completed:
                    print(" Successfully generated and backtested QuantConnect strategy!")
                    return {
                        "quantconnect_code": raw_response,
                        "backtest_results": backtest_status
                    }

                if error_message:
                    print("❌ QuantConnect Error Detected. Retrying with AI Improvement...")
                    print(f"🔴 ERROR MESSAGE: {error_message}")
                    print(f"💀 STACK TRACE: {stacktrace}")

                    # Pass error message to AI for correction
                    response = openai.ChatCompletion.create(
                        model="gpt-4o",
                        messages=[
                            {
                                "role": "system",
                                "content": (
                                    "The last attempt to generate QuantConnect code resulted in an error. "
                                    "Here is the **error message and stack trace from QuantConnect**:\n\n"
                                    f"🛑 ERROR: {error_message}\n\n"
                                    f"💀 STACK TRACE: {stacktrace}\n\n"
                                    f"Your last generated code: {raw_response}\n\n"
                                    "### 🔧 Your task:\n"
                                    "1️⃣ Analyze the error and **fix any mistakes** in the generated QuantConnect Python algorithm.\n"
                                    "2️⃣ Ensure all forex pairs, timeframes, and timezones are correctly initialized.\n"
                                    "3️⃣ Make sure the trading logic runs correctly without errors.\n"
                                    "4️⃣ Ensure the code compiles successfully in QuantConnect.\n"
                                    "5️⃣ If the error is related to missing imports, **include the necessary imports**.\n\n"
                                    "**Return only the corrected Python code. Do not include any extra explanation.**"
                                )
                            },
                            {
                                "role": "user",
                                "content": f"Please correct the previous QuantConnect strategy to fix this error:\n\n{raw_response}"
                            }
                        ]
                    )

                    #  Extract improved code
                    improved_raw_response = response["choices"][0]["message"]["content"].strip()
                    match = re.search(r"```python\s*([\s\S]+?)\s*```", improved_raw_response)
                    improved_code = match.group(1).strip() if match else improved_raw_response.strip()

                    print("🔍 AI Improved QuantConnect Strategy Response:", improved_code)

                    #  Retry with the improved code
                    raw_response = improved_code
                    break  # Exit polling loop and retry the generation

        # ❌ If all attempts fail, return an error
        raise HTTPException(status_code=500, detail="❌ All retry attempts failed to generate working QuantConnect code.")

    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
