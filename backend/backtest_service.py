import tempfile
import subprocess
import re
import textwrap
import backtrader as bt
import pandas as pd
import yfinance as yf
import os
import importlib
import sys
import requests
from fastapi import APIRouter, HTTPException
import base64
import hashlib
import time
import uuid

router = APIRouter()

#   QuantConnect API Details
QUANTCONNECT_API_URL = "https://www.quantconnect.com/api/v2"
QUANTCONNECT_PROJECT_NAME = "AI_Backtest_Project"  # Project name for QuantConnect

QUANTCONNECT_API_KEY = os.getenv("QUANTCONNECT_API_KEY")
QUANTCONNECT_BASE_URL = "https://www.quantconnect.com/api/v2/"
QUANTCONNECT_USER_ID = os.getenv("QUANTCONNECT_USER_ID")
BACKTEST_NAME = "AI_Generated_Backtest"


def get_quantconnect_project_id(project_name="AI_Generated_Backtest"):
    """
    Retrieves or creates a QuantConnect project.
    """
    # Get timestamp
    timestamp = str(int(time.time()))
    time_stamped_token = QUANTCONNECT_API_KEY + ':' + timestamp
    # Get hased API token
    hashed_token = hashlib.sha256(time_stamped_token.encode('utf-8')).hexdigest()
    authentication = "{}:{}".format(348375, hashed_token)
    api_token = base64.b64encode(authentication.encode('utf-8')).decode('ascii')
    print(api_token)
    url = f"{QUANTCONNECT_BASE_URL}projects/read"
    headers = {
        'Authorization': 'Basic %s' % api_token,
        'Timestamp': timestamp
    }

    response = requests.get(url, headers=headers)

    print(f"🔍 QuantConnect Project Read Response: {response.status_code} - {response.text}")

    if response.status_code != 200:
        raise HTTPException(status_code=500, detail="❌ Failed to retrieve projects from QuantConnect")

    projects = response.json().get("projects", [])
    
    #   Debug: Print existing projects
    print(f"🔍 Existing Projects: {projects}")
    if projects:
        project_id = projects[0]["projectId"]
        print(f"  Found existing project: {project_id}")
        return project_id
    raise HTTPException(status_code=500, detail="❌ No projectId returned from QuantConnect.")

def generate_api_token():
    """
    Generates the API token for QuantConnect.
    """
    # Get timestamp
    timestamp = str(int(time.time()))
    time_stamped_token = QUANTCONNECT_API_KEY + ':' + timestamp
    # Get hased API token
    hashed_token = hashlib.sha256(time_stamped_token.encode('utf-8')).hexdigest()
    authentication = "{}:{}".format(QUANTCONNECT_USER_ID, hashed_token)
    api_token = base64.b64encode(authentication.encode('utf-8')).decode('ascii')
    return api_token, timestamp

def delete_existing_file(project_id, file_name):
    """
    Deletes an existing file from the QuantConnect project.
    """
    api_token, timestamp = generate_api_token()
    headers = {
        'Authorization': 'Basic %s' % api_token,
        'Timestamp': timestamp
    }
    delete_url = f"{QUANTCONNECT_BASE_URL}files/delete"
    delete_data = {
        "projectId": project_id,
        "name": file_name
    }

    delete_response = requests.post(delete_url, headers=headers, json=delete_data)
    print("🗑️ Delete Response:", delete_response.status_code, delete_response.json())

def list_project_files(project_id):
    """
    Retrieves all files in the QuantConnect project to check for any existing errors.
    """
    api_token, timestamp = generate_api_token()
    headers = {'Authorization': f'Basic {api_token}', 'Timestamp': timestamp}

    url = f"{QUANTCONNECT_BASE_URL}files/read"
    params = {"projectId": project_id}
    
    response = requests.get(url, headers=headers, params=params)
    files = response.json().get("files", [])
    
    print(f"📂 Existing Files in Project: {[file['name'] for file in files]}")
    return files


def delete_all_files(project_id):
    """
    Deletes all files in the project to clear any broken code before compiling.
    """
    api_token, timestamp = generate_api_token()
    headers = {'Authorization': f'Basic {api_token}', 'Timestamp': timestamp}

    files = list_project_files(project_id)
    for file in files:
        delete_url = f"{QUANTCONNECT_BASE_URL}files/delete"
        delete_data = {"projectId": project_id, "name": file["name"]}

        delete_response = requests.post(delete_url, headers=headers, json=delete_data)
        print(f"🗑️ Deleting {file['name']}: {delete_response.status_code} - {delete_response.json()}")


def submit_backtest(project_id, strategy_code, compile_id):
    """
    Submits the backtest request to QuantConnect.
    """
    # delete_existing_file(project_id, "main.py")

    api_token, timestamp = generate_api_token()
    url = f"{QUANTCONNECT_BASE_URL}projects/read"
    headers = {
        'Authorization': 'Basic %s' % api_token,
        'Timestamp': timestamp
    }
    #  Wait for Compilation to Complete
    while True:
        time.sleep(5)  # 🔄 Wait 5 seconds before checking status
        status_url = f"{QUANTCONNECT_BASE_URL}compile/read?projectId={project_id}&compileId={compile_id}"
        status_response = requests.get(status_url, headers=headers)
        status_json = status_response.json()

        if status_json.get("state") == "BuildSuccess":
            print("  Compilation Completed Successfully!")
            break
        elif status_json.get("state") in ["BuildError", "RuntimeError"]:
            print(f"❌ Compilation Failed: {status_json}")
            # delete_all_files(project_id)
            # submit_backtest(project_id, strategy_code, compile_id)
        else:
            print(f"⏳ Waiting for compilation... Current status: {status_json.get('state')}")

    # #   Generate a unique filename for the strategy
    # strategy_filename = f"strategy_{uuid.uuid4().hex[:8]}.py"

    delete_existing_file(project_id, "main.py")

    ### **STEP 2: UPLOAD NEW STRATEGY FILE**
    upload_url = f"{QUANTCONNECT_BASE_URL}files/create"
    upload_data = {
        "projectId": project_id,
        "name": "main.py",
        "content": strategy_code
    }

    upload_response = requests.post(upload_url, headers=headers, json=upload_data)
    print("🔍 Upload Response:", upload_response.status_code, upload_response.json())
    if upload_response.status_code != 200 or not upload_response.json().get("success", False):
        print("❌ ERROR: Failed to upload strategy code.")
        raise HTTPException(status_code=500, detail="Failed to upload strategy code.")
    

    #   Start Backtest
    backtest_url = f"{QUANTCONNECT_BASE_URL}backtests/create"
    backtest_data = {
        "projectId": project_id,
        "backtestName": BACKTEST_NAME,
        "compileId": compile_id
    }
    backtest_response = requests.post(backtest_url, headers=headers, json=backtest_data)
    backtest_json = backtest_response.json()

    if backtest_json.get("success"):
        #   Extract Backtest ID Properly
        backtest_id = backtest_json.get("backtest", {}).get("backtestId")

    if backtest_id:
        print(f"🚀 Backtest Started! Backtest ID: {backtest_id}")
        return backtest_id
    
    print(f"❌ API Error: {backtest_json}")  # Print detailed error message
    raise HTTPException(status_code=500, detail="❌ Failed to start backtest on QuantConnect")


def fetch_backtest_results(project_id, backtest_id):
    """
    Fetches the backtest results from QuantConnect.
    """
    api_token, timestamp = generate_api_token()
    headers = {
        'Authorization': 'Basic %s' % api_token,
        'Timestamp': timestamp
    }
    url = f"{QUANTCONNECT_BASE_URL}backtests/read"
    data = {
        "projectId": project_id,
        "backtestId": backtest_id
    }
    results_response = requests.get(f"{url}", headers=headers, json=data)

    if results_response.status_code == 200:
        return results_response.json()
    else:
        raise HTTPException(status_code=500, detail="❌ Failed to fetch backtest results from QuantConnect")
    
def get_quantconnect_compile_id(project_id):
    """
    Retrieves the compile id for the backtest.
    """
    api_token, timestamp = generate_api_token()
    #   Set Headers
    headers = {
        "Authorization": f"Basic {api_token}",
        "Timestamp": timestamp
    }
    #   Compile Request
    compile_url = f"{QUANTCONNECT_BASE_URL}compile/create"
    compile_data = {"projectId": project_id}

    compile_response = requests.post(compile_url, headers=headers, json=compile_data)
    compile_response_json = compile_response.json()

    if compile_response_json.get("success") is False:
        print(f"❌ Compile Error: {compile_response_json}")
        exit(1)

    compile_id = compile_response_json.get("compileId")
    print(f"  Compile ID: {compile_id}")
    return compile_id

def fetch_backtest_status(project_id, backtest_id):
    """
    Fetches the status of a QuantConnect backtest.
    """
    api_token, timestamp = generate_api_token()
    headers = {"Authorization": f"Basic {api_token}", "Timestamp": timestamp}
    url = f"{QUANTCONNECT_BASE_URL}backtests/read"
    data = {"projectId": project_id, "backtestId": backtest_id}

    status_response = requests.get(url, headers=headers, json=data)

    if status_response.status_code == 200:
        return status_response.json().get("backtest", {})
    else:
        return {"status": "Unknown", "error": "Failed to fetch backtest status"}



@router.post("/run")
def run_backtest(data: dict):
    """
    Runs the QuantConnect backtest and fetches results when completed.
    """
    try:
        quantconnect_code = data.get("quantconnect_code")

        if not quantconnect_code:
            raise HTTPException(status_code=400, detail="Missing QuantConnect strategy code")

        #   Step 1: Get or create a QuantConnect project
        project_id = get_quantconnect_project_id()

        #   Step 2: Compile the strategy code
        compile_id = get_quantconnect_compile_id(project_id)

        #   Step 3: Submit Backtest
        backtest_id = submit_backtest(project_id, quantconnect_code, compile_id)

        #   Step 5: Fetch Final Results
        final_results = fetch_backtest_results(project_id, backtest_id)
        return {"backtest_id": backtest_id, "results": final_results}

    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status")
def get_backtest_status(backtest_id: str, retries=3):
    """
    Fetches the current status of a running backtest.
    Stops polling when the status is 'Completed'.
    """
    try:
        api_token, timestamp = generate_api_token()
        headers = {
            'Authorization': f'Basic {api_token}',
            'Timestamp': timestamp
        }
        url = f"{QUANTCONNECT_BASE_URL}backtests/read"
        data = {
            "projectId": get_quantconnect_project_id(),
            "backtestId": backtest_id
        }
        
        response = requests.get(url, headers=headers, json=data)
        response_json = response.json()

        if response.status_code == 200 and response_json.get("success"):
            status = response_json.get("backtest", {}).get("status", "Unknown")
            print(f"🔄 Backtest Status: {status}")
            if status == "Completed.":
                print("  Backtest Completed Successfully!")
                return {"status": status, "completed": True, "results": response_json}

             # **Check if there's an error message in the backtest logs**
            error_message = response_json.get("backtest", {}).get("error")
            stacktrace = response_json.get("backtest", {}).get("stacktrace")
            if error_message:
                if stacktrace:
                    print(f"💀 **Stacktrace:**\n{stacktrace}")  
                print(f"❌ **QuantConnect Backtest Error:** {error_message}")
                # raise HTTPException(status_code=500, detail=f"QuantConnect Backtest Error: {error_message}")
            
            
            #   Return a "completed" flag to stop polling
            return {"status": status, "completed": status == "Completed", "results": response_json}

        return {"status": "Error fetching status", "completed": False}
    
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch backtest status: {str(e)}")
