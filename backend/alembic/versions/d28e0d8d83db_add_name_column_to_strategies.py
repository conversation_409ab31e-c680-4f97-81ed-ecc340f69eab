"""Add name column to strategies

Revision ID: d28e0d8d83db
Revises: 
Create Date: 2025-03-02 21:52:52.100535

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd28e0d8d83db'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('strategies', sa.Column('name', sa.String(), nullable=False))
    op.drop_column('strategies', 'strategy_name')
    op.drop_column('strategies', 'pine_script')
    op.drop_column('strategies', 'created_at')
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('users', 'password',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('users', 'account_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'account_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('users', 'password',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.add_column('strategies', sa.Column('created_at', sa.VARCHAR(), nullable=False))
    op.add_column('strategies', sa.Column('pine_script', sa.TEXT(), nullable=True))
    op.add_column('strategies', sa.Column('strategy_name', sa.VARCHAR(), nullable=False))
    op.drop_column('strategies', 'name')
    # ### end Alembic commands ###
