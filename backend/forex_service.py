import requests
from fastapi import APIRouter, HTTPException, Depends
import random
import os
from sqlalchemy.orm import Session
from dotenv import load_dotenv
from database import SessionLocal, User

load_dotenv()

router = APIRouter(prefix="/forex", tags=["forex"])

FOREX_API_KEY = os.getenv("FOREX_RATE_API_KEY")
FOREX_API_URL = "https://api.forexrateapi.com/v1/latest"
OANDA_API_URL = "https://api-fxpractice.oanda.com/v3" # set oanda url

if not FOREX_API_KEY:
    raise Exception("🚨 ERROR: FOREX_RATE_API_KEY is missing. Set it in your .env file.")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/chart/{pair}")
def get_forex_chart(pair: str):
    """
    Simulates Forex price chart data.
    Replace with real API calls if needed.
    """
    try:
        formatted_pair = pair.replace("-", "/")
        prices = [round(random.uniform(1.0, 1.5), 5) for _ in range(30)]
        return {
            "labels": [f"Point {i}" for i in range(30)],
            "datasets": [{"label": formatted_pair, "data": prices}]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/price/{pair}")
def get_forex_price(pair: str):
    """
    Fetch the latest exchange rate for the given forex pair.
    """
    try:
        base_currency, quote_currency = pair.split("-")
        url = f"{FOREX_API_URL}?api_key={FOREX_API_KEY}&base={base_currency}"

        print(f"🔍 Fetching Forex Price: {url}")  #   Debugging line

        response = requests.get(url)

        #   Check if response is valid JSON
        try:
            data = response.json()
        except ValueError:
            raise HTTPException(status_code=500, detail="Invalid JSON response from Forex API")

        # print(f"🔍 API Response Status: {response.status_code}")
        # print(f"🔍 API Response JSON: {data}")

        #   Check if API returned success
        if not data.get("success"):
            raise HTTPException(status_code=500, detail="Forex API request failed")

        #   Extract Exchange Rate
        rates = data.get("rates", {})
        exchange_rate = rates.get(quote_currency)

        if exchange_rate is None:
            raise HTTPException(status_code=400, detail=f"Exchange rate not available for {pair}")

        return {"pair": pair, "price": exchange_rate}

    except Exception as e:
        print(f"❌ Error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/pairs/{user_id}")
def get_forex_pairs(user_id: int, db: Session = Depends(get_db)):
    """
    Fetch tradable forex pairs from OANDA dynamically using the user's account ID.
    """
    #   Retrieve the user's OANDA account ID dynamically
    user = db.query(User).filter(User.id == user_id).first()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if not user.account_id:
        raise HTTPException(status_code=400, detail="User does not have an OANDA account ID")

    account_id = user.account_id
    oanda_api_key = user.api_key

    headers = {"Authorization": f"Bearer {oanda_api_key}"}
    instruments_url = f"{OANDA_API_URL}/accounts/{account_id}/instruments"
    
    response = requests.get(instruments_url, headers=headers)

    # print(f"🔍 Fetching Pairs from: {instruments_url}")
    # print(f"🔍 API Response Status: {response.status_code}")
    # print(f"🔍 API Response Text: {response.text}")

    if response.status_code != 200:
        raise HTTPException(status_code=response.status_code, detail="Failed to fetch forex pairs from OANDA")

    data = response.json()
    instruments = data.get("instruments", [])
    #   Extract only forex pairs (exclude CFDs, metals, etc.)
    #   Extract only forex pairs (skip metals, CFDs, etc.)
    forex_pairs = [instr["name"] for instr in instruments if instr["type"] == "CURRENCY"]
    # print(forex_pairs)
    return {"forex_pairs": forex_pairs}

@router.post("/trade/execute")
def execute_trade(data: dict):
    """
    Simulates trade execution.
    Replace with real execution logic.
    """
    return {"message": f"Trade executed: {data['type']} {data['lotSize']} lots of {data['pair']}"}