from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from pydantic import BaseModel
import json

from database import SessionLocal, User, Strategy, engine, Base
Base.metadata.create_all(engine)

router = APIRouter()

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Request model for saving a strategy
class SaveStrategyRequest(BaseModel):
    userId: int
    strategy: dict

# Request model for updating a strategy
class UpdateStrategyRequest(BaseModel):
    userId: int
    strategyId: int
    strategy: dict

@router.put("/update")
def update_strategy(request: UpdateStrategyRequest, db: Session = Depends(get_db)):
    # Verify the user exists
    user = db.query(User).filter(User.id == request.userId).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Find the strategy by ID and ensure it belongs to the user
    strategy = (
        db.query(Strategy)
        .filter(Strategy.id == request.strategyId, Strategy.user_id == request.userId)
        .first()
    )
    if not strategy:
        raise HTTPException(status_code=404, detail="Strategy not found")
    
    # Update strategy fields
    strategy.name = request.strategy.get("name", strategy.name)
    strategy.strategy_json = json.dumps(request.strategy)
    
    db.commit()
    db.refresh(strategy)
    return {"message": "Strategy updated successfully", "strategy_id": strategy.id}


@router.post("/save")
def save_strategy(request: SaveStrategyRequest, db: Session = Depends(get_db)):
    # Check if user exists
    user = db.query(User).filter(User.id == request.userId).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Convert strategy dict to JSON string.
    strategy_json = json.dumps(request.strategy)
    
    # Create and store the strategy.
    new_strategy = Strategy(
        user_id=request.userId,
        name=request.strategy.get("name", "Unnamed Strategy"),
        strategy_json=strategy_json
    )
    db.add(new_strategy)
    db.commit()
    db.refresh(new_strategy)
    return {"message": "Strategy saved successfully", "strategy_id": new_strategy.id}

@router.get("/strategies/{userId}")
def get_strategies(userId: int, db: Session = Depends(get_db)):
    strategies = db.query(Strategy).filter(Strategy.user_id == userId).all()
    return [
        {
            "id": s.id,
            "name": s.name,
            "strategy_json": s.strategy_json
        }
        for s in strategies
    ]