#!/usr/bin/env python3
"""
<PERSON>ript to publish a strategy execution message to production PubSub.
This will trigger the strategy controller to create a trade bot pod.
"""

import json
import os
import subprocess
from datetime import datetime, timezone

# Configuration
PROJECT_ID = "oryntrade"
TOPIC_NAME = "strategy-execution"
USER_ID = "FZ7Le0COHDNjlMWAO9O6hzc0xdz2"
STRATEGY_ID = "RmeQgNY7gm6cN8ZnVgzb"

def publish_strategy_message():
    """Publish a strategy execution message to PubSub using gcloud CLI."""
    try:
        print(f"🚀 Publishing strategy execution message...")
        print(f"   Project: {PROJECT_ID}")
        print(f"   Topic: {TOPIC_NAME}")
        print(f"   User ID: {USER_ID}")
        print(f"   Strategy ID: {STRATEGY_ID}")

        # Create the message payload
        message_data = {
            "strategy": {
                "id": STRATEGY_ID,
                "user_id": USER_ID,
                "name": "<PERSON><PERSON>'s Strat",
                "instruments": "EUR/USD",
                "timeframe": "5m",
                "status": "pending"
            },
            "action": "start",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": "manual_test"
        }

        # Convert to JSON
        message_json = json.dumps(message_data)

        print(f"\n📋 Message payload:")
        print(json.dumps(message_data, indent=2))

        # Publish using gcloud CLI
        print(f"\n📤 Publishing message using gcloud CLI...")

        cmd = [
            "gcloud", "pubsub", "topics", "publish", TOPIC_NAME,
            "--message", message_json,
            "--project", PROJECT_ID
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print(f"✅ Message published successfully!")
            print(f"   Output: {result.stdout.strip()}")
            print(f"   Timestamp: {datetime.now(timezone.utc).isoformat()}")
            return True
        else:
            print(f"❌ Error publishing message:")
            print(f"   Return code: {result.returncode}")
            print(f"   Error: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ Error publishing message: {str(e)}")
        return False

def create_topic_if_needed():
    """Create the topic if it doesn't exist using gcloud CLI."""
    try:
        print(f"🔧 Checking/creating topic: {TOPIC_NAME}")

        # Try to create the topic (will succeed if it doesn't exist, fail if it does)
        cmd = [
            "gcloud", "pubsub", "topics", "create", TOPIC_NAME,
            "--project", PROJECT_ID
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            print(f"✅ Topic created: {TOPIC_NAME}")
            return True
        elif "already exists" in result.stderr.lower():
            print(f"✅ Topic already exists: {TOPIC_NAME}")
            return True
        else:
            print(f"⚠️  Topic check result:")
            print(f"   Return code: {result.returncode}")
            print(f"   Error: {result.stderr}")
            print(f"   Continuing anyway - strategy controller should handle it")
            return True

    except Exception as e:
        print(f"⚠️  Error checking topic: {str(e)}")
        print(f"   Continuing anyway - strategy controller should handle it")
        return True

def main():
    """Main function."""
    print("🎯 Publishing Strategy Execution Message to Production PubSub")
    print("=" * 60)
    
    # Check/create topic
    if not create_topic_if_needed():
        print("❌ Failed to ensure topic exists")
        return
    
    print()
    
    # Publish the message
    if publish_strategy_message():
        print(f"\n🎉 Success! Strategy execution message published.")
        print(f"\n👀 Now watch the strategy controller logs to see:")
        print(f"   1. Message received and processed")
        print(f"   2. Trade bot pod creation")
        print(f"   3. Trade bot startup and execution")
        print(f"\n📊 Monitor with:")
        print(f"   kubectl logs -f -l app=strategy-controller")
        print(f"   kubectl get pods -l app=trade-bot")
    else:
        print(f"\n❌ Failed to publish message")

if __name__ == "__main__":
    main()
