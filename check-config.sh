#!/bin/bash
# Configuration Status Checker
# This script checks the current configuration status across all services

echo "🔍 OrynTrade Configuration Status Checker"
echo "========================================"
echo ""

# Check centralized configuration
echo "📋 Centralized Configuration:"
if [ -f "config/config_loader.py" ]; then
    python3 -c "
import sys
from pathlib import Path
config_dir = Path('config')
sys.path.append(str(config_dir))
try:
    from config_loader import get_config_loader
    config = get_config_loader()
    print(f'   Mode: {config.environment_name.upper()}')
    print(f'   Development: {config.is_development}')
except Exception as e:
    print(f'   Error: {e}')
"
else
    echo "   ❌ config/config_loader.py not found"
fi
echo ""

# Check frontend configuration
echo "🌐 Frontend Configuration:"
if [ -f "frontend/src/config.js" ]; then
    firebase_emulator=$(grep "USE_FIREBASE_EMULATOR" frontend/src/config.js | head -1)
    echo "   config.js: $firebase_emulator"
else
    echo "   ❌ frontend/src/config.js not found"
fi

if [ -f "frontend/.env.local" ]; then
    echo "   .env.local settings:"
    grep -E "NEXT_PUBLIC_USE_PRODUCTION|NEXT_PUBLIC_USE_FIREBASE_EMULATOR" frontend/.env.local | sed 's/^/      /'
else
    echo "   ❌ frontend/.env.local not found"
fi
echo ""

# Check trade-bot-service configuration
echo "🤖 Trade-Bot Service Configuration:"
if [ -f "trade-bot-service/.env" ]; then
    echo "   .env settings:"
    grep -E "USE_FIREBASE_EMULATOR|MARKET_DATA_PROVIDER|BYPASS_MARKET_IS_CLOSED|FIRESTORE_EMULATOR_HOST|PUBSUB_EMULATOR_HOST" trade-bot-service/.env | sed 's/^/      /'
else
    echo "   ❌ trade-bot-service/.env not found"
fi
echo ""

# Check polygon-websocket-service configuration
echo "📡 Polygon WebSocket Service Configuration:"
if [ -f "polygon-websocket-service/.env" ]; then
    echo "   .env settings:"
    grep -E "PUBSUB_EMULATOR_HOST|GOOGLE_CLOUD_PROJECT" polygon-websocket-service/.env | sed 's/^/      /'
    if ! grep -q "PUBSUB_EMULATOR_HOST" polygon-websocket-service/.env; then
        echo "      PUBSUB_EMULATOR_HOST: not set (production mode)"
    fi
else
    echo "   ❌ polygon-websocket-service/.env not found"
fi
echo ""

# Check websocket-data-distribution-service configuration
echo "🌐 WebSocket Data Distribution Service Configuration:"
if [ -f "websocket-data-distribution-service/.env" ]; then
    echo "   .env settings:"
    grep -E "WEBSOCKET_PORT|PUBSUB_EMULATOR_HOST|GOOGLE_CLOUD_PROJECT" websocket-data-distribution-service/.env | sed 's/^/      /'
    if ! grep -q "PUBSUB_EMULATOR_HOST" websocket-data-distribution-service/.env; then
        echo "      PUBSUB_EMULATOR_HOST: not set (production mode)"
    fi
else
    echo "   ❌ websocket-data-distribution-service/.env not found"
fi
echo ""

# Check running services
echo "🚀 Running Services:"
echo "   Checking ports..."

# Check Firebase emulator
if lsof -i :4000 > /dev/null 2>&1; then
    echo "   ✅ Firebase UI (port 4000)"
else
    echo "   ❌ Firebase UI (port 4000) - not running"
fi

# Check Firestore emulator
if lsof -i :8082 > /dev/null 2>&1; then
    echo "   ✅ Firestore Emulator (port 8082)"
else
    echo "   ❌ Firestore Emulator (port 8082) - not running"
fi

# Check PubSub emulator
if lsof -i :8085 > /dev/null 2>&1; then
    echo "   ✅ PubSub Emulator (port 8085)"
else
    echo "   ❌ PubSub Emulator (port 8085) - not running"
fi

# Check Strategy Controller
if lsof -i :8080 > /dev/null 2>&1; then
    echo "   ✅ Strategy Controller (port 8080)"
else
    echo "   ❌ Strategy Controller (port 8080) - not running"
fi

# Check WebSocket Data Service
if lsof -i :8765 > /dev/null 2>&1; then
    echo "   ✅ WebSocket Data Service (port 8765)"
else
    echo "   ❌ WebSocket Data Service (port 8765) - not running"
fi

# Check Polygon WebSocket Service
if lsof -i :8081 > /dev/null 2>&1; then
    echo "   ✅ Polygon WebSocket Service (port 8081)"
else
    echo "   ❌ Polygon WebSocket Service (port 8081) - not running"
fi

# Check Frontend
if lsof -i :3000 > /dev/null 2>&1; then
    echo "   ✅ Frontend (port 3000)"
else
    echo "   ❌ Frontend (port 3000) - not running"
fi

echo ""

# Environment variables check
echo "🔧 Environment Variables:"
echo "   DEVELOPMENT_MODE: ${DEVELOPMENT_MODE:-not set}"
echo "   USE_FIREBASE_EMULATOR: ${USE_FIREBASE_EMULATOR:-not set}"
echo "   FIRESTORE_EMULATOR_HOST: ${FIRESTORE_EMULATOR_HOST:-not set}"
echo "   PUBSUB_EMULATOR_HOST: ${PUBSUB_EMULATOR_HOST:-not set}"
echo "   GOOGLE_CLOUD_PROJECT: ${GOOGLE_CLOUD_PROJECT:-not set}"
echo ""

# Summary
echo "📊 Configuration Summary:"
if [ -f "frontend/.env.local" ] && grep -q "NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET=false" frontend/.env.local; then
    echo "   🔧 Mode: DEVELOPMENT"
    echo "   📝 Services should use local emulators and localhost URLs"
elif [ -f "frontend/.env.local" ] && grep -q "NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET=true" frontend/.env.local; then
    echo "   🚀 Mode: PRODUCTION"
    echo "   📝 Services should use production Firebase/PubSub and Cloud Run URLs"
else
    echo "   ❓ Mode: UNKNOWN - check configuration files"
fi
echo ""
echo "💡 Use './use-dev.sh' to switch to development mode"
echo "💡 Use './use-prod.sh' to switch to production mode"
