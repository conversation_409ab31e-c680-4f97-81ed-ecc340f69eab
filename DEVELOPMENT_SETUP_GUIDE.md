# 🛠️ Development Setup Guide

## 📋 Overview

This guide explains how to set up and run the Oryn Trading Bot system locally for development. The system now uses a **single source of truth configuration** that makes it easy to switch between development and production environments.

## 🎯 Key Features

- **Single Flag Control**: One `DEVELOPMENT_MODE` flag controls all services
- **Automatic Environment Setup**: Services automatically configure themselves based on the flag
- **Easy Switching**: Simple scripts to switch between development and production
- **Local Development**: Run everything locally with Firebase and PubSub emulators

## 🔧 Configuration System

### **Master Switch**
The entire system is controlled by a single `DEVELOPMENT_MODE` flag in `config/config_loader.py`:

```python
DEVELOPMENT_MODE: True   # Development mode
DEVELOPMENT_MODE: False  # Production mode
```

### **Environment Configurations**

#### **Development Mode** (`DEVELOPMENT_MODE: True`)
- **Firebase**: Local emulator (localhost:8082)
- **PubSub**: Local emulator (localhost:8085)
- **Strategy Controller**: Local process (localhost:8080)
- **Trade Bots**: Local processes (not Kubernetes pods)

#### **Production Mode** (`DEVELOPMENT_MODE: False`)
- **Firebase**: Production Firestore
- **PubSub**: Production PubSub
- **Strategy Controller**: Kubernetes cluster
- **Trade Bots**: Kubernetes pods

## 🚀 Quick Start

### **Development Mode**
```bash
./use-dev.sh
```
Then follow the terminal instructions to start each service.

### **Production Mode**
```bash
./use-prod.sh
```
Then follow the terminal instructions to connect to production.

### **6. Test a Strategy (Optional)**
```bash
# Set environment variables for trade bot
export STRATEGY_ID=test-strategy-123
export USER_ID=test-user-456

# Run trade bot locally
cd trade-bot-service
./run_local.sh
```

## 📝 Available Scripts

### **Environment Management**

#### **Use Development Mode**
```bash
./use-dev.sh
```
- Switches to development mode
- Updates all configurations
- Provides step-by-step instructions to start services
- Shows access URLs

#### **Use Production Mode**
```bash
./use-prod.sh
```
- Switches to production mode
- Updates all configurations
- Provides instructions for production access
- Shows monitoring URLs

### **Development Runners (Using Existing Scripts)**

#### **Strategy Controller (Local)**
```bash
cd strategy-controller-service
./run_local.sh
```
- Uses existing run_local.sh script
- Automatically configures environment variables
- Loads centralized configuration if available
- Uses local trade bot processes instead of Kubernetes

#### **Trade Bot (Local)**
```bash
# Set required environment variables
export STRATEGY_ID=your-strategy-id
export USER_ID=your-user-id

cd trade-bot-service
./run_local.sh
```
- Uses existing run_local.sh script
- Requires `STRATEGY_ID` and `USER_ID` environment variables
- Automatically configures Firebase and PubSub connections
- Loads centralized configuration if available

### **Configuration Testing**
```bash
python3 test-config.py
```
- Tests the configuration system
- Shows current environment settings
- Displays environment variables

## 🔍 How It Works

### **Configuration Flow**
```
config_loader.py
    ↓
Sets DEVELOPMENT_MODE flag
    ↓
Configures environment variables:
- FIRESTORE_EMULATOR_HOST
- PUBSUB_EMULATOR_HOST
- DEVELOPMENT_MODE
    ↓
Services read environment variables
    ↓
Services connect to appropriate endpoints
```

### **Service Configuration**

#### **Strategy Controller**
- Reads configuration on startup
- Sets up Firebase and PubSub connections
- Uses local trade bot processes in development mode
- Uses Kubernetes pods in production mode

#### **Trade Bot**
- Inherits environment variables from strategy controller
- Connects to appropriate Firebase and PubSub endpoints
- Runs as local process in development
- Runs as Kubernetes pod in production

#### **Frontend**
- Uses `USE_FIREBASE_EMULATOR` flag
- Automatically connects to correct Firebase instance
- Strategy controller URL configured per environment

## 🧪 Testing the Setup

### **1. Test Configuration**
```bash
python3 test-config.py
```

### **2. Test Development Environment**
```bash
# Switch to development and follow instructions
./use-dev.sh
```

### **3. Test Production Environment**
```bash
# Switch to production and follow instructions
./use-prod.sh
```

## 🔧 Environment Variables

### **Automatically Set by Configuration System**
- `DEVELOPMENT_MODE`: `true` or `false`
- `FIRESTORE_EMULATOR_HOST`: Set only in development mode
- `PUBSUB_EMULATOR_HOST`: Set only in development mode

### **Required for Trade Bot (Development)**
- `STRATEGY_ID`: ID of the strategy to execute
- `USER_ID`: ID of the user who owns the strategy

### **Optional**
- `USE_LOCAL_TRADE_BOT`: Set to `true` for local trade bot processes

## 🚨 Troubleshooting

### **Configuration Issues**
```bash
# Reset to development mode
./use-dev.sh

# Reset to production mode
./use-prod.sh
```

### **Firebase Emulator Issues**
```bash
# Stop all emulators
pkill -f firebase

# Restart emulators
cd functions && ./start_emulator.sh
```

### **PubSub Emulator Issues**
```bash
# Stop PubSub emulator
pkill -f "gcloud.*pubsub"

# Restart PubSub emulator
gcloud beta emulators pubsub start --project=oryntrade

# Check if PubSub emulator is running
ps aux | grep pubsub
```

### **Strategy Controller Issues**
```bash
# Check if running in correct mode
cd strategy-controller-service
python3 run_local_dev.py
```

### **Trade Bot Issues**
```bash
# Ensure environment variables are set
echo $STRATEGY_ID
echo $USER_ID

# Run with debug output
cd trade-bot-service
python3 run_local_dev.py
```

## 📊 Development vs Production Comparison

| Component | Development | Production |
|-----------|-------------|------------|
| Firebase | Local Emulator (8082) | Production Firestore |
| PubSub | Local Emulator (8085) | Production PubSub |
| Strategy Controller | Local Process (8080) | Kubernetes Pod |
| Trade Bots | Local Processes | Kubernetes Pods |
| Frontend | localhost:3000 | localhost:3000 |
| Configuration | Single flag switch | Single flag switch |
| **Setup Command** | `./use-dev.sh` | `./use-prod.sh` |
| **PubSub Command** | `gcloud beta emulators pubsub start --project=oryntrade` | N/A (Production) |

## 🎉 Benefits

- **🔄 Easy Switching**: One command to switch environments
- **🧪 Local Testing**: Full system testing without cloud resources
- **💰 Cost Effective**: No cloud costs during development
- **⚡ Fast Iteration**: Auto-reload and local processes
- **🔒 Safe Development**: Isolated from production data
- **📋 Consistent**: Same codebase for both environments

This setup provides a seamless development experience while maintaining production compatibility!
