# GitHub Actions CI/CD Setup

This document provides a complete guide for setting up automated deployments for the Trade Bot Service and Strategy Controller Service using GitHub Actions.

## 🎯 Overview

The GitHub Actions setup provides:

- **Automated deployments** when code changes are pushed to main/master
- **Path-based triggering** - only deploys services when their directories change
- **Production configuration validation** - ensures development flags are disabled
- **Validation pipelines** for pull requests
- **Manual deployment triggers** via GitHub Actions UI
- **Comprehensive logging and status reporting**

## 📁 Files Created

### GitHub Actions Workflows (`.github/workflows/`)
- `deploy-trade-bot-service.yml` - Deploys trade bot when `trade-bot-service/**` changes
- `deploy-strategy-controller-service.yml` - Deploys strategy controller when `strategy-controller-service/**` changes
- `deploy-websocket-data-distribution-service.yml` - Deploys websocket data distribution when `websocket-data-distribution-service/**` changes
- `deploy-polygon-websocket-service.yml` - Deploys polygon websocket when `polygon-websocket-service/**` changes
- `deploy-all-services.yml` - Deploys multiple services (manual trigger or shared config changes)
- `validate-deployments.yml` - Validates scripts and manifests on pull requests
- `validate-production-config.yml` - Validates production configuration flags
- `README.md` - Detailed workflow documentation

### Setup Scripts
- `setup-github-actions.sh` - Automated setup script for Google Cloud service account
- `GITHUB_ACTIONS_SETUP.md` - This documentation file

## 🚀 Quick Setup

### 1. Run the Setup Script
```bash
./setup-github-actions.sh
```

This script will:
- Create a Google Cloud service account with necessary permissions
- Generate a service account key file
- Verify cluster and registry access
- Provide next steps

### 2. Add GitHub Secret
1. Copy the service account key content:
   ```bash
   cat github-actions-key.json
   ```

2. Go to your GitHub repository → Settings → Secrets and variables → Actions

3. Click "New repository secret"
   - **Name**: `GCP_SA_KEY`
   - **Value**: Paste the entire JSON content

### 3. Commit and Push Workflows
```bash
git add .github/
git commit -m "Add GitHub Actions CI/CD workflows"
git push origin main
```

## 🔄 How It Works

### Deployment Triggers

#### Trade Bot Service
- **Triggers**: Changes to `trade-bot-service/**`
- **Script**: Uses `trade-bot-service/deploy-to-k8s.sh`
- **Target**: Updates `trade-bot-base` deployment

#### Strategy Controller Service
- **Triggers**: Changes to `strategy-controller-service/**`
- **Script**: Uses `strategy-controller-service/deploy-to-k8s.sh`
- **Target**: Updates `strategy-controller` deployment

#### WebSocket Data Distribution Service
- **Triggers**: Changes to `websocket-data-distribution-service/**`
- **Script**: Uses `websocket-data-distribution-service/deploy-cloudrun.sh`
- **Target**: Updates `websocket-data-distribution-service` Cloud Run service

#### Polygon WebSocket Service
- **Triggers**: Changes to `polygon-websocket-service/**`
- **Script**: Uses `polygon-websocket-service/deploy.sh`
- **Target**: Updates `polygon-websocket-ingestion` Cloud Run service

#### Shared Changes
- **Triggers**: Changes to `k8s-manifests/**`, `config/**`, or workflows
- **Action**: Deploys all services via `deploy-all-services.yml`

#### Production Configuration Validation
- **Triggers**: All deployments and pull requests
- **Validates**:
  - `DEVELOPMENT_MODE: False` in `config/config_loader.py`
  - `USE_FIREBASE_EMULATOR = false` in `frontend/src/config.js`
  - Other production-related flags

### Validation Pipeline
- **Triggers**: All pull requests
- **Validates**: Shell scripts, Kubernetes manifests, Dockerfiles
- **Tools**: shellcheck, kubeval, hadolint

## 📊 Workflow Status

### Monitoring Deployments
1. **GitHub Actions Tab**: View real-time deployment status
2. **Kubernetes Dashboard**: Monitor cluster deployments
3. **Logs**: Access detailed deployment logs

### Deployment Verification
Each workflow includes verification steps:
- Deployment rollout status
- Pod health checks  
- Service endpoint validation
- Resource status summary

## 🛠️ Manual Operations

### Manual Deployment
You can still deploy manually using the existing scripts:
```bash
# Trade Bot Service
cd trade-bot-service
./deploy-to-k8s.sh

# Strategy Controller Service
cd strategy-controller-service  
./deploy-to-k8s.sh
```

### Manual Workflow Triggers
1. Go to GitHub Actions tab
2. Select desired workflow
3. Click "Run workflow"
4. Choose options and run

## 🔧 Troubleshooting

### Common Issues

#### Authentication Errors
- Verify `GCP_SA_KEY` secret is correctly set
- Check service account permissions
- Ensure project ID matches (`oryntrade`)

#### Deployment Failures
- Check deployment script syntax with shellcheck
- Verify Kubernetes manifests with kubeval
- Ensure cluster connectivity

#### Docker Build Issues
- Validate Dockerfile with hadolint
- Check base image availability
- Verify registry permissions

### Debug Commands
```bash
# Test service account locally
gcloud auth activate-service-account --key-file=github-actions-key.json

# Test cluster access
kubectl cluster-info
kubectl get deployments

# Test registry access
docker pull us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest
```

## 🔒 Security Considerations

### Service Account Permissions
The GitHub Actions service account has these roles:
- `roles/container.admin` - Kubernetes cluster management
- `roles/storage.admin` - Cloud Storage access
- `roles/artifactregistry.admin` - Docker registry access
- `roles/cloudbuild.builds.editor` - Build permissions

### Secret Management
- Service account key is stored as GitHub secret
- Key file is excluded from version control
- Rotate keys periodically for security

### Branch Protection
- Deployments only run from main/master branches
- Pull requests run validation only
- Manual triggers require repository access

## 📈 Benefits

### Automation
- **Zero-touch deployments** on code changes
- **Consistent deployment process** across environments
- **Reduced human error** through automation

### Visibility
- **Real-time status** in GitHub Actions
- **Detailed logs** for troubleshooting
- **Deployment history** tracking

### Safety
- **Validation before deployment** via PR checks
- **Rollback capabilities** through Kubernetes
- **Branch protection** prevents accidental deployments

## 🎉 Next Steps

1. **Test the setup** by making a small change to either service
2. **Monitor the deployment** in GitHub Actions
3. **Verify the deployment** in your Kubernetes cluster
4. **Set up notifications** for deployment status (optional)
5. **Consider adding staging environments** for additional testing

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Google Cloud SDK Documentation](https://cloud.google.com/sdk/docs)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Docker Documentation](https://docs.docker.com/)

For detailed workflow information, see `.github/workflows/README.md`.
