#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Configuration
PROJECT_ID="oryntrade"
CLUSTER_NAME="oryn-trading-cluster"
REGION="us-central1"

echo "🚀 Setting up GKE Autopilot cluster for Oryn Trading System"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud is not installed. Please install Google Cloud SDK first."
    exit 1
fi

# Ensure the required APIs are enabled
echo "🔌 Enabling required Google Cloud APIs..."
gcloud services enable container.googleapis.com --project=${PROJECT_ID}

# Create GKE Autopilot cluster (managed, serverless Kubernetes)
echo "🏗️ Creating GKE Autopilot cluster ${CLUSTER_NAME}..."
gcloud container clusters create-auto ${CLUSTER_NAME} \
  --region=${REGION} \
  --project=${PROJECT_ID}

if [ $? -ne 0 ]; then
    echo "❌ Failed to create GKE cluster."
    exit 1
fi

# Get credentials for kubectl
echo "🔑 Getting cluster credentials..."
gcloud container clusters get-credentials ${CLUSTER_NAME} \
  --region=${REGION} \
  --project=${PROJECT_ID}

if [ $? -ne 0 ]; then
    echo "❌ Failed to get cluster credentials."
    exit 1
fi

# Verify cluster is working
echo "🔍 Verifying cluster status..."
kubectl get nodes

echo "✅ GKE Autopilot cluster setup completed successfully!"
echo "📝 You can now deploy the Strategy Controller and Trade Bot pods to this cluster." 