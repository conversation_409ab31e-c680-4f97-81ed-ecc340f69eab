# Deployment Scripts Documentation

This document provides detailed information about the deployment scripts created for the Oryn Trading Platform's dedicated trade bot architecture.

## Overview

The deployment process involves several scripts that automate the setup and deployment of various components:

1. GKE Cluster Setup
2. Strategy Controller Service Deployment
3. Trade Bot Service Deployment
4. Authentication Configuration

## GKE Cluster Setup (`setup_gke_cluster.sh`)

**Purpose:** Creates a Google Kubernetes Engine Autopilot cluster that will host all services.

**Location:** `/scripts/setup_gke_cluster.sh`

**Key Operations:**

- Creates a regional GKE Autopilot cluster
- Configures networking and authentication
- Sets up kubectl configuration for cluster access

**Usage:**

```bash
./scripts/setup_gke_cluster.sh
```

**Example Output:**

```
Creating GKE Autopilot cluster 'oryn-trading-cluster'...
Cluster created successfully!
Fetching credentials for cluster...
Credentials saved. You can now use kubectl to interact with the cluster.
```

## Strategy Controller Service Deployment (`deploy-to-k8s.sh`)

**Purpose:** Builds and deploys the Strategy Controller service to the Kubernetes cluster.

**Location:** `/strategy-controller-service/deploy-to-k8s.sh`

**Key Operations:**

- Builds a Docker image for the Strategy Controller
- Pushes the image to Google Container Registry
- Applies Kubernetes manifests for deployment, service, and RBAC
- Sets up port forwarding for testing (optional)

**Usage:**

```bash
cd strategy-controller-service
./deploy-to-k8s.sh
```

**Environment Requirements:**

- Google Cloud SDK installed and configured
- Docker with buildx capability
- kubectl configured for the GKE cluster

## Trade Bot Service Deployment (`deploy-to-k8s.sh`)

**Purpose:** Builds and deploys the Trade Bot service to the Kubernetes cluster.

**Location:** `/trade-bot-service/deploy-to-k8s.sh`

**Key Operations:**

- Creates Dockerfile and requirements.txt if they don't exist
- Builds a Docker image for the Trade Bot
- Pushes the image to Google Container Registry
- Applies Kubernetes manifests for deployment and service
- Sets up port forwarding for testing (optional)

**Usage:**

```bash
cd trade-bot-service
./deploy-to-k8s.sh
```

**Services Created:**

- Base Trade Bot service that can process multiple strategies
- Ready for Strategy Controller to create dedicated instances

## Kubernetes Manifests

### Strategy Controller (`deployment.yaml`)

**Purpose:** Defines the Kubernetes resources for the Strategy Controller.

**Location:** `/strategy-controller-service/deployment.yaml`

**Resources Created:**

- Deployment with one replica
- Service for network access
- ServiceAccount for authentication
- Role and RoleBinding for pod management permissions

**Key Configurations:**

- Image: `gcr.io/oryntrade/strategy-controller:latest`
- Service port: 80 (mapped to container port 8080)
- Resource limits and requests
- ImagePullSecrets for GCR authentication

### Trade Bot (`deployment.yaml`)

**Purpose:** Defines the Kubernetes resources for the base Trade Bot service.

**Location:** `/trade-bot-service/deployment.yaml`

**Resources Created:**

- Deployment with one replica
- Service for network access

**Key Configurations:**

- Image: `gcr.io/oryntrade/trade-bot:latest`
- Service port: 80 (mapped to container port 8080)
- Environment variable: `SINGLE_STRATEGY_MODE=false` for the base service
- Resource limits and requests
- ImagePullSecrets for GCR authentication

## Authentication Setup

The `gcr-json-key` secret is created to allow Kubernetes to pull images from Google Container Registry:

```bash
# Create service account
gcloud iam service-accounts create gcr-puller --display-name="GCR Pull Service Account"

# Grant permissions
gcloud projects add-iam-policy-binding PROJECT_ID \
  --member=serviceAccount:gcr-puller@PROJECT_ID.iam.gserviceaccount.com \
  --role=roles/storage.objectViewer

# Create key
gcloud iam service-accounts keys create gcr-key.json \
  --iam-account=gcr-puller@PROJECT_ID.iam.gserviceaccount.com

# Create Kubernetes secret
kubectl create secret docker-registry gcr-json-key \
  --docker-server=gcr.io \
  --docker-username=_json_key \
  --docker-password="$(cat gcr-key.json)" \
  --docker-email=gcr-puller@PROJECT_ID.iam.gserviceaccount.com
```

## Troubleshooting

### Image Pull Errors

If pods show `ErrImagePull` or `ImagePullBackOff` status:

1. Verify the secret is created correctly
2. Check that the service account has the correct permissions
3. Ensure the image exists in GCR with the correct tag

### Authentication Errors

For "Invalid kube-config file" errors when running services locally:

- The service is trying to access Kubernetes API but can't find credentials
- This is expected when running locally without cluster access

### Strategy Controller Startup Errors

If the Strategy Controller fails with "Compute Engine Metadata server unavailable":

- The service is attempting to use Google Cloud authentication
- When running locally, set up Application Default Credentials:
  ```bash
  gcloud auth application-default login
  ```
