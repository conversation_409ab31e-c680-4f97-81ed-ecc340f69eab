#!/bin/bash
# Switch to development mode and start all services

echo "🔧 Switching to DEVELOPMENT mode..."
echo ""

# Update the configuration in config_loader.py
echo "📝 Updating config/config_loader.py..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/'DEVELOPMENT_MODE': False/'DEVELOPMENT_MODE': True/" config/config_loader.py
else
    # Linux
    sed -i "s/'DEVELOPMENT_MODE': False/'DEVELOPMENT_MODE': True/" config/config_loader.py
fi
echo "✅ Backend configuration updated to DEVELOPMENT mode"

# Update frontend configuration
echo "📝 Updating frontend/src/config.js..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' 's/export const USE_FIREBASE_EMULATOR = false/export const USE_FIREBASE_EMULATOR = true/' frontend/src/config.js
else
    # Linux
    sed -i 's/export const USE_FIREBASE_EMULATOR = false/export const USE_FIREBASE_EMULATOR = true/' frontend/src/config.js
fi
echo "✅ Frontend configuration updated to DEVELOPMENT mode"

echo ""
echo "✅ Switched to DEVELOPMENT mode"
echo ""
echo "📋 Development Configuration:"
echo "   - Firebase: Using local emulator"
echo "   - PubSub: Using local emulator" 
echo "   - Strategy Controller: localhost:8080"
echo "   - Frontend: localhost:3000"
echo ""
echo "🚀 To start development environment:"
echo ""
echo "   Terminal 1 - Firebase Emulators:"
echo "   cd functions && ./start_emulator.sh"
echo ""
echo "   Terminal 2 - PubSub Emulator:"
echo "   gcloud beta emulators pubsub start --project=oryntrade"
echo ""
echo "   Terminal 3 - Strategy Controller:"
echo "   cd strategy-controller-service && ./run_local.sh"
echo ""
echo "   Terminal 4 - Frontend:"
echo "   cd frontend && npm run dev"
echo ""
echo "🌐 Access URLs:"
echo "   • Frontend: http://localhost:3000"
echo "   • Strategy Controller: http://localhost:8080"
echo "   • Firebase UI: http://localhost:4000"
echo ""
