#!/usr/bin/env python3
"""
Centralized configuration loader for Oryn Trading Bot System.
Provides a single source of truth for development vs production settings.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional

class ConfigLoader:
    """Loads and manages environment configuration."""
    
    def __init__(self):
        """Initialize the config loader."""
        # Define configuration directly in code for simplicity
        self._config = {
            'DEVELOPMENT_MODE': False,  # This will be the master switch
            'development': {
                'firebase': {
                    'use_emulator': True,
                    'emulator_host': 'localhost',
                    'firestore_port': 8082,
                    'auth_port': 9099,
                    'functions_port': 5001
                },
                'pubsub': {
                    'use_emulator': True,
                    'emulator_host': 'localhost:8085'
                },
                'strategy_controller': {
                    'host': 'localhost',
                    'port': 8080
                }
            },
            'production': {
                'firebase': {
                    'use_emulator': False,
                    'project_id': 'oryntrade'
                },
                'pubsub': {
                    'use_emulator': False,
                    'project_id': 'oryntrade',
                    'topic_name': 'strategy-execution'
                },
                'strategy_controller': {
                    'service_name': 'strategy-controller-service',
                    'port': 80
                }
            }
        }
    
    @property
    def is_development(self) -> bool:
        """Check if running in development mode."""
        # Allow environment variable override
        env_override = os.getenv('DEVELOPMENT_MODE')
        if env_override is not None:
            return env_override.lower() in ('true', '1', 'yes', 'on')

        return self._config.get('DEVELOPMENT_MODE', False)

    def set_development_mode(self, development: bool):
        """Set development mode."""
        self._config['DEVELOPMENT_MODE'] = development
        os.environ['DEVELOPMENT_MODE'] = str(development).lower()
    
    @property
    def environment_name(self) -> str:
        """Get current environment name."""
        return "development" if self.is_development else "production"
    
    def get_config(self, section: str = None) -> Dict[str, Any]:
        """Get configuration for current environment."""
        env_config = self._config.get(self.environment_name, {})

        if section:
            return env_config.get(section, {})

        return env_config
    
    def get_firebase_config(self) -> Dict[str, Any]:
        """Get Firebase configuration."""
        return self.get_config('firebase')
    
    def get_pubsub_config(self) -> Dict[str, Any]:
        """Get PubSub configuration."""
        return self.get_config('pubsub')
    
    def get_strategy_controller_config(self) -> Dict[str, Any]:
        """Get Strategy Controller configuration."""
        return self.get_config('strategy_controller')
    

    
    def setup_environment_variables(self):
        """Set up environment variables based on configuration."""
        firebase_config = self.get_firebase_config()
        pubsub_config = self.get_pubsub_config()
        
        # Firebase environment variables
        if firebase_config.get('use_emulator', False):
            os.environ['FIRESTORE_EMULATOR_HOST'] = f"{firebase_config['emulator_host']}:{firebase_config['firestore_port']}"
            os.environ['FIREBASE_AUTH_EMULATOR_HOST'] = f"{firebase_config['emulator_host']}:{firebase_config['auth_port']}"
        else:
            # Remove emulator environment variables if they exist
            os.environ.pop('FIRESTORE_EMULATOR_HOST', None)
            os.environ.pop('FIREBASE_AUTH_EMULATOR_HOST', None)
        
        # PubSub environment variables
        if pubsub_config.get('use_emulator', False):
            os.environ['PUBSUB_EMULATOR_HOST'] = pubsub_config['emulator_host']
        else:
            os.environ.pop('PUBSUB_EMULATOR_HOST', None)
        
        # Set development mode flag
        os.environ['DEVELOPMENT_MODE'] = str(self.is_development).lower()
        
        print(f"🔧 Environment configured for: {self.environment_name.upper()}")
        print(f"   Firebase Emulator: {firebase_config.get('use_emulator', False)}")
        print(f"   PubSub Emulator: {pubsub_config.get('use_emulator', False)}")
    
    def print_config_summary(self):
        """Print a summary of the current configuration."""
        print(f"\n📋 Configuration Summary")
        print(f"=" * 40)
        print(f"Environment: {self.environment_name.upper()}")
        print(f"Development Mode: {self.is_development}")
        
        firebase = self.get_firebase_config()
        pubsub = self.get_pubsub_config()
        
        print(f"\n🔥 Firebase:")
        print(f"   Use Emulator: {firebase.get('use_emulator', False)}")
        if firebase.get('use_emulator'):
            print(f"   Firestore: {firebase.get('emulator_host')}:{firebase.get('firestore_port')}")
            print(f"   Auth: {firebase.get('emulator_host')}:{firebase.get('auth_port')}")
        else:
            print(f"   Project: {firebase.get('project_id', 'Not set')}")
        
        print(f"\n📡 PubSub:")
        print(f"   Use Emulator: {pubsub.get('use_emulator', False)}")
        if pubsub.get('use_emulator'):
            print(f"   Host: {pubsub.get('emulator_host')}")
        else:
            print(f"   Project: {pubsub.get('project_id', 'Not set')}")

# Global config instance
_config_loader = None

def get_config_loader() -> ConfigLoader:
    """Get the global configuration loader instance."""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader()
    return _config_loader

def is_development() -> bool:
    """Quick check if running in development mode."""
    return get_config_loader().is_development

def setup_environment():
    """Set up environment variables for current configuration."""
    config_loader = get_config_loader()
    config_loader.setup_environment_variables()
    return config_loader

if __name__ == "__main__":
    # Test the configuration loader
    config = setup_environment()
    config.print_config_summary()
