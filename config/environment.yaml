# Environment Configuration for Oryn Trading Bot System
# This file controls whether services run in development or production mode

# MASTER SWITCH: Set to true for local development, false for production
DEVELOPMENT_MODE: true

# Development Environment Settings (when DEVELOPMENT_MODE: true)
development:
  firebase:
    use_emulator: true
    emulator_host: "localhost"
    firestore_port: 8082
    auth_port: 9099
    functions_port: 5001

  pubsub:
    use_emulator: true
    emulator_host: "localhost:8085"

  strategy_controller:
    host: "localhost"
    port: 8080

# Production Environment Settings (when DEVELOPMENT_MODE: false)
production:
  firebase:
    use_emulator: false
    project_id: "oryntrade"

  pubsub:
    use_emulator: false
    project_id: "oryntrade"
    topic_name: "strategy-execution"

  strategy_controller:
    service_name: "strategy-controller-service"
    port: 80
