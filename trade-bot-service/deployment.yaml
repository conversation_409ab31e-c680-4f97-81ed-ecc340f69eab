apiVersion: apps/v1
kind: Deployment
metadata:
  name: trade-bot-base
  labels:
    app: trade-bot
spec:
  replicas: 0  # Base deployment with 0 replicas - Strategy Controller creates instances
  selector:
    matchLabels:
      app: trade-bot
  template:
    metadata:
      labels:
        app: trade-bot
    spec:
      serviceAccountName: trade-bot-sa
      terminationGracePeriodSeconds: 120  # Give 2 minutes for graceful shutdown
      imagePullSecrets:
        - name: gcr-json-key
      containers:
        - name: trade-bot
          image: us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8001
          # Health checks for faster startup and better reliability
          readinessProbe:
            httpGet:
              path: /health
              port: 8001
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          livenessProbe:
            httpGet:
              path: /health
              port: 8001
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "echo 'Graceful shutdown initiated' && sleep 10"]
          env:
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: "/var/secrets/google/firebase-key.json"
            - name: GOOGLE_CLOUD_PROJECT
              value: "oryntrade"
            - name: MARKET_DATA_PROVIDER
              value: "pubsub"
            - name: USE_ENHANCED_MARKET_DATA
              value: "true"
            - name: USE_FIREBASE_EMULATOR
              value: "false"
            - name: BYPASS_MARKET_IS_CLOSED
              value: "false"
            - name: OANDA_PRACTICE_MODE
              value: "true"
            - name: STRATEGY_CONTROLLER_URL
              value: "http://strategy-controller-service:80"
            - name: VPC_CONNECTOR
              value: "oanda-vpc-conn-c1"
            - name: LOG_LEVEL
              value: "INFO"
            - name: ENABLE_HEALTH_API
              value: "true"
            - name: HEALTH_API_PORT
              value: "8001"
            - name: POLYGON_API_KEY
              valueFrom:
                secretKeyRef:
                  name: polygon-api-key
                  key: api-key
          volumeMounts:
            - name: firebase-key
              mountPath: "/var/secrets/google"
              readOnly: true
          resources:
            requests:
              cpu: "200m"
              memory: "512Mi"
            limits:
              cpu: "1000m"
              memory: "2Gi"
      volumes:
        - name: firebase-key
          secret:
            secretName: firebase-key
---
apiVersion: v1
kind: Service
metadata:
  name: trade-bot-service
spec:
  selector:
    app: trade-bot
  ports:
    - port: 80
      targetPort: 8001
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: trade-bot-sa
  annotations:
    iam.gke.io/gcp-service-account: "<EMAIL>"
