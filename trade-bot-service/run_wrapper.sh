#!/bin/bash
echo "Starting trade bot from wrapper script"
echo "Working directory: $(pwd)"
echo "Strategy ID: V7u9KHh8Hk2Ks2fciNOi"
echo "User ID: PiZZ5xXSGDhpQz6tOJZSdNyLbNtO"
echo "Strategy file: /Users/<USER>/workspace/oryn/trade-bot-service/strategy_V7u9KHh8Hk2Ks2fciNOi.json"

# Source .env file if it exists
if [ -f .env ]; then
    echo "Sourcing .env file"
    set -a
    source .env
    set +a
fi

# Override with our values
export STRATEGY_ID="V7u9KHh8Hk2Ks2fciNOi"
export USER_ID="PiZZ5xXSGDhpQz6tOJZSdNyLbNtO"
export STRATEGY_JSON_FILE="/Users/<USER>/workspace/oryn/trade-bot-service/strategy_V7u9KHh8Hk2Ks2fciNOi.json"
export USE_FIREBASE_EMULATOR=true
export FIRESTORE_EMULATOR_HOST=127.0.0.1:8082
export BYPASS_MARKET_IS_CLOSED=false
export OANDA_PRACTICE_MODE=true

echo "Environment variables:"
echo "STRATEGY_ID=$STRATEGY_ID"
echo "USER_ID=$USER_ID"
echo "USE_FIREBASE_EMULATOR=$USE_FIREBASE_EMULATOR"
echo "FIRESTORE_EMULATOR_HOST=$FIRESTORE_EMULATOR_HOST"
echo "BYPASS_MARKET_IS_CLOSED=$BYPASS_MARKET_IS_CLOSED"
echo "OANDA_PRACTICE_MODE=$OANDA_PRACTICE_MODE"

# Create empty credentials file if needed
if [ ! -f ./firebase-key.json ]; then
    echo "Creating empty firebase-key.json file"
    echo "{}" > ./firebase-key.json
fi

# Verify we can connect to Firestore emulator
echo "Checking Firestore emulator connection..."
curl -s http://127.0.0.1:8082/ > /dev/null
if [ $? -ne 0 ]; then
    echo "ERROR: Cannot connect to Firestore emulator at 127.0.0.1:8082"
    echo "Make sure the Firebase emulators are running (firebase emulators:start)"
    exit 1
else
    echo "Successfully connected to Firestore emulator"
fi

# Create required PubSub topics in the emulator
echo "Creating PubSub topics in emulator..."
if [ ! -z "$PUBSUB_EMULATOR_HOST" ]; then
    # Create the command topic
    curl -s -X PUT "http://localhost:8085/v1/projects/oryntrade/topics/strategy-commands" -H "Content-Type: application/json" -d "{}" > /dev/null
    echo "Created PubSub topic: strategy-commands"
    # Create the execution topic
    curl -s -X PUT "http://localhost:8085/v1/projects/oryntrade/topics/strategy-execution" -H "Content-Type: application/json" -d "{}" > /dev/null
    echo "Created PubSub topic: strategy-execution"
fi

# Add PYTHONPATH if not set
if [ -z "$PYTHONPATH" ]; then
    export PYTHONPATH=$(pwd)
    echo "Set PYTHONPATH=$PYTHONPATH"
fi

# Install required dependencies
echo "Installing required dependencies..."
pip install python-dateutil>=2.8.0 > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "Dependencies installed successfully"
else
    echo "Warning: Failed to install some dependencies, continuing anyway..."
fi

# Run the trade bot
echo "Starting Python trade bot..."
python -u main.py