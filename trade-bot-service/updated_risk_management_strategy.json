{"name": "High Frequency EUR/USD Scalper - Large Position", "description": "Optimized for larger positions with tight stop loss/take profit near current price action", "instruments": "EUR/USD", "timeframe": "5m", "tradingSession": ["All"], "indicators": [{"id": "17503800253760keuarslw7pq", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 7}, "source": "price"}, {"id": "1750380029020j42vnnq648", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 14}, "source": "price"}, {"id": "1750380037926vok42b2ubkg", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 5}, "source": "price"}, {"id": "17503800449431loissr9smai", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 13}, "source": "price"}, {"id": "1750380064169f6z71comxno", "type": "MACD", "indicator_class": "MACD", "parameters": {"fast": 8, "slow": 17, "signal": 5}, "source": "price"}], "entryRules": [{"tradeType": "long", "indicator1": "17503800253760keuarslw7pq", "operator": ">", "compareType": "value", "indicator2": "", "value": "35", "logicalOperator": "AND", "barRef": "close", "id": "1750380148367nr1920iawx"}, {"id": "1750380162760gnyam24rjkm", "tradeType": "long", "indicator1": "17503800253760keuarslw7pq", "operator": "<", "compareType": "value", "indicator2": "", "value": "65", "barRef": "close"}, {"id": "175038017321281p8n81zt7m", "tradeType": "long", "indicator1": "1750380037926vok42b2ubkg", "operator": ">", "compareType": "indicator", "indicator2": "17503800449431loissr9smai", "value": "", "barRef": "close"}, {"id": "1750380186084mqjm2qppbds", "tradeType": "long", "indicator1": "1750380064169f6z71comxno", "operator": ">", "compareType": "value", "indicator2": "", "value": "0", "barRef": "close", "macdComponent": "macd"}, {"id": "1750380205262ofn2u60acjn", "tradeType": "short", "indicator1": "17503800253760keuarslw7pq", "operator": "<", "compareType": "value", "indicator2": "", "value": "65", "barRef": "close"}, {"id": "17503802179411v78mpejqk", "tradeType": "short", "indicator1": "17503800253760keuarslw7pq", "operator": ">", "compareType": "value", "indicator2": "", "value": "35", "barRef": "close"}, {"id": "1750380227850wkeme7syzut", "tradeType": "short", "indicator1": "1750380037926vok42b2ubkg", "operator": "<", "compareType": "indicator", "indicator2": "17503800449431loissr9smai", "value": "", "barRef": "close"}, {"id": "1750380237623v07m1gq8s9", "tradeType": "short", "indicator1": "1750380064169f6z71comxno", "operator": "<", "compareType": "value", "indicator2": "", "value": "0", "barRef": "close", "macdComponent": "macd"}], "exitRules": [{"tradeType": "long", "indicator1": "17503800253760keuarslw7pq", "operator": ">", "compareType": "value", "indicator2": "", "value": "70", "logicalOperator": "OR", "barRef": "close", "id": "1750380250296jycmdiwp0q"}, {"id": "17503802682768dsxmyqh709", "tradeType": "long", "indicator1": "1750380037926vok42b2ubkg", "operator": "<", "compareType": "indicator", "indicator2": "17503800449431loissr9smai", "value": "", "barRef": "close"}, {"id": "1750380279785bd7cxc07goc", "tradeType": "short", "indicator1": "17503800253760keuarslw7pq", "operator": "<", "compareType": "value", "indicator2": "", "value": "30", "barRef": "close"}, {"id": "1750380302116ielsxjy5smg", "tradeType": "short", "indicator1": "1750380037926vok42b2ubkg", "operator": ">", "compareType": "indicator", "indicator2": "17503800449431loissr9smai", "value": "", "barRef": "close"}], "riskManagement": {"riskPercentage": "1.5", "riskRewardRatio": "1.5", "stopLossMethod": "fixed", "fixedPips": "8", "indicatorBasedSL": {"indicator": "", "parameters": {}}, "lotSize": "6.0", "maxDailyLoss": "5%", "maxPositionSize": "50%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "8", "stopLossUnit": "pips", "takeProfit": "12", "takeProfitUnit": "pips"}, "entryLongGroupOperator": "AND", "entryShortGroupOperator": "AND", "exitLongGroupOperator": "OR", "exitShortGroupOperator": "OR"}