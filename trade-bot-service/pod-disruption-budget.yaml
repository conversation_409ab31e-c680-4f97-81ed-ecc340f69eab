apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: trade-bot-pdb
  labels:
    app: trade-bot
spec:
  minAvailable: 1  # Always keep at least 1 trade-bot running during disruptions
  selector:
    matchLabels:
      app: trade-bot
---
# Additional PDB for individual strategy instances
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: trade-bot-strategy-pdb
  labels:
    app: trade-bot-strategy
spec:
  minAvailable: 0  # Allow individual strategy pods to be disrupted (they will restart)
  selector:
    matchLabels:
      app: trade-bot-strategy
