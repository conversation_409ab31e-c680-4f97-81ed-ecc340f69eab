# Trade-Bot Service Production Configuration

## ✅ Production URLs Configured

The trade-bot service has been updated to use production URLs for all external services:

### 🔗 Service URLs Updated

1. **Strategy Controller Service**
   - **Production URL**: `https://control-strategy-ihjc6tjxia-uc.a.run.app`
   - **Updated in**:
     - `main.py` (default fallback URL)
     - `Dockerfile` (environment variable)
     - `utils/health_client.py` (default URL)
     - `.env.production` (configuration file)

2. **Polygon WebSocket Service**
   - **Communication Method**: Google Cloud Pub/Sub Topics (project-wide)
   - **Topics Used**:
     - `polygon.subscription_commands` - For sending subscription requests
     - `polygon.minute_aggregates` - For receiving real-time data
   - **No URL configuration needed** - Uses project-wide Pub/Sub topics

### 📋 Environment Configuration

#### Production Environment File: `.env.production`
```bash
# Market Data Provider - Uses Pub/Sub for production
MARKET_DATA_PROVIDER=pubsub

# Google Cloud Project
GOOGLE_CLOUD_PROJECT=oryntrade

# Strategy Controller - Production URL
STRATEGY_CONTROLLER_URL=https://control-strategy-ihjc6tjxia-uc.a.run.app

# Firebase - Production mode
USE_FIREBASE_EMULATOR=false

# OANDA - Set to false for live trading
OANDA_PRACTICE_MODE=false
```

#### Dockerfile Configuration
- Uses production Strategy Controller URL by default
- Configured for `pubsub` market data provider
- Production Google Cloud project: `oryntrade`

### 🚀 Deployment Ready

The trade-bot service is now configured to:

1. **✅ Connect to Production Polygon WebSocket Service**
   - Uses Pub/Sub topics for communication
   - Automatically connects when deployed to `oryntrade` project

2. **✅ Connect to Production Strategy Controller**
   - Uses production URL: `https://control-strategy-ihjc6tjxia-uc.a.run.app`
   - Health checks and command processing configured

3. **✅ Use Production Firebase**
   - Disabled emulator mode
   - Uses production Firestore database

4. **✅ Ready for Live Trading**
   - OANDA configuration supports both demo and live modes
   - Set `OANDA_PRACTICE_MODE=false` for live trading

### 🔧 Testing Production Configuration

Use the provided script to test with production services:

```bash
cd trade-bot-service
./run_production_test.sh
```

This script will:
1. Load `.env.production` configuration
2. Verify all required environment variables
3. Test connection to production Polygon WebSocket Service
4. Validate real-time data flow via Pub/Sub

### 📊 Data Flow Architecture

```
Polygon.io API
      ↓
Polygon WebSocket Service (Production)
      ↓
Google Cloud Pub/Sub Topics
      ↓
Trade-Bot Service (Production)
      ↓
Strategy Controller (Production)
```

All services are now configured to use production URLs and will communicate properly when deployed.
