# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set a working directory inside the container
WORKDIR /app

# Install system dependencies
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file first to leverage Docker cache
COPY requirements.txt .

# Install any needed packages specified in requirements.txt
# hadolint ignore=DL3013
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy the rest of your trade bot code into the container
COPY . .

# Set the Firebase credentials path
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/firebase-key/firebase-key.json

# Set production environment variables
ENV ENABLE_HEALTH_API=true
ENV HEALTH_API_PORT=8001
ENV USE_FIREBASE_EMULATOR=false
ENV BYPASS_MARKET_IS_CLOSED=false
ENV OANDA_PRACTICE_MODE=true
ENV GOOGLE_CLOUD_PROJECT=oryntrade
ENV STRATEGY_CONTROLLER_URL=https://control-strategy-ihjc6tjxia-uc.a.run.app
# Market Data Provider Configuration
ENV MARKET_DATA_PROVIDER=pubsub
ENV USE_ENHANCED_MARKET_DATA=true
ENV LOG_LEVEL=INFO

# Expose ports for the main bot and the health API
EXPOSE 8001

# Run the trade bot directly
CMD ["python", "-u", "main.py"]