#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Enable GKE auth plugin
export USE_GKE_GCLOUD_AUTH_PLUGIN=True

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="trade-bot"
REPOSITORY="oryn-containers"
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${SERVICE_NAME}:latest"

echo "🚀 Starting deployment process for ${SERVICE_NAME}"

# Check if running in GitHub Actions or local environment
if [ -n "${GITHUB_ACTIONS}" ]; then
    echo "🤖 Running in GitHub Actions - using service account authentication"
    # In GitHub Actions, authentication is handled by google-github-actions/auth
    # Just configure Docker for the registry
    gcloud auth configure-docker us-central1-docker.pkg.dev --quiet
else
    echo "💻 Running locally - checking gcloud authentication..."
    # Check if gcloud is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo "❌ No active gcloud authentication found"
        echo "Please run: gcloud auth login"
        exit 1
    fi

    # Set the gcloud project
    echo "📋 Setting gcloud project to ${PROJECT_ID}..."
    gcloud config set project "${PROJECT_ID}"

    # Configure Docker authentication for Google Container Registry
    echo "🔐 Configuring Docker authentication..."
    gcloud auth configure-docker us-central1-docker.pkg.dev --quiet
fi

# Build the Docker image for linux/amd64 platform
echo "🏗️ Building Docker image for linux/amd64 platform..."
docker buildx build --platform linux/amd64 \
    -t ${IMAGE_NAME} \
    --push \
    .

echo "✅ Trade Bot image has been built and pushed to the container registry"

# Apply Kubernetes manifests
echo "🔄 Applying Kubernetes manifests..."
kubectl apply -f deployment.yaml
kubectl apply -f pod-disruption-budget.yaml

# Force a rollout to use the new image (if deployment exists)
echo "🔄 Rolling out new deployment..."
kubectl rollout restart deployment/trade-bot-base

echo "✅ Deployment completed successfully!"
echo "✅ The new image is available at: ${IMAGE_NAME}"
echo "✅ Trade-bot base deployment is ready"
echo "📝 Note: Strategy Controller will create individual trade-bot instances from this base image"
echo "🔍 To test, submit a strategy through the Strategy Controller API"