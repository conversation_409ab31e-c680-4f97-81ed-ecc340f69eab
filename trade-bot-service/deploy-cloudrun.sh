#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="trade-bot-service"
REPOSITORY="oryn-containers"
REGION="us-east1"
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/trade-bot:latest"

echo "🚀 Starting Cloud Run deployment for ${SERVICE_NAME}"

# Build and push the Docker image
echo "🏗️ Building Docker image for linux/amd64 platform..."
docker buildx build --platform linux/amd64 \
    -t ${IMAGE_NAME} \
    --push \
    .

if [ $? -ne 0 ]; then
    echo "❌ Failed to build and push Docker image"
    exit 1
fi

echo "✅ Docker image built and pushed successfully"

# Deploy to Cloud Run with VPC connector
echo "🚀 Deploying to Cloud Run with VPC connector..."
gcloud run deploy ${SERVICE_NAME} \
    --image=${IMAGE_NAME} \
    --platform=managed \
    --region=${REGION} \
    --no-allow-unauthenticated \
    --memory=2Gi \
    --cpu=1 \
    --min-instances=0 \
    --max-instances=100 \
    --timeout=3600 \
    --concurrency=1 \
    --execution-environment=gen2 \
    --vpc-connector=oanda-vpc-conn-e1 \
    --vpc-egress=private-ranges-only \
    --set-env-vars="GOOGLE_CLOUD_PROJECT=${PROJECT_ID},MARKET_DATA_PROVIDER=pubsub,USE_ENHANCED_MARKET_DATA=true,USE_FIREBASE_EMULATOR=false,BYPASS_MARKET_IS_CLOSED=false,OANDA_PRACTICE_MODE=true,STRATEGY_CONTROLLER_URL=https://control-strategy-ihjc6tjxia-uc.a.run.app,LOG_LEVEL=INFO,ENABLE_HEALTH_API=true,HEALTH_API_PORT=8001" \
    --set-secrets="POLYGON_API_KEY=polygon-api-key:latest"

if [ $? -ne 0 ]; then
    echo "❌ Failed to deploy to Cloud Run"
    exit 1
fi

echo "✅ Cloud Run deployment completed successfully!"

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format="value(status.url)")
echo "🌐 Service URL: ${SERVICE_URL}"

echo ""
echo "📋 Deployment Summary:"
echo "   - Service: ${SERVICE_NAME}"
echo "   - Image: ${IMAGE_NAME}"
echo "   - Region: ${REGION}"
echo "   - URL: ${SERVICE_URL}"
echo ""
echo "🔧 Next Steps:"
echo "   1. Configure Strategy Controller to use this trade-bot service"
echo "   2. Set up proper IAM permissions for service-to-service communication"
echo "   3. Test deployment with a sample strategy"
echo ""
echo "⚠️  Note: This service is configured for internal use by Strategy Controller"
echo "   It requires authentication and should not be publicly accessible"
