#!/usr/bin/env python3
"""
Test script for pause timeout functionality.
"""

import sys
import os
import time
from datetime import datetime, timezone

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pause_timeout():
    """Test the pause timeout manager."""
    
    print("🧪 Testing Pause Timeout Manager...")
    
    # Mock trading bot
    class MockTradingBot:
        def __init__(self):
            self.strategy_id = "test-strategy-123"
            self.user_id = "test-user"
            self.strategy_controller_url = "http://localhost:8080"
            self.should_exit = False
            self.stop_reason = None
            self.is_paused = False
    
    # Mock Firebase client
    class MockFirebaseClient:
        class BotStatus:
            PAUSED = "paused"
            STOPPED = "stopped"
            RUNNING = "running"
        
        def update_bot_status(self, status, data):
            print(f"📊 Status updated: {status} - {data}")
        
        def append_user_log(self, message):
            print(f"📝 User log: {message}")
    
    # Mock OANDA client
    class MockOandaClient:
        def get_open_trades(self):
            return [
                {"trade_id": "123", "instrument": "EUR_USD", "units": "1000"},
                {"trade_id": "456", "instrument": "GBP_USD", "units": "500"}
            ]
        
        def close_trade(self, trade_id):
            print(f"💰 Closed trade: {trade_id}")
            return True
    
    # Create mock trading bot
    mock_bot = MockTradingBot()
    mock_bot.firebase_client = MockFirebaseClient()
    mock_bot.oanda_client = MockOandaClient()
    
    # Import and create pause timeout manager
    from utils.pause_timeout_manager import PauseTimeoutManager
    
    # Test with short timeout for testing (10 seconds instead of 24 hours)
    timeout_manager = PauseTimeoutManager(mock_bot)
    timeout_manager.timeout_hours = 10 / 3600  # 10 seconds converted to hours
    
    print("\n1️⃣ Testing pause timeout start...")
    timeout_manager.start_pause_timeout()
    
    # Check status
    status = timeout_manager.get_pause_status()
    print(f"📊 Pause status: {status}")
    
    print("\n2️⃣ Waiting 5 seconds...")
    time.sleep(5)
    
    # Check remaining time
    remaining = timeout_manager.get_remaining_time()
    print(f"⏰ Remaining time: {remaining}")
    
    print("\n3️⃣ Testing pause timeout cancellation...")
    timeout_manager.cancel_pause_timeout()
    
    # Check status after cancellation
    status = timeout_manager.get_pause_status()
    print(f"📊 Status after cancel: {status}")
    
    print("\n4️⃣ Testing timeout expiry...")
    # Start again and let it expire
    timeout_manager.start_pause_timeout()
    
    print("⏳ Waiting for timeout to expire (10 seconds)...")
    time.sleep(12)  # Wait a bit longer than timeout
    
    # Check if bot was marked for exit
    print(f"🛑 Bot should_exit: {mock_bot.should_exit}")
    print(f"🛑 Bot stop_reason: {mock_bot.stop_reason}")
    
    print("\n✅ Pause timeout test completed!")

if __name__ == "__main__":
    test_pause_timeout()
