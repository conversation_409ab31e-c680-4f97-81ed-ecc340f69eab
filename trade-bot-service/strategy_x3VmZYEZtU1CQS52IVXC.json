{"name": "RSI dummy", "description": "", "instruments": "EUR/USD", "timeframe": "5m", "tradingSession": ["All"], "indicators": [{"id": "1749419051755gaj1c8hr4ft", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 14}, "source": "price"}], "entryRules": [{"tradeType": "long", "indicator1": "1749419051755gaj1c8hr4ft", "operator": ">", "compareType": "value", "indicator2": "", "value": "0", "logicalOperator": "AND", "barRef": "close", "id": "1749419063126rzwy6s4iz9"}], "exitRules": [{"tradeType": "long", "indicator1": "1749419051755gaj1c8hr4ft", "operator": "==", "compareType": "value", "indicator2": "", "value": "0", "logicalOperator": "AND", "barRef": "close", "id": "1749419075043ddl7xqpckk9"}], "riskManagement": {"riskPercentage": "1", "riskRewardRatio": "2", "stopLossMethod": "fixed", "fixedPips": "15", "indicatorBasedSL": {"indicator": "", "parameters": {}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "user_id": "Yvsoz4b9taBpaUiD39zJKhPpgy0j", "id": "x3VmZYEZtU1CQS52IVXC"}