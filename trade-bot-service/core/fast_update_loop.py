"""
Fast update loop for the trading bot.

This module contains the FastUpdateLoop class, which is responsible for updating
time-sensitive data at a higher frequency than the main strategy loop.
"""

import time
import threading
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from core.trading_engine import TradingEngine
from execution.oanda_client import OandaClient
from execution.firebase_client import FirebaseClient
from utils.logger import Logger


class FastUpdateLoop:
    """
    Fast update loop for the trading bot.

    This class is responsible for updating time-sensitive data at a higher frequency
    than the main strategy loop, including:
    - Unrealized P&L for open trades
    - Take profit and stop loss prices for new trades
    - Risk management thresholds
    """

    def __init__(
        self,
        trading_engine: TradingEngine,
        oanda_client: OandaClient,
        firebase_client: FirebaseClient,
        update_interval: int = 5,  # Default to 5 seconds
    ):
        """
        Initialize the fast update loop.

        Args:
            trading_engine: Trading engine instance
            oanda_client: OANDA client instance
            firebase_client: Firebase client instance
            update_interval: Update interval in seconds (default: 5)
        """
        self.trading_engine = trading_engine
        self.oanda_client = oanda_client
        self.firebase_client = firebase_client
        self.update_interval = update_interval
        self.logger = Logger("FastUpdateLoop")

        self.running = False
        self.thread = None
        self.last_update_time = None

        # Risk management thresholds
        self.daily_loss_limit = None
        self.total_profit_target = None
        self.total_loss_limit = None

        # Local memory for tracking open trades to detect closures
        self.local_open_trades = {}  # {trade_id: trade_info}

        # Track trades processed by fast loop to avoid duplicate processing in main loop
        self.processed_closed_trades = set()  # {trade_id}

        # Initialize risk management thresholds from trading engine
        self._initialize_risk_thresholds()

    def _initialize_risk_thresholds(self):
        """Initialize risk management thresholds from trading engine."""
        self.daily_loss_limit = self.trading_engine.daily_loss_limit
        self.total_profit_target = self.trading_engine.total_profit_target
        self.total_loss_limit = self.trading_engine.total_loss_limit

        self.logger.log_info(
            f"Initialized risk thresholds: "
            f"daily_loss_limit={self.daily_loss_limit}%, "
            f"total_profit_target={self.total_profit_target}%, "
            f"total_loss_limit={self.total_loss_limit}%"
        )

    def _filter_trades_by_instrument(self, trades):
        """
        Filter trades to only include those for the current strategy's instrument.

        Args:
            trades: List of trades to filter

        Returns:
            List of trades that match the current strategy's instrument
        """
        if not trades:
            return []

        current_instrument = self.trading_engine.strategy.instrument
        self.logger.log_info(f"🔍 FastUpdateLoop: Filtering {len(trades)} trades for instrument: {current_instrument}")

        # Handle both formats: EUR/USD and EUR_USD
        current_instrument_alt = current_instrument.replace('/', '_') if '/' in current_instrument else current_instrument.replace('_', '/')

        filtered_trades = []
        for trade in trades:
            trade_instrument = trade.instrument
            # Check both formats to be safe
            if trade_instrument == current_instrument or trade_instrument == current_instrument_alt:
                filtered_trades.append(trade)
                self.logger.log_info(f"✅ FastUpdateLoop: Including trade {trade.tradeID} for {trade_instrument}")
            else:
                self.logger.log_info(f"🚫 FastUpdateLoop: Filtering out trade {trade.tradeID} for {trade_instrument} (current strategy is for {current_instrument})")

        self.logger.log_info(f"🔍 FastUpdateLoop: Filtered result: {len(filtered_trades)}/{len(trades)} trades match instrument {current_instrument}")
        return filtered_trades

    def start(self):
        """Start the fast update loop."""
        if self.running:
            self.logger.log_warning("Fast update loop is already running")
            return

        self.running = True
        self.thread = threading.Thread(target=self._run_loop)
        self.thread.daemon = True
        self.thread.start()

        self.logger.log_info(f"Fast update loop started with interval {self.update_interval}s")

    def stop(self):
        """Stop the fast update loop."""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
            self.thread = None

        self.logger.log_info("Fast update loop stopped")

    def _run_loop(self):
        """Run the fast update loop."""
        while self.running:
            try:
                self._perform_fast_updates()
                self.last_update_time = datetime.now(timezone.utc)
            except Exception as e:
                self.logger.log_error(e, "Error in fast update loop")

            # Sleep for the update interval
            time.sleep(self.update_interval)

    def _perform_fast_updates(self):
        """Perform fast updates."""
        try:
            # Get open trades from OANDA - this will have the latest unrealized P&L values and TP/SL prices
            all_open_trades = self.oanda_client.get_open_trades()

            # Filter trades to only include those for the current strategy's instrument
            open_trades = self._filter_trades_by_instrument(all_open_trades)

            # Check for automatically closed trades using local memory (use all trades for detection)
            self._detect_closed_trades_fast(all_open_trades)

            if open_trades:
                self.logger.log_info(f"Updating information for {len(open_trades)} open trades")

                # Update open trades in Firebase - this will update unrealized P&L, TP/SL prices, and other trade data
                self.firebase_client.update_open_trades(open_trades)

                # Update local memory with current open trades
                self._update_local_memory(open_trades)

                # Log the updated unrealized P&L
                total_unrealized_pl = sum(float(trade.unrealizedPL) for trade in open_trades)
                self.logger.log_info(f"Total unrealized P&L: {total_unrealized_pl:.2f}")

                # Log individual trade details for debugging
                for trade in open_trades:
                    # Log basic trade information
                    trade_info = (
                        f"Trade {trade.tradeID}: {trade.instrument}, {trade.type.value}, "
                        f"Units: {trade.units}, Entry: {trade.price}, "
                        f"Unrealized P&L: {trade.unrealizedPL}"
                    )

                    # Log TP/SL information
                    if not trade.takeProfitPrice or not trade.stopLossPrice:
                        self.logger.log_info(f"{trade_info} - Missing TP/SL prices")
                    else:
                        self.logger.log_info(
                            f"{trade_info} - TP: {trade.takeProfitPrice}, SL: {trade.stopLossPrice}"
                        )
            else:
                self.logger.log_info("No open trades to update")
                # Clear local memory if no open trades
                if self.local_open_trades:
                    self.logger.log_info("Clearing local memory - no open trades")
                    self.local_open_trades.clear()

        except Exception as e:
            self.logger.log_error(e, "Error updating trade information")

        # Check risk management thresholds
        self._check_risk_thresholds()

    def _detect_closed_trades_fast(self, current_open_trades):
        """
        Fast detection of closed trades using local memory.

        Args:
            current_open_trades: List of currently open trades from OANDA
        """
        try:
            # Get current trade IDs
            current_trade_ids = {trade.tradeID for trade in current_open_trades}

            # Check if any trades in local memory are no longer open
            closed_trade_ids = set(self.local_open_trades.keys()) - current_trade_ids

            if closed_trade_ids:
                self.logger.log_info(f"🚨 FAST CLOSURE DETECTION: Found {len(closed_trade_ids)} closed trades: {closed_trade_ids}")

                for trade_id in closed_trade_ids:
                    try:
                        # Skip if this trade was already processed
                        if trade_id in self.processed_closed_trades:
                            self.logger.log_info(f"Trade {trade_id} already processed by fast loop, skipping duplicate processing")
                            continue

                        # Get the original trade info from local memory
                        original_trade_info = self.local_open_trades[trade_id]

                        self.logger.log_info(f"🔍 Processing closed trade {trade_id}: {original_trade_info}")

                        # Get closure details from OANDA, passing the trade open time to help narrow the search
                        trade_open_time = original_trade_info.get('openTime')
                        closed_trade_details = self.oanda_client.get_closed_trade_details(trade_id, trade_open_time)

                        if closed_trade_details:
                            realized_pl = closed_trade_details.get('realizedPL', 0.0)
                            close_reason = closed_trade_details.get('closeReason', 'UNKNOWN')
                            close_time = closed_trade_details.get('closeTime')
                            close_price = closed_trade_details.get('closePrice', 0.0)

                            self.logger.log_info(f"✅ Trade {trade_id} closure details: P&L=${realized_pl:.2f}, Reason={close_reason}, ClosePrice={close_price}")

                            # Create closed trade history row
                            from core.trading_engine_types import TradeHistoryRow, TradeStatus, TradeType

                            closed_trade = TradeHistoryRow(
                                tradeID=trade_id,
                                type=TradeType(original_trade_info['type']),
                                status=TradeStatus.CLOSED,
                                instrument=original_trade_info['instrument'],
                                price=original_trade_info['price'],
                                openTime=original_trade_info['openTime'],
                                units=original_trade_info['units'],
                                initialMarginRequired=original_trade_info.get('initialMarginRequired', 0.0),
                                unrealizedPL=0.0,  # Now closed
                                takeProfitPrice=original_trade_info.get('takeProfitPrice'),
                                stopLossPrice=original_trade_info.get('stopLossPrice'),
                                halfSpreadCost=original_trade_info.get('halfSpreadCost'),
                                commission=original_trade_info.get('commission'),
                                realizedPL=realized_pl,
                                closeTime=close_time
                            )

                            # Update Firebase with closed trade
                            self.firebase_client.update_open_trades([closed_trade])

                            # CRITICAL: Update risk management metrics with the realized P&L
                            if realized_pl != 0:
                                self.logger.log_info(f"Updating risk management metrics for automatically closed trade {trade_id} with P&L: {realized_pl}")

                                # Update daily loss tracker if negative P&L
                                if realized_pl < 0:
                                    self.trading_engine._update_daily_loss(realized_pl, update_metrics=False)

                                # Update total profit and loss trackers
                                self.trading_engine._update_total_pnl(realized_pl, update_metrics=False)

                                # Update risk management metrics in Firebase
                                self.trading_engine._update_risk_management_metrics()

                                self.logger.log_info(f"Risk management metrics updated: totalProfit={self.trading_engine.total_profit:.2f}, totalLoss={self.trading_engine.total_loss:.2f}, dailyLoss={self.trading_engine.daily_loss:.2f}")

                            # Log closure
                            closure_reason = self._determine_closure_reason_fast(close_reason, realized_pl)
                            self.logger.log_info(f"🎯 Trade {trade_id} automatically closed: {closure_reason}")
                            self.firebase_client.append_user_log(
                                f"🔴 Trade {trade_id} automatically closed: {closure_reason} | P&L: ${realized_pl:.2f}"
                            )

                            # Update trading engine metrics
                            if hasattr(self.trading_engine, 'metrics'):
                                self.trading_engine.metrics['trades_closed'] += 1

                            # Mark this trade as processed by fast loop
                            self.processed_closed_trades.add(trade_id)
                            self.logger.log_info(f"Marked trade {trade_id} as processed by fast loop (total processed: {len(self.processed_closed_trades)})")

                            # Keep the set from growing too large
                            if len(self.processed_closed_trades) > 1000:
                                # Remove oldest entries (keep last 500)
                                self.processed_closed_trades = set(list(self.processed_closed_trades)[-500:])
                                self.logger.log_info(f"Cleaned up processed trades set, now contains {len(self.processed_closed_trades)} entries")

                        else:
                            self.logger.log_warning(f"❌ Could not get closure details for trade {trade_id}")

                    except Exception as e:
                        self.logger.log_error(e, f"Error processing closed trade {trade_id}")

                # Remove closed trades from local memory
                for trade_id in closed_trade_ids:
                    self.local_open_trades.pop(trade_id, None)

        except Exception as e:
            self.logger.log_error(e, "Error in fast closure detection")

    def was_trade_processed_by_fast_loop(self, trade_id: str) -> bool:
        """
        Check if a trade was already processed by the fast loop.

        Args:
            trade_id: The trade ID to check

        Returns:
            bool: True if the trade was processed by fast loop, False otherwise
        """
        return trade_id in self.processed_closed_trades

    def _update_local_memory(self, open_trades):
        """
        Update local memory with current open trades.

        Args:
            open_trades: List of currently open trades
        """
        try:
            # Clear and rebuild local memory
            self.local_open_trades.clear()

            for trade in open_trades:
                self.local_open_trades[trade.tradeID] = {
                    'type': trade.type.value,
                    'instrument': trade.instrument,
                    'price': trade.price,
                    'openTime': trade.openTime,
                    'units': trade.units,
                    'initialMarginRequired': getattr(trade, 'initialMarginRequired', 0.0),
                    'takeProfitPrice': getattr(trade, 'takeProfitPrice', None),
                    'stopLossPrice': getattr(trade, 'stopLossPrice', None),
                    'halfSpreadCost': getattr(trade, 'halfSpreadCost', None),
                    'commission': getattr(trade, 'commission', None)
                }

        except Exception as e:
            self.logger.log_error(e, "Error updating local memory")

    def _determine_closure_reason_fast(self, close_reason: str, realized_pl: float) -> str:
        """
        Determine closure reason for fast detection.

        Args:
            close_reason: Close reason from OANDA
            realized_pl: Realized P&L

        Returns:
            str: Human-readable closure reason
        """
        if close_reason == 'STOP_LOSS_ORDER':
            return f"Stop Loss hit (${realized_pl:.2f})"
        elif close_reason == 'TAKE_PROFIT_ORDER':
            return f"Take Profit hit (${realized_pl:.2f})"
        elif realized_pl > 0:
            return f"Closed with profit (${realized_pl:.2f})"
        elif realized_pl < 0:
            return f"Closed with loss (${realized_pl:.2f})"
        else:
            return f"Automatically closed (${realized_pl:.2f})"

    def was_trade_processed_by_fast_loop(self, trade_id: str) -> bool:
        """
        Check if a trade was already processed by the fast loop.

        Args:
            trade_id: The trade ID to check

        Returns:
            bool: True if already processed by fast loop
        """
        return trade_id in self.processed_closed_trades

    def _check_risk_thresholds(self):
        """
        Check risk management thresholds including unrealized P&L.

        This method checks if any risk thresholds have been exceeded,
        considering both realized and unrealized P&L.

        Returns:
            Dict[str, Any]: Risk check result with status and message
        """
        try:
            # Get account summary
            account_summary = self.oanda_client.get_account_summary()
            if not account_summary:
                self.logger.log_warning("Failed to get account summary, skipping risk check")
                return

            # Get account balance
            account_balance = float(account_summary.get('balance', 0))
            if account_balance <= 0:
                self.logger.log_warning("Invalid account balance, skipping risk check")
                return

            # Get open trades
            open_trades = self.oanda_client.get_open_trades()

            # Calculate total unrealized P&L
            total_unrealized_pl = sum(float(trade.unrealizedPL) for trade in open_trades)

            # Get total realized P&L from trading engine
            total_realized_pl = self.trading_engine.total_profit - self.trading_engine.total_loss

            # Calculate total P&L (realized + unrealized)
            total_pl = total_realized_pl + total_unrealized_pl

            # Calculate total P&L as percentage of account balance
            total_pl_pct = (total_pl / account_balance) * 100

            self.logger.log_info(
                f"Risk check: total_realized_pl={total_realized_pl:.2f}, "
                f"total_unrealized_pl={total_unrealized_pl:.2f}, "
                f"total_pl={total_pl:.2f} ({total_pl_pct:.2f}%)"
            )

            # Check total profit target
            if self.total_profit_target and total_pl_pct >= self.total_profit_target:
                self.logger.log_warning(
                    f"Total profit target reached: {total_pl_pct:.2f}% >= {self.total_profit_target}%"
                )
                self.firebase_client.append_user_log(
                    f"🎯 Total profit target of {self.total_profit_target}% reached! Bot will stop."
                )

                # Update bot status
                self.firebase_client.update_bot_status(
                    "STOPPING",
                    {"message": f"Total profit target of {self.total_profit_target}% reached"}
                )

                # Signal the main bot to stop
                self._signal_bot_to_stop()

                return {
                    "status": "total_profit_target_reached",
                    "message": f"Total profit target of {self.total_profit_target}% reached"
                }

            # Check total loss limit
            if self.total_loss_limit and total_pl_pct <= -self.total_loss_limit:
                self.logger.log_warning(
                    f"Total loss limit exceeded: {total_pl_pct:.2f}% <= -{self.total_loss_limit}%"
                )
                self.firebase_client.append_user_log(
                    f"⛔ Total loss limit of {self.total_loss_limit}% exceeded. Bot will stop."
                )

                # Update bot status
                self.firebase_client.update_bot_status(
                    "STOPPING",
                    {"message": f"Total loss limit of {self.total_loss_limit}% exceeded"}
                )

                # Signal the main bot to stop
                self._signal_bot_to_stop()

                return {
                    "status": "total_loss_limit_exceeded",
                    "message": f"Total loss limit of {self.total_loss_limit}% exceeded"
                }

            # Calculate daily P&L
            # For simplicity, we'll use the trading engine's daily loss tracker
            # and add the unrealized P&L from today's open trades
            daily_loss = self.trading_engine.daily_loss

            # Add unrealized P&L from trades opened today
            today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            for trade in open_trades:
                try:
                    # Check if the trade was opened today
                    # Handle different formats of openTime
                    if hasattr(trade, 'openTime'):
                        # If openTime is a string, parse it
                        if isinstance(trade.openTime, str):
                            try:
                                # Try ISO format parsing
                                trade_open_time = datetime.fromisoformat(trade.openTime.replace('Z', '+00:00'))
                            except ValueError:
                                # Try other common formats
                                try:
                                    trade_open_time = datetime.strptime(trade.openTime, '%Y-%m-%dT%H:%M:%S.%fZ')
                                except ValueError:
                                    try:
                                        trade_open_time = datetime.strptime(trade.openTime, '%Y-%m-%d %H:%M:%S')
                                    except ValueError:
                                        self.logger.log_warning(f"Could not parse trade open time: {trade.openTime}")
                                        continue
                        # If openTime is already a datetime object
                        elif isinstance(trade.openTime, datetime):
                            trade_open_time = trade.openTime
                        # If openTime is a timestamp (int or float)
                        elif isinstance(trade.openTime, (int, float)):
                            trade_open_time = datetime.fromtimestamp(trade.openTime, tz=timezone.utc)
                        else:
                            self.logger.log_warning(f"Unknown type for trade open time: {type(trade.openTime)}")
                            continue
                    else:
                        # If openTime is not available, skip this trade
                        self.logger.log_warning(f"Trade has no openTime attribute: {trade}")
                        continue

                    # Now check if the trade was opened today
                    if trade_open_time >= today_start:
                        # Add this trade's unrealized P&L to the daily loss calculation
                        if hasattr(trade, 'unrealizedPL'):
                            unrealized_pl = float(trade.unrealizedPL) if isinstance(trade.unrealizedPL, (str, int, float)) else 0
                            daily_loss += unrealized_pl if unrealized_pl < 0 else 0
                except Exception as e:
                    self.logger.log_error(e, f"Error processing trade for daily loss calculation: {trade}")

            # Calculate daily loss as percentage of account balance
            daily_loss_pct = (daily_loss / account_balance) * 100 if daily_loss > 0 else 0

            # Check daily loss limit
            if self.daily_loss_limit and daily_loss_pct >= self.daily_loss_limit:
                self.logger.log_warning(
                    f"Daily loss limit exceeded: {daily_loss_pct:.2f}% >= {self.daily_loss_limit}%"
                )
                self.firebase_client.append_user_log(
                    f"⚠️ Daily loss limit of {self.daily_loss_limit}% exceeded. Trading paused for today."
                )

                # Update bot status
                self.firebase_client.update_bot_status(
                    "PAUSED",
                    {"message": f"Daily loss limit of {self.daily_loss_limit}% exceeded"}
                )

                # Signal the main bot to pause
                self._signal_bot_to_pause()

                return {
                    "status": "daily_loss_limit_exceeded",
                    "message": f"Daily loss limit of {self.daily_loss_limit}% exceeded"
                }

            return {
                "status": "ok",
                "message": "Risk thresholds not exceeded"
            }
        except Exception as e:
            self.logger.log_error(e, "Error checking risk thresholds")
            return {
                "status": "error",
                "message": f"Error checking risk thresholds: {str(e)}"
            }

    def _signal_bot_to_stop(self):
        """
        Signal the main bot to stop by creating a stop flag file.
        """
        try:
            # Create a stop flag file to signal the main bot to stop
            stop_flag_file = f"/tmp/trade_bot_stopped_{self.trading_engine.strategy_id}"
            with open(stop_flag_file, 'w') as f:
                f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()} due to risk threshold")
            self.logger.log_info(f"Created stop flag file: {stop_flag_file}")
        except Exception as e:
            self.logger.log_error(e, "Error creating stop flag file")

    def _signal_bot_to_pause(self):
        """
        Signal the main bot to pause by setting a pause flag in the trading engine.
        """
        try:
            # Set the is_paused flag in the trading engine
            self.trading_engine.is_paused = True
            self.logger.log_info("Set trading engine pause flag")

            # Close all open trades
            self.trading_engine.close_and_update_open_trades()
            self.logger.log_info("Closed all open trades due to daily loss limit")
        except Exception as e:
            self.logger.log_error(e, "Error pausing trading engine")
