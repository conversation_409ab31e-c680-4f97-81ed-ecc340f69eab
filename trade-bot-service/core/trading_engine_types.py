from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from enum import Enum

class TradeType(Enum):
    LONG = "long"  # Previously "buy"
    SHORT = "short"  # Previously "sell"
    CLOSE = "close"

class TradeStatus(Enum):
    OPEN = "open"
    CLOSED = "closed"

@dataclass
class TradeExecutionResponse:
    """
    Represents the response from a trade execution request.
    This is OrynTrade's trade execution response structure.
    This structure will exist regardless of the broker used.
    """

    # Type of trade
    type : TradeType

    # Primary key for tracking the trade
    tradeID : str

    # Instrument traded
    instrument : str

    # Price at which the trade was executed
    price : float

    # Time at which the trade was executed
    time : datetime

    # Number of units traded (positive for buy, negative for sell)
    units : float

    # Initial margin required to open the position
    initialMarginRequired : float

    # Initial cost paid to open the position
    halfSpreadCost : float

    # Commission paid to open the position
    commission : float

    # Account balance after the trade was executed
    accountBalance : float

    # Profit/Loss from the trade
    # This is the unrealized profit/loss in the quote currency
    pl : float

@dataclass
class TradeHistoryRow:
    # Primary key for tracking the trade
    tradeID : str

    # Type of trade
    type : TradeType

    # If the trade is open or closed
    status : TradeStatus

    # Instrument traded
    instrument : str

    # Latest price of the trade
    price : float

    # Time at which the trade was executed
    openTime : datetime

    # Number of units traded (positive for buy, negative for sell)
    units : float

    # Initial margin required to open the position
    initialMarginRequired : float

    # Unrealized profit/loss from the trade
    unrealizedPL : Optional[float] = None

    # Take profit price
    takeProfitPrice : Optional[float] = None

    # Stop loss price
    stopLossPrice : Optional[float] = None

    # Initial cost paid to open the position
    halfSpreadCost : Optional[float] = None

    # Commission paid to open the position
    commission : Optional[float] = None

    # Realized profit/loss from the trade
    realizedPL : Optional[float] = None

    # Time at which the trade was closed
    closeTime : Optional[datetime] = None

    # Entry conditions that triggered this trade (for UI display)
    entryConditions : Optional[dict] = None
