import os
import time
import threading
import requests
from typing import Dict
from datetime import datetime, timezone, timedelta
from collections import deque
from utils.logger import Logger
from data.market_data import MarketDataProvider

class PollingMarketDataProvider(MarketDataProvider):
    """
    Market data provider that uses Polygon API for both historical and real-time data.
    Maintains a 1000-candle FIFO window by polling Polygon API after each candle period completes.
    """

    def __init__(self, api_key: str):
        """Initialize the Polling market data provider."""
        super().__init__(api_key)

        # Override logger name
        self.logger = Logger("PollingMarketDataProvider")

        # Real-time data management
        self.candle_buffer = {}  # symbol_timeframe -> deque of candles (FIFO)
        self.buffer_size = 1000  # Maintain 1000 candles
        self.subscriptions = set()  # Track active subscriptions
        self.update_callbacks = {}  # symbol_timeframe -> callback function

        # Polling management
        self.polling_threads = {}  # symbol_timeframe -> thread
        self.should_poll = {}  # symbol_timeframe -> bool
        self.last_candle_time = {}  # symbol_timeframe -> datetime

        # Threading
        self.thread_lock = threading.Lock()

        self.logger.log_info("Polling Market Data Provider initialized (Polygon API only)")

    def get_candles(self, symbol: str, timespan: str = "minute",
                   multiplier: int = 1, count: int = 1000) -> Dict:
        """
        Get candles using Polygon API for both initial data and real-time updates.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            timespan (str): Time interval
            multiplier (int): Multiplier (not used in current implementation)
            count (int): Number of candles to fetch

        Returns:
            Dict: Candle data response
        """
        try:
            self.logger.log_info(f"🔍 get_candles called with: symbol={symbol}, timespan={timespan}, count={count}")

            # Convert symbol format for consistency
            normalized_symbol = symbol.replace('/', '').replace('_', '')
            subscription_key = f"{normalized_symbol}_{timespan}"

            self.logger.log_info(f"🔑 Subscription key: {subscription_key}")

            # Check if we have real-time data in buffer
            if subscription_key in self.candle_buffer:
                buffer_candles = list(self.candle_buffer[subscription_key])
                if len(buffer_candles) >= count:
                    self.logger.log_info(f"Returning {len(buffer_candles)} candles from polling buffer")
                    return {
                        "status": "success",
                        "candles": buffer_candles[-count:],
                        "source": "polling_buffer"
                    }

            # First time or insufficient buffer data - get from Polygon API
            self.logger.log_info(f"Fetching initial {count} candles from Polygon API for {symbol} {timespan}")
            polygon_result = self._fetch_from_polygon(symbol, timespan, count)

            if polygon_result["status"] == "success":
                # Initialize buffer with Polygon data
                candles = polygon_result["candles"]
                self.candle_buffer[subscription_key] = deque(candles, maxlen=self.buffer_size)

                # Log buffer initialization details
                self.logger.log_info(f"📊 Initialized buffer with {len(candles)} candles from Polygon API for {symbol} {timespan}")
                if candles:
                    first_candle_time = datetime.fromtimestamp(candles[0]["time"], tz=timezone.utc)
                    last_candle_time = datetime.fromtimestamp(candles[-1]["time"], tz=timezone.utc)
                    time_ago = datetime.now(timezone.utc) - last_candle_time

                    self.logger.log_info(f"   📅 Range: {first_candle_time.isoformat()} to {last_candle_time.isoformat()}")
                    self.logger.log_info(f"   💰 Latest close: {candles[-1]['close']}")
                    self.logger.log_info(f"   ⏰ Latest candle age: {time_ago.total_seconds():.0f} seconds ago")

                # Start polling for this symbol/timeframe
                self._start_polling(normalized_symbol, timespan)
                return {
                    "status": "success",
                    "candles": candles[-count:],
                    "source": "polygon_api"
                }

            # If Polygon fails, return the error
            return polygon_result

        except Exception as e:
            self.logger.log_error(f"Error in Polling get_candles: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }

    def _fetch_from_polygon(self, symbol: str, timespan: str, count: int) -> Dict:
        """Fetch candle data from Polygon API."""
        try:
            # Format symbol for Polygon API (remove slashes and underscores)
            clean_symbol = symbol.replace('/', '').replace('_', '')
            polygon_symbol = f"C:{clean_symbol}"

            self.logger.log_info(f"🔄 Converting symbol: {symbol} -> {polygon_symbol}")

            # Calculate date range for fetching historical data
            end_date = datetime.now(timezone.utc)

            # Calculate how many days back we need to get enough candles
            timeframe_minutes = self._get_timeframe_minutes(timespan)
            candles_per_day = 1440 // timeframe_minutes  # How many candles per day
            days_needed = max(7, (count // candles_per_day) + 2)  # At least 7 days, or enough for count + buffer

            start_date = end_date - timedelta(days=days_needed)

            # Format dates for Polygon API
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")

            # Map timeframe to Polygon format
            if timeframe_minutes < 60:
                base_timeframe = "minute"
                multiplier = timeframe_minutes
            elif timeframe_minutes < 1440:
                base_timeframe = "hour"
                multiplier = timeframe_minutes // 60
            else:
                base_timeframe = "day"
                multiplier = timeframe_minutes // 1440

            # Construct API URL
            url = f"https://api.polygon.io/v2/aggs/ticker/{polygon_symbol}/range/{multiplier}/{base_timeframe}/{start_date_str}/{end_date_str}"

            # Make API request
            params = {
                "apiKey": self.api_key,
                "limit": 50000,  # Maximum limit to get as much data as possible
                "_t": int(time.time() * 1000)  # Cache busting parameter
            }

            self.logger.log_info(f"🔍 Fetching {count} candles from Polygon API: {symbol} {timespan}")
            self.logger.log_info(f"   📅 Date range: {start_date_str} to {end_date_str}")
            self.logger.log_info(f"   🔗 URL: {url}")
            self.logger.log_info(f"   ⚙️ Params: {params}")

            response = requests.get(url, params=params, timeout=30)

            self.logger.log_info(f"📡 Polygon API response: {response.status_code}")

            if response.status_code == 429:
                self.logger.log_error("❌ Rate limited by Polygon API")
                return {
                    "status": "error",
                    "message": "Rate limited by Polygon API"
                }

            if response.status_code != 200:
                self.logger.log_error(f"❌ Polygon API request failed: {response.status_code}")
                self.logger.log_error(f"❌ Response content: {response.text[:500]}")
                return {
                    "status": "error",
                    "message": f"Polygon API request failed: {response.status_code}"
                }

            try:
                data = response.json()
                self.logger.log_info(f"📊 Polygon API response status: {data.get('status', 'unknown')}")
                self.logger.log_info(f"📊 Response keys: {list(data.keys())}")
            except Exception as e:
                self.logger.log_error(f"❌ Failed to parse JSON response: {str(e)}")
                self.logger.log_error(f"❌ Raw response: {response.text[:500]}")
                return {
                    "status": "error",
                    "message": f"Failed to parse Polygon API response: {str(e)}"
                }

            results = data.get("results", [])

            if not results:
                self.logger.log_error(f"❌ No candle data returned from Polygon for {symbol} {timespan}")
                self.logger.log_error(f"❌ Full response: {data}")
                return {
                    "status": "error",
                    "message": f"No candle data returned from Polygon for {symbol} {timespan}"
                }

            # Convert to our format
            formatted_candles = []
            for candle_data in results:
                formatted_candle = {
                    "time": int(candle_data["t"] / 1000),  # Convert from milliseconds
                    "open": float(candle_data["o"]),
                    "high": float(candle_data["h"]),
                    "low": float(candle_data["l"]),
                    "close": float(candle_data["c"]),
                    "volume": int(candle_data.get("v", 0))
                }
                formatted_candles.append(formatted_candle)

            # Sort by timestamp and take the most recent candles
            formatted_candles.sort(key=lambda x: x["time"])
            recent_candles = formatted_candles[-count:] if len(formatted_candles) > count else formatted_candles

            self.logger.log_info(f"✅ Successfully fetched {len(recent_candles)} candles from Polygon API")

            # Log the latest candle to verify data freshness
            if recent_candles:
                latest_candle = recent_candles[-1]
                latest_time = datetime.fromtimestamp(latest_candle["time"], tz=timezone.utc)
                time_ago = datetime.now(timezone.utc) - latest_time

                self.logger.log_info(f"📊 Latest candle from Polygon API: {symbol} {timespan}")
                self.logger.log_info(f"   🕐 Time: {latest_time.isoformat()}")
                self.logger.log_info(f"   💰 OHLC: O:{latest_candle['open']}, H:{latest_candle['high']}, L:{latest_candle['low']}, C:{latest_candle['close']}")
                self.logger.log_info(f"   📈 Volume: {latest_candle['volume']}")
                self.logger.log_info(f"   ⏰ Age: {time_ago.total_seconds():.0f} seconds ago")

                # Warn if data is more than expected timeframe old
                expected_max_age_minutes = timeframe_minutes * 2  # Allow 2 periods of delay
                if time_ago.total_seconds() > expected_max_age_minutes * 60:
                    self.logger.log_warning(f"⚠️ Polygon data may be stale! Latest candle is {time_ago.total_seconds()/60:.1f} minutes old, expected max {expected_max_age_minutes} minutes for {timespan} timeframe")
                else:
                    self.logger.log_info(f"✅ Polygon data appears fresh for {timespan} timeframe")

            return {
                "status": "success",
                "candles": recent_candles,
                "source": "polygon_api"
            }

        except requests.exceptions.RequestException as e:
            self.logger.log_error(f"❌ Network error fetching from Polygon API: {str(e)}")
            return {
                "status": "error",
                "message": f"Network error: {str(e)}"
            }
        except Exception as e:
            self.logger.log_error(f"❌ Unexpected error fetching from Polygon API: {str(e)}")
            return {
                "status": "error",
                "message": f"Unexpected error: {str(e)}"
            }

    def _start_polling(self, symbol: str, timeframe: str):
        """Start polling for new candles for the given symbol/timeframe."""
        subscription_key = f"{symbol}_{timeframe}"
        
        with self.thread_lock:
            if subscription_key in self.polling_threads:
                self.logger.log_info(f"Polling already active for {subscription_key}")
                return

            self.should_poll[subscription_key] = True
            
            # Get the last candle time from buffer to know when to start polling
            if subscription_key in self.candle_buffer and self.candle_buffer[subscription_key]:
                last_candle = self.candle_buffer[subscription_key][-1]
                self.last_candle_time[subscription_key] = datetime.fromtimestamp(last_candle["time"], tz=timezone.utc)
            else:
                self.last_candle_time[subscription_key] = datetime.now(timezone.utc)

            # Start polling thread
            polling_thread = threading.Thread(
                target=self._polling_loop,
                args=(symbol, timeframe),
                daemon=True
            )
            polling_thread.start()
            self.polling_threads[subscription_key] = polling_thread
            
            self.logger.log_info(f"🔄 Started polling for {subscription_key}")

    def _polling_loop(self, symbol: str, timeframe: str):
        """Main polling loop for a specific symbol/timeframe."""
        subscription_key = f"{symbol}_{timeframe}"
        
        try:
            while self.should_poll.get(subscription_key, False):
                try:
                    # Calculate next candle completion time
                    next_poll_time = self._calculate_next_poll_time(timeframe)
                    
                    # Wait until next poll time
                    now = datetime.now(timezone.utc)
                    if next_poll_time > now:
                        sleep_seconds = (next_poll_time - now).total_seconds()
                        self.logger.log_info(f"⏰ Waiting {sleep_seconds:.1f}s until next {timeframe} candle for {symbol}")
                        time.sleep(sleep_seconds)

                    # Wait time based on timeframe - Optimized based on actual Polygon API performance
                    timeframe_minutes = self._get_timeframe_minutes(timeframe)
                    if timeframe_minutes <= 1:  # 1m timeframe
                        wait_seconds = 10  # Reduced from 30s to 10s based on actual performance
                    elif timeframe_minutes <= 3:  # 3m timeframe
                        wait_seconds = 90  # 1.5 minutes for 3m
                    else:  # 5m, 15m, 30m, 1h, 4h, 1d timeframes
                        wait_seconds = 120  # 2 minutes for 5m+ timeframes

                    self.logger.log_info(f"⏳ Waiting {wait_seconds} seconds for Polygon to process new {timeframe} candle")
                    self.logger.log_info(f"⏰ Polygon API seems to have significant processing delays for {timeframe} timeframes")
                    time.sleep(wait_seconds)

                    # Fetch latest candle from Polygon with retry mechanism
                    success = self._fetch_and_add_latest_candle_with_retry(symbol, timeframe)

                    if not success:
                        self.logger.log_warning(f"⚠️ Failed to get new candle after retries for {symbol} {timeframe}")
                        self.logger.log_warning(f"⚠️ This may create a gap in data - will try again next cycle")
                    
                except Exception as e:
                    self.logger.log_error(f"Error in polling loop for {subscription_key}: {str(e)}")
                    time.sleep(5)  # Wait before retrying
                    
        except Exception as e:
            self.logger.log_error(f"Fatal error in polling loop for {subscription_key}: {str(e)}")
        finally:
            with self.thread_lock:
                if subscription_key in self.polling_threads:
                    del self.polling_threads[subscription_key]
            self.logger.log_info(f"🛑 Polling stopped for {subscription_key}")

    def _calculate_next_poll_time(self, timeframe: str) -> datetime:
        """Calculate when the next candle period will complete."""
        now = datetime.now(timezone.utc)
        
        # Get timeframe in minutes
        timeframe_minutes = self._get_timeframe_minutes(timeframe)
        
        # Calculate the start of the current period
        if timeframe_minutes < 60:  # Minutes
            current_minute = now.minute
            period_start_minute = (current_minute // timeframe_minutes) * timeframe_minutes
            period_start = now.replace(minute=period_start_minute, second=0, microsecond=0)
        elif timeframe_minutes == 60:  # 1 hour
            period_start = now.replace(minute=0, second=0, microsecond=0)
        elif timeframe_minutes == 240:  # 4 hours
            current_hour = now.hour
            period_start_hour = (current_hour // 4) * 4
            period_start = now.replace(hour=period_start_hour, minute=0, second=0, microsecond=0)
        elif timeframe_minutes == 1440:  # 1 day
            period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            # Default to hourly for unknown timeframes
            period_start = now.replace(minute=0, second=0, microsecond=0)
        
        # Calculate next period completion
        next_period_start = period_start + timedelta(minutes=timeframe_minutes)
        
        return next_period_start

    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe to minutes."""
        timeframe_map = {
            "1m": 1, "3m": 3, "5m": 5, "15m": 15, "30m": 30,
            "1h": 60, "4h": 240, "1d": 1440
        }
        return timeframe_map.get(timeframe, 1)

    def stop_polling(self, symbol: str = None, timeframe: str = None):
        """Stop polling for specific symbol/timeframe or all."""
        with self.thread_lock:
            if symbol and timeframe:
                subscription_key = f"{symbol}_{timeframe}"
                self.should_poll[subscription_key] = False
                self.logger.log_info(f"🛑 Stopping polling for {subscription_key}")
            else:
                # Stop all polling
                for key in list(self.should_poll.keys()):
                    self.should_poll[key] = False
                self.logger.log_info("🛑 Stopping all polling")

    def _fetch_and_add_latest_candle(self, symbol: str, timeframe: str):
        """Fetch fresh 1000 candles and replace entire buffer - eliminates all edge cases!"""
        subscription_key = f"{symbol}_{timeframe}"

        try:
            self.logger.log_info(f"🔄 Fetching fresh 1000 candles and replacing entire buffer!")

            # Use the exact same _fetch_from_polygon method that works perfectly
            # Get 1000 candles just like initial fetch - no latency difference!
            polygon_result = self._fetch_from_polygon(symbol, timeframe, 1000)

            if polygon_result["status"] != "success":
                self.logger.log_error(f"Failed to fetch from Polygon: {polygon_result.get('message', 'Unknown error')}")
                return False

            candles = polygon_result["candles"]
            if not candles:
                self.logger.log_warning(f"No candles returned from Polygon for {symbol} {timeframe}")
                return False

            # Get the latest candle info for logging
            latest_candle = candles[-1]
            latest_candle_time = datetime.fromtimestamp(latest_candle["time"], tz=timezone.utc)

            # Check if we have new data
            if subscription_key in self.last_candle_time:
                buffer_latest = self.last_candle_time[subscription_key]
                self.logger.log_info(f"📊 Buffer latest: {buffer_latest.isoformat()}")
                self.logger.log_info(f"📊 Polygon latest: {latest_candle_time.isoformat()}")

                if latest_candle_time <= buffer_latest:
                    self.logger.log_info(f"⚠️ No new candle available yet for {symbol} {timeframe}")
                    return None  # No new data

            # Replace entire buffer with fresh 1000 candles - simple and clean!
            self.candle_buffer[subscription_key] = deque(candles, maxlen=self.buffer_size)
            self.last_candle_time[subscription_key] = latest_candle_time

            self.logger.log_info(f"🔄 Replaced entire buffer with {len(candles)} fresh candles for {symbol} {timeframe}")
            self.logger.log_info(f"   📊 Latest candle: {latest_candle_time.isoformat()}")
            self.logger.log_info(f"   💰 OHLC: O:{latest_candle['open']}, H:{latest_candle['high']}, L:{latest_candle['low']}, C:{latest_candle['close']}")
            self.logger.log_info(f"   📈 Volume: {latest_candle['volume']}")
            self.logger.log_info(f"✅ Buffer refresh complete - no FIFO complexity needed!")

            return True  # Successfully refreshed buffer

        except Exception as e:
            self.logger.log_error(f"Error refreshing buffer for {subscription_key}: {str(e)}")
            return False

    def _fetch_and_add_latest_candle_with_retry(self, symbol: str, timeframe: str) -> bool:
        """Fetch the latest candle with retry mechanism to avoid gaps."""
        max_retries = 3

        # Adjust retry delays based on timeframe
        timeframe_minutes = self._get_timeframe_minutes(timeframe)
        if timeframe_minutes <= 1:  # 1m timeframe
            retry_delays = [0, 10, 20]  # 0s, 10s, 20s additional delays (reduced from 15s, 30s)
        elif timeframe_minutes <= 3:  # 3m timeframe
            retry_delays = [0, 30, 60]  # 0s, 30s, 60s additional delays
        else:  # 5m+ timeframes (with 2-minute initial wait)
            retry_delays = [0, 60, 120]  # 0s, 60s, 120s additional delays

        for attempt in range(max_retries):
            if attempt > 0:
                delay = retry_delays[attempt]
                self.logger.log_info(f"🔄 Retry {attempt}/{max_retries-1}: Waiting additional {delay}s before retry")
                time.sleep(delay)

            self.logger.log_info(f"🎯 Attempt {attempt + 1}/{max_retries}: Fetching latest candle")

            # Try to fetch the candle
            result = self._fetch_and_add_latest_candle(symbol, timeframe)

            if result is not False:  # Success (either True or None for "already exists")
                if result is None:  # "Already exists" case
                    if attempt == 0:
                        # First attempt got "already exists" - this is the delay issue
                        self.logger.log_info(f"📊 First attempt: candle already exists, will retry")
                        continue
                    else:
                        # Still getting "already exists" after retries - accept it
                        self.logger.log_info(f"📊 Retry {attempt}: still getting existing candle, accepting delay")
                        return False
                else:
                    # Successfully added new candle
                    self.logger.log_info(f"✅ Successfully fetched new candle on attempt {attempt + 1}")
                    return True
            else:
                # Error occurred
                self.logger.log_warning(f"❌ Attempt {attempt + 1} failed with error")

        self.logger.log_error(f"❌ All {max_retries} attempts failed to get new candle")
        return False

    def cleanup(self):
        """Clean up resources."""
        self.stop_polling()
        # Wait for threads to finish
        time.sleep(2)
        self.logger.log_info("Polling market data provider cleaned up")
