#!/usr/bin/env python3
"""
Pub/Sub Market Data Provider

This module provides real-time market data via Google Cloud Pub/Sub
integration with the Polygon WebSocket Ingestion Service.
"""

import asyncio
import json
import logging
import os
import threading
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Callable, Any

from google.cloud import pubsub_v1
from google.api_core import exceptions

from utils.logger import Logger
from data.market_data import MarketDataProvider  # For initial historical data fetching
from utils.market_conditions import MarketConditions  # For market conditions checking

logger = Logger("PubSubMarketDataProvider")

class PubSubMarketDataProvider:
    """
    Market data provider that uses Google Cloud Pub/Sub to receive
    real-time forex data from the Polygon WebSocket Ingestion Service.
    """
    
    def __init__(
        self,
        project_id: str,
        trade_bot_id: str,
        on_candle_callback: Optional[Callable] = None,
        polygon_api_key: Optional[str] = None
    ):
        self.project_id = project_id
        self.trade_bot_id = trade_bot_id
        self.on_candle_callback = on_candle_callback

        # Initialize historical data provider for initial candle fetching
        self.historical_provider = None
        self.market_conditions = None

        if polygon_api_key:
            try:
                self.historical_provider = MarketDataProvider(polygon_api_key)
                # Also initialize market conditions handler for compatibility
                self.market_conditions = MarketConditions(polygon_api_key)
                logger.log_info("📊 Historical data provider and market conditions initialized")
            except Exception as e:
                logger.log_error(f"❌ Failed to initialize historical data provider: {e}")
                # Continue without historical provider - will rely only on real-time data
        
        # Pub/Sub configuration
        self.subscription_commands_topic = "polygon.subscription_commands"
        self.minute_aggregates_topic = "polygon.minute_aggregates"
        self.minute_aggregates_subscription = f"trade-bot-{trade_bot_id}-minute-aggregates"
        
        # Pub/Sub clients
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        
        # Topic and subscription paths
        self.subscription_commands_topic_path = self.publisher.topic_path(
            project_id, self.subscription_commands_topic
        )
        self.minute_aggregates_topic_path = self.publisher.topic_path(
            project_id, self.minute_aggregates_topic
        )
        self.minute_aggregates_subscription_path = self.subscriber.subscription_path(
            project_id, self.minute_aggregates_subscription
        )
        
        # State management
        self.subscribed_symbols = set()
        self.running = False
        self.subscriber_thread = None
        self.latest_candles = {}  # symbol -> latest candle data

        # Data flow monitoring for heartbeat detection
        self.last_data_received = {}  # symbol -> timestamp of last data received

        # Backfill management for higher timeframes
        self.missing_candle_timestamp = None
        self.missing_candle_symbol = None
        self.missing_candle_timespan = None
        self.missing_candle_multiplier = None
        self.backfill_thread = None
        self.backfill_running = False
        self.backfill_callback = None
        self.subscription_retry_counts = {}  # symbol -> number of retry attempts
        self.max_subscription_retries = 3  # Maximum retries before giving up
        self.data_timeout_seconds = 90  # Expect data within 90 seconds (1.5 minutes)
        self.heartbeat_check_interval = 30  # Check every 30 seconds

        # Debug: Instance tracking
        import time
        self.instance_id = f"{trade_bot_id}-{int(time.time())}"
        logger.log_info(f"🔍 DEBUG: Created PubSubMarketDataProvider instance: {self.instance_id}")

        # Check for existing instances (debugging)
        import threading
        if not hasattr(PubSubMarketDataProvider, '_instances'):
            PubSubMarketDataProvider._instances = {}

        if trade_bot_id in PubSubMarketDataProvider._instances:
            existing_instance = PubSubMarketDataProvider._instances[trade_bot_id]
            logger.log_warning(f"⚠️ Multiple instances detected for trade-bot {trade_bot_id}!")
            logger.log_warning(f"   Existing: {existing_instance}")
            logger.log_warning(f"   New: {self.instance_id}")

        PubSubMarketDataProvider._instances[trade_bot_id] = self.instance_id
        
        # Statistics
        self.messages_received = 0
        self.subscription_commands_sent = 0
        
        logger.log_info(f"📡 PubSubMarketDataProvider initialized for trade-bot: {trade_bot_id}")
        logger.log_info(f"🏗️ Project ID: {project_id}")

    def _normalize_symbol_format(self, symbol: str) -> str:
        """
        Normalize symbol format for consistent comparison.

        Converts between different forex symbol formats:
        - 'EUR/USD' -> 'EURUSD'
        - 'EURUSD' -> 'EURUSD'
        - 'EUR_USD' -> 'EURUSD'

        Args:
            symbol: Symbol in any format

        Returns:
            Normalized symbol without separators
        """
        if not symbol:
            return symbol

        # Remove common separators and convert to uppercase
        normalized = symbol.replace('/', '').replace('_', '').upper()
        return normalized

    def start(self):
        """Start the Pub/Sub market data provider."""
        if self.running:
            logger.log_warning("⚠️ PubSubMarketDataProvider already running")
            return

        logger.log_info("🚀 Starting PubSubMarketDataProvider...")

        # Create subscription for minute aggregates if it doesn't exist
        self._ensure_minute_aggregates_subscription()

        # Start the subscriber thread
        self.running = True
        self.subscriber_thread = threading.Thread(
            target=self._run_subscriber,
            daemon=True
        )
        self.subscriber_thread.start()

        # Start subscription monitoring thread
        self.monitor_thread = threading.Thread(
            target=self._monitor_subscriptions,
            daemon=True
        )
        self.monitor_thread.start()

        # Start heartbeat monitoring thread
        self.heartbeat_thread = threading.Thread(
            target=self._monitor_data_heartbeat,
            daemon=True
        )
        self.heartbeat_thread.start()

        # Start subscription heartbeat thread (sends periodic heartbeats to WebSocket service)
        self.subscription_heartbeat_thread = threading.Thread(
            target=self._send_subscription_heartbeats,
            daemon=True
        )
        self.subscription_heartbeat_thread.start()

        logger.log_info("✅ PubSubMarketDataProvider started")
        logger.log_info("ℹ️ Note: Real-time subscription will be activated during proper initialization")

    def _monitor_subscriptions(self):
        """Monitor subscription state and log changes."""
        last_subscriptions = set()

        while self.running:
            try:
                current_subscriptions = self.subscribed_symbols.copy()

                if current_subscriptions != last_subscriptions:
                    logger.log_info(f"🔍 DEBUG: Instance {self.instance_id} - Subscription change detected!")
                    logger.log_info(f"   Previous: {list(last_subscriptions)}")
                    logger.log_info(f"   Current: {list(current_subscriptions)}")

                    # Check for lost subscriptions
                    lost = last_subscriptions - current_subscriptions
                    gained = current_subscriptions - last_subscriptions

                    if lost:
                        logger.log_warning(f"⚠️ Lost subscriptions: {list(lost)}")
                    if gained:
                        logger.log_info(f"✅ Gained subscriptions: {list(gained)}")

                    last_subscriptions = current_subscriptions

                time.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.log_error(f"❌ Error in subscription monitor: {e}")
                time.sleep(30)

    def _monitor_data_heartbeat(self):
        """Monitor data flow and detect when minute aggregates stop coming."""
        logger.log_info("💓 Starting data heartbeat monitor...")

        while self.running:
            try:
                current_time = time.time()

                # Check each subscribed symbol for data flow
                for symbol in self.subscribed_symbols.copy():
                    last_received = self.last_data_received.get(symbol)

                    if last_received is None:
                        # No data received yet - this is normal for new subscriptions
                        # Give it some time before considering it a problem
                        continue

                    time_since_last_data = current_time - last_received

                    if time_since_last_data > self.data_timeout_seconds:
                        logger.log_warning(f"⚠️ No data received for {symbol} in {time_since_last_data:.0f} seconds")
                        logger.log_warning(f"💓 Heartbeat timeout detected for {symbol} - attempting resubscription")

                        # Attempt to resubscribe
                        self._attempt_resubscription(symbol)

                # Sleep before next check
                time.sleep(self.heartbeat_check_interval)

            except Exception as e:
                logger.log_error(f"❌ Error in heartbeat monitor: {e}")
                time.sleep(self.heartbeat_check_interval)

    def _attempt_resubscription(self, symbol: str):
        """
        Attempt to resubscribe to a symbol that has stopped sending data.

        Args:
            symbol: The forex pair symbol to resubscribe to
        """
        # Get current retry count
        retry_count = self.subscription_retry_counts.get(symbol, 0)

        if retry_count >= self.max_subscription_retries:
            logger.log_error(f"❌ Max resubscription attempts ({self.max_subscription_retries}) reached for {symbol}")
            logger.log_error(f"🛑 Stopping trade-bot due to persistent data flow issues with {symbol}")

            # This is a critical error that should stop the bot
            self._handle_critical_data_flow_error(symbol)
            return

        # Increment retry count
        self.subscription_retry_counts[symbol] = retry_count + 1

        logger.log_info(f"🔄 Attempting resubscription for {symbol} (attempt {retry_count + 1}/{self.max_subscription_retries})")

        try:
            # Step 1: Unsubscribe
            logger.log_info(f"📤 Sending unsubscribe command for {symbol}")
            unsubscribe_command = {
                "command": "unsubscribe",
                "symbol": symbol,
                "trade_bot_id": self.trade_bot_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            if not self._send_subscription_command(unsubscribe_command):
                logger.log_error(f"❌ Failed to send unsubscribe command for {symbol}")
                return

            # Wait a moment
            time.sleep(2)

            # Step 2: Resubscribe
            logger.log_info(f"📤 Sending subscribe command for {symbol}")
            subscribe_command = {
                "command": "subscribe",
                "symbol": symbol,
                "trade_bot_id": self.trade_bot_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            if not self._send_subscription_command(subscribe_command):
                logger.log_error(f"❌ Failed to send subscribe command for {symbol}")
                return

            # Reset the last received time to give the resubscription a chance
            self.last_data_received[symbol] = time.time()

            logger.log_info(f"✅ Resubscription attempt completed for {symbol}")

        except Exception as e:
            logger.log_error(f"❌ Error during resubscription attempt for {symbol}: {e}")

    def _handle_critical_data_flow_error(self, symbol: str):
        """
        Handle critical data flow errors that require stopping the bot.

        Args:
            symbol: The symbol that caused the critical error
        """
        # Create user-friendly error message
        user_friendly_message = (
            f"Market data feed interrupted for {symbol}. "
            f"The bot has stopped to prevent trading with outdated information. "
            f"Please check the system status and restart the bot when the issue is resolved."
        )

        # Technical error message for logs
        technical_error = (
            f"Critical data flow error: No minute aggregate data received for {symbol} "
            f"after {self.max_subscription_retries} resubscription attempts. "
            f"This indicates a persistent issue with the market data feed. "
            f"Trade-bot is stopping to prevent trading with stale data."
        )

        logger.log_error(f"🚨 CRITICAL ERROR: {technical_error}")

        # Stop the provider
        self.running = False

        # Signal the main bot to stop by raising a SystemExit
        # This will be caught by the main trading loop and handled properly
        logger.log_error("🚨" * 20)
        logger.log_error("🚨 TRADE-BOT STOPPING DUE TO DATA FLOW ISSUES")
        logger.log_error("🚨 USER ACTION REQUIRED: Check WebSocket ingestion service")
        logger.log_error("🚨" * 20)

        # Raise SystemExit with the user-friendly message
        # This will be caught by the main trading loop and properly handled
        raise SystemExit(user_friendly_message)

    def initialize_with_symbol(self, symbol: str, timespan: str, multiplier: int = 1, count: int = 1000):
        """
        Properly initialize the provider with historical data and real-time subscription.

        This follows the correct sequence:
        1. For 1m timeframe: Wait until current period is 3+ seconds in, then fetch 1000 candles
        2. For higher timeframes: Fetch 1000 candles immediately, wait for period end
        3. Subscribe to real-time data at appropriate time
        4. Validate sequential continuity and backfill if needed

        Args:
            symbol: Forex pair symbol (e.g., 'EUR/USD')
            timespan: Target timespan (e.g., '5m', '1h')
            multiplier: Multiplier for timespan
            count: Number of historical candles to fetch

        Returns:
            Dict with initialization results
        """
        try:
            logger.log_info(f"🚀 Initializing PubSub provider for {symbol} {timespan}")

            # Check if this is 1m timeframe
            is_1m_timeframe = (timespan == "minute" and multiplier == 1) or timespan == "1m"
            timeframe_minutes = self._get_timeframe_minutes(timespan, multiplier)

            if is_1m_timeframe:
                # For 1m timeframe: Wait until current period is at least 3 seconds in
                logger.log_info(f"⏰ 1m timeframe detected - waiting for current period to be 3+ seconds in")
                self._wait_for_period_buffer()

            # Step 1: Fetch historical FIFO buffer
            logger.log_info(f"📊 Step 1: Fetching {count} historical candles for FIFO buffer")
            historical_result = self.get_candles(symbol, timespan, multiplier, count)

            if not is_1m_timeframe:
                # For higher timeframes: Wait for current period to end before subscribing
                logger.log_info(f"⏰ Higher timeframe ({timeframe_minutes}m) detected - waiting for current period to end")
                missing_candle_timestamp = self._wait_for_period_end(timeframe_minutes)
                # Store the missing candle parameters for later backfill
                self.missing_candle_timestamp = missing_candle_timestamp
                self.missing_candle_symbol = symbol
                self.missing_candle_timespan = timespan
                self.missing_candle_multiplier = multiplier

            if historical_result.get("status") != "success":
                return {
                    "status": "error",
                    "message": f"Failed to fetch historical data: {historical_result.get('message', 'Unknown error')}",
                    "candles": []
                }

            candles = historical_result["candles"]
            logger.log_info(f"✅ Step 1 complete: {len(candles)} historical candles fetched")

            # Step 2: Fetch missing 1m candles for current period (for candle builder)
            logger.log_info(f"🔄 Step 2: Fetching missing 1m candles for current {timespan} period")

            # Construct proper timespan string (e.g., "5m", "1h", "1d")
            if timespan == "minute":
                timespan_str = f"{multiplier}m"
            elif timespan == "hour":
                timespan_str = f"{multiplier}h"
            elif timespan == "day":
                timespan_str = f"{multiplier}d"
            else:
                # If timespan is already in format like "5m", use as-is
                timespan_str = timespan

            logger.log_info(f"📊 Using timespan string: {timespan_str}")
            gap_result = self._fetch_current_period_1m_candles(symbol, timespan_str)

            candle_builder_1m_candles = []
            if gap_result.get("status") == "success":
                candle_builder_1m_candles = gap_result.get("candles", [])
                gap_added = len(candle_builder_1m_candles)
                if gap_added > 0:
                    logger.log_info(f"✅ Step 2 complete: Fetched {gap_added} 1m candles for candle builder")
                else:
                    logger.log_info(f"✅ Step 2 complete: No 1m candles needed for current period")
            else:
                logger.log_warning(f"⚠️ Step 2 warning: {gap_result.get('message', 'Gap fetch failed')}")

            # Step 3: Subscribe to real-time data
            logger.log_info(f"📡 Step 3: Subscribing to real-time data for {symbol}")
            if self.subscribe_to_symbol(symbol):
                logger.log_info(f"✅ Step 3 complete: Successfully subscribed to real-time data")
            else:
                logger.log_warning(f"⚠️ Step 3 warning: Failed to subscribe to real-time data")

            logger.log_info(f"🎉 Initialization complete for {symbol}")
            logger.log_info(f"📊 Strategy buffer: {len(candles)} {timespan} candles")
            logger.log_info(f"📊 Candle builder: {len(candle_builder_1m_candles)} 1m candles for current period")

            return {
                "status": "success",
                "message": "Provider initialized successfully",
                "candles": candles,  # 5m candles for strategy
                "candle_builder_1m_candles": candle_builder_1m_candles,  # 1m candles for candle builder
                "historical_count": len(candles),
                "gap_candles_added": len(candle_builder_1m_candles),
                "total_candles": len(candles)
            }

        except Exception as e:
            logger.log_error(f"❌ Error during provider initialization: {e}")
            return {
                "status": "error",
                "message": f"Initialization failed: {str(e)}",
                "candles": []
            }

    def _get_timeframe_minutes(self, timespan: str, multiplier: int = 1) -> int:
        """Convert timespan to minutes."""
        if timespan == "minute" or timespan == "1m":
            return multiplier
        elif timespan == "hour" or timespan == "1h":
            return multiplier * 60
        elif timespan == "day" or timespan == "1d":
            return multiplier * 1440
        else:
            # Try to parse formats like "5m", "15m", etc.
            if timespan.endswith("m"):
                return int(timespan[:-1])
            elif timespan.endswith("h"):
                return int(timespan[:-1]) * 60
            else:
                return multiplier  # Default fallback

    def _wait_for_period_buffer(self):
        """
        Wait until the current 1-minute period is at least 3 seconds in.
        This ensures the last candle in our 1000-candle buffer is complete.
        """
        import time

        while True:
            now = datetime.now(timezone.utc)
            seconds_into_minute = now.second

            if seconds_into_minute >= 3:
                logger.log_info(f"✅ Current period is {seconds_into_minute} seconds in - proceeding with initialization")
                break
            else:
                wait_time = 3 - seconds_into_minute
                logger.log_info(f"⏳ Current period is only {seconds_into_minute} seconds in - waiting {wait_time} more seconds")
                time.sleep(wait_time)
                # Check again in case we overslept
                continue

    def _wait_for_period_end(self, timeframe_minutes: int) -> datetime:
        """
        Wait for the current period to end and return the timestamp of the missing candle.

        Args:
            timeframe_minutes: The timeframe in minutes (e.g., 5 for 5m, 15 for 15m)

        Returns:
            datetime: The timestamp of the candle that will be missing from REST API
        """
        import time

        now = datetime.now(timezone.utc)

        # Calculate the current period start
        minutes_since_hour = now.minute
        periods_since_hour = minutes_since_hour // timeframe_minutes
        current_period_start_minute = periods_since_hour * timeframe_minutes

        current_period_start = now.replace(minute=current_period_start_minute, second=0, microsecond=0)
        next_period_start = current_period_start + timedelta(minutes=timeframe_minutes)

        # The missing candle will have the timestamp of the current period start
        missing_candle_timestamp = current_period_start

        # Calculate how long to wait
        time_until_next_period = (next_period_start - now).total_seconds()

        if time_until_next_period > 0:
            logger.log_info(f"⏳ Waiting {time_until_next_period:.1f} seconds for current {timeframe_minutes}m period to end")
            logger.log_info(f"📅 Current period: {current_period_start} → {next_period_start}")
            logger.log_info(f"🔍 Missing candle timestamp will be: {missing_candle_timestamp}")
            time.sleep(time_until_next_period)

        logger.log_info(f"✅ Period ended - ready to subscribe to websocket")
        return missing_candle_timestamp

    def start_backfill_process(self, callback):
        """
        Start the backfill process to fetch the missing candle.

        Args:
            callback: Function to call when the missing candle is found
        """
        if not self.missing_candle_timestamp:
            logger.log_info("📊 No missing candle to backfill - skipping backfill process")
            return

        self.backfill_callback = callback
        self.backfill_running = True

        logger.log_info(f"🔄 Starting backfill process for missing candle: {self.missing_candle_timestamp}")

        # Start backfill thread
        import threading
        self.backfill_thread = threading.Thread(target=self._backfill_worker, daemon=True)
        self.backfill_thread.start()

    def _backfill_worker(self):
        """
        Worker thread that polls for the missing candle every minute.
        """
        import time

        while self.backfill_running and self.missing_candle_timestamp:
            try:
                logger.log_info(f"🔍 Attempting to fetch missing candle: {self.missing_candle_timestamp}")

                # Try to fetch the specific missing candle
                result = self.historical_provider.get_candles(
                    symbol=self.missing_candle_symbol,
                    timespan=self.missing_candle_timespan,
                    multiplier=self.missing_candle_multiplier,
                    count=1,
                    from_timestamp=self.missing_candle_timestamp,
                    to_timestamp=self.missing_candle_timestamp + timedelta(minutes=1)
                )

                if result.get("status") == "success" and result.get("candles"):
                    candle = result["candles"][0]
                    # Verify this is the exact candle we're looking for
                    if "time" in candle:
                        candle_time = datetime.fromtimestamp(candle["time"], tz=timezone.utc)
                    elif "timestamp" in candle:
                        candle_time = datetime.fromisoformat(candle["timestamp"].replace('Z', '+00:00'))
                    else:
                        logger.log_error(f"❌ Backfill candle missing timestamp field. Available fields: {list(candle.keys())}")
                        continue

                    if candle_time == self.missing_candle_timestamp:
                        logger.log_info(f"✅ Found missing candle! Timestamp: {candle_time}")

                        # Call the callback with the found candle
                        if self.backfill_callback:
                            self.backfill_callback(candle)

                        # Stop backfill process
                        self.backfill_running = False
                        self.missing_candle_timestamp = None
                        break
                    else:
                        logger.log_warning(f"⚠️ Fetched candle timestamp {candle_time} doesn't match expected {self.missing_candle_timestamp}")

                # If not found, wait 60 seconds before trying again
                if self.backfill_running:
                    logger.log_info("⏳ Missing candle not available yet - waiting 60 seconds before retry")
                    time.sleep(60)

            except Exception as e:
                logger.log_error(f"❌ Error during backfill attempt: {e}")
                if self.backfill_running:
                    time.sleep(60)  # Wait before retrying on error

    def stop_backfill_process(self):
        """Stop the backfill process."""
        self.backfill_running = False
        if self.backfill_thread and self.backfill_thread.is_alive():
            logger.log_info("🛑 Stopping backfill process")
            self.backfill_thread.join(timeout=5)

    def validate_sequential_candles(self, candles, timeframe_minutes: int) -> bool:
        """
        Validate that the last 5 candles in the buffer are sequential.

        Args:
            candles: List of candle data
            timeframe_minutes: The timeframe in minutes

        Returns:
            bool: True if the last 5 candles are sequential, False otherwise
        """
        if len(candles) < 5:
            logger.log_warning(f"⚠️ Not enough candles for validation (have {len(candles)}, need 5)")
            return False

        # Get the last 5 candles
        last_5_candles = candles[-5:]

        logger.log_info(f"🔍 Validating sequential continuity of last 5 candles (timeframe: {timeframe_minutes}m)")

        # Debug: Log the structure of the first candle
        if last_5_candles:
            sample_candle = last_5_candles[0]
            logger.log_info(f"🔍 Sample candle fields: {list(sample_candle.keys())}")
            if "time" in sample_candle:
                sample_time = datetime.fromtimestamp(sample_candle["time"], tz=timezone.utc)
                logger.log_info(f"🔍 Sample candle time: {sample_time}")
            elif "timestamp" in sample_candle:
                logger.log_info(f"🔍 Sample candle timestamp: {sample_candle['timestamp']}")

        for i in range(1, len(last_5_candles)):
            prev_candle = last_5_candles[i-1]
            curr_candle = last_5_candles[i]

            # Parse timestamps - candles from MarketDataProvider use "time" field (Unix timestamp)
            if "time" in prev_candle:
                prev_time = datetime.fromtimestamp(prev_candle["time"], tz=timezone.utc)
                curr_time = datetime.fromtimestamp(curr_candle["time"], tz=timezone.utc)
            elif "timestamp" in prev_candle:
                # Handle ISO format timestamps if they exist
                prev_time = datetime.fromisoformat(prev_candle["timestamp"].replace('Z', '+00:00'))
                curr_time = datetime.fromisoformat(curr_candle["timestamp"].replace('Z', '+00:00'))
            else:
                logger.log_error(f"❌ Candle data missing timestamp field. Available fields: {list(prev_candle.keys())}")
                return False

            # Expected time difference
            expected_diff = timedelta(minutes=timeframe_minutes)
            actual_diff = curr_time - prev_time

            if actual_diff != expected_diff:
                logger.log_error(f"❌ Sequential validation failed!")
                logger.log_error(f"   Candle {i-1}: {prev_time}")
                logger.log_error(f"   Candle {i}: {curr_time}")
                logger.log_error(f"   Expected diff: {expected_diff}, Actual diff: {actual_diff}")
                return False

            logger.log_info(f"✅ Candles {i-1} → {i}: {prev_time} → {curr_time} (diff: {actual_diff})")

        logger.log_info(f"✅ Sequential validation passed - all 5 candles are properly sequential")
        return True

    def stop(self):
        """Stop the Pub/Sub market data provider."""
        if not self.running:
            return
        
        logger.log_info("🛑 Stopping PubSubMarketDataProvider...")
        self.running = False

        # Unsubscribe from all symbols
        logger.log_info(f"🔍 DEBUG: Unsubscribing from {len(self.subscribed_symbols)} symbols during stop")
        for symbol in self.subscribed_symbols.copy():
            logger.log_info(f"🔍 DEBUG: Unsubscribing from {symbol} during stop")
            self.unsubscribe_from_symbol(symbol)

        # Wait for subscriber thread to finish
        if self.subscriber_thread and self.subscriber_thread.is_alive():
            self.subscriber_thread.join(timeout=5)

        # Wait for heartbeat thread to finish
        if hasattr(self, 'heartbeat_thread') and self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=5)

        # Wait for subscription heartbeat thread to finish
        if hasattr(self, 'subscription_heartbeat_thread') and self.subscription_heartbeat_thread and self.subscription_heartbeat_thread.is_alive():
            self.subscription_heartbeat_thread.join(timeout=5)

        logger.log_info("✅ PubSubMarketDataProvider stopped")

    def subscribe_to_symbol(self, symbol: str):
        """
        Subscribe to real-time data for a forex pair.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        if symbol in self.subscribed_symbols:
            logger.log_info(f"ℹ️ Already subscribed to {symbol}")
            return
        
        logger.log_info(f"📈 Subscribing to {symbol}")
        
        # Send subscription command
        command = {
            "command": "subscribe",
            "symbol": symbol,
            "trade_bot_id": self.trade_bot_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        success = self._send_subscription_command(command)
        
        if success:
            self.subscribed_symbols.add(symbol)
            # Initialize heartbeat tracking for this symbol
            self.last_data_received[symbol] = time.time()  # Start tracking from subscription time
            self.subscription_retry_counts[symbol] = 0  # Reset retry count
            logger.log_info(f"✅ Successfully subscribed to {symbol}")
            logger.log_info(f"🔍 DEBUG: Instance {self.instance_id} - Current subscriptions: {list(self.subscribed_symbols)}")
        else:
            logger.log_error(f"❌ Failed to subscribe to {symbol}")

    def unsubscribe_from_symbol(self, symbol: str):
        """
        Unsubscribe from real-time data for a forex pair.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        if symbol not in self.subscribed_symbols:
            logger.log_info(f"ℹ️ Not subscribed to {symbol}")
            return
        
        logger.log_info(f"📉 Unsubscribing from {symbol}")
        
        # Send unsubscription command
        command = {
            "command": "unsubscribe",
            "symbol": symbol,
            "trade_bot_id": self.trade_bot_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        success = self._send_subscription_command(command)
        
        if success:
            self.subscribed_symbols.discard(symbol)
            # Clean up heartbeat tracking data
            self.last_data_received.pop(symbol, None)
            self.subscription_retry_counts.pop(symbol, None)
            logger.log_info(f"✅ Successfully unsubscribed from {symbol}")
            logger.log_info(f"🔍 DEBUG: Remaining subscriptions after unsubscribe: {list(self.subscribed_symbols)}")
        else:
            logger.log_error(f"❌ Failed to unsubscribe from {symbol}")

    def get_latest_candle(self, symbol: str) -> Optional[Dict]:
        """
        Get the latest candle data for a symbol.
        
        Args:
            symbol: Forex pair symbol
            
        Returns:
            Latest candle data or None if not available
        """
        return self.latest_candles.get(symbol)

    def get_subscribed_symbols(self) -> set:
        """Get currently subscribed symbols."""
        return self.subscribed_symbols.copy()

    def get_candles(self, symbol: str, timespan: str = "minute",
                   multiplier: int = 1, count: int = 1000) -> Dict:
        """
        Fetch historical candle data using the Polygon API.

        This method fetches initial historical data for strategy initialization.
        Real-time data will come through the Pub/Sub WebSocket integration.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            timespan: Time span for candles ('minute', 'hour', 'day')
            multiplier: Multiplier for timespan (e.g., 1 for 1-minute candles)
            count: Number of candles to fetch

        Returns:
            Dict containing candle data in the expected format
        """
        if not self.historical_provider:
            logger.log_error("❌ No historical data provider available")
            return {
                "status": "error",
                "message": "Historical data provider not initialized",
                "candles": []
            }

        try:
            logger.log_info(f"📊 Fetching {count} historical candles for {symbol} (timespan: {timespan}, multiplier: {multiplier})")

            # Use the historical provider to fetch candles
            result = self.historical_provider.get_candles(
                symbol=symbol,
                timespan=timespan,
                multiplier=multiplier,
                count=count
            )

            if result.get("status") == "success":
                candle_count = len(result.get("candles", []))
                logger.log_info(f"✅ Successfully fetched {candle_count} historical candles for {symbol}")

                # Fill the gap between last historical candle and current time
                timespan_str = f"{multiplier}{timespan[0]}"  # e.g., "5m", "1h"
                gap_fill_result = self._fill_current_period_gap(symbol, timespan_str, result["candles"])

                if gap_fill_result["status"] == "success":
                    # Use the gap-filled candles
                    result["candles"] = gap_fill_result["candles"]
                    gap_added = gap_fill_result.get("gap_candles_added", 0)
                    if gap_added > 0:
                        logger.log_info(f"✅ Filled current period gap: {gap_added} additional 1m candles")
                        logger.log_info(f"📊 Total candles after gap fill: {len(result['candles'])}")
                else:
                    logger.log_warning(f"⚠️ Could not fill current period gap: {gap_fill_result.get('message', 'Unknown error')}")

                # Note: Real-time subscription will be handled separately after proper initialization

            else:
                logger.log_warning(f"⚠️ Historical data fetch returned: {result.get('message', 'Unknown error')}")

            return result

        except Exception as e:
            logger.log_error(f"❌ Error fetching historical candles for {symbol}: {e}")
            return {
                "status": "error",
                "message": f"Failed to fetch historical data: {str(e)}",
                "candles": []
            }

    def _fill_current_period_gap(self, symbol: str, timespan: str, historical_candles: list) -> Dict:
        """
        Fill the gap between the last historical candle and current time with 1m candles.

        This ensures we have complete data for the current timeframe period.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            timespan: Target timespan (e.g., '5m', '15m', '1h')
            historical_candles: List of historical candles already fetched

        Returns:
            Dict with status and updated candles list
        """
        try:
            if not historical_candles:
                return {"status": "error", "message": "No historical candles provided"}

            # Parse timespan to get minutes
            timespan_minutes = self._parse_timespan_to_minutes(timespan)
            if timespan_minutes <= 1:
                # For 1m timespan, no gap filling needed
                return {"status": "success", "candles": historical_candles, "gap_candles_added": 0}

            # Get the last historical candle timestamp
            last_candle = historical_candles[-1]
            last_timestamp = last_candle.get('time', 0)

            # Calculate current time and current period start
            from datetime import datetime, timezone
            current_time = datetime.now(timezone.utc)
            current_timestamp = int(current_time.timestamp())

            logger.log_info(f"🕐 Current time: {current_time.isoformat()}")
            logger.log_info(f"📊 Last historical candle timestamp: {last_timestamp} ({datetime.fromtimestamp(last_timestamp, timezone.utc).isoformat()})")
            logger.log_info(f"⏱️ Timespan: {timespan_minutes} minutes")

            # Calculate how many minutes into the current period we are
            minutes_since_epoch = current_timestamp // 60
            period_start_minutes = (minutes_since_epoch // timespan_minutes) * timespan_minutes
            period_start_timestamp = period_start_minutes * 60

            logger.log_info(f"📅 Current period start: {datetime.fromtimestamp(period_start_timestamp, timezone.utc).isoformat()}")

            # Calculate how many 1m candles we need to fetch
            last_candle_minutes = last_timestamp // 60

            # Always start from the minute after the last historical candle
            gap_start_timestamp = (last_candle_minutes + 1) * 60

            # Calculate gap end (up to current minute, but not including incomplete current minute)
            gap_end_timestamp = (minutes_since_epoch - 1) * 60  # Up to previous completed minute

            logger.log_info(f"🔄 Gap calculation:")
            logger.log_info(f"   Gap start: {datetime.fromtimestamp(gap_start_timestamp, timezone.utc).isoformat()}")
            logger.log_info(f"   Gap end: {datetime.fromtimestamp(gap_end_timestamp, timezone.utc).isoformat()}")
            logger.log_info(f"   Gap duration: {(gap_end_timestamp - gap_start_timestamp) // 60} minutes")

            if gap_start_timestamp >= gap_end_timestamp:
                # No gap to fill
                logger.log_info(f"ℹ️ No gap to fill for {symbol} - already up to date")
                logger.log_info(f"   Reason: gap_start ({gap_start_timestamp}) >= gap_end ({gap_end_timestamp})")
                return {"status": "success", "candles": historical_candles, "gap_candles_added": 0}

            # Calculate number of minutes in the gap
            gap_minutes = (gap_end_timestamp - gap_start_timestamp) // 60
            logger.log_info(f"📊 Gap identified: {gap_minutes} minutes of 1m candles needed")

            # Calculate how many 1m candles we need
            gap_minutes = int((gap_end_timestamp - gap_start_timestamp) // 60)

            # Fetch 1m candles for the gap
            logger.log_info(f"🔄 Filling gap for {symbol}: {datetime.fromtimestamp(gap_start_timestamp)} to {datetime.fromtimestamp(gap_end_timestamp)}")
            logger.log_info(f"📊 Fetching {gap_minutes} 1m candles to fill the gap")

            gap_result = self.historical_provider.get_candles(
                symbol=symbol,
                timespan="minute",
                multiplier=1,
                count=gap_minutes + 10  # Add buffer to ensure we get enough candles
            )

            if gap_result.get("status") != "success":
                logger.log_warning(f"⚠️ Failed to fetch gap candles: {gap_result.get('message', 'Unknown error')}")
                return {"status": "success", "candles": historical_candles, "gap_candles_added": 0}

            gap_candles = gap_result.get("candles", [])
            if gap_candles:
                # Filter candles to only include those in our gap range
                filtered_gap_candles = []
                for candle in gap_candles:
                    candle_timestamp = candle.get("time", 0)
                    if gap_start_timestamp <= candle_timestamp <= gap_end_timestamp:
                        filtered_gap_candles.append(candle)

                logger.log_info(f"📊 Filtered {len(filtered_gap_candles)} gap candles from {len(gap_candles)} fetched")

                if filtered_gap_candles:
                    # Combine historical candles with filtered gap candles
                    combined_candles = historical_candles + filtered_gap_candles
                    logger.log_info(f"✅ Added {len(filtered_gap_candles)} gap candles to complete current period")
                    return {"status": "success", "candles": combined_candles, "gap_candles_added": len(filtered_gap_candles)}
                else:
                    logger.log_info(f"ℹ️ No gap candles in the required time range for {symbol}")
                    return {"status": "success", "candles": historical_candles, "gap_candles_added": 0}
            else:
                logger.log_info(f"ℹ️ No gap candles available for {symbol}")
                return {"status": "success", "candles": historical_candles, "gap_candles_added": 0}

        except Exception as e:
            logger.log_error(f"❌ Error filling current period gap: {e}")
            return {"status": "error", "message": str(e), "candles": historical_candles}

    def _fetch_current_period_1m_candles(self, symbol: str, timespan: str) -> Dict:
        """
        Fetch 1m candles for the current timeframe period.

        This calculates what 1m candles are needed to complete the current period
        and fetches them for the candle builder.

        Args:
            symbol: Forex pair symbol (e.g., 'EUR/USD')
            timespan: Target timespan (e.g., '5m', '15m', '1h')

        Returns:
            Dict with status and 1m candles for current period
        """
        try:
            # Parse timespan to get minutes
            timespan_minutes = self._parse_timespan_to_minutes(timespan)
            if timespan_minutes <= 1:
                # For 1m timespan, no gap filling needed
                return {"status": "success", "candles": []}

            # Calculate current time and current period start
            from datetime import datetime, timezone
            current_time = datetime.now(timezone.utc)
            current_timestamp = int(current_time.timestamp())

            logger.log_info(f"🕐 Current time: {current_time.isoformat()}")
            logger.log_info(f"⏱️ Timespan: {timespan_minutes} minutes")

            # Calculate current period boundaries
            minutes_since_epoch = current_timestamp // 60
            period_start_minutes = (minutes_since_epoch // timespan_minutes) * timespan_minutes
            period_start_timestamp = period_start_minutes * 60

            # Calculate how many minutes into the current period we are
            current_period_minutes = minutes_since_epoch - period_start_minutes

            logger.log_info(f"📅 Current period start: {datetime.fromtimestamp(period_start_timestamp, timezone.utc).isoformat()}")
            logger.log_info(f"📊 Minutes into current period: {current_period_minutes}")

            if current_period_minutes == 0:
                # We're at the start of a new period, no 1m candles needed
                logger.log_info(f"ℹ️ At start of new period - no 1m candles needed")
                return {"status": "success", "candles": []}

            # We need 1m candles from period start up to the last completed minute
            # For example: if we're 1 minute into period, we need the first minute (period_start)
            # If we're 3 minutes into period, we need minutes 0, 1, 2 (but not the current incomplete minute 3)

            if current_period_minutes <= 0:
                logger.log_info(f"ℹ️ No 1m candles needed - at start of period")
                return {"status": "success", "candles": []}

            gap_start_timestamp = period_start_timestamp
            # We need candles for minutes 0, 1, 2, ..., (current_period_minutes - 1)
            # The last candle we need is at: period_start + (current_period_minutes - 1) * 60
            gap_end_timestamp = period_start_timestamp + (current_period_minutes - 1) * 60

            logger.log_info(f"🔄 1m candles needed:")
            logger.log_info(f"   From: {datetime.fromtimestamp(gap_start_timestamp, timezone.utc).isoformat()}")
            logger.log_info(f"   To: {datetime.fromtimestamp(gap_end_timestamp, timezone.utc).isoformat()}")
            logger.log_info(f"   Count: {current_period_minutes} minutes")

            # Fetch 1m candles
            logger.log_info(f"🔄 Fetching {current_period_minutes} 1m candles for current period")

            gap_result = self.historical_provider.get_candles(
                symbol=symbol,
                timespan="minute",
                multiplier=1,
                count=current_period_minutes + 5  # Add buffer to ensure we get enough
            )

            if gap_result.get("status") != "success":
                logger.log_warning(f"⚠️ Failed to fetch 1m candles: {gap_result.get('message', 'Unknown error')}")
                return {"status": "success", "candles": []}

            all_1m_candles = gap_result.get("candles", [])
            if not all_1m_candles:
                logger.log_info(f"ℹ️ No 1m candles available")
                return {"status": "success", "candles": []}

            # Filter candles to only include those in our current period
            filtered_1m_candles = []
            for candle in all_1m_candles:
                candle_timestamp = candle.get("time", 0)
                if gap_start_timestamp <= candle_timestamp <= gap_end_timestamp:
                    filtered_1m_candles.append(candle)

            logger.log_info(f"📊 Filtered {len(filtered_1m_candles)} 1m candles from {len(all_1m_candles)} fetched")

            return {"status": "success", "candles": filtered_1m_candles}

        except Exception as e:
            logger.log_error(f"❌ Error fetching current period 1m candles: {e}")
            return {"status": "error", "message": str(e), "candles": []}

    def _parse_timespan_to_minutes(self, timespan: str) -> int:
        """
        Parse timespan string to minutes.

        Args:
            timespan: Timespan string (e.g., '1m', '5m', '15m', '1h', '4h', '1d')

        Returns:
            int: Number of minutes
        """
        timespan = timespan.lower().strip()

        if timespan.endswith('m'):
            return int(timespan[:-1])
        elif timespan.endswith('h'):
            return int(timespan[:-1]) * 60
        elif timespan.endswith('d'):
            return int(timespan[:-1]) * 24 * 60
        else:
            # Default to 1 minute
            return 1

    def check_market_status(self) -> Dict:
        """
        Check market status using the historical data provider.

        Returns:
            Dict containing market status information
        """
        if not self.historical_provider:
            logger.log_warning("⚠️ No historical data provider available for market status check")
            return {
                "status": "error",
                "message": "Historical data provider not initialized",
                "market_open": False
            }

        try:
            return self.historical_provider.check_market_status()
        except Exception as e:
            logger.log_error(f"❌ Error checking market status: {e}")
            return {
                "status": "error",
                "message": f"Failed to check market status: {str(e)}",
                "market_open": False
            }

    def check_market_conditions(self, symbol: str, trading_sessions: list = None,
                               avoid_high_spread: bool = True) -> Dict:
        """
        Check market conditions using the historical data provider.

        Args:
            symbol: Forex pair symbol
            trading_sessions: List of trading sessions to check
            avoid_high_spread: Whether to avoid high spread conditions

        Returns:
            Dict containing market conditions information
        """
        if not self.historical_provider:
            logger.log_warning("⚠️ No historical data provider available for market conditions check")
            return {
                "status": "error",
                "message": "Historical data provider not initialized",
                "market_suitable": False
            }

        try:
            return self.historical_provider.check_market_conditions(
                symbol=symbol,
                trading_sessions=trading_sessions,
                avoid_high_spread=avoid_high_spread
            )
        except Exception as e:
            logger.log_error(f"❌ Error checking market conditions for {symbol}: {e}")
            return {
                "status": "error",
                "message": f"Failed to check market conditions: {str(e)}",
                "market_suitable": False
            }

    def get_statistics(self) -> Dict:
        """Get provider statistics."""
        current_time = time.time()
        heartbeat_status = {}

        # Calculate heartbeat status for each subscribed symbol
        for symbol in self.subscribed_symbols:
            last_received = self.last_data_received.get(symbol)
            if last_received:
                time_since_last = current_time - last_received
                heartbeat_status[symbol] = {
                    "last_data_received": last_received,
                    "seconds_since_last_data": int(time_since_last),
                    "retry_count": self.subscription_retry_counts.get(symbol, 0),
                    "status": "healthy" if time_since_last < self.data_timeout_seconds else "timeout"
                }
            else:
                heartbeat_status[symbol] = {
                    "last_data_received": None,
                    "seconds_since_last_data": None,
                    "retry_count": self.subscription_retry_counts.get(symbol, 0),
                    "status": "waiting_for_first_data"
                }

        return {
            "messages_received": self.messages_received,
            "subscription_commands_sent": self.subscription_commands_sent,
            "subscribed_symbols": len(self.subscribed_symbols),
            "running": self.running,
            "heartbeat_status": heartbeat_status,
            "data_timeout_seconds": self.data_timeout_seconds,
            "max_subscription_retries": self.max_subscription_retries
        }

    def cleanup(self):
        """Clean up resources when the trade-bot shuts down."""
        logger.log_info("🧹 Cleaning up PubSubMarketDataProvider...")

        # Unsubscribe from all symbols before stopping
        subscribed_symbols = self.get_subscribed_symbols()
        for symbol in subscribed_symbols:
            logger.log_info(f"📉 Unsubscribing from {symbol} during cleanup")
            self.unsubscribe_from_symbol(symbol)

        # Stop the provider
        self.stop()

        # Clean up historical provider if it exists
        if self.historical_provider and hasattr(self.historical_provider, 'cleanup'):
            try:
                self.historical_provider.cleanup()
                logger.log_info("✅ Historical data provider cleaned up")
            except Exception as e:
                logger.log_error(f"❌ Error cleaning up historical provider: {e}")

        logger.log_info("✅ PubSubMarketDataProvider cleanup completed")

    def _ensure_minute_aggregates_subscription(self):
        """
        Ensure the minute aggregates subscription exists.

        To prevent receiving old cached messages, we delete and recreate
        the subscription when the trade-bot starts.
        """
        try:
            # First, try to delete any existing subscription to clear old messages
            try:
                self.subscriber.delete_subscription(
                    request={"subscription": self.minute_aggregates_subscription_path}
                )
                logger.log_info(f"🗑️ Deleted existing subscription to clear old messages: {self.minute_aggregates_subscription}")
            except exceptions.NotFound:
                # Subscription doesn't exist, which is fine
                logger.log_info(f"ℹ️ No existing subscription to delete: {self.minute_aggregates_subscription}")
            except Exception as e:
                logger.log_warning(f"⚠️ Could not delete existing subscription (continuing anyway): {e}")

            # Create a fresh subscription
            try:
                # Create subscription that only receives messages for this trade-bot's symbols
                # Note: We'll receive all messages and filter client-side since Pub/Sub
                # attribute filtering is limited
                request = {
                    "name": self.minute_aggregates_subscription_path,
                    "topic": self.minute_aggregates_topic_path,
                    "ack_deadline_seconds": 60
                }

                subscription = self.subscriber.create_subscription(request=request)
                logger.log_info(f"🆕 Created fresh subscription: {subscription.name}")

            except exceptions.AlreadyExists:
                # Subscription already exists, which is fine
                logger.log_info(f"✅ Subscription already exists: {self.minute_aggregates_subscription}")

            except Exception as e:
                logger.log_error(f"❌ Failed to create subscription: {e}")
                raise

        except Exception as e:
            logger.log_error(f"❌ Error setting up subscription: {e}")
            raise

    def _send_subscription_command(self, command: Dict) -> bool:
        """
        Send a subscription command to the WebSocket ingestion service.
        
        Args:
            command: Subscription command data
            
        Returns:
            bool: True if command was sent successfully
        """
        try:
            message_data = json.dumps(command).encode('utf-8')
            
            # Publish the command
            future = self.publisher.publish(
                self.subscription_commands_topic_path,
                message_data
            )
            
            # Wait for publish to complete
            message_id = future.result(timeout=10.0)
            
            self.subscription_commands_sent += 1
            logger.log_info(f"📤 Sent subscription command: {command['command']} {command['symbol']} (ID: {message_id})")
            
            return True
            
        except Exception as e:
            logger.log_error(f"❌ Failed to send subscription command: {e}")
            return False

    def _run_subscriber(self):
        """Run the Pub/Sub subscriber in a separate thread."""
        logger.log_info("👂 Starting Pub/Sub subscriber...")
        
        # Configure flow control
        flow_control = pubsub_v1.types.FlowControl(max_messages=100)
        
        try:
            # Start pulling messages
            streaming_pull_future = self.subscriber.subscribe(
                self.minute_aggregates_subscription_path,
                callback=self._handle_minute_aggregate_message,
                flow_control=flow_control
            )
            
            logger.log_info(f"🔄 Listening for minute aggregates on: {self.minute_aggregates_subscription}")
            
            # Keep the subscriber running
            while self.running:
                time.sleep(1)
            
            # Cancel the subscriber
            streaming_pull_future.cancel()
            logger.log_info("🛑 Pub/Sub subscriber stopped")
            
        except Exception as e:
            logger.log_error(f"❌ Error in Pub/Sub subscriber: {e}")

    def _handle_minute_aggregate_message(self, message):
        """
        Handle incoming minute aggregate messages.

        Args:
            message: Pub/Sub message containing minute aggregate data
        """
        try:
            # Parse the message
            data = json.loads(message.data.decode('utf-8'))
            symbol = data.get('symbol')

            # Debug: Log symbol filtering
            logger.log_info(f"🔍 DEBUG: Instance {self.instance_id} - Received symbol '{symbol}', subscribed to: {list(self.subscribed_symbols)}")

            # Normalize symbol formats for comparison
            # Convert both received symbol and subscribed symbols to the same format
            normalized_received_symbol = self._normalize_symbol_format(symbol)
            normalized_subscribed_symbols = {self._normalize_symbol_format(s) for s in self.subscribed_symbols}

            # Only process messages for symbols we're subscribed to
            if normalized_received_symbol not in normalized_subscribed_symbols:
                logger.log_warning(f"⚠️ Filtering out message for unsubscribed symbol: '{symbol}' (normalized: '{normalized_received_symbol}')")
                logger.log_info(f"🔍 DEBUG: Normalized subscribed symbols: {normalized_subscribed_symbols}")
                message.ack()  # Acknowledge but don't process
                return

            # Check message age to filter out old cached messages
            if not self._is_message_fresh(data):
                logger.log_warning(f"⏰ Filtering out stale message for {symbol} - too old")
                message.ack()  # Acknowledge but don't process old messages
                return

            logger.log_info(f"📊 Received minute aggregate for {symbol}: {data.get('close', 'N/A')}")

            # Find the original subscribed symbol format that matches this message
            original_symbol = None
            for subscribed_symbol in self.subscribed_symbols:
                if self._normalize_symbol_format(subscribed_symbol) == normalized_received_symbol:
                    original_symbol = subscribed_symbol
                    break

            # Use the original subscribed symbol format for storage and tracking
            storage_symbol = original_symbol if original_symbol else symbol

            # Update latest candle data using the original symbol format
            self.latest_candles[storage_symbol] = data
            self.messages_received += 1

            # Update heartbeat tracking - data received successfully
            self.last_data_received[storage_symbol] = time.time()
            # Reset retry count since we received data successfully
            self.subscription_retry_counts[storage_symbol] = 0

            # Call the callback if provided
            if self.on_candle_callback:
                self.on_candle_callback(data)

            # Acknowledge the message
            message.ack()

        except json.JSONDecodeError as e:
            logger.log_error(f"❌ Failed to parse minute aggregate message: {e}")
            message.ack()  # Ack to avoid reprocessing
        except Exception as e:
            logger.log_error(f"❌ Error handling minute aggregate message: {e}")
            message.nack()  # Nack to retry

    def _is_message_fresh(self, data: Dict[str, Any]) -> bool:
        """
        Check if a minute aggregate message is fresh enough to process.

        This filters out old cached messages that may have accumulated in the
        Pub/Sub subscription when the previous trade-bot stopped.

        Args:
            data: Minute aggregate data

        Returns:
            bool: True if message is fresh, False if too old
        """
        try:
            # Get the candle timestamp from the data
            timestamp_str = data.get('timestamp')
            if not timestamp_str:
                logger.log_warning("⚠️ Message missing timestamp, treating as fresh")
                return True

            # Parse the timestamp
            from datetime import datetime, timezone
            candle_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            current_time = datetime.now(timezone.utc)

            # Calculate age in minutes
            age_seconds = (current_time - candle_time).total_seconds()
            age_minutes = age_seconds / 60

            # Consider messages older than 5 minutes as stale
            # This accounts for normal market data delays plus some buffer
            max_age_minutes = 5

            if age_minutes > max_age_minutes:
                logger.log_info(f"⏰ Message age: {age_minutes:.1f} minutes (threshold: {max_age_minutes} minutes)")
                return False

            logger.log_info(f"✅ Fresh message: {age_minutes:.1f} minutes old")
            return True

        except Exception as e:
            logger.log_error(f"❌ Error checking message freshness: {e}")
            # If we can't determine age, treat as fresh to avoid blocking valid data
            return True

    def _send_subscription_heartbeats(self):
        """
        Send periodic heartbeats to the WebSocket service to indicate this trade-bot is alive.

        This helps the WebSocket service detect when trade-bots are forcefully terminated
        and clean up their subscriptions automatically.
        """
        logger.log_info("💓 Starting subscription heartbeat sender...")

        while self.running:
            try:
                # Send heartbeat for each subscribed symbol
                for symbol in self.subscribed_symbols.copy():
                    heartbeat_command = {
                        "command": "heartbeat",
                        "symbol": symbol,
                        "trade_bot_id": self.trade_bot_id,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }

                    # Send heartbeat (don't worry if it fails, it's just for cleanup)
                    try:
                        self._send_subscription_command(heartbeat_command)
                        logger.log_debug(f"💓 Sent heartbeat for {symbol}")
                    except Exception as e:
                        logger.log_warning(f"⚠️ Failed to send heartbeat for {symbol}: {e}")

                # Send heartbeats every 2 minutes (well within the 5-minute timeout)
                time.sleep(120)

            except Exception as e:
                logger.log_error(f"❌ Error in subscription heartbeat sender: {e}")
                time.sleep(120)  # Continue trying even if there's an error

def main():
    """Test function for the Pub/Sub market data provider."""
    import os
    
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
    trade_bot_id = 'test-bot-123'
    
    def on_candle(data):
        print(f"📊 Received candle: {data['symbol']} - {data['close']}")
    
    provider = PubSubMarketDataProvider(project_id, trade_bot_id, on_candle)
    
    try:
        provider.start()
        
        # Subscribe to EURUSD
        provider.subscribe_to_symbol('EURUSD')
        
        # Keep running
        while True:
            time.sleep(10)
            stats = provider.get_statistics()
            print(f"📊 Stats: {stats}")
            
    except KeyboardInterrupt:
        print("👋 Test stopped by user")
        provider.stop()

if __name__ == "__main__":
    main()
