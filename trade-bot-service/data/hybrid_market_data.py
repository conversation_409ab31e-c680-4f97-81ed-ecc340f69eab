import os
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timezone
from utils.logger import Logger
from data.market_data import MarketDataProvider
from data.polling_market_data import PollingMarketDataProvider

class HybridMarketDataWrapper:
    """
    Hybrid market data wrapper that intelligently chooses between basic and enhanced providers.

    This wrapper:
    1. <PERSON><PERSON> to use PollingMarketDataProvider (GCS + Polygon polling) first
    2. Falls back to basic MarketDataProvider (Polygon only) if GCS fails
    3. Provides seamless switching based on availability and performance
    """

    def __init__(self, api_key: str):
        """
        Initialize the hybrid market data wrapper.

        Args:
            api_key (str): Polygon.io API key
        """
        self.api_key = api_key
        self.logger = Logger("HybridMarketDataWrapper")
        
        # Initialize providers
        self.enhanced_provider = None
        self.basic_provider = None
        self.current_provider = None
        
        # Performance tracking
        self.provider_performance = {
            'enhanced': {'success_count': 0, 'error_count': 0, 'avg_time': 0},
            'basic': {'success_count': 0, 'error_count': 0, 'avg_time': 0}
        }
        
        # Configuration
        self.use_enhanced = os.getenv('USE_ENHANCED_MARKET_DATA', 'true').lower() == 'true'
        self.fallback_threshold = 3  # Number of consecutive errors before switching providers
        self.consecutive_errors = {'enhanced': 0, 'basic': 0}

        # Staleness detection settings
        self.max_staleness_minutes = 10  # Consider data stale after 10 minutes
        self.last_staleness_warning = None
        self.staleness_warning_interval = 300  # Warn every 5 minutes about staleness
        
        self._initialize_providers()
        
        self.logger.log_info("Hybrid market data wrapper initialized")

    def _initialize_providers(self):
        """Initialize market data providers."""
        try:
            # Always initialize basic provider as fallback
            self.basic_provider = MarketDataProvider(self.api_key)
            self.logger.log_info("Basic market data provider initialized")
            
            # Try to initialize enhanced provider if enabled
            if self.use_enhanced:
                try:
                    self.enhanced_provider = PollingMarketDataProvider(self.api_key)
                    self.current_provider = self.enhanced_provider
                    self.logger.log_info("Polling market data provider initialized and set as primary")
                except Exception as e:
                    self.logger.log_warning(f"Failed to initialize enhanced provider: {str(e)}")
                    self.current_provider = self.basic_provider
                    self.logger.log_info("Using basic provider as primary")
            else:
                self.current_provider = self.basic_provider
                self.logger.log_info("Enhanced provider disabled, using basic provider")
                
        except Exception as e:
            self.logger.log_error(f"Failed to initialize providers: {str(e)}")
            raise

    def get_candles(self, symbol: str, timespan: str = "minute",
                   multiplier: int = 1, count: int = 1000) -> Dict:
        """
        Fetch candle data using the best available provider.

        Args:
            symbol (str): Trading symbol (e.g., "EUR/USD")
            timespan (str): Time interval
            multiplier (int): Number of timespans to multiply by
            count (int): Number of candles to fetch

        Returns:
            Dict: Candle data response
        """
        import time
        start_time = time.time()
        
        # Determine which provider to use
        provider_to_use = self._select_provider()
        provider_name = self._get_provider_name(provider_to_use)
        
        try:
            # Attempt to fetch data
            result = provider_to_use.get_candles(symbol, timespan, multiplier, count)

            # Check for stale data if using WebSocket provider
            if provider_name == "enhanced" and result.get("status") == "success":
                is_stale, stale_info = self._check_data_staleness(result, timespan)
                if is_stale:
                    self.logger.log_warning(f"🚨 STALE DATA DETECTED: {stale_info}")

                    # Try fallback to REST API for fresh data
                    fallback_provider = self.basic_provider
                    if fallback_provider and fallback_provider != provider_to_use:
                        self.logger.log_info("🔄 Falling back to REST API for fresh data")
                        try:
                            fresh_result = fallback_provider.get_candles(symbol, timespan, multiplier, count)
                            if fresh_result.get("status") == "success":
                                fresh_result["source"] = "rest_api_fallback_stale_data"
                                self.logger.log_info("✅ Fresh data obtained from REST API fallback")
                                execution_time = time.time() - start_time
                                self._track_success("basic", execution_time)
                                return fresh_result
                        except Exception as fallback_error:
                            self.logger.log_error(f"REST API fallback failed: {str(fallback_error)}")

                    # If fallback fails, continue with stale data but mark it
                    result["data_staleness"] = stale_info
                    result["source"] = f"{result.get('source', 'unknown')}_STALE"

            # Track success
            execution_time = time.time() - start_time
            self._track_success(provider_name, execution_time)

            # Add source information to result
            if result.get("status") == "success":
                source = result.get("source", "unknown")
                if provider_name == "enhanced" and source in ["gcs", "cache"]:
                    result["source"] = "gcs_websocket_hybrid"
                else:
                    result["source"] = source

                self.logger.log_info(f"Candles fetched from: {result.get('source', 'unknown')}")

            return result
            
        except Exception as e:
            # Track error and try fallback
            self._track_error(provider_name)
            self.logger.log_error(f"Error with {provider_name} provider: {str(e)}")
            
            # Try fallback provider if available
            fallback_provider = self._get_fallback_provider(provider_to_use)
            if fallback_provider and fallback_provider != provider_to_use:
                fallback_name = self._get_provider_name(fallback_provider)
                self.logger.log_info(f"Trying fallback provider: {fallback_name}")
                
                try:
                    result = fallback_provider.get_candles(symbol, timespan, multiplier, count)
                    execution_time = time.time() - start_time
                    self._track_success(fallback_name, execution_time)
                    
                    # Add source information
                    if result.get("status") == "success":
                        result["source"] = f"{fallback_name}_fallback"
                        self.logger.log_info(f"Candles fetched from: {result.get('source', 'unknown')}")
                    
                    return result
                    
                except Exception as fallback_error:
                    self._track_error(fallback_name)
                    self.logger.log_error(f"Fallback provider also failed: {str(fallback_error)}")
            
            # If all providers fail, return error
            return {
                "status": "error",
                "message": f"All market data providers failed: {str(e)}"
            }

    def _select_provider(self):
        """Select the best provider based on performance and availability."""
        # If enhanced provider is disabled or not available, use basic
        if not self.enhanced_provider or not self.use_enhanced:
            return self.basic_provider
        
        # Check if enhanced provider has too many consecutive errors
        if self.consecutive_errors['enhanced'] >= self.fallback_threshold:
            self.logger.log_warning("Enhanced provider has too many errors, using basic provider")
            return self.basic_provider
        
        # Use enhanced provider by default
        return self.enhanced_provider

    def _get_fallback_provider(self, current_provider):
        """Get fallback provider for the given provider."""
        if current_provider == self.enhanced_provider:
            return self.basic_provider
        elif current_provider == self.basic_provider:
            return self.enhanced_provider
        return None

    def _get_provider_name(self, provider) -> str:
        """Get the name of the provider."""
        if provider == self.enhanced_provider:
            return "enhanced"
        elif provider == self.basic_provider:
            return "basic"
        return "unknown"

    def _track_success(self, provider_name: str, execution_time: float):
        """Track successful operation for a provider."""
        if provider_name in self.provider_performance:
            stats = self.provider_performance[provider_name]
            stats['success_count'] += 1
            
            # Update average execution time
            total_operations = stats['success_count'] + stats['error_count']
            if total_operations > 1:
                stats['avg_time'] = (stats['avg_time'] * (total_operations - 1) + execution_time) / total_operations
            else:
                stats['avg_time'] = execution_time
            
            # Reset consecutive errors on success
            self.consecutive_errors[provider_name] = 0

    def _track_error(self, provider_name: str):
        """Track error for a provider."""
        if provider_name in self.provider_performance:
            self.provider_performance[provider_name]['error_count'] += 1
            self.consecutive_errors[provider_name] += 1

    def _check_data_staleness(self, result: Dict, timespan: str) -> tuple[bool, str]:
        """
        Check if the data is stale (too old).

        Args:
            result: The data result from provider
            timespan: The requested timespan (e.g., "5m")

        Returns:
            tuple: (is_stale: bool, stale_info: str)
        """
        import time
        from datetime import datetime, timezone

        try:
            # Get the latest candle timestamp from the result
            candles = result.get("candles", [])
            if not candles:
                return False, "No candles to check"

            # Get the latest candle
            latest_candle = candles[-1]
            latest_timestamp = latest_candle.get("time")

            if not latest_timestamp:
                return False, "No timestamp in latest candle"

            # Convert timestamp to datetime
            if isinstance(latest_timestamp, (int, float)):
                latest_time = datetime.fromtimestamp(latest_timestamp, tz=timezone.utc)
            else:
                # Try to parse string timestamp
                latest_time = datetime.fromisoformat(str(latest_timestamp).replace('Z', '+00:00'))

            # Get current time
            current_time = datetime.now(timezone.utc)

            # Calculate age of data
            age_seconds = (current_time - latest_time).total_seconds()
            age_minutes = age_seconds / 60

            # Determine staleness threshold based on timespan
            if timespan == "5m" or timespan == "5minute":
                # For 5-minute candles, data is stale if older than 10 minutes
                staleness_threshold = self.max_staleness_minutes
            elif timespan == "1m" or timespan == "1minute":
                # For 1-minute candles, data is stale if older than 5 minutes
                staleness_threshold = 5
            else:
                # Default threshold
                staleness_threshold = self.max_staleness_minutes

            is_stale = age_minutes > staleness_threshold

            # Create info string
            stale_info = (
                f"Latest candle: {latest_time.strftime('%Y-%m-%d %H:%M:%S UTC')}, "
                f"Age: {age_minutes:.1f} minutes, "
                f"Threshold: {staleness_threshold} minutes, "
                f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
            )

            # Log staleness warnings periodically
            if is_stale:
                current_timestamp = time.time()
                if (self.last_staleness_warning is None or
                    current_timestamp - self.last_staleness_warning > self.staleness_warning_interval):
                    self.logger.log_warning(f"⚠️ Data staleness detected: {stale_info}")
                    self.last_staleness_warning = current_timestamp

            return is_stale, stale_info

        except Exception as e:
            self.logger.log_error(f"Error checking data staleness: {str(e)}")
            return False, f"Error checking staleness: {str(e)}"

    def get_quote(self, symbol: str) -> Dict:
        """Get quote using current provider with fallback."""
        try:
            # Try primary provider
            result = self.current_provider.get_quote(symbol)

            # Check if result indicates an error or stale data
            if result.get("source") in ["error_fallback", "fallback_default"] or result.get("error"):
                self.logger.log_warning(f"Primary provider returned fallback quote for {symbol}, trying REST API")

                # Try REST API fallback if available
                if self.basic_provider and self.current_provider != self.basic_provider:
                    try:
                        fallback_result = self.basic_provider.get_quote(symbol)
                        if not fallback_result.get("error"):
                            fallback_result["source"] = f"{fallback_result.get('source', 'rest_api')}_quote_fallback"
                            self.logger.log_info(f"✅ Got quote from REST API fallback for {symbol}")
                            return fallback_result
                    except Exception as e:
                        self.logger.log_error(f"REST API quote fallback failed: {str(e)}")

            return result

        except Exception as e:
            self.logger.log_error(f"Error getting quote for {symbol}: {str(e)}")

            # Try fallback provider
            if self.basic_provider and self.current_provider != self.basic_provider:
                try:
                    fallback_result = self.basic_provider.get_quote(symbol)
                    fallback_result["source"] = f"{fallback_result.get('source', 'rest_api')}_quote_error_fallback"
                    return fallback_result
                except Exception as fallback_error:
                    self.logger.log_error(f"Quote fallback also failed: {str(fallback_error)}")

            # Return error result
            return {
                "symbol": symbol,
                "bid": 0.0,
                "ask": 0.0,
                "spread": 0.0,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "source": "hybrid_error_fallback"
            }

    def check_market_status(self) -> Dict:
        """Check market status using current provider with fallback."""
        try:
            result = self.current_provider.check_market_status()

            # Check if result indicates an error
            if result.get("status") == "error":
                self.logger.log_warning("Primary provider market status failed, trying REST API fallback")

                # Try REST API fallback
                if self.basic_provider and self.current_provider != self.basic_provider:
                    try:
                        fallback_result = self.basic_provider.check_market_status()
                        if fallback_result.get("status") != "error":
                            fallback_result["source"] = f"{fallback_result.get('source', 'rest_api')}_status_fallback"
                            self.logger.log_info("✅ Got market status from REST API fallback")
                            return fallback_result
                    except Exception as e:
                        self.logger.log_error(f"Market status fallback failed: {str(e)}")

            return result

        except Exception as e:
            self.logger.log_error(f"Error checking market status: {str(e)}")

            # Try fallback provider
            if self.basic_provider and self.current_provider != self.basic_provider:
                try:
                    return self.basic_provider.check_market_status()
                except Exception as fallback_error:
                    self.logger.log_error(f"Market status fallback also failed: {str(fallback_error)}")

            return {"status": "error", "message": str(e)}

    def check_market_conditions(self, symbol: str, trading_sessions: List[str] = None,
                               avoid_high_spread: bool = True) -> Dict:
        """Check market conditions using current provider with fallback."""
        try:
            result = self.current_provider.check_market_conditions(symbol, trading_sessions, avoid_high_spread)

            # Check if result indicates an error
            if result.get("status") == "error":
                self.logger.log_warning(f"Primary provider market conditions failed for {symbol}, trying REST API fallback")

                # Try REST API fallback
                if self.basic_provider and self.current_provider != self.basic_provider:
                    try:
                        fallback_result = self.basic_provider.check_market_conditions(symbol, trading_sessions, avoid_high_spread)
                        if fallback_result.get("status") != "error":
                            fallback_result["source"] = f"{fallback_result.get('source', 'rest_api')}_conditions_fallback"
                            self.logger.log_info(f"✅ Got market conditions from REST API fallback for {symbol}")
                            return fallback_result
                    except Exception as e:
                        self.logger.log_error(f"Market conditions fallback failed: {str(e)}")

            return result

        except Exception as e:
            self.logger.log_error(f"Error checking market conditions for {symbol}: {str(e)}")

            # Try fallback provider
            if self.basic_provider and self.current_provider != self.basic_provider:
                try:
                    return self.basic_provider.check_market_conditions(symbol, trading_sessions, avoid_high_spread)
                except Exception as fallback_error:
                    self.logger.log_error(f"Market conditions fallback also failed: {str(fallback_error)}")

            return {"status": "error", "message": str(e)}

    def force_fresh_data(self, symbol: str, timespan: str = "minute",
                        multiplier: int = 1, count: int = 1000) -> Dict:
        """
        Force fetch fresh data from REST API, bypassing WebSocket cache.

        Args:
            symbol (str): Trading symbol
            timespan (str): Time interval
            multiplier (int): Number of timespans to multiply by
            count (int): Number of candles to fetch

        Returns:
            Dict: Fresh candle data from REST API
        """
        self.logger.log_info(f"🔄 Force fetching fresh data for {symbol} from REST API")

        try:
            if self.basic_provider:
                result = self.basic_provider.get_candles(symbol, timespan, multiplier, count)
                if result.get("status") == "success":
                    result["source"] = "rest_api_forced_fresh"
                    self.logger.log_info("✅ Fresh data obtained from forced REST API call")
                return result
            else:
                return {
                    "status": "error",
                    "message": "No REST API provider available for fresh data"
                }
        except Exception as e:
            self.logger.log_error(f"Error forcing fresh data: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to fetch fresh data: {str(e)}"
            }

    def get_performance_metrics(self) -> Dict:
        """Get performance metrics for all providers."""
        metrics = {
            'hybrid_wrapper': self.provider_performance.copy(),
            'current_provider': self._get_provider_name(self.current_provider),
            'consecutive_errors': self.consecutive_errors.copy()
        }
        
        # Add individual provider metrics
        if self.enhanced_provider:
            try:
                metrics['enhanced_provider'] = self.enhanced_provider.get_performance_metrics()
            except:
                pass
                
        if self.basic_provider:
            try:
                metrics['basic_provider'] = self.basic_provider.get_performance_metrics()
            except:
                pass
        
        return metrics

    def cleanup(self):
        """Clean up resources for all providers."""
        try:
            if self.enhanced_provider and hasattr(self.enhanced_provider, 'cleanup'):
                self.enhanced_provider.cleanup()
                self.logger.log_info("Enhanced provider cleaned up")

            if self.basic_provider and hasattr(self.basic_provider, 'cleanup'):
                self.basic_provider.cleanup()
                self.logger.log_info("Basic provider cleaned up")

        except Exception as e:
            self.logger.log_error(f"Error during cleanup: {str(e)}")

        self.logger.log_info("Hybrid market data wrapper cleaned up")

    def cleanup(self):
        """Clean up all providers."""
        try:
            if self.enhanced_provider:
                self.enhanced_provider.cleanup()
            if self.basic_provider and hasattr(self.basic_provider, 'cleanup'):
                self.basic_provider.cleanup()
            self.logger.log_info("Hybrid market data wrapper cleaned up")
        except Exception as e:
            self.logger.log_error(f"Error during cleanup: {str(e)}")
