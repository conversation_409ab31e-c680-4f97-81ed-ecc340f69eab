#!/usr/bin/env python3
"""
Test script to verify trade closure detection fixes.
This script tests the improvements made to prevent duplicate processing
and handle 404 errors when trades are already closed.
"""

import sys
import os
import time
from datetime import datetime, timezone
from unittest.mock import Mock, patch

# Add the trade-bot-service directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.fast_update_loop import FastUpdateLoop
from core.trading_engine import TradingEngine
from execution.oanda_client import OandaClient
from firebase.firebase_client import FirebaseClient
from core.trading_engine_types import TradeHistoryRow, TradeType, TradeStatus
import requests


def test_duplicate_processing_prevention():
    """Test that duplicate trade closure processing is prevented."""
    print("Testing duplicate processing prevention...")
    
    # Create mock objects
    mock_trading_engine = Mock()
    mock_oanda_client = Mock()
    mock_firebase_client = Mock()
    
    # Create fast update loop
    fast_loop = FastUpdateLoop(
        trading_engine=mock_trading_engine,
        oanda_client=mock_oanda_client,
        firebase_client=mock_firebase_client,
        update_interval=1
    )
    
    # Test trade ID
    test_trade_id = "test_trade_123"
    
    # Initially, trade should not be processed
    assert not fast_loop.was_trade_processed_by_fast_loop(test_trade_id)
    
    # Mark trade as processed
    fast_loop.processed_closed_trades.add(test_trade_id)
    
    # Now it should be marked as processed
    assert fast_loop.was_trade_processed_by_fast_loop(test_trade_id)
    
    print("✅ Duplicate processing prevention test passed")


def test_404_error_handling():
    """Test that 404 errors are handled gracefully when closing trades."""
    print("Testing 404 error handling...")
    
    # Create a mock OANDA client
    mock_oanda_client = OandaClient("test_account", "test_key", "test_url")
    
    # Mock the _make_request method to raise 404 error
    def mock_make_request(method, endpoint):
        if "close" in endpoint:
            # Simulate 404 error
            error_response = Mock()
            error_response.status_code = 404
            error = requests.exceptions.HTTPError()
            error.response = error_response
            raise error
        return {}
    
    # Mock get_open_trades to return empty list (trade already closed)
    mock_oanda_client.get_open_trades = Mock(return_value=[])
    
    # Test closing a non-existent trade
    result = mock_oanda_client.close_trade("non_existent_trade")
    
    # Should return None without raising an exception
    assert result is None
    
    print("✅ 404 error handling test passed")


def test_processed_trades_cleanup():
    """Test that the processed trades set is cleaned up when it gets too large."""
    print("Testing processed trades cleanup...")
    
    # Create mock objects
    mock_trading_engine = Mock()
    mock_oanda_client = Mock()
    mock_firebase_client = Mock()
    
    # Create fast update loop
    fast_loop = FastUpdateLoop(
        trading_engine=mock_trading_engine,
        oanda_client=mock_oanda_client,
        firebase_client=mock_firebase_client,
        update_interval=1
    )
    
    # Add more than 1000 trades to trigger cleanup
    for i in range(1100):
        fast_loop.processed_closed_trades.add(f"trade_{i}")
    
    # Simulate the cleanup logic
    if len(fast_loop.processed_closed_trades) > 1000:
        fast_loop.processed_closed_trades = set(list(fast_loop.processed_closed_trades)[-500:])
    
    # Should be cleaned up to 500 entries
    assert len(fast_loop.processed_closed_trades) == 500
    
    print("✅ Processed trades cleanup test passed")


def test_trade_already_closed_detection():
    """Test detection of trades that are already closed."""
    print("Testing trade already closed detection...")
    
    # Create mock OANDA client
    mock_oanda_client = Mock()
    
    # Mock get_open_trades to return empty list (no open trades)
    mock_oanda_client.get_open_trades = Mock(return_value=[])
    
    # Create a real OandaClient instance for testing
    oanda_client = OandaClient("test_account", "test_key", "test_url")
    oanda_client.get_open_trades = mock_oanda_client.get_open_trades
    
    # Test closing a trade when no trades are open
    with patch.object(oanda_client, '_make_request') as mock_request:
        result = oanda_client.close_trade("already_closed_trade")
        
        # Should return None without making the close request
        assert result is None
        mock_request.assert_not_called()
    
    print("✅ Trade already closed detection test passed")


def test_fast_loop_integration():
    """Test integration between fast loop and main trading engine."""
    print("Testing fast loop integration...")
    
    # Create mock objects
    mock_oanda_client = Mock()
    mock_firebase_client = Mock()
    
    # Create a minimal trading engine
    trading_engine = Mock()
    trading_engine.daily_loss_limit = 5.0
    trading_engine.total_profit_target = 10.0
    trading_engine.total_loss_limit = 10.0
    trading_engine.total_profit = 0.0
    trading_engine.total_loss = 0.0
    trading_engine.daily_loss = 0.0
    
    # Create fast update loop
    fast_loop = FastUpdateLoop(
        trading_engine=trading_engine,
        oanda_client=mock_oanda_client,
        firebase_client=mock_firebase_client,
        update_interval=1
    )
    
    # Test that trading engine can check if trade was processed by fast loop
    test_trade_id = "integration_test_trade"
    
    # Initially not processed
    assert not fast_loop.was_trade_processed_by_fast_loop(test_trade_id)
    
    # Mark as processed
    fast_loop.processed_closed_trades.add(test_trade_id)
    
    # Now should be processed
    assert fast_loop.was_trade_processed_by_fast_loop(test_trade_id)
    
    print("✅ Fast loop integration test passed")


def run_all_tests():
    """Run all tests."""
    print("Running trade closure fixes tests...\n")
    
    try:
        test_duplicate_processing_prevention()
        test_404_error_handling()
        test_processed_trades_cleanup()
        test_trade_already_closed_detection()
        test_fast_loop_integration()
        
        print("\n🎉 All tests passed! Trade closure fixes are working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
