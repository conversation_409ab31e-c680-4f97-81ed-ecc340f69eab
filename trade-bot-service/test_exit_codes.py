#!/usr/bin/env python3
"""
Test script to verify exit code behavior in Kubernetes.
This script simulates different exit scenarios to test pod restart behavior.
"""

import sys
import time
import os
from main import ExitCodes

def test_exit_code(exit_code: int, description: str):
    """Test a specific exit code."""
    print(f"🧪 Testing exit code {exit_code}: {description}")
    print(f"⏰ Waiting 5 seconds before exit...")
    time.sleep(5)
    print(f"🚪 Exiting with code {exit_code}")
    sys.exit(exit_code)

def main():
    """Main test function."""
    test_type = os.getenv("TEST_TYPE", "help")
    
    print("🧪 Trade-Bot Exit Code Test Script")
    print("=" * 50)
    
    if test_type == "help":
        print("Available test types (set TEST_TYPE environment variable):")
        print(f"  success           - Exit code {ExitCodes.SUCCESS} (should NOT restart)")
        print(f"  system_error      - Exit code {ExitCodes.SYSTEM_ERROR} (should restart)")
        print(f"  business_logic    - Exit code {ExitCodes.BUSINESS_LOGIC_SHUTDOWN} (should NOT restart)")
        print(f"  config_error      - Exit code {ExitCodes.CONFIGURATION_ERROR} (should NOT restart)")
        print(f"  auth_error        - Exit code {ExitCodes.AUTHENTICATION_ERROR} (should NOT restart)")
        print(f"  margin_error      - Exit code {ExitCodes.INSUFFICIENT_MARGIN} (should NOT restart)")
        print(f"  invalid_strategy  - Exit code {ExitCodes.INVALID_STRATEGY} (should NOT restart)")
        print(f"  market_closed     - Exit code {ExitCodes.MARKET_CLOSED} (should NOT restart)")
        print()
        print("Usage:")
        print("  kubectl run exit-test --image=trade-bot:latest --env='TEST_TYPE=margin_error' --rm -it")
        sys.exit(0)
    
    elif test_type == "success":
        test_exit_code(ExitCodes.SUCCESS, "Normal successful completion")
    
    elif test_type == "system_error":
        test_exit_code(ExitCodes.SYSTEM_ERROR, "System error (should trigger restart)")
    
    elif test_type == "business_logic":
        test_exit_code(ExitCodes.BUSINESS_LOGIC_SHUTDOWN, "Business logic shutdown")
    
    elif test_type == "config_error":
        test_exit_code(ExitCodes.CONFIGURATION_ERROR, "Configuration error")
    
    elif test_type == "auth_error":
        test_exit_code(ExitCodes.AUTHENTICATION_ERROR, "Authentication error")
    
    elif test_type == "margin_error":
        test_exit_code(ExitCodes.INSUFFICIENT_MARGIN, "Insufficient margin error")
    
    elif test_type == "invalid_strategy":
        test_exit_code(ExitCodes.INVALID_STRATEGY, "Invalid strategy error")
    
    elif test_type == "market_closed":
        test_exit_code(ExitCodes.MARKET_CLOSED, "Market closed shutdown")
    
    else:
        print(f"❌ Unknown test type: {test_type}")
        print("Run with TEST_TYPE=help for available options")
        sys.exit(1)

if __name__ == "__main__":
    main()
