#!/bin/bash

echo "🚀 Setting up Trade Bot for Production WebSocket Service Testing..."

# Check if production environment file exists
if [ ! -f .env.production ]; then
    echo "❌ Error: .env.production file not found."
    echo "Please create .env.production with your configuration."
    exit 1
fi

# Load production environment variables
set -a
source .env.production
set +a

echo "✅ Loaded production environment configuration"

# Check required environment variables
required_vars=("POLYGON_API_KEY" "USER_ID" "STRATEGY_ID")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    echo "❌ Error: Missing required environment variables:"
    printf '   - %s\n' "${missing_vars[@]}"
    echo "Please update .env.production with these values."
    exit 1
fi

# Check if Firebase emulator is running
if ! lsof -i :8082 > /dev/null 2>&1; then
    echo "❌ Error: Firebase emulator not running on port 8082."
    echo "Please start it first with: firebase emulators:start"
    exit 1
fi

echo "✅ Firebase emulator is running"

# Check if config directory exists
if [ ! -d "config" ]; then
    echo "❌ Error: config directory not found."
    echo "Please ensure it exists with engine_config.json."
    exit 1
fi

# Check if required config files exist
if [ ! -f "config/engine_config.json" ]; then
    echo "❌ Error: Required configuration file missing."
    echo "Please ensure engine_config.json exists."
    exit 1
fi

echo "✅ Configuration files found"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
else
    source venv/bin/activate
fi

echo "✅ Virtual environment activated"

# Add current directory to PYTHONPATH
export PYTHONPATH=$PYTHONPATH:$(pwd)

echo "🔧 Environment Configuration:"
echo "   - Market Data Provider: $MARKET_DATA_PROVIDER"
echo "   - Google Cloud Project: $GOOGLE_CLOUD_PROJECT"
echo "   - Firebase Emulator: $USE_FIREBASE_EMULATOR"
echo "   - OANDA Practice Mode: $OANDA_PRACTICE_MODE"
echo "   - User ID: $USER_ID"
echo "   - Strategy ID: $STRATEGY_ID"

echo ""
echo "🎯 This will test the complete flow:"
echo "   1. Trade-bot sends subscription to Polygon WebSocket Service"
echo "   2. Polygon WebSocket Service fetches data from Polygon.io"
echo "   3. Real-time data flows to trade-bot via Pub/Sub"
echo "   4. DDS also receives the same data (passive mode)"
echo "   5. Frontend can connect to DDS for real-time charts"
echo ""

read -p "🚀 Ready to start trade-bot with production WebSocket connection? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🤖 Starting trade-bot..."
    python main.py
else
    echo "👋 Test cancelled by user"
fi
