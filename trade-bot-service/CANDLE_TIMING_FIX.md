# Candle Timing Logic Fix

This document describes the fix for the candle fetching logic that was returning incorrect timestamps due to period calculation errors.

## 🐛 **Issue Identified**

The polling system was fetching candles with incorrect timestamps:

```
INFO: 🔄 Fetching latest 5m candle for EURUSD from Polygon
INFO: ⚠️ Candle already exists in buffer for EURUSD 5m
INFO:    📊 Fetched: 2025-06-23T00:00:00+00:00  ← WRONG (midnight)
INFO:    📊 Last in buffer: 2025-06-23T22:55:00+00:00  ← CORRECT (22:55)
```

**Root Cause**: The period calculation logic was incorrectly determining which candle to fetch from Polygon API.

## 🕐 **Timeline Analysis**

### **Scenario**: 5m candle polling at 16:00:03 UTC

**Expected Behavior**:
- 15:55:00 - 16:00:00 candle just completed
- Should fetch the 16:00:00 candle (which represents 15:55-16:00 period)

**Actual Behavior**:
- Was fetching midnight candle (00:00:00) instead
- Period calculation was wrong

## 🔧 **Root Cause Analysis**

### **Original Logic (Broken)**
```python
# Get the end time of the last completed period
current_minute = now.minute  # 0 (at 16:00:03)
period_start_minute = (0 // 5) * 5  # 0
period_end = now.replace(minute=0, second=0, microsecond=0)  # 16:00:00

# Get the start of the period  
period_start = period_end - timedelta(minutes=5)  # 15:55:00

# This was looking for 15:55:00 candle, but Polygon labels it as 16:00:00
```

### **Fixed Logic**
```python
# Get the start time of the CURRENT period (which just completed)
current_minute = now.minute  # 0 (at 16:00:03)
period_start_minute = (0 // 5) * 5  # 0
current_period_start = now.replace(minute=0, second=0, microsecond=0)  # 16:00:00

# The period we want is the one that just completed (previous period)
target_period_start = current_period_start - timedelta(minutes=5)  # 15:55:00
target_period_end = current_period_start  # 16:00:00

# Look for candle with timestamp 15:55:00 (which represents 15:55-16:00 period)
```

## 🎯 **Key Fixes Implemented**

### **1. Correct Period Calculation**
```python
# Before: Calculated period end, then subtracted
period_end = now.replace(minute=period_start_minute, second=0, microsecond=0)
period_start = period_end - timedelta(minutes=timeframe_minutes)

# After: Calculate current period start, then get previous period
current_period_start = now.replace(minute=period_start_minute, second=0, microsecond=0)
target_period_start = current_period_start - timedelta(minutes=timeframe_minutes)
target_period_end = current_period_start
```

### **2. Enhanced Debugging**
```python
self.logger.log_info(f"🕐 Current time: {now.isoformat()}")
self.logger.log_info(f"⏱️ Timeframe: {timeframe} ({timeframe_minutes} minutes)")
self.logger.log_info(f"📅 Target period: {target_period_start.isoformat()} to {target_period_end.isoformat()}")
```

### **3. Exact Candle Matching**
```python
# Find the candle that matches our target period
target_candle = None
target_timestamp = int(target_period_start.timestamp())

for candle_data in results:
    candle_timestamp = int(candle_data["t"] / 1000)
    if candle_timestamp == target_timestamp:
        target_candle = candle_data
        break
```

### **4. Fallback Logic**
```python
if not target_candle:
    # If exact match not found, get the latest candle and check if it's newer
    latest_candle_data = results[-1]
    latest_timestamp = int(latest_candle_data["t"] / 1000)
    latest_datetime = datetime.fromtimestamp(latest_timestamp, tz=timezone.utc)
    
    # Use latest if it's newer than what we have
    if latest_datetime > self.last_candle_time[subscription_key]:
        target_candle = latest_candle_data
```

## 📊 **Expected Behavior Now**

### **At 16:00:03 UTC (5m candle completion)**
```
🕐 Current time: 2025-06-23T16:00:03+00:00
⏱️ Timeframe: 5m (5 minutes)
📅 Target period: 2025-06-23T15:55:00+00:00 to 2025-06-23T16:00:00+00:00
🔄 Fetching latest 5m candle for EURUSD from Polygon
📊 Received 5 candles from Polygon
🎯 Selected candle: 2025-06-23T15:55:00+00:00
📈 Added new 5m candle for EURUSD: buffer size: 1000 -> 1000
   🕐 Time: 2025-06-23T15:55:00+00:00
   💰 OHLC: O:1.1590, H:1.1592, L:1.1588, C:1.1591
```

### **At 16:05:03 UTC (Next 5m candle completion)**
```
🕐 Current time: 2025-06-23T16:05:03+00:00
⏱️ Timeframe: 5m (5 minutes)
📅 Target period: 2025-06-23T16:00:00+00:00 to 2025-06-23T16:05:00+00:00
🔄 Fetching latest 5m candle for EURUSD from Polygon
📊 Received 5 candles from Polygon
🎯 Selected candle: 2025-06-23T16:00:00+00:00
📈 Added new 5m candle for EURUSD: buffer size: 1000 -> 1000
   🕐 Time: 2025-06-23T16:00:00+00:00
   💰 OHLC: O:1.1591, H:1.1595, L:1.1589, C:1.1593
```

## 🎯 **Polygon API Candle Labeling**

### **Understanding Polygon Timestamps**
- **5m candle for 15:55-16:00 period** → Labeled as `15:55:00`
- **5m candle for 16:00-16:05 period** → Labeled as `16:00:00`
- **Timestamp represents the START of the period**

### **Our Polling Logic**
- **At 16:00:03**: Look for candle with timestamp `15:55:00`
- **At 16:05:03**: Look for candle with timestamp `16:00:00`
- **At 16:10:03**: Look for candle with timestamp `16:05:00`

## 🚀 **Benefits of the Fix**

1. **Correct Timestamps**: Fetches the right candle for each period
2. **Better Debugging**: Clear logs showing period calculations
3. **Exact Matching**: Finds the specific candle we need
4. **Fallback Logic**: Handles edge cases gracefully
5. **Reliable Polling**: Consistent real-time updates

The fix ensures that the polling system correctly identifies and fetches the newly completed candle from Polygon API, providing accurate real-time data for trading decisions.
