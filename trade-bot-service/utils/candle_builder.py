"""
Candle builder for trade-bot service.
Builds higher timeframe candles from 1m candles.
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, Any
from utils.logger import Logger


class CandleBuilder:
    """Build higher timeframe candles from 1m candles."""

    def __init__(self, symbol: str, timeframe: str):
        self.symbol = symbol
        self.timeframe = timeframe
        self.current_candle = None
        self.timeframe_minutes = self._get_timeframe_minutes(timeframe)
        self.logger = Logger("CandleBuilder")

        # Completeness validation
        self.require_complete_candles = False  # Allow incomplete candles for real-time trading

        # Duplicate detection
        self.last_processed_candle_time = None  # Track last processed candle timestamp
        
    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe string to minutes."""
        if timeframe == '1m':
            return 1
        elif timeframe == '5m':
            return 5
        elif timeframe == '15m':
            return 15
        elif timeframe == '30m':
            return 30
        elif timeframe == '1h':
            return 60
        elif timeframe == '4h':
            return 240
        elif timeframe == '1d':
            return 1440
        else:
            return 1  # Default to 1m
    
    def add_1m_candle(self, candle_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Add a 1m candle and return a completed higher timeframe candle if ready.
        
        Args:
            candle_data: 1m candle data
            
        Returns:
            Completed higher timeframe candle or None
        """
        try:
            # Extract candle timestamp (in seconds)
            timestamp = candle_data.get('time', candle_data.get('timestamp', 0))
            if isinstance(timestamp, (int, float)):
                candle_time = datetime.fromtimestamp(timestamp, tz=timezone.utc)
            else:
                return None

            # Check for duplicate candles
            if self.last_processed_candle_time == timestamp:
                self.logger.log_warning(f"🔄 Skipping duplicate 1m candle: {candle_time} (timestamp: {timestamp})")
                return None

            # Check for out-of-order candles
            if self.last_processed_candle_time and timestamp < self.last_processed_candle_time:
                self.logger.log_warning(f"⚠️ Out-of-order candle detected: {candle_time} (timestamp: {timestamp}) is older than last processed: {self.last_processed_candle_time}")

            self.last_processed_candle_time = timestamp
            self.logger.log_info(f"✅ Processing new 1m candle: {candle_time} (timestamp: {timestamp})")

            # Calculate which period this 1m candle belongs to
            candle_period_start = self._get_period_start(candle_time)
            self.logger.log_info(f"📊 Processing candle: time={candle_time}, timestamp={timestamp}, period={candle_period_start}")

            # Debug: Show the relationship between candle time and period
            self.logger.log_info(f"🔍 DEBUG: Candle timestamp {timestamp} -> datetime {candle_time} -> period {candle_period_start}")
            self.logger.log_info(f"🔍 DEBUG: For 1m timeframe, candle time should equal period start: {candle_time == candle_period_start}")

            # If this is a new period, complete the previous candle and start a new one
            if self.current_candle and self.current_candle['period_start'] != candle_period_start:
                # This 1m candle belongs to a new period, so complete the previous period
                prev_period = self.current_candle['period_start']
                prev_candle_count = self.current_candle.get('candle_count', 0)
                self.logger.log_info(f"🔄 Period boundary detected: completing {prev_period} (had {prev_candle_count} candles) -> starting {candle_period_start}")
                self.logger.log_info(f"📊 Candle time: {candle_time}, belongs to period: {candle_period_start}")

                completed_candle = self._finalize_candle()
                self._start_new_candle_with_validation(candle_data, candle_period_start)
                return completed_candle
            
            # If no current candle, start a new one
            if not self.current_candle:
                self.logger.log_info(f"🆕 No current candle - starting new {self.timeframe} period")
                self._start_new_candle_with_validation(candle_data, candle_period_start)

                # For 1m timeframe, complete immediately since we only need 1 candle
                if self.timeframe_minutes == 1:
                    self.logger.log_info(f"⚡ 1m timeframe - completing candle immediately")
                    completed_candle = self._finalize_candle()
                    self.current_candle = None  # Clear for next period
                    return completed_candle

                return None



            # Validate that this candle belongs to the current period
            current_period = self.current_candle['period_start']
            period_end = current_period + timedelta(minutes=self.timeframe_minutes)

            if not (current_period <= candle_time < period_end):
                self.logger.log_error(f"❌ Candle {candle_time} does not belong to current period {current_period} - {period_end}")
                return None

            # Update the current candle with this 1m candle
            self.logger.log_info(f"🔄 Updating existing {self.timeframe} candle for period {current_period}")
            old_count = self.current_candle.get('candle_count', 0)
            self._update_current_candle(candle_data)
            new_count = self.current_candle.get('candle_count', 0)

            self.logger.log_info(f"📈 Updated {self.timeframe} candle: {old_count} -> {new_count}/{self.timeframe_minutes} candles")
            self.logger.log_info(f"📊 Period: {current_period} - {period_end}, Current candle: {candle_time}")

            # Check if we have too many candles
            if new_count > self.timeframe_minutes:
                self.logger.log_warning(f"⚠️ Period has {new_count} candles, expected {self.timeframe_minutes} - likely duplicates!")

            # Check if period is complete (we have all required candles)
            if new_count >= self.timeframe_minutes:
                self.logger.log_info(f"✅ Period complete with {new_count} candles - finalizing immediately")
                completed_candle = self._finalize_candle()
                self.current_candle = None  # Clear for next period
                self.logger.log_info(f"🚀 Next {self.timeframe} period ready for seamless continuation")
                return completed_candle

            return None
            
        except Exception as e:
            self.logger.log_error(f"Error in CandleBuilder.add_1m_candle: {e}")
            return None
    
    def _get_period_start(self, candle_time: datetime) -> datetime:
        """Get the start of the timeframe period for the given time."""
        if self.timeframe_minutes == 1:
            return candle_time.replace(second=0, microsecond=0)
        elif self.timeframe_minutes == 5:
            minute = (candle_time.minute // 5) * 5
            return candle_time.replace(minute=minute, second=0, microsecond=0)
        elif self.timeframe_minutes == 15:
            minute = (candle_time.minute // 15) * 15
            return candle_time.replace(minute=minute, second=0, microsecond=0)
        elif self.timeframe_minutes == 30:
            minute = (candle_time.minute // 30) * 30
            return candle_time.replace(minute=minute, second=0, microsecond=0)
        elif self.timeframe_minutes == 60:
            return candle_time.replace(minute=0, second=0, microsecond=0)
        elif self.timeframe_minutes == 240:
            hour = (candle_time.hour // 4) * 4
            return candle_time.replace(hour=hour, minute=0, second=0, microsecond=0)
        elif self.timeframe_minutes == 1440:
            return candle_time.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            return candle_time.replace(second=0, microsecond=0)

    def _start_new_candle_with_validation(self, candle_data: Dict[str, Any], period_start: datetime):
        """Start a new candle (simplified without waiting logic)."""
        try:
            # Start the new candle immediately - no more waiting logic
            self._start_new_candle(candle_data, period_start)

        except Exception as e:
            self.logger.log_error(f"Error in validation: {e}")
            self._start_new_candle(candle_data, period_start)







    def _start_new_candle(self, candle_data: Dict[str, Any], period_start: datetime):
        """Start a new candle for the current period."""
        self.current_candle = {
            'period_start': period_start,
            'open': candle_data.get('open', 0),
            'high': candle_data.get('high', 0),
            'low': candle_data.get('low', 0),
            'close': candle_data.get('close', 0),
            'volume': candle_data.get('volume', 0),
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'candle_count': 1  # Track how many 1m candles we've received
        }

        # No stored candles to process - simplified logic
    
    def _update_current_candle(self, candle_data: Dict[str, Any]):
        """Update the current candle with new 1m candle data."""
        if not self.current_candle:
            return
            
        # Update high, low, close, and volume
        self.current_candle['high'] = max(self.current_candle['high'], candle_data.get('high', 0))
        self.current_candle['low'] = min(self.current_candle['low'], candle_data.get('low', 0))
        self.current_candle['close'] = candle_data.get('close', 0)
        self.current_candle['volume'] += candle_data.get('volume', 0)
        self.current_candle['candle_count'] = self.current_candle.get('candle_count', 0) + 1
    
    def _finalize_candle(self) -> Optional[Dict[str, Any]]:
        """Finalize and return the current candle."""
        if not self.current_candle:
            return None
            
        # Validate candle completeness
        expected_candles = self.timeframe_minutes
        actual_candles = self.current_candle.get('candle_count', 0)
        completeness = actual_candles / expected_candles if expected_candles > 0 else 0
        
        if actual_candles < expected_candles:
            self.logger.log_warning(f"⚠️ Incomplete {self.timeframe} candle: {actual_candles}/{expected_candles} 1m candles ({completeness:.1%})")

            if self.require_complete_candles:
                self.logger.log_error(f"❌ Rejecting incomplete candle for trading accuracy")
                return None
            else:
                self.logger.log_info(f"✅ Allowing incomplete candle for real-time trading")
        elif actual_candles > expected_candles:
            self.logger.log_warning(f"⚠️ Too many candles in {self.timeframe} period: {actual_candles}/{expected_candles} 1m candles - possible duplicates!")
            self.logger.log_info(f"✅ Using {self.timeframe} candle despite extra candles")
        else:
            self.logger.log_info(f"✅ Perfect {self.timeframe} candle: {actual_candles}/{expected_candles} 1m candles")
            
        # Convert period_start to timestamp
        timestamp = int(self.current_candle['period_start'].timestamp())

        # Debug: Show the finalized candle timestamp
        self.logger.log_info(f"🔍 DEBUG: Finalizing candle - period_start: {self.current_candle['period_start']} -> timestamp: {timestamp}")

        finalized_candle = {
            'time': timestamp,
            'timestamp': timestamp,
            'open': self.current_candle['open'],
            'high': self.current_candle['high'],
            'low': self.current_candle['low'],
            'close': self.current_candle['close'],
            'volume': self.current_candle['volume'],
            'symbol': self.current_candle['symbol'],
            'timeframe': self.current_candle['timeframe'],
            'is_complete': actual_candles >= expected_candles,
            'completeness': completeness,
            'candle_count': actual_candles
        }
        
        return finalized_candle
    
    def force_finalize_current_candle(self) -> Optional[Dict[str, Any]]:
        """Force finalize the current candle (for shutdown/testing)."""
        return self._finalize_candle()
