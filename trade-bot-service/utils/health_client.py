"""
Health check client for the trade bot.
Sends health data to the strategy controller.
"""

import os
import socket
import threading
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import requests

from utils.logger import Logger

class HealthClient:
    """Client for sending health data to the strategy controller."""

    def __init__(self, strategy_id: str, user_id: str, strategy_controller_url: str = None):
        """
        Initialize the health client.

        Args:
            strategy_id: Strategy ID
            user_id: User ID
            strategy_controller_url: URL of the strategy controller
        """
        self.strategy_id = strategy_id
        self.user_id = user_id

        self.logger = Logger("HealthClient")

        # Try to get the strategy controller URL from environment variables
        # Default to production URL, fallback to localhost for local development
        default_url = "https://control-strategy-ihjc6tjxia-uc.a.run.app"
        self.strategy_controller_url = strategy_controller_url or os.getenv("STRATEGY_CONTROLLER_URL", default_url)

        # If the URL contains 'strategy-controller' hostname and we're running locally,
        # replace it with localhost to avoid hostname resolution issues
        if "strategy-controller" in self.strategy_controller_url and not os.getenv("KUBERNETES_SERVICE_HOST"):
            self.strategy_controller_url = self.strategy_controller_url.replace("strategy-controller", "localhost")
            self.logger.log_info(f"Replaced strategy-controller with localhost in URL: {self.strategy_controller_url}")
        self.start_time = datetime.now(timezone.utc)
        self.dependencies = {}
        self.status = "healthy"
        self.error = None
        self.version = os.getenv("VERSION", "dev")
        self.pod_name = os.getenv("HOSTNAME", socket.gethostname())

        # Handle potential hostname resolution errors
        try:
            self.pod_ip = socket.gethostbyname(socket.gethostname())
        except Exception as e:
            self.logger.log_warning(f"Could not resolve hostname: {str(e)}")
            self.pod_ip = "127.0.0.1"  # Default to localhost

        self.running = True
        self.thread = None
        self.consecutive_failures = 0
        self.max_consecutive_failures = 5  # After this many failures, reduce reporting frequency

    def start(self, interval: int = 60):
        """
        Start sending health data to the strategy controller.

        Args:
            interval: Interval in seconds between health checks
        """
        def run():
            base_interval = interval
            current_interval = base_interval

            while self.running:
                try:
                    self.send_health_data()
                    # Reset consecutive failures and interval on success
                    self.consecutive_failures = 0
                    current_interval = base_interval
                except Exception as e:
                    self.consecutive_failures += 1
                    self.logger.log_error(f"Error sending health data: {str(e)}")

                    # Exponential backoff for repeated failures
                    if self.consecutive_failures > self.max_consecutive_failures:
                        # Increase interval up to a maximum of 5 minutes
                        current_interval = min(base_interval * 2, 300)
                        self.logger.log_warning(
                            f"Too many consecutive failures ({self.consecutive_failures}), "
                            f"reducing reporting frequency to {current_interval}s"
                        )

                # Sleep for the current interval
                time.sleep(current_interval)

        self.thread = threading.Thread(target=run, daemon=True)
        self.thread.start()
        self.logger.log_info(f"Health client started with interval {interval}s")

    def stop(self):
        """Stop sending health data."""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1)
        self.logger.log_info("Health client stopped")

    def update_dependency(self, name: str, status: str, message: str = ""):
        """
        Update the status of a dependency.

        Args:
            name: Name of the dependency
            status: Status of the dependency (healthy, unhealthy)
            message: Optional message
        """
        self.dependencies[name] = {
            "status": status,
            "message": message
        }

    def update_status(self, status: str, error: Optional[str] = None):
        """
        Update the status of the trade bot.

        Args:
            status: Status of the trade bot (healthy, unhealthy)
            error: Optional error message
        """
        self.status = status
        self.error = error

    def send_health_data(self):
        """Send health data to the strategy controller."""
        # Calculate uptime
        uptime_seconds = (datetime.now(timezone.utc) - self.start_time).total_seconds()

        # Prepare health data
        health_data = {
            "status": self.status,
            "version": self.version,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime": uptime_seconds,
            "service": "trade-bot",
            "pod_name": self.pod_name,
            "pod_ip": self.pod_ip,
            "strategy_id": self.strategy_id,
            "user_id": self.user_id,
            "dependencies": self.dependencies,
        }

        if self.error:
            health_data["error"] = self.error

        # Send health data to the strategy controller
        try:
            # Use a shorter timeout for the first attempt
            # Use the special trade-bot-health endpoint that doesn't require authentication
            response = requests.post(
                f"{self.strategy_controller_url}/monitoring/trade-bot-health",
                json=health_data,
                timeout=2
            )

            if response.status_code == 200:
                self.logger.log_debug("Health data sent successfully")
            elif response.status_code == 401:
                self.logger.log_warning("Authentication failed when sending health data")
                raise Exception("Authentication failed")
            else:
                self.logger.log_warning(f"Failed to send health data: {response.status_code} {response.text}")
                raise Exception(f"Failed with status code: {response.status_code}")
        except requests.exceptions.ConnectTimeout:
            self.logger.log_warning(f"Connection timeout to {self.strategy_controller_url}")
            raise Exception(f"Connection timeout to {self.strategy_controller_url}")
        except requests.exceptions.ConnectionError as e:
            # Check if it's a DNS resolution error
            if "NameResolutionError" in str(e):
                self.logger.log_warning(f"Could not resolve hostname in URL: {self.strategy_controller_url}")
                # Try to fix the URL if it's a common issue
                if "strategy-controller" in self.strategy_controller_url:
                    old_url = self.strategy_controller_url
                    self.strategy_controller_url = self.strategy_controller_url.replace("strategy-controller", "localhost")
                    self.logger.log_info(f"Updated URL from {old_url} to {self.strategy_controller_url}")
            raise Exception(f"Connection error: {str(e)}")
        except Exception as e:
            self.logger.log_error(f"Error sending health data: {str(e)}")
            raise

    def send_alert(self, message: str, status: str = "unhealthy"):
        """
        Send an alert to the strategy controller.

        Args:
            message: Alert message
            status: Alert status (unhealthy, warning)
        """
        # Prepare alert data
        alert_data = {
            "service": "trade-bot",
            "status": status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": message,
            "strategy_id": self.strategy_id,
            "user_id": self.user_id
        }

        # Send alert to the strategy controller
        try:
            # Use the special trade-bot-alerts endpoint that doesn't require authentication
            response = requests.post(
                f"{self.strategy_controller_url}/monitoring/trade-bot-alerts",
                json=alert_data,
                timeout=2
            )

            if response.status_code == 200:
                self.logger.log_info(f"Alert sent: {message}")
                return True
            else:
                self.logger.log_warning(f"Failed to send alert: {response.status_code} {response.text}")
                return False
        except requests.exceptions.ConnectTimeout:
            self.logger.log_warning(f"Connection timeout to {self.strategy_controller_url} when sending alert")
            return False
        except requests.exceptions.ConnectionError as e:
            # Check if it's a DNS resolution error
            if "NameResolutionError" in str(e):
                self.logger.log_warning(f"Could not resolve hostname in URL when sending alert: {self.strategy_controller_url}")
            self.logger.log_warning(f"Connection error when sending alert: {str(e)}")
            return False
        except Exception as e:
            self.logger.log_error(f"Error sending alert: {str(e)}")
            return False
