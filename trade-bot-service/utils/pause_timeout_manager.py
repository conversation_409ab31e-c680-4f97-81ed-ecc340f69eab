#!/usr/bin/env python3
"""
Pause timeout manager for trade-bots.
Automatically stops trade-bots that have been paused for more than 24 hours.
"""

import time
import threading
from datetime import datetime, timezone, timedelta
from typing import Optional
from utils.logger import Logger

class PauseTimeoutManager:
    """
    Manages pause timeout for trade-bots.
    
    When a trade-bot is paused:
    - Starts a 24-hour countdown timer
    - If not resumed within 24 hours, automatically stops the bot
    - Cleans up resources to prevent unnecessary resource usage
    """
    
    def __init__(self, trading_bot):
        self.trading_bot = trading_bot
        self.logger = Logger("PauseTimeoutManager")
        
        # Pause state tracking
        self.pause_start_time: Optional[datetime] = None
        self.timeout_thread: Optional[threading.Thread] = None
        self.timeout_hours = 24  # Maximum pause duration in hours
        self.is_timeout_active = False
        
        self.logger.log_info("🕐 Pause timeout manager initialized")
    
    def start_pause_timeout(self):
        """Start the pause timeout when bot is paused."""
        if self.is_timeout_active:
            self.logger.log_warning("⚠️ Pause timeout already active")
            return
        
        self.pause_start_time = datetime.now(timezone.utc)
        self.is_timeout_active = True
        
        # Start timeout thread
        self.timeout_thread = threading.Thread(
            target=self._timeout_worker,
            daemon=True,
            name=f"PauseTimeout-{self.trading_bot.strategy_id}"
        )
        self.timeout_thread.start()
        
        self.logger.log_info(f"⏸️ Pause timeout started - bot will auto-stop in {self.timeout_hours} hours")
        self.logger.log_info(f"🕐 Pause started at: {self.pause_start_time.isoformat()}")
        
        # Update Firebase with pause timeout info
        self._update_pause_status("paused_with_timeout")
    
    def cancel_pause_timeout(self):
        """Cancel the pause timeout when bot is resumed."""
        if not self.is_timeout_active:
            self.logger.log_info("ℹ️ No active pause timeout to cancel")
            return
        
        self.is_timeout_active = False
        
        # Calculate how long the bot was paused
        if self.pause_start_time:
            pause_duration = datetime.now(timezone.utc) - self.pause_start_time
            pause_hours = pause_duration.total_seconds() / 3600
            self.logger.log_info(f"▶️ Pause timeout cancelled after {pause_hours:.1f} hours")
        
        # Reset state
        self.pause_start_time = None
        self.timeout_thread = None
        
        # Update Firebase status
        self._update_pause_status("resumed")
        
        self.logger.log_info("✅ Pause timeout cancelled - bot can continue running")
    
    def get_remaining_time(self) -> Optional[timedelta]:
        """Get remaining time before timeout."""
        if not self.is_timeout_active or not self.pause_start_time:
            return None
        
        elapsed = datetime.now(timezone.utc) - self.pause_start_time
        timeout_duration = timedelta(hours=self.timeout_hours)
        remaining = timeout_duration - elapsed
        
        return remaining if remaining.total_seconds() > 0 else timedelta(0)
    
    def get_pause_status(self) -> dict:
        """Get current pause status information."""
        if not self.is_timeout_active:
            return {
                "is_paused": False,
                "timeout_active": False
            }
        
        remaining = self.get_remaining_time()
        
        return {
            "is_paused": True,
            "timeout_active": True,
            "pause_start_time": self.pause_start_time.isoformat() if self.pause_start_time else None,
            "timeout_hours": self.timeout_hours,
            "remaining_seconds": remaining.total_seconds() if remaining else 0,
            "remaining_hours": remaining.total_seconds() / 3600 if remaining else 0
        }
    
    def _timeout_worker(self):
        """Worker thread that handles the timeout."""
        timeout_seconds = self.timeout_hours * 3600  # Convert hours to seconds
        check_interval = 300  # Check every 5 minutes
        
        elapsed = 0
        while elapsed < timeout_seconds and self.is_timeout_active:
            time.sleep(min(check_interval, timeout_seconds - elapsed))
            elapsed += check_interval
            
            if not self.is_timeout_active:
                self.logger.log_info("🛑 Timeout worker stopped - pause was cancelled")
                return
            
            # Log progress every hour
            if elapsed % 3600 == 0:
                hours_elapsed = elapsed / 3600
                hours_remaining = self.timeout_hours - hours_elapsed
                self.logger.log_info(f"⏰ Pause timeout: {hours_elapsed:.0f}h elapsed, {hours_remaining:.0f}h remaining")
        
        # If we reach here, timeout has expired
        if self.is_timeout_active:
            self.logger.log_warning(f"⏰ PAUSE TIMEOUT EXPIRED after {self.timeout_hours} hours")
            self._handle_timeout_expiry()
    
    def _handle_timeout_expiry(self):
        """Handle when pause timeout expires."""
        try:
            self.logger.log_warning("🛑 Pause timeout expired - automatically stopping trade-bot")
            
            # Update status to indicate timeout stop
            self._update_pause_status("timeout_stopped")
            
            # Close any open positions (since this is effectively a user stop)
            self._close_positions_on_timeout()
            
            # Notify Strategy Controller to mark as stopped
            self._notify_timeout_stop()
            
            # Update Firebase with final status
            if hasattr(self.trading_bot, 'firebase_client'):
                self.trading_bot.firebase_client.update_bot_status(
                    self.trading_bot.firebase_client.BotStatus.STOPPED,
                    {
                        "message": f"Trading bot automatically stopped after being paused for {self.timeout_hours} hours",
                        "stop_reason": "pause_timeout",
                        "paused_at": self.pause_start_time.isoformat() if self.pause_start_time else None
                    }
                )
                
                self.trading_bot.firebase_client.append_user_log(
                    f"🕐 Trading bot was automatically stopped after being paused for {self.timeout_hours} hours to save resources"
                )
            
            # Set flags to trigger shutdown
            self.trading_bot.should_exit = True
            self.trading_bot.stop_reason = "pause_timeout"
            
            self.logger.log_info("💤 Trade-bot will now shut down due to pause timeout")
            
        except Exception as e:
            self.logger.log_error(f"❌ Error handling pause timeout expiry: {e}")
    
    def _close_positions_on_timeout(self):
        """Close positions when timeout expires (treat as user stop)."""
        try:
            if hasattr(self.trading_bot, 'oanda_client'):
                open_positions = self.trading_bot.oanda_client.get_open_trades()
                
                if open_positions:
                    self.logger.log_info(f"💰 Closing {len(open_positions)} positions due to pause timeout")
                    
                    for position in open_positions:
                        try:
                            trade_id = position.get('trade_id')
                            if trade_id:
                                result = self.trading_bot.oanda_client.close_trade(trade_id)
                                if result:
                                    self.logger.log_info(f"✅ Closed position {trade_id}")
                        except Exception as e:
                            self.logger.log_error(f"❌ Error closing position {trade_id}: {e}")
                else:
                    self.logger.log_info("ℹ️ No open positions to close")
        except Exception as e:
            self.logger.log_error(f"❌ Error closing positions on timeout: {e}")
    
    def _notify_timeout_stop(self):
        """Notify Strategy Controller that bot stopped due to timeout."""
        try:
            import requests
            
            # This will add the strategy to the stopped set
            response = requests.post(
                f"{self.trading_bot.strategy_controller_url}/control-strategy/{self.trading_bot.user_id}/{self.trading_bot.strategy_id}",
                json={"command": "stop"},
                timeout=10
            )
            
            if response.status_code == 200:
                self.logger.log_info("📡 Notified Strategy Controller of timeout stop")
            else:
                self.logger.log_warning(f"⚠️ Failed to notify Strategy Controller: {response.status_code}")
                
        except Exception as e:
            self.logger.log_error(f"❌ Error notifying Strategy Controller: {e}")
    
    def _update_pause_status(self, status: str):
        """Update pause status in Firebase."""
        try:
            if hasattr(self.trading_bot, 'firebase_client'):
                status_data = {
                    "pause_status": status,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                
                if status == "paused_with_timeout":
                    status_data.update({
                        "pause_start_time": self.pause_start_time.isoformat() if self.pause_start_time else None,
                        "timeout_hours": self.timeout_hours,
                        "auto_stop_at": (self.pause_start_time + timedelta(hours=self.timeout_hours)).isoformat() if self.pause_start_time else None
                    })
                
                # Update bot status with pause info
                current_status = self.trading_bot.firebase_client.BotStatus.PAUSED if status.startswith("paused") else self.trading_bot.firebase_client.BotStatus.RUNNING
                
                self.trading_bot.firebase_client.update_bot_status(
                    current_status,
                    status_data
                )
                
        except Exception as e:
            self.logger.log_error(f"❌ Error updating pause status: {e}")
