#!/usr/bin/env python3
"""
State restoration for trade-bots after Kubernetes pod restarts.
Quickly restores trading state to minimize downtime.
"""

import time
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
from utils.logger import Logger

class StateRestoration:
    """
    Handles restoration of trade-bot state after pod restarts.
    
    Key principles:
    - Restore state as quickly as possible
    - Sync with OANDA to get current positions
    - Resume trading from last processed candle
    - Validate state consistency
    """
    
    def __init__(self, trading_bot):
        self.trading_bot = trading_bot
        self.logger = Logger("StateRestoration")
        self.restoration_start_time = None
        
    def restore_state_if_needed(self) -> bool:
        """
        Check if there's a saved state and restore it.
        
        Returns:
            bool: True if state was restored, False if starting fresh
        """
        self.restoration_start_time = time.time()
        self.logger.log_info("🔄 Checking for saved state to restore...")
        
        try:
            # Try to get saved state from Firestore
            saved_state = self._get_saved_state()
            
            if not saved_state:
                self.logger.log_info("ℹ️ No saved state found, starting fresh")
                return False
            
            # Check if state is recent (within last 10 minutes)
            if not self._is_state_recent(saved_state):
                self.logger.log_warning("⚠️ Saved state is too old, starting fresh")
                self._cleanup_old_state()
                return False
            
            # Restore the state
            self.logger.log_info("🔄 Restoring saved state...")
            self._restore_state(saved_state)
            
            restoration_duration = time.time() - self.restoration_start_time
            self.logger.log_info(f"✅ State restored successfully in {restoration_duration:.2f}s")
            return True
            
        except Exception as e:
            self.logger.log_error(f"❌ Failed to restore state: {e}")
            self.logger.log_info("🆕 Starting fresh due to restoration failure")
            return False
    
    def _get_saved_state(self) -> Optional[Dict[str, Any]]:
        """Get saved state from Firestore."""
        try:
            if not hasattr(self.trading_bot, 'firebase_client'):
                return None
                
            doc_ref = self.trading_bot.firebase_client.db.collection('trade_bot_states').document(self.trading_bot.strategy_id)
            doc = doc_ref.get()
            
            if doc.exists:
                state = doc.to_dict()
                self.logger.log_info(f"📥 Found saved state from {state.get('shutdown_time', 'unknown time')}")
                return state
            
        except Exception as e:
            self.logger.log_error(f"❌ Failed to get saved state: {e}")
        
        return None
    
    def _is_state_recent(self, state: Dict[str, Any]) -> bool:
        """Check if saved state is recent enough to restore."""
        try:
            shutdown_time_str = state.get('shutdown_time')
            if not shutdown_time_str:
                return False
            
            shutdown_time = datetime.fromisoformat(shutdown_time_str.replace('Z', '+00:00'))
            current_time = datetime.now(timezone.utc)
            age_minutes = (current_time - shutdown_time).total_seconds() / 60
            
            # Only restore state if it's less than 10 minutes old
            max_age_minutes = 10
            is_recent = age_minutes <= max_age_minutes
            
            self.logger.log_info(f"⏰ State age: {age_minutes:.1f} minutes (max: {max_age_minutes})")
            return is_recent
            
        except Exception as e:
            self.logger.log_error(f"❌ Failed to check state age: {e}")
            return False
    
    def _restore_state(self, state: Dict[str, Any]):
        """Restore the trading bot state."""
        
        # Step 1: Restore strategy state
        self._restore_strategy_state(state.get('strategy_state', {}))
        
        # Step 2: Sync with OANDA to get current positions
        self._sync_positions_with_oanda(state.get('open_positions', []))
        
        # Step 3: Restore market data subscriptions
        self._restore_market_data_subscriptions(state)
        
        # Step 4: Restore risk metrics
        self._restore_risk_metrics(state.get('risk_metrics', {}))
        
        # Step 5: Resume from last processed candle
        self._resume_from_last_candle(state.get('last_processed_candle'))
        
        self.logger.log_info("🎯 All state components restored")
    
    def _restore_strategy_state(self, strategy_state: Dict[str, Any]):
        """Restore strategy-specific state."""
        try:
            if strategy_state and hasattr(self.trading_bot, 'strategy'):
                if hasattr(self.trading_bot.strategy, 'restore_state'):
                    self.trading_bot.strategy.restore_state(strategy_state)
                    self.logger.log_info("📊 Strategy state restored")
                else:
                    self.logger.log_warning("⚠️ Strategy doesn't support state restoration")
        except Exception as e:
            self.logger.log_error(f"❌ Failed to restore strategy state: {e}")
    
    def _sync_positions_with_oanda(self, saved_positions: list):
        """Sync current positions with OANDA (positions should still be open)."""
        try:
            if not hasattr(self.trading_bot, 'oanda_client'):
                return
            
            # Get current positions from OANDA
            current_positions = self.trading_bot.oanda_client.get_open_trades()
            
            self.logger.log_info(f"📊 Saved positions: {len(saved_positions)}, Current positions: {len(current_positions)}")
            
            # Validate that positions are still open
            if len(current_positions) != len(saved_positions):
                self.logger.log_warning(f"⚠️ Position count mismatch! Expected {len(saved_positions)}, found {len(current_positions)}")
                # This is normal - some positions might have hit SL/TP during restart
            
            # Update fast update loop with current positions
            if hasattr(self.trading_bot, 'fast_update_loop'):
                self.trading_bot.fast_update_loop.current_positions = current_positions
                
        except Exception as e:
            self.logger.log_error(f"❌ Failed to sync positions: {e}")
    
    def _restore_market_data_subscriptions(self, state: Dict[str, Any]):
        """Restore market data subscriptions."""
        try:
            subscribed_symbols = state.get('subscription_symbols', [])
            instrument = state.get('instrument')
            
            if not subscribed_symbols and instrument:
                subscribed_symbols = [instrument]
            
            if subscribed_symbols and hasattr(self.trading_bot, 'market_data_provider'):
                for symbol in subscribed_symbols:
                    if hasattr(self.trading_bot.market_data_provider, 'subscribe_to_symbol'):
                        self.trading_bot.market_data_provider.subscribe_to_symbol(symbol)
                        
                self.logger.log_info(f"📡 Restored subscriptions for {len(subscribed_symbols)} symbols")
                
        except Exception as e:
            self.logger.log_error(f"❌ Failed to restore market data subscriptions: {e}")
    
    def _restore_risk_metrics(self, risk_metrics: Dict[str, Any]):
        """Restore risk management metrics."""
        try:
            if risk_metrics and hasattr(self.trading_bot, 'fast_update_loop'):
                # Restore key risk metrics
                if 'daily_realized_pl' in risk_metrics:
                    self.trading_bot.fast_update_loop.daily_realized_pl = risk_metrics['daily_realized_pl']
                
                if 'account_balance' in risk_metrics:
                    # Will be refreshed from OANDA anyway, but good for continuity
                    pass
                    
                self.logger.log_info("💰 Risk metrics restored")
                
        except Exception as e:
            self.logger.log_error(f"❌ Failed to restore risk metrics: {e}")
    
    def _resume_from_last_candle(self, last_candle_timestamp: Optional[str]):
        """Resume market data processing from last processed candle."""
        try:
            if not last_candle_timestamp:
                self.logger.log_info("ℹ️ No last candle timestamp, will start from current time")
                return
            
            # The market data provider will handle resuming from the right point
            if hasattr(self.trading_bot, 'trading_engine') and hasattr(self.trading_bot.trading_engine, 'candle_builder'):
                # Set the resume point
                self.logger.log_info(f"⏰ Will resume processing from: {last_candle_timestamp}")
                
        except Exception as e:
            self.logger.log_error(f"❌ Failed to set resume point: {e}")
    
    def _cleanup_old_state(self):
        """Clean up old saved state."""
        try:
            if hasattr(self.trading_bot, 'firebase_client'):
                doc_ref = self.trading_bot.firebase_client.db.collection('trade_bot_states').document(self.trading_bot.strategy_id)
                doc_ref.delete()
                self.logger.log_info("🧹 Old state cleaned up")
        except Exception as e:
            self.logger.log_error(f"❌ Failed to cleanup old state: {e}")
    
    def mark_restoration_complete(self):
        """Mark that restoration is complete and clean up saved state."""
        try:
            if hasattr(self.trading_bot, 'firebase_client'):
                doc_ref = self.trading_bot.firebase_client.db.collection('trade_bot_states').document(self.trading_bot.strategy_id)
                doc_ref.delete()
                self.logger.log_info("✅ Restoration complete, saved state cleaned up")
        except Exception as e:
            self.logger.log_error(f"❌ Failed to cleanup restored state: {e}")
