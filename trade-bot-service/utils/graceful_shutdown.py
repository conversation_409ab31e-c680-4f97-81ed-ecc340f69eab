#!/usr/bin/env python3
"""
Graceful shutdown handler for trade-bots.
Handles SIGTERM/SIGINT signals during Kubernetes pod restarts.
"""

import signal
import sys
import time
import json
import requests
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from utils.logger import Logger

class GracefulShutdownHandler:
    """
    Handles graceful shutdown of trade-bots during Kubernetes pod restarts.
    
    Key principles:
    - DON'T close open trades (they continue running)
    - Save current state for quick restoration
    - Minimize downtime during pod replacement
    """
    
    def __init__(self, trading_bot):
        self.trading_bot = trading_bot
        self.logger = Logger("GracefulShutdown")
        self.shutdown_initiated = False
        self.shutdown_start_time = None
        
        # Register signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        self.logger.log_info("🛡️ Graceful shutdown handler initialized")
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals from Kubernetes."""
        if self.shutdown_initiated:
            self.logger.log_warning("⚠️ Shutdown already in progress, ignoring signal")
            return
            
        self.shutdown_initiated = True
        self.shutdown_start_time = time.time()
        
        signal_name = "SIGTERM" if signum == signal.SIGTERM else "SIGINT"
        self.logger.log_info(f"🛑 Received {signal_name}, initiating graceful shutdown...")
        
        try:
            self._perform_graceful_shutdown()
        except Exception as e:
            self.logger.log_error(f"❌ Error during graceful shutdown: {e}")
            # Force exit if graceful shutdown fails
            sys.exit(1)
    
    def _perform_graceful_shutdown(self):
        """Perform the graceful shutdown sequence."""

        # Step 1: Determine if this is a user stop or system restart
        shutdown_reason = self._determine_shutdown_reason()

        if shutdown_reason == "user_stop":
            self._perform_user_stop_shutdown()
        else:
            self._perform_system_restart_shutdown()

    def _determine_shutdown_reason(self) -> str:
        """Determine if this is a user stop or system restart."""
        try:
            # Check if Strategy Controller marked this strategy as stopped
            response = requests.get(
                f"{self.trading_bot.strategy_controller_url}/check-strategy-status/{self.trading_bot.user_id}/{self.trading_bot.strategy_id}",
                timeout=5
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("is_stopped", False):
                    return "user_stop"
        except Exception as e:
            self.logger.log_warning(f"⚠️ Could not check stop status: {e}")

        # Default to system restart if we can't determine or if not stopped
        return "system_restart"

    def _perform_user_stop_shutdown(self):
        """Handle user-initiated stop (complete shutdown)."""
        self.logger.log_info("🛑 USER STOP DETECTED - Performing complete shutdown")

        # Step 1: Pause new trades immediately
        self.logger.log_info("1️⃣ Stopping all trading activity...")
        if hasattr(self.trading_bot, 'pause_new_trades'):
            self.trading_bot.pause_new_trades()

        # Step 2: Close all open positions (user wants complete stop)
        self.logger.log_info("2️⃣ Closing all open positions...")
        self._close_all_positions()

        # Step 3: Clean up any saved state (no recovery needed)
        self.logger.log_info("3️⃣ Cleaning up saved state...")
        self._cleanup_saved_state()

        # Step 4: Update final status
        self.logger.log_info("4️⃣ Updating final status...")
        self._update_final_status()

        # Step 5: Clean up connections
        self.logger.log_info("5️⃣ Cleaning up connections...")
        self._cleanup_connections()

        # Step 6: Final logging
        shutdown_duration = time.time() - self.shutdown_start_time
        self.logger.log_info(f"✅ User stop completed in {shutdown_duration:.2f}s")
        self.logger.log_info("🏁 Trading bot has been permanently stopped")

        # Exit cleanly
        sys.exit(0)

    def _perform_system_restart_shutdown(self):
        """Handle system restart (preserve state and positions)."""
        self.logger.log_info("🔄 SYSTEM RESTART DETECTED - Preserving state for recovery")

        # Step 1: Pause new trade signals (but keep existing trades)
        self.logger.log_info("1️⃣ Pausing new trade signals...")
        if hasattr(self.trading_bot, 'pause_new_trades'):
            self.trading_bot.pause_new_trades()

        # Step 2: Save current state to persistent storage
        self.logger.log_info("2️⃣ Saving current state...")
        self._save_bot_state()

        # Step 3: Notify Strategy Controller of restart
        self.logger.log_info("3️⃣ Notifying Strategy Controller...")
        self._notify_strategy_controller()

        # Step 4: Clean up connections and resources
        self.logger.log_info("4️⃣ Cleaning up connections...")
        self._cleanup_connections()

        # Step 5: Final logging
        shutdown_duration = time.time() - self.shutdown_start_time
        self.logger.log_info(f"✅ Graceful shutdown completed in {shutdown_duration:.2f}s")
        self.logger.log_info("🔄 Pod will restart and resume trading automatically")

        # Exit cleanly
        sys.exit(0)
    
    def _save_bot_state(self):
        """Save current bot state for restoration after restart."""
        try:
            # Get current state from trading bot
            state = {
                'bot_id': self.trading_bot.strategy_id,
                'user_id': getattr(self.trading_bot, 'user_id', None),
                'strategy_id': self.trading_bot.strategy_id,
                'instrument': getattr(self.trading_bot.strategy, 'instrument', None),
                'timeframe': getattr(self.trading_bot.strategy, 'timeframe', None),
                
                # Trading state (DON'T close positions)
                'open_positions': self._get_open_positions(),
                'strategy_state': self._get_strategy_state(),
                'risk_metrics': self._get_risk_metrics(),
                
                # Market data state
                'last_processed_candle': self._get_last_candle_timestamp(),
                'subscription_symbols': self._get_subscribed_symbols(),
                
                # Restart metadata
                'shutdown_time': datetime.now(timezone.utc).isoformat(),
                'shutdown_reason': 'pod_restart',
                'restart_expected': True
            }
            
            # Save to Firestore for quick retrieval
            if hasattr(self.trading_bot, 'firebase_client'):
                doc_ref = self.trading_bot.firebase_client.db.collection('trade_bot_states').document(self.trading_bot.strategy_id)
                doc_ref.set(state)
                self.logger.log_info(f"💾 State saved to Firestore: {len(json.dumps(state))} bytes")
            else:
                self.logger.log_warning("⚠️ No Firebase client available, state not saved")
                
        except Exception as e:
            self.logger.log_error(f"❌ Failed to save bot state: {e}")
            # Don't fail the shutdown if state saving fails
    
    def _get_open_positions(self) -> list:
        """Get current open positions (keep them open)."""
        try:
            if hasattr(self.trading_bot, 'oanda_client'):
                positions = self.trading_bot.oanda_client.get_open_trades()
                self.logger.log_info(f"📊 Found {len(positions)} open positions to preserve")
                return positions
        except Exception as e:
            self.logger.log_error(f"❌ Failed to get open positions: {e}")
        return []
    
    def _get_strategy_state(self) -> dict:
        """Get current strategy state."""
        try:
            if hasattr(self.trading_bot, 'strategy') and hasattr(self.trading_bot.strategy, 'get_state'):
                return self.trading_bot.strategy.get_state()
        except Exception as e:
            self.logger.log_error(f"❌ Failed to get strategy state: {e}")
        return {}
    
    def _get_risk_metrics(self) -> dict:
        """Get current risk metrics."""
        try:
            if hasattr(self.trading_bot, 'fast_update_loop'):
                return {
                    'daily_realized_pl': getattr(self.trading_bot.fast_update_loop, 'daily_realized_pl', 0),
                    'total_unrealized_pl': getattr(self.trading_bot.fast_update_loop, 'total_unrealized_pl', 0),
                    'account_balance': getattr(self.trading_bot.fast_update_loop, 'account_balance', 0),
                }
        except Exception as e:
            self.logger.log_error(f"❌ Failed to get risk metrics: {e}")
        return {}
    
    def _get_last_candle_timestamp(self) -> Optional[str]:
        """Get timestamp of last processed candle."""
        try:
            if hasattr(self.trading_bot, 'trading_engine') and hasattr(self.trading_bot.trading_engine, 'candle_builder'):
                last_candle = self.trading_bot.trading_engine.candle_builder.get_latest_candle()
                if last_candle:
                    return last_candle.get('timestamp')
        except Exception as e:
            self.logger.log_error(f"❌ Failed to get last candle timestamp: {e}")
        return None
    
    def _get_subscribed_symbols(self) -> list:
        """Get currently subscribed symbols."""
        try:
            if hasattr(self.trading_bot, 'market_data_provider') and hasattr(self.trading_bot.market_data_provider, 'subscribed_symbols'):
                return list(self.trading_bot.market_data_provider.subscribed_symbols)
        except Exception as e:
            self.logger.log_error(f"❌ Failed to get subscribed symbols: {e}")
        return []
    
    def _notify_strategy_controller(self):
        """Notify Strategy Controller of planned restart."""
        try:
            # This is optional - Strategy Controller will detect the restart anyway
            notification = {
                'event': 'trade_bot_restart',
                'strategy_id': self.trading_bot.strategy_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'reason': 'pod_restart',
                'expected_downtime_seconds': 60
            }
            
            # Could send to Pub/Sub topic if needed
            self.logger.log_info("📡 Strategy Controller notification prepared")
            
        except Exception as e:
            self.logger.log_error(f"❌ Failed to notify Strategy Controller: {e}")
    
    def _cleanup_connections(self):
        """Clean up connections and resources."""
        try:
            # Stop market data provider
            if hasattr(self.trading_bot, 'market_data_provider') and hasattr(self.trading_bot.market_data_provider, 'stop'):
                self.trading_bot.market_data_provider.stop()
            
            # Stop fast update loop
            if hasattr(self.trading_bot, 'fast_update_loop') and hasattr(self.trading_bot.fast_update_loop, 'stop'):
                self.trading_bot.fast_update_loop.stop()
            
            # Close any open connections
            if hasattr(self.trading_bot, 'oanda_client'):
                # OANDA client connections will be recreated on restart
                pass
                
            self.logger.log_info("🧹 Connections cleaned up")
            
        except Exception as e:
            self.logger.log_error(f"❌ Failed to cleanup connections: {e}")

    def _close_all_positions(self):
        """Close all open positions (for user stops)."""
        try:
            if hasattr(self.trading_bot, 'oanda_client'):
                open_positions = self.trading_bot.oanda_client.get_open_trades()

                if not open_positions:
                    self.logger.log_info("ℹ️ No open positions to close")
                    return

                self.logger.log_info(f"💰 Closing {len(open_positions)} open positions...")

                for position in open_positions:
                    try:
                        # Close the position
                        trade_id = position.get('trade_id')
                        if trade_id:
                            result = self.trading_bot.oanda_client.close_trade(trade_id)
                            if result:
                                self.logger.log_info(f"✅ Closed position {trade_id}")
                            else:
                                self.logger.log_error(f"❌ Failed to close position {trade_id}")
                    except Exception as e:
                        self.logger.log_error(f"❌ Error closing position {trade_id}: {e}")

                self.logger.log_info("💰 All positions closed")
            else:
                self.logger.log_warning("⚠️ No OANDA client available to close positions")

        except Exception as e:
            self.logger.log_error(f"❌ Failed to close positions: {e}")

    def _cleanup_saved_state(self):
        """Clean up any saved state (for user stops)."""
        try:
            if hasattr(self.trading_bot, 'firebase_client'):
                doc_ref = self.trading_bot.firebase_client.db.collection('trade_bot_states').document(self.trading_bot.strategy_id)
                doc_ref.delete()
                self.logger.log_info("🧹 Saved state cleaned up (no recovery needed)")
        except Exception as e:
            self.logger.log_error(f"❌ Failed to cleanup saved state: {e}")

    def _update_final_status(self):
        """Update final status for user stop."""
        try:
            if hasattr(self.trading_bot, 'firebase_client'):
                self.trading_bot.firebase_client.update_bot_status(
                    self.trading_bot.firebase_client.BotStatus.STOPPED,
                    {"message": "Trading bot stopped by user", "final_stop": True}
                )

                self.trading_bot.firebase_client.append_user_log(
                    "🛑 Trading bot has been stopped and all positions closed"
                )

                self.logger.log_info("📊 Final status updated")
        except Exception as e:
            self.logger.log_error(f"❌ Failed to update final status: {e}")
