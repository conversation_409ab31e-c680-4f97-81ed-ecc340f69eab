(base) Mac:strategy-controller-service mainiy$ ./run_local.sh
🔧 Setting up development environment for Strategy Controller...
📋 Loading centralized configuration...
🔧 Environment configured for: DEVELOPMENT
   Firebase Emulator: True
   PubSub Emulator: True
✅ Configuration loaded: DEVELOPMENT mode
Loading environment from ../trade-bot-service/.env
Activating existing virtual environment...
Checking and installing trade bot dependencies...
Requirement already satisfied: numpy in ./venv/lib/python3.12/site-packages (2.2.5)
Requirement already satisfied: pandas in ./venv/lib/python3.12/site-packages (2.2.3)
Requirement already satisfied: matplotlib in ./venv/lib/python3.12/site-packages (3.10.3)
Requirement already satisfied: ta in ./venv/lib/python3.12/site-packages (0.11.0)
Requirement already satisfied: scikit-learn in ./venv/lib/python3.12/site-packages (1.6.1)
Requirement already satisfied: python-dateutil>=2.8.2 in ./venv/lib/python3.12/site-packages (from pandas) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in ./venv/lib/python3.12/site-packages (from pandas) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in ./venv/lib/python3.12/site-packages (from pandas) (2025.2)
Requirement already satisfied: contourpy>=1.0.1 in ./venv/lib/python3.12/site-packages (from matplotlib) (1.3.2)
Requirement already satisfied: cycler>=0.10 in ./venv/lib/python3.12/site-packages (from matplotlib) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in ./venv/lib/python3.12/site-packages (from matplotlib) (4.57.0)
Requirement already satisfied: kiwisolver>=1.3.1 in ./venv/lib/python3.12/site-packages (from matplotlib) (1.4.8)
Requirement already satisfied: packaging>=20.0 in ./venv/lib/python3.12/site-packages (from matplotlib) (25.0)
Requirement already satisfied: pillow>=8 in ./venv/lib/python3.12/site-packages (from matplotlib) (11.2.1)
Requirement already satisfied: pyparsing>=2.3.1 in ./venv/lib/python3.12/site-packages (from matplotlib) (3.2.3)
Requirement already satisfied: scipy>=1.6.0 in ./venv/lib/python3.12/site-packages (from scikit-learn) (1.15.3)
Requirement already satisfied: joblib>=1.2.0 in ./venv/lib/python3.12/site-packages (from scikit-learn) (1.5.0)
Requirement already satisfied: threadpoolctl>=3.1.0 in ./venv/lib/python3.12/site-packages (from scikit-learn) (3.6.0)
Requirement already satisfied: six>=1.5 in ./venv/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)
Please ensure Firebase emulators are running (Firestore on port 8082, PubSub on port 8085)
You can start them with: cd functions && npm run serve
Starting strategy controller with local trade bot mode...
🔧 Environment configured for: DEVELOPMENT
   Firebase Emulator: True
   PubSub Emulator: True
🔧 Configuration loaded: DEVELOPMENT mode
INFO:root:Running in DEVELOPMENT mode
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.290957", "service": "strategy-controller", "message": "Monitoring service initialized successfully", "level": "INFO"}
INFO:root:Using local Kubernetes configuration
INFO:root:Using Kubernetes namespace: default
/Users/<USER>/workspace/oryn/strategy-controller-service/main.py:113: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
/Users/<USER>/workspace/oryn/strategy-controller-service/main.py:153: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("shutdown")
/Users/<USER>/workspace/oryn/strategy-controller-service/venv/lib/python3.12/site-packages/google/cloud/firestore_v1/base_collection.py:303: UserWarning: Detected filter using positional arguments. Prefer using the 'filter' keyword argument instead.
  return query.where(field_path, op_string, value)
INFO:     Started server process [70181]
INFO:     Waiting for application startup.
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.321062", "service": "strategy-controller", "message": "Starting strategy controller service", "level": "INFO", "project_id": "oryntrade", "pubsub_emulator": true, "firestore_emulator": true}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.346678", "service": "strategy-controller", "message": "Created command topic", "level": "INFO", "topic": "projects/oryntrade/topics/strategy-commands"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.347200", "service": "strategy-controller", "message": "PubSub worker initialization", "level": "INFO", "project_id": "oryntrade", "subscription_id": "strategy-controller-sub", "subscription_path": "projects/oryntrade/subscriptions/strategy-controller-sub", "topic_path": "projects/oryntrade/topics/strategy-execution"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.347227", "service": "strategy-controller", "message": "Checking if topic exists", "level": "INFO", "topic_path": "projects/oryntrade/topics/strategy-execution"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.351444", "service": "strategy-controller", "message": "Topic not found, creating it", "level": "INFO", "topic_path": "projects/oryntrade/topics/strategy-execution"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.354628", "service": "strategy-controller", "message": "Created new topic", "level": "INFO", "topic_path": "projects/oryntrade/topics/strategy-execution"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.354674", "service": "strategy-controller", "message": "Checking if subscription exists", "level": "INFO", "subscription_path": "projects/oryntrade/subscriptions/strategy-controller-sub"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.364180", "service": "strategy-controller", "message": "Subscription not found, creating it", "level": "INFO", "subscription_path": "projects/oryntrade/subscriptions/strategy-controller-sub", "error": "404 Subscription does not exist"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.374600", "service": "strategy-controller", "message": "Created new subscription", "level": "INFO", "subscription_path": "projects/oryntrade/subscriptions/strategy-controller-sub", "topic": "projects/oryntrade/topics/strategy-execution"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.374689", "service": "strategy-controller", "message": "PubSub worker initialized", "level": "INFO", "project_id": "oryntrade", "subscription": "strategy-controller-sub"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.375618", "service": "strategy-controller", "message": "PubSub worker running", "level": "INFO"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.375679", "service": "strategy-controller", "message": "PubSub worker started", "level": "INFO"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:37.375915", "service": "strategy-controller", "message": "PubSub worker started successfully", "level": "INFO"}
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.536930", "service": "strategy-controller", "message": "Processing messages", "level": "INFO", "count": 1}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537030", "service": "strategy-controller", "message": "Message received", "level": "INFO", "message_id": "3", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi"}
WARNING:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537071", "service": "strategy-controller", "message": "Base64 decode error, trying alternative approach", "level": "WARNING", "message_id": "3", "error": "'utf-8' codec can't decode byte 0xba in position 0: invalid start byte"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537108", "service": "strategy-controller", "message": "Successfully extracted JSON from raw data", "level": "INFO", "message_id": "3"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537124", "service": "strategy-controller", "message": "Parsed message data", "level": "INFO", "message_id": "3", "keys": ["user_id", "strategy_id"]}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537136", "service": "strategy-controller", "message": "Found user_id at root level", "level": "INFO", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537147", "service": "strategy-controller", "message": "Found strategy_id at root level", "level": "INFO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537222", "service": "strategy-controller", "message": "Processing strategy", "level": "INFO", "message_id": "3", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.537267", "service": "strategy-controller", "message": "Fetching strategy from Firestore (attempt 1/5)", "level": "INFO", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.555170", "service": "strategy-controller", "message": "Successfully loaded strategy from Firestore", "level": "INFO", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi", "keys": ["name", "description", "instruments", "timeframe", "tradingSession", "indicators", "entryRules", "exitRules", "riskManagement"]}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.555271", "service": "strategy-controller", "message": "Creating strategy pod", "level": "INFO", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi"}
INFO:root:Running trade bot locally for strategy V7u9KHh8Hk2Ks2fciNOi
INFO:root:Using trade bot at: /Users/<USER>/workspace/oryn/trade-bot-service
INFO:root:Reading environment from /Users/<USER>/workspace/oryn/trade-bot-service/.env
INFO:root:Environment for trade bot: {'TERM_PROGRAM': 'Apple_Terminal', 'rvm_bin_path': '/Users/<USER>/.rvm/bin', 'GEM_HOME': '/Users/<USER>/.rvm/gems/ruby-2.5.0', 'SHELL': '/bin/bash', 'TERM': 'xterm-256color', 'TMPDIR': '/var/folders/dv/lqd3hgys4kl840l3bg_sx8f80000gn/T/', 'IRBRC': '/Users/<USER>/.rvm/rubies/ruby-2.5.0/.irbrc', 'HOMEBREW_REPOSITORY': '/opt/homebrew', 'CONDA_SHLVL': '1', 'TERM_PROGRAM_VERSION': '455.1', 'CONDA_PROMPT_MODIFIER': '(base) ', 'DEVELOPMENT_MODE': 'true', 'GSETTINGS_SCHEMA_DIR_CONDA_BACKUP': '', 'TERM_SESSION_ID': '294BBA94-D610-4E4F-8D61-9730618B7038', 'MY_RUBY_HOME': '/Users/<USER>/.rvm/rubies/ruby-2.5.0', 'FIRESTORE_EMULATOR_HOST': '127.0.0.1:8082', 'USER': 'mainiy', 'rvm_stored_umask': '0022', 'CONDA_EXE': '/opt/anaconda3/bin/conda', 'BYPASS_MARKET_IS_CLOSED': 'false', 'rvm_path': '/Users/<USER>/.rvm', 'SSH_AUTH_SOCK': '/private/tmp/com.apple.launchd.8zkLXwTJBD/Listeners', 'VIRTUAL_ENV': '/Users/<USER>/workspace/oryn/strategy-controller-service/venv', 'GOOGLE_CLOUD_PROJECT': 'oryntrade', 'rvm_prefix': '/Users/<USER>', 'PATH': '/Users/<USER>/workspace/oryn/strategy-controller-service/venv/bin:/Users/<USER>/Downloads/google-cloud-sdk/bin:/opt/anaconda3/bin:/opt/anaconda3/condabin:/Users/<USER>/.rbenv/shims:/usr/local/opt/ruby/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Users/<USER>/.rvm/gems/ruby-2.5.0/bin:/Users/<USER>/.rvm/gems/ruby-2.5.0@global/bin:/Users/<USER>/.rvm/rubies/ruby-2.5.0/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Users/<USER>/Downloads/apache-ant-1.10.9/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/Applications/VMware Fusion.app/Contents/Public:/Users/<USER>/.rvm/bin:/Users/<USER>/npm/bin:/Users/<USER>/.local/bin:~/google-cloud-sdk/bin', 'GSETTINGS_SCHEMA_DIR': '/opt/anaconda3/share/glib-2.0/schemas', '__CFBundleIdentifier': 'com.apple.Terminal', 'rvm_loaded_flag': '1', 'CONDA_PREFIX': '/opt/anaconda3', 'PWD': '/Users/<USER>/workspace/oryn/strategy-controller-service', 'LANG': 'en_US.UTF-8', 'XPC_FLAGS': '0x0', 'PS1': '(venv) ', 'RBENV_SHELL': 'bash', 'XPC_SERVICE_NAME': '0', 'rvm_version': '1.29.12 (latest)', 'OANDA_PRACTICE_MODE': 'true', 'SHLVL': '2', 'HOME': '/Users/<USER>', 'USE_LOCAL_TRADE_BOT': 'true', 'HOMEBREW_PREFIX': '/opt/homebrew', 'PUBSUB_EMULATOR_HOST': 'localhost:8085', 'LOGNAME': 'mainiy', 'CONDA_PYTHON_EXE': '/opt/anaconda3/bin/python', 'GEM_PATH': '/Users/<USER>/.rvm/gems/ruby-2.5.0:/Users/<USER>/.rvm/gems/ruby-2.5.0@global', 'CONDA_DEFAULT_ENV': 'base', 'HOMEBREW_CELLAR': '/opt/homebrew/Cellar', 'INFOPATH': '/opt/homebrew/share/info:', 'RUBY_VERSION': 'ruby-2.5.0', 'VIRTUAL_ENV_PROMPT': '(venv) ', 'rvm_user_install_flag': '1', '_': '/Users/<USER>/workspace/oryn/strategy-controller-service/venv/bin/python', 'FIREBASE_AUTH_EMULATOR_HOST': 'localhost:9099', 'PUBSUB_SUBSCRIPTION': 'strategy-controller-sub', 'K8S_NAMESPACE': 'default', 'PORT': '8080', 'HOST': '0.0.0.0', 'WORKERS': '1', 'ENVIRONMENT': 'development', 'STRATEGY_ID': 'V7u9KHh8Hk2Ks2fciNOi', 'USER_ID': 'PiZZ5xXSGDhpQz6tOJZSdNyLbNtO', 'USE_FIREBASE_EMULATOR': 'true', 'WEBSOCKET_SERVICE_URL': 'wss://oryn-websocket-service-87400455587.us-central1.run.app/ws  # Production', 'PYTHONPATH': ':/Users/<USER>/workspace/oryn/trade-bot-service', 'STRATEGY_JSON_FILE': '/Users/<USER>/workspace/oryn/trade-bot-service/strategy_V7u9KHh8Hk2Ks2fciNOi.json', 'MARKET_DATA_PROVIDER': 'pubsub', 'PYTHONUNBUFFERED': '1'}
INFO:root:Running trade bot with wrapper script: /Users/<USER>/workspace/oryn/trade-bot-service/run_wrapper.sh
INFO:root:Starting output logging for PID 70212
INFO:root:Started local trade bot process with PID 70212
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.560829", "service": "strategy-controller", "message": "Strategy pod creation result", "level": "INFO", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi", "result": {"process_id": 70212, "status": "created", "local": true}}
INFO:root:[Trade Bot] Starting trade bot from wrapper script
INFO:root:[Trade Bot] Working directory: /Users/<USER>/workspace/oryn/trade-bot-service
INFO:root:[Trade Bot] Strategy ID: V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] User ID: PiZZ5xXSGDhpQz6tOJZSdNyLbNtO
INFO:root:[Trade Bot] Strategy file: /Users/<USER>/workspace/oryn/trade-bot-service/strategy_V7u9KHh8Hk2Ks2fciNOi.json
INFO:root:[Trade Bot] Sourcing .env file
INFO:root:[Trade Bot] Environment variables:
INFO:root:[Trade Bot] STRATEGY_ID=V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] USER_ID=PiZZ5xXSGDhpQz6tOJZSdNyLbNtO
INFO:root:[Trade Bot] USE_FIREBASE_EMULATOR=true
INFO:root:[Trade Bot] FIRESTORE_EMULATOR_HOST=127.0.0.1:8082
INFO:root:[Trade Bot] BYPASS_MARKET_IS_CLOSED=false
INFO:root:[Trade Bot] OANDA_PRACTICE_MODE=true
INFO:root:[Trade Bot] Checking Firestore emulator connection...
INFO:root:[Trade Bot] Successfully connected to Firestore emulator
INFO:root:[Trade Bot] Creating PubSub topics in emulator...
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.598601", "service": "strategy-controller", "message": "Updated strategy status in Firestore", "level": "INFO", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi", "status": "running"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.598845", "service": "strategy-controller", "message": "Strategy pod created successfully", "level": "INFO", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi", "pod_or_process": 70212}
INFO:     127.0.0.1:53876 - "OPTIONS /control-strategy/PiZZ5xXSGDhpQz6tOJZSdNyLbNtO/V7u9KHh8Hk2Ks2fciNOi HTTP/1.1" 200 OK
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.603758", "service": "strategy-controller", "message": "Request received", "level": "INFO", "endpoint": "/control-strategy/PiZZ5xXSGDhpQz6tOJZSdNyLbNtO/V7u9KHh8Hk2Ks2fciNOi", "method": "POST", "client_ip": "127.0.0.1"}
INFO:root:Using default test user in development mode
WARNING:root:Development mode: allowing access for test-user-development to strategy of PiZZ5xXSGDhpQz6tOJZSdNyLbNtO
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:42.631131", "service": "strategy-controller", "message": "API request processed", "level": "INFO", "endpoint": "/control-strategy/PiZZ5xXSGDhpQz6tOJZSdNyLbNtO/V7u9KHh8Hk2Ks2fciNOi", "method": "POST", "status_code": 200, "client_ip": "127.0.0.1", "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "duration_ms": 27.42910385131836, "request_id": "1751511222.603691-290250105"}
INFO:     127.0.0.1:53876 - "POST /control-strategy/PiZZ5xXSGDhpQz6tOJZSdNyLbNtO/V7u9KHh8Hk2Ks2fciNOi HTTP/1.1" 200 OK
INFO:root:[Trade Bot] Created PubSub topic: strategy-commands
INFO:root:[Trade Bot] Created PubSub topic: strategy-execution
INFO:root:[Trade Bot] Installing required dependencies...
INFO:root:[Trade Bot] Dependencies installed successfully
INFO:root:[Trade Bot] Starting Python trade bot...
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:43.379347", "service": "strategy-controller", "message": "Request received", "level": "INFO", "endpoint": "/monitoring/trade-bot-health", "method": "POST", "client_ip": "127.0.0.1"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:43.380821", "service": "strategy-controller", "message": "Received trade bot health update", "level": "INFO", "strategy_id": "V7u9KHh8Hk2Ks2fciNOi", "user_id": "PiZZ5xXSGDhpQz6tOJZSdNyLbNtO", "pod_name": "Mac", "status": "stopped"}
INFO:strategy-controller:{"timestamp": "2025-07-02T19:53:43.385619", "service": "strategy-controller", "message": "API request processed", "level": "INFO", "endpoint": "/monitoring/trade-bot-health", "method": "POST", "status_code": 200, "client_ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "duration_ms": 6.29115104675293, "request_id": "**********.379316-290250042"}
INFO:     127.0.0.1:53913 - "POST /monitoring/trade-bot-health HTTP/1.1" 200 OK
INFO:root:[Trade Bot] 2025-07-02 12:53:43,562 - INFO - [TradingBot] Using modified URL for health client: http://localhost:8080
INFO:root:[Trade Bot] 2025-07-02 12:53:43,575 - INFO - [TradingBot] Removed existing stop flag file: /tmp/trade_bot_stopped_V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] 2025-07-02 12:53:43,575 - INFO - [TradingBot] User ID found: PiZZ5xXSGDhpQz6tOJZSdNyLbNtO
INFO:root:[Trade Bot] 2025-07-02 12:53:43,575 - INFO - [TradingBot] Strategy ID found: V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] 2025-07-02 12:53:43,575 - INFO - [FirebaseClient] Initializing Firebase app in emulator mode
INFO:root:[Trade Bot] 2025-07-02 12:53:43,575 - INFO - [FirebaseClient] Firebase app initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:43,948 - INFO - [FirebaseClient] Updated bot status to: BotStatus.INITIALIZING
INFO:root:[Trade Bot] 2025-07-02 12:53:43,948 - INFO - [TradingBot] 🚀 Using Pub/Sub market data provider (real-time via WebSocket ingestion)
INFO:root:[Trade Bot] 2025-07-02 12:53:43,949 - INFO - [MarketConditions] MarketConditions initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:43,950 - INFO - [MarketDataProvider] MarketDataProvider initialized with API key
INFO:root:[Trade Bot] 2025-07-02 12:53:43,950 - INFO - [MarketConditions] MarketConditions initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:43,950 - INFO - [PubSubMarketDataProvider] 📊 Historical data provider and market conditions initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:43,951 - INFO - [PubSubMarketDataProvider] 📡 PubSubMarketDataProvider initialized for trade-bot: bot-V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] 2025-07-02 12:53:43,951 - INFO - [PubSubMarketDataProvider] 🏗️ Project ID: oryntrade
INFO:root:[Trade Bot] 2025-07-02 12:53:43,951 - INFO - [PubSubMarketDataProvider] 🚀 Starting PubSubMarketDataProvider...
INFO:root:[Trade Bot] 2025-07-02 12:53:43,975 - INFO - [PubSubMarketDataProvider] 🆕 Created subscription: projects/oryntrade/subscriptions/trade-bot-bot-V7u9KHh8Hk2Ks2fciNOi-minute-aggregates
INFO:root:[Trade Bot] 2025-07-02 12:53:43,975 - INFO - [PubSubMarketDataProvider] 👂 Starting Pub/Sub subscriber...
INFO:root:[Trade Bot] 2025-07-02 12:53:43,975 - INFO - [PubSubMarketDataProvider] ✅ PubSubMarketDataProvider started
INFO:root:[Trade Bot] 2025-07-02 12:53:43,975 - INFO - [PubSubMarketDataProvider] ℹ️ Note: Real-time subscription will be activated during proper initialization
INFO:root:[Trade Bot] 2025-07-02 12:53:43,975 - INFO - [TradingBot] Market data provider initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:43,976 - INFO - [PubSubMarketDataProvider] 🔄 Listening for minute aggregates on: trade-bot-bot-V7u9KHh8Hk2Ks2fciNOi-minute-aggregates
INFO:root:[Trade Bot] 2025-07-02 12:53:43,976 - INFO - [oanda_client] Fetching OANDA credentials...
INFO:root:[Trade Bot] 2025-07-02 12:53:43,976 - INFO - [FirebaseClient] Fetching OANDA credentials for user PiZZ5xXSGDhpQz6tOJZSdNyLbNtO
INFO:root:[Trade Bot] 2025-07-02 12:53:44,133 - INFO - [ConnectionManager-oanda] Connecting to oanda...
INFO:root:[Trade Bot] 2025-07-02 12:53:44,134 - INFO - [ConnectionManager-oanda] Successfully connected to oanda
INFO:root:[Trade Bot] 2025-07-02 12:53:44,134 - INFO - [TradingBot] OANDA client initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:44,135 - INFO - [TradingBot] Strategy fetched from Firestore: {"name":"RSI Momentum Strategy (Copy)","description":"","instruments":"EUR/USD","timeframe":"5m","tradingSession":["All"],"indicators":[{"id":"1749602603482ul9vl6hq0ba","type":"RSI","indicator_class":"RSI","parameters":{"period":14},"source":"price"},{"id":"1751436154920bjcb8zxdzi6","type":"BollingerBands","indicator_class":"BollingerBands","parameters":{"period":20,"devfactor":"4","offset":0},"source":"close"}],"entryRules":[{"tradeType":"long","indicator1":"1749602603482ul9vl6hq0ba","operator":"Crossing above","compareType":"value","indicator2":"","value":"30","logicalOperator":"AND","barRef":"close","id":"174960261544400fvqqs4jl8g"},{"id":"1749602641412osent9n2jgm","tradeType":"short","indicator1":"1749602603482ul9vl6hq0ba","operator":"Crossing below","compareType":"value","indicator2":"","value":"70","barRef":"close"}],"exitRules":[{"tradeType":"long","indicator1":"1749602603482ul9vl6hq0ba","operator":"Crossing below","compareType":"value","indicator2":"","value":"70","logicalOperator":"AND","barRef":"close","id":"1749602650643inblkvldmif"},{"id":"1749602657619tru9v8puuln","tradeType":"short","indicator1":"1749602603482ul9vl6hq0ba","operator":"Crossing above","compareType":"value","indicator2":"","value":"30","barRef":"close"}],"riskManagement":{"riskPercentage":"0.1","riskRewardRatio":"2","stopLossMethod":"indicator","fixedPips":"","indicatorBasedSL":{"indicator":"bollinger","parameters":{"period":20,"stdDev":"4"}},"lotSize":"","maxDailyLoss":"5%","maxPositionSize":"10%","runtime":7,"totalProfitTarget":"20%","totalLossLimit":"10%","avoidHighSpread":false,"stopLoss":"1","stopLossUnit":"percentage","takeProfit":"2","takeProfitUnit":"percentage"}}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,135 - INFO - [TradingBot] 🚀 Using Pub/Sub market data provider (real-time via WebSocket ingestion)
INFO:root:[Trade Bot] 2025-07-02 12:53:44,136 - INFO - [MarketConditions] MarketConditions initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:44,136 - INFO - [MarketDataProvider] MarketDataProvider initialized with API key
INFO:root:[Trade Bot] 2025-07-02 12:53:44,136 - INFO - [MarketConditions] MarketConditions initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:44,136 - INFO - [PubSubMarketDataProvider] 📊 Historical data provider and market conditions initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:44,137 - INFO - [PubSubMarketDataProvider] 📡 PubSubMarketDataProvider initialized for trade-bot: bot-V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] 2025-07-02 12:53:44,137 - INFO - [PubSubMarketDataProvider] 🏗️ Project ID: oryntrade
INFO:root:[Trade Bot] 2025-07-02 12:53:44,137 - INFO - [PubSubMarketDataProvider] 🚀 Starting PubSubMarketDataProvider...
INFO:root:[Trade Bot] 2025-07-02 12:53:44,151 - INFO - [PubSubMarketDataProvider] ✅ Subscription exists: trade-bot-bot-V7u9KHh8Hk2Ks2fciNOi-minute-aggregates
INFO:root:[Trade Bot] 2025-07-02 12:53:44,151 - INFO - [PubSubMarketDataProvider] 👂 Starting Pub/Sub subscriber...
INFO:root:[Trade Bot] 2025-07-02 12:53:44,151 - INFO - [PubSubMarketDataProvider] ✅ PubSubMarketDataProvider started
INFO:root:[Trade Bot] 2025-07-02 12:53:44,152 - INFO - [PubSubMarketDataProvider] ℹ️ Note: Real-time subscription will be activated during proper initialization
INFO:root:[Trade Bot] 2025-07-02 12:53:44,152 - INFO - [TradingBot] Market data provider initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:44,152 - INFO - [PubSubMarketDataProvider] 🔄 Listening for minute aggregates on: trade-bot-bot-V7u9KHh8Hk2Ks2fciNOi-minute-aggregates
INFO:root:[Trade Bot] 2025-07-02 12:53:44,152 - INFO - [BaseStrategy] Successfully parsed strategy JSON string
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Strategy dict before validation: {"name": "RSI Momentum Strategy (Copy)", "description": "", "instruments": "EUR/USD", "timeframe": "5m", "tradingSession": ["All"], "indicators": [{"id": "1749602603482ul9vl6hq0ba", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 14}, "source": "price"}, {"id": "1751436154920bjcb8zxdzi6", "type": "BollingerBands", "indicator_class": "BollingerBands", "parameters": {"period": 20, "devfactor": "4", "offset": 0}, "source": "close"}], "entryRules": [{"tradeType": "long", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "logicalOperator": "AND", "barRef": "close", "id": "174960261544400fvqqs4jl8g"}, {"id": "1749602641412osent9n2jgm", "tradeType": "short", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "barRef": "close"}], "exitRules": [{"tradeType": "long", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "logicalOperator": "AND", "barRef": "close", "id": "1749602650643inblkvldmif"}, {"id": "1749602657619tru9v8puuln", "tradeType": "short", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "barRef": "close"}], "riskManagement": {"riskPercentage": "0.1", "riskRewardRatio": "2", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "bollinger", "parameters": {"period": 20, "stdDev": "4"}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Validating strategy dict: {"name": "RSI Momentum Strategy (Copy)", "description": "", "instruments": "EUR/USD", "timeframe": "5m", "tradingSession": ["All"], "indicators": [{"id": "1749602603482ul9vl6hq0ba", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 14}, "source": "price"}, {"id": "1751436154920bjcb8zxdzi6", "type": "BollingerBands", "indicator_class": "BollingerBands", "parameters": {"period": 20, "devfactor": "4", "offset": 0}, "source": "close"}], "entryRules": [{"tradeType": "long", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "logicalOperator": "AND", "barRef": "close", "id": "174960261544400fvqqs4jl8g"}, {"id": "1749602641412osent9n2jgm", "tradeType": "short", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "barRef": "close"}], "exitRules": [{"tradeType": "long", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "logicalOperator": "AND", "barRef": "close", "id": "1749602650643inblkvldmif"}, {"id": "1749602657619tru9v8puuln", "tradeType": "short", "indicator1": "1749602603482ul9vl6hq0ba", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "barRef": "close"}], "riskManagement": {"riskPercentage": "0.1", "riskRewardRatio": "2", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "bollinger", "parameters": {"period": 20, "stdDev": "4"}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Risk management data: {"riskPercentage": "0.1", "riskRewardRatio": "2", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "bollinger", "parameters": {"period": 20, "stdDev": "4"}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Using strategy format with deployment additions (stopLoss/takeProfit)
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found stopLoss: 1
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found takeProfit: 2
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: riskPercentage = 0.1
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: riskRewardRatio = 2
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: stopLossMethod = indicator
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: fixedPips =
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: indicatorBasedSL = {'indicator': 'bollinger', 'parameters': {'period': 20, 'stdDev': '4'}}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: lotSize =
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: maxDailyLoss = 5%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: maxPositionSize = 10%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: runtime = 7
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: totalProfitTarget = 20%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: totalLossLimit = 10%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: stopLossUnit = percentage
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found strategy risk field: takeProfitUnit = percentage
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found tradingSession: ['All']
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Found timezone: UTC
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Strategy JSON validated successfully
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Extracted risk percentage: 0.1
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Extracted risk reward ratio: 2
INFO:root:[Trade Bot] 2025-07-02 12:53:44,153 - INFO - [BaseStrategy] Extracted stop loss method: indicator
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Extracted indicator based SL: {'indicator': 'bollinger', 'parameters': {'period': 20, 'stdDev': '4'}}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Using indicator-based stop loss with indicator: bollinger
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Using Bollinger Bands-based stop loss: period=20, stdDev=4, SL=20.0 pips (will be calculated dynamically)
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Calculated take profit from indicator-based SL: 40.0 pips
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Using stop loss: 20.0 pips
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Using take profit: 40.0 pips
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Extracted risk percentage: 0.1%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Extracted max daily loss: 5%%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Extracted max position size: 10%%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Extracted runtime: 7
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Extracted total profit target: 20%%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Extracted total loss limit: 10%%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Avoid high spread: False
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [BaseStrategy] Strategy RSI Momentum Strategy (Copy) initialized successfully
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [TradingBot] Strategy initialized with timeframe: 5m
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [TradingBot] Strategy instrument: EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [TradingBot] Strategy indicators: ['RSI', 'BollingerBands']
INFO:root:[Trade Bot] 2025-07-02 12:53:44,154 - INFO - [FirebaseClient] Updating human-readable rules for user PiZZ5xXSGDhpQz6tOJZSdNyLbNtO, strategy V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] 2025-07-02 12:53:44,163 - INFO - [FirebaseClient] Human-readable rules updated successfully
INFO:root:[Trade Bot] 2025-07-02 12:53:44,163 - INFO - [TradingBot] Human-readable rules saved to strategy document
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Using strategy-defined stop-loss: 20.0
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Using strategy-defined take-profit: 40.0
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Using new risk management structure
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Using risk percentage: 0.1%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Using risk reward ratio: 2.0
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Using stop loss method: indicator
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Calculated take profit: 4.0% based on risk reward ratio: 2.0
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Final stop-loss: 2.0%, take-profit: 4.0%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] ✅ Risk management parsed from config: maxDailyLoss='5%' → 5.0%, maxPositionSize='10%' → 10.0%, totalProfitTarget='20%' → 20.0%, totalLossLimit='10%' → 10.0%, avoidHighSpread=False
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Risk management initialized: daily_loss_limit=5.0%, max_position_size=10.0%, stop_loss=2.0%, take_profit=4.0%, risk_percentage=0.1%, runtime_days=7, total_profit_target=20.0%, total_loss_limit=10.0%, avoid_high_spread=False
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [TradingEngine] Attempt 1/5: Calling OANDA get_account_summary()
INFO:root:[Trade Bot] 2025-07-02 12:53:44,164 - INFO - [oanda_client] Making GET request to OANDA API: https://api-fxpractice.oanda.com/v3/accounts/101-001-********-001/summary
INFO:root:[Trade Bot] 2025-07-02 12:53:44,258 - INFO - [oanda_client] Response status code: 200
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [oanda_client] Account balance: 95027.4214
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [TradingEngine] Attempt 1/5: Received response: {'guaranteedStopLossOrderMode': 'DISABLED', 'hedgingEnabled': False, 'id': '101-001-********-001', 'createdTime': '2025-06-09T20:01:24.134769199Z', 'currency': 'USD', 'createdByUserID': ********, 'alias': 'Primary', 'marginRate': '0.02', 'lastTransactionID': '553', 'balance': '95027.4214', 'openTradeCount': 0, 'openPositionCount': 0, 'pendingOrderCount': 0, 'pl': '-4972.5786', 'resettablePL': '-4972.5786', 'resettablePLTime': '0', 'financing': '0.0000', 'commission': '0.0000', 'dividendAdjustment': '0', 'guaranteedExecutionFees': '0.0000', 'unrealizedPL': '0.0000', 'NAV': '95027.4214', 'marginUsed': '0.0000', 'marginAvailable': '95027.4214', 'positionValue': '0.0000', 'marginCloseoutUnrealizedPL': '0.0000', 'marginCloseoutNAV': '95027.4214', 'marginCloseoutMarginUsed': '0.0000', 'marginCloseoutPositionValue': '0.0000', 'marginCloseoutPercent': '0.00000', 'withdrawalLimit': '95027.4214', 'marginCallMarginUsed': '0.0000', 'marginCallPercent': '0.00000'}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [TradingEngine] Attempt 1/5: Parsed balance: 95027.4214
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [TradingEngine] ✅ Successfully retrieved account balance: $95027.4214
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [TradingEngine] Calculated absolute values: daily_loss_limit_abs=4751.37107, max_position_size_abs=9502.74214, total_profit_target_abs=19005.48428, total_loss_limit_abs=9502.74214
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [TradingEngine] Updating risk management metrics: totalProfit=0.0, totalLoss=0.0, dailyLoss=0.0, accountBalance=95027.4214
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [TradingEngine] Risk parameters being saved to Firebase: {'riskPercentage': '0.1%', 'riskRewardRatio': '2', 'stopLossMethod': 'indicator', 'fixedPips': None, 'indicatorBasedSL': {'indicator': 'bollinger', 'parameters': {'period': 20, 'stdDev': '4'}}, 'lotSize': None, 'maxDailyLoss': '5.0%', 'maxPositionSize': '10.0%', 'totalProfitTarget': '20.0%', 'totalLossLimit': '10.0%', 'runtime': 7, 'startTime': '2025-07-02T19:53:44.164157+00:00', 'avoidHighSpread': False}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,259 - INFO - [FirebaseClient] Updating risk management data for user PiZZ5xXSGDhpQz6tOJZSdNyLbNtO, strategy V7u9KHh8Hk2Ks2fciNOi
INFO:root:[Trade Bot] 2025-07-02 12:53:44,266 - INFO - [FirebaseClient] Successfully updated risk management data
INFO:root:[Trade Bot] 2025-07-02 12:53:44,266 - INFO - [TradingEngine] Updated risk management metrics in Firebase
INFO:root:[Trade Bot] 2025-07-02 12:53:44,266 - INFO - [TradingEngine] 🔍 DEBUG: Checking if market data provider supports real-time subscriptions...
INFO:root:[Trade Bot] 2025-07-02 12:53:44,266 - INFO - [TradingEngine] 📈 Subscribing to real-time data for EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:44,266 - INFO - [PubSubMarketDataProvider] 📈 Subscribing to EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:44,282 - INFO - [PubSubMarketDataProvider] 📤 Sent subscription command: subscribe EUR/USD (ID: 5)
INFO:root:[Trade Bot] 2025-07-02 12:53:44,282 - INFO - [PubSubMarketDataProvider] ✅ Successfully subscribed to EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:44,282 - INFO - [TradingEngine] ✅ Real-time subscription request sent
INFO:root:[Trade Bot] 2025-07-02 12:53:44,283 - INFO - [TradingEngine] Initialized candle builder for EUR/USD 5m
INFO:root:[Trade Bot] 2025-07-02 12:53:44,283 - INFO - [TradingBot] Trading engine initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:44,283 - INFO - [FastUpdateLoop] Initialized risk thresholds: daily_loss_limit=5.0%, total_profit_target=20.0%, total_loss_limit=10.0%
INFO:root:[Trade Bot] 2025-07-02 12:53:44,284 - INFO - [TradingBot] Fast update loop initialized
INFO:root:[Trade Bot] 2025-07-02 12:53:44,284 - INFO - [TradingBot] 🔥 Warming up trading engine...
INFO:root:[Trade Bot] 2025-07-02 12:53:44,284 - INFO - [TradingEngine] Warming up trading engine...
INFO:root:[Trade Bot] 2025-07-02 12:53:44,542 - INFO - [MarketConditions] Polygon API: Forex Market is open
INFO:root:[Trade Bot] 2025-07-02 12:53:44,795 - INFO - [MarketConditions] Got 0 upcoming forex holidays from Polygon API
INFO:root:[Trade Bot] 2025-07-02 12:53:44,796 - INFO - [MarketDataProvider] Forex Market is open: True
INFO:root:[Trade Bot] 2025-07-02 12:53:44,796 - INFO - [MarketDataProvider] Active trading centers: New York
INFO:root:[Trade Bot] 2025-07-02 12:53:44,796 - INFO - [MarketDataProvider] Market activity: medium
INFO:root:[Trade Bot] 2025-07-02 12:53:44,796 - INFO - [TradingEngine] Market status: {'is_open': True, 'reason': None, 'current_time_utc': '2025-07-02T19:53:44.284303+00:00', 'server_time': '2025-07-02T15:53:44-04:00', 'active_centers': ['New York'], 'market_activity': 'medium', 'source': 'polygon_api'}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,796 - INFO - [TradingEngine] Trading sessions: ['All']
INFO:root:[Trade Bot] 2025-07-02 12:53:44,796 - INFO - [TradingEngine] Trading session info: {'in_session': True, 'active_sessions': ['All'], 'current_time_utc': '2025-07-02T19:53:44.796663+00:00'}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,797 - INFO - [oanda_client] Making GET request to OANDA API: https://api-fxpractice.oanda.com/v3/accounts/101-001-********-001/openTrades
INFO:root:[Trade Bot] 2025-07-02 12:53:44,916 - INFO - [oanda_client] Response status code: 200
INFO:root:[Trade Bot] 2025-07-02 12:53:44,917 - INFO - [oanda_client] OANDA open trades response data: {'trades': [], 'lastTransactionID': '553'}
INFO:root:[Trade Bot] 2025-07-02 12:53:44,917 - INFO - [TradingEngine] 🔍 Market data provider type: PubSubMarketDataProvider
INFO:root:[Trade Bot] 2025-07-02 12:53:44,918 - INFO - [TradingEngine] 🚀 Using PubSub provider - initializing with proper sequence
INFO:root:[Trade Bot] 2025-07-02 12:53:44,918 - INFO - [PubSubMarketDataProvider] 🚀 Initializing PubSub provider for EUR/USD 5m
INFO:root:[Trade Bot] 2025-07-02 12:53:44,918 - INFO - [PubSubMarketDataProvider] 📊 Step 1: Fetching 1000 historical candles for FIFO buffer
INFO:root:[Trade Bot] 2025-07-02 12:53:44,918 - INFO - [PubSubMarketDataProvider] 📊 Fetching 1000 historical candles for EUR/USD (timespan: 5m, multiplier: 1)
INFO:root:[Trade Bot] 2025-07-02 12:53:44,918 - INFO - [MarketDataProvider] Fetching candles for EUR/USD with timespan 5m, multiplier 1, count 1000
INFO:root:[Trade Bot] 2025-07-02 12:53:44,918 - INFO - [MarketDataProvider] Extracted multiplier 5 from timespan 5m
INFO:root:[Trade Bot] 2025-07-02 12:53:44,919 - INFO - [MarketDataProvider] Formatted symbol: C:EURUSD
INFO:root:[Trade Bot] 2025-07-02 12:53:44,919 - INFO - [MarketDataProvider] Request URL: https://api.polygon.io/v2/aggs/ticker/C:EURUSD/range/5/minute/*************/*************
INFO:root:[Trade Bot] 2025-07-02 12:53:44,919 - INFO - [MarketDataProvider] Making request to Polygon API...
INFO:root:[Trade Bot] 2025-07-02 12:53:45,135 - INFO - [oanda_client] Running delayed connection check...
INFO:root:[Trade Bot] 2025-07-02 12:53:45,135 - INFO - [oanda_client] Using cached account summary data
INFO:root:[Trade Bot] 2025-07-02 12:53:45,135 - INFO - [oanda_client] Connection check successful
INFO:root:[Trade Bot] 2025-07-02 12:53:45,380 - INFO - [MarketDataProvider] Response status code: 200
INFO:root:[Trade Bot] 2025-07-02 12:53:45,381 - INFO - [MarketDataProvider] Response data status: OK
INFO:root:[Trade Bot] 2025-07-02 12:53:45,382 - INFO - [MarketDataProvider] Fetched 845 candles from Polygon for EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:45,382 - INFO - [MarketDataProvider] Formatted 845 candles
INFO:root:[Trade Bot] 2025-07-02 12:53:45,383 - INFO - [PubSubMarketDataProvider] ✅ Successfully fetched 845 historical candles for EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:45,383 - INFO - [PubSubMarketDataProvider] ✅ Step 1 complete: 845 historical candles fetched
INFO:root:[Trade Bot] 2025-07-02 12:53:45,383 - INFO - [PubSubMarketDataProvider] 🔄 Step 2: Calculating current period gap for 5m
INFO:root:[Trade Bot] 2025-07-02 12:53:45,383 - INFO - [PubSubMarketDataProvider] ✅ Step 2 complete: No gap to fill
INFO:root:[Trade Bot] 2025-07-02 12:53:45,383 - INFO - [PubSubMarketDataProvider] 📡 Step 3: Subscribing to real-time data for EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:45,383 - INFO - [PubSubMarketDataProvider] ℹ️ Already subscribed to EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:45,383 - WARNING - [PubSubMarketDataProvider] ⚠️ Step 3 warning: Failed to subscribe to real-time data
INFO:root:[Trade Bot] 2025-07-02 12:53:45,384 - INFO - [PubSubMarketDataProvider] 🎉 Initialization complete for EUR/USD
INFO:root:[Trade Bot] 2025-07-02 12:53:45,384 - INFO - [PubSubMarketDataProvider] 📊 Final buffer: 845 candles ready for trading
INFO:root:[Trade Bot] 2025-07-02 12:53:45,384 - INFO - [TradingEngine] Fetched 845 formatted_candles
INFO:root:[Trade Bot] 2025-07-02 12:53:45,385 - INFO - [BaseStrategy] RSI Debug: 845 values, 844 changes, gains: 416 (total: 0.105600), losses: 410 (total: 0.098570)
INFO:root:[Trade Bot] 2025-07-02 12:53:45,385 - INFO - [BaseStrategy] RSI Initial averages: gain=0.000133, loss=0.000152
INFO:root:[Trade Bot] 2025-07-02 12:53:45,386 - INFO - [BaseStrategy] RSI calculated: 831 valid values, min=25.06, max=76.63, last_5=[55.61, 59.02, 55.16, 51.83, 54.49]
INFO:root:[Trade Bot] 2025-07-02 12:53:45,390 - INFO - [BaseStrategy] Bollinger Bands calculated: 826 valid values
INFO:root:[Trade Bot] 2025-07-02 12:53:45,390 - INFO - [TradingEngine] Attempt 1/5: Calling OANDA get_account_summary()
INFO:root:[Trade Bot] 2025-07-02 12:53:45,390 - INFO - [oanda_client] Using cached account summary data
INFO:root:[Trade Bot] 2025-07-02 12:53:45,390 - INFO - [TradingEngine] Attempt 1/5: Received response: {'guaranteedStopLossOrderMode': 'DISABLED', 'hedgingEnabled': False, 'id': '101-001-********-001', 'createdTime': '2025-06-09T20:01:24.134769199Z', 'currency': 'USD', 'createdByUserID': ********, 'alias': 'Primary', 'marginRate': '0.02', 'lastTransactionID': '553', 'balance': '95027.4214', 'openTradeCount': 0, 'openPositionCount': 0, 'pendingOrderCount': 0, 'pl': '-4972.5786', 'resettablePL': '-4972.5786', 'resettablePLTime': '0', 'financing': '0.0000', 'commission': '0.0000', 'dividendAdjustment': '0', 'guaranteedExecutionFees': '0.0000', 'unrealizedPL': '0.0000', 'NAV': '95027.4214', 'marginUsed': '0.0000', 'marginAvailable': '95027.4214', 'positionValue': '0.0000', 'marginCloseoutUnrealizedPL': '0.0000', 'marginCloseoutNAV': '95027.4214', 'marginCloseoutMarginUsed': '0.0000', 'marginCloseoutPositionValue': '0.0000', 'marginCloseoutPercent': '0.00000', 'withdrawalLimit': '95027.4214', 'marginCallMarginUsed': '0.0000', 'marginCallPercent': '0.00000'}
INFO:root:[Trade Bot] 2025-07-02 12:53:45,390 - INFO - [TradingEngine] Attempt 1/5: Parsed balance: 95027.4214
INFO:root:[Trade Bot] 2025-07-02 12:53:45,390 - INFO - [TradingEngine] ✅ Successfully retrieved account balance: $95027.4214
INFO:root:[Trade Bot] 2025-07-02 12:53:45,391 - ERROR - [TradingEngine] Error warming up trading engine - Error: float() argument must be a string or a real number, not 'NoneType'
INFO:root:[Trade Bot] Traceback (most recent call last):
INFO:root:[Trade Bot]   File "/Users/<USER>/workspace/oryn/trade-bot-service/core/trading_engine.py", line 479, in warm_up
INFO:root:[Trade Bot]     "value": float(value)
INFO:root:[Trade Bot]              ^^^^^^^^^^^^
INFO:root:[Trade Bot] TypeError: float() argument must be a string or a real number, not 'NoneType'
INFO:root:[Trade Bot] 2025-07-02 12:53:45,393 - ERROR - [TradingEngine] Error type: <class 'TypeError'>
INFO:root:[Trade Bot] 2025-07-02 12:53:45,393 - ERROR - [TradingEngine] Error details: {}
INFO:root:[Trade Bot] 2025-07-02 12:53:45,393 - ERROR - [TradingBot] Trading engine warmup failed: float() argument must be a string or a real number, not 'NoneType' - Error: Trading engine warmup failed: float() argument must be a string or a real number, not 'NoneType'
INFO:root:[Trade Bot] NoneType: None
INFO:root:[Trade Bot] 2025-07-02 12:53:45,393 - ERROR - [TradingBot] Error type: <class 'Exception'>
INFO:root:[Trade Bot] 2025-07-02 12:53:45,393 - ERROR - [TradingBot] Error details: {}
INFO:root:[Trade Bot] 2025-07-02 12:53:45,393 - ERROR - [TradingBot] Failed to initialize trading bot - Error: Trading engine warmup failed: float() argument must be a string or a real number, not 'NoneType'
INFO:root:[Trade Bot] Traceback (most recent call last):
INFO:root:[Trade Bot]   File "/Users/<USER>/workspace/oryn/trade-bot-service/main.py", line 176, in __init__
INFO:root:[Trade Bot]     raise Exception(error_msg)
INFO:root:[Trade Bot] Exception: Trading engine warmup failed: float() argument must be a string or a real number, not 'NoneType'
INFO:root:[Trade Bot] 2025-07-02 12:53:45,394 - ERROR - [TradingBot] Error type: <class 'Exception'>
INFO:root:[Trade Bot] 2025-07-02 12:53:45,394 - ERROR - [TradingBot] Error details: {}
INFO:root:[Trade Bot] Traceback (most recent call last):
INFO:root:[Trade Bot]   File "/Users/<USER>/workspace/oryn/trade-bot-service/main.py", line 1348, in <module>
INFO:root:[Trade Bot]     main()
INFO:root:[Trade Bot]   File "/Users/<USER>/workspace/oryn/trade-bot-service/main.py", line 1344, in main
INFO:root:[Trade Bot]     bot = TradingBot(os.getenv("USER_ID"), os.getenv("STRATEGY_ID"))
INFO:root:[Trade Bot]           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
INFO:root:[Trade Bot]   File "/Users/<USER>/workspace/oryn/trade-bot-service/main.py", line 176, in __init__
INFO:root:[Trade Bot]     raise Exception(error_msg)
INFO:root:[Trade Bot] Exception: Trading engine warmup failed: float() argument must be a string or a real number, not 'NoneType'
INFO:root:Process 70212 exited with code 1