{"name": "EUR/USD 1m strategy", "description": "This strategy trades the EUR/USD pair on a 1-minute timeframe. It uses the MACD and EMA indicators to generate entry and exit signals. It enters a long trade when the MACD line crosses above the signal line and the price is above the EMA. It exits the trade when the MACD line crosses below the signal line. The strategy uses a risk management system that risks 1% of the account balance per trade and employs a risk-to-reward ratio of 2:1.", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "1746646401769ml6p3j64kie", "type": "MACD", "indicator_class": "MACD", "parameters": {"fast": 12, "slow": 26, "signal": 9}, "source": "close"}, {"id": "1746646401770ml6p3j64kie", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 20}, "source": "close"}, {"id": "1751244297011g1nkswwptye", "type": "ATR", "indicator_class": "ATR", "parameters": {"period": 14, "multiplier": 2}, "source": "price"}], "entryRules": [{"id": "1746646401771ml6p3j64kie", "tradeType": "long", "indicator1": "1746646401769ml6p3j64kie", "operator": "Crossing above", "compareType": "indicator", "indicator2": "1746646401769ml6p3j64kie", "value": "", "logicalOperator": "AND", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}, {"id": "1746646401772ml6p3j64kie", "tradeType": "long", "indicator1": "price", "operator": "Crossing above", "compareType": "indicator", "indicator2": "1746646401770ml6p3j64kie", "value": "", "logicalOperator": "AND", "barRef": "close"}], "exitRules": [{"id": "1746646401773ml6p3j64kie", "tradeType": "long", "indicator1": "1746646401769ml6p3j64kie", "operator": "Crossing below", "compareType": "indicator", "indicator2": "1746646401769ml6p3j64kie", "value": "", "logicalOperator": "AND", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}], "riskManagement": {"riskPercentage": "2", "riskRewardRatio": "2", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "atr", "parameters": {"period": 14, "multiplier": 2}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "entryLongGroupOperator": "AND", "user_id": "2tixoSldb3K4Fmz015AZQbTm2QfJ", "id": "4juWaL0kWkfpEjCwF2Lq"}