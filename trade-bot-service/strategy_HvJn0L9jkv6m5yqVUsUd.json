{"name": "RSI Strategy", "description": "", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "1748979219181lkag236daf", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 14}, "source": "price"}], "entryRules": [{"tradeType": "long", "indicator1": "1748979219181lkag236daf", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "logicalOperator": "AND", "barRef": "close", "id": "1748979229066w6y6k4a7zmo"}, {"id": "174897923773644lioo2fa61", "tradeType": "short", "indicator1": "1748979219181lkag236daf", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "barRef": "close"}], "exitRules": [{"tradeType": "long", "indicator1": "1748979219181lkag236daf", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "logicalOperator": "AND", "barRef": "close", "id": "17489792478172dvqbfj16m4"}, {"id": "1748979262378ntrkd894id", "tradeType": "short", "indicator1": "1748979219181lkag236daf", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "barRef": "close"}], "riskManagement": {"riskPercentage": "1", "riskRewardRatio": "2", "stopLossMethod": "risk", "fixedPips": "", "indicatorBasedSL": {"indicator": "", "parameters": {}}, "lotSize": "0.1", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "user_id": "Yvsoz4b9taBpaUiD39zJKhPpgy0j", "id": "HvJn0L9jkv6m5yqVUsUd"}