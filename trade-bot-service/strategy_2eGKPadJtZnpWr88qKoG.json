{"name": "<PERSON><PERSON>'s Strat", "description": "", "instruments": "EUR/USD", "timeframe": "5m", "tradingSession": ["All"], "indicators": [{"id": "1750221966236ocalyn4e7z", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 9}, "source": "price"}, {"id": "1750221974801eeeypo0gr8", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 21}, "source": "price"}, {"id": "1750221984384fq8v2kmr9p", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 8}, "source": "price"}, {"id": "1750221991613p67eatxn0k9", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 21}, "source": "price"}, {"id": "1750222045489z9374ng6c5", "type": "BollingerBands", "indicator_class": "BollingerBands", "parameters": {"period": 20, "devfactor": 1.5, "offset": 0}, "source": "price"}], "entryRules": [{"id": "175023924273006ebuj0xeius", "tradeType": "long", "indicator1": "1750221966236ocalyn4e7z", "operator": ">", "compareType": "value", "indicator2": "", "value": "40", "barRef": "close"}, {"id": "1750239267400rj9k116fvci", "tradeType": "long", "indicator1": "1750221984384fq8v2kmr9p", "operator": ">", "compareType": "indicator", "indicator2": "1750221991613p67eatxn0k9", "value": "", "barRef": "close"}, {"id": "1750239299198n0x9mssgmnq", "tradeType": "short", "indicator1": "1750221966236ocalyn4e7z", "operator": "<", "compareType": "value", "indicator2": "", "value": "60", "barRef": "close"}, {"id": "17502393339477rjeur316zw", "tradeType": "short", "indicator1": "1750221984384fq8v2kmr9p", "operator": "<", "compareType": "indicator", "indicator2": "1750221991613p67eatxn0k9", "value": "", "barRef": "close"}], "exitRules": [{"id": "1750222481734f4brixyfb2r", "tradeType": "short", "indicator1": "1750221966236ocalyn4e7z", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "barRef": "close"}, {"id": "1750239400605j2fl04sob8", "tradeType": "long", "indicator1": "1750221966236ocalyn4e7z", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "barRef": "close"}], "riskManagement": {"riskPercentage": "5", "riskRewardRatio": "2", "stopLossMethod": "fixed", "fixedPips": "15", "indicatorBasedSL": {"indicator": "", "parameters": {}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "entryLongGroupOperator": "AND", "entryShortGroupOperator": "AND", "user_id": "2tixoSldb3K4Fmz015AZQbTm2QfJ", "id": "2eGKPadJtZnpWr88qKoG"}