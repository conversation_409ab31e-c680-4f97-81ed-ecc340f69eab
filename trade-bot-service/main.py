import os
import time
import signal
import sys
import json
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta, timezone
import requests

# Exit codes for different shutdown scenarios
class ExitCodes:
    SUCCESS = 0                    # Normal shutdown
    SYSTEM_ERROR = 1              # System failure (should restart)
    BUSINESS_LOGIC_SHUTDOWN = 2   # Business decision (should NOT restart)
    CONFIGURATION_ERROR = 3       # Config issue (should NOT restart)
    AUTHENTICATION_ERROR = 4      # Auth issue (should NOT restart)
    INSUFFICIENT_MARGIN = 5       # Margin error (should NOT restart)
    INVALID_STRATEGY = 6          # Strategy validation error (should NOT restart)
    MARKET_CLOSED = 7            # Market closed shutdown (should NOT restart)
from google.cloud import pubsub_v1
import google.api_core.exceptions
import threading
from core.config_manager import ConfigManager
from core.trading_engine import TradingEngine
from core.fast_update_loop import FastUpdateLoop
from data.hybrid_market_data import HybridMarketDataWrapper
from data.pubsub_market_data import PubSubMarketDataProvider
from execution.oanda_client import OandaClient
from utils.logger import Logger
from utils.error_messages import get_user_friendly_error_message
from execution.firebase_client import FirebaseClient
from strategies.base_strategy import BaseStrategy
from utils.health_client import HealthClient
from utils.recovery_manager import RecoveryManager
from utils.retry_utils import OrderTracker
from utils.graceful_shutdown import GracefulShutdownHandler
from utils.state_restoration import StateRestoration
from utils.pause_timeout_manager import PauseTimeoutManager
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

# Contacted Polygon support and they recommended a buffer of 1 second
# to ensure the new candle data is available.
POLYGON_BUFFER_SECONDS = 1

class HealthHandler(BaseHTTPRequestHandler):
    """Simple HTTP handler for health checks."""

    def __init__(self, trading_bot, *args, **kwargs):
        self.trading_bot = trading_bot
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """Handle GET requests."""
        if self.path == '/health':
            try:
                # Check if trading bot is running
                if hasattr(self.trading_bot, 'running') and self.trading_bot.running:
                    status = "healthy"
                    status_code = 200
                else:
                    status = "unhealthy"
                    status_code = 503

                # Get pause timeout status
                pause_status = {}
                if hasattr(self.trading_bot, 'get_pause_timeout_status'):
                    try:
                        pause_status = self.trading_bot.get_pause_timeout_status()
                    except Exception:
                        pass

                response = {
                    "status": status,
                    "service": "trade-bot",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "strategy_id": getattr(self.trading_bot, 'strategy_id', 'unknown'),
                    "is_paused": getattr(self.trading_bot, 'is_paused', False),
                    "pause_timeout": pause_status
                }

                self.send_response(status_code)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode())

            except Exception as e:
                self.send_response(500)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                error_response = {
                    "status": "error",
                    "message": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                self.wfile.write(json.dumps(error_response).encode())
        else:
            self.send_response(404)
            self.end_headers()

    def log_message(self, format, *args):
        """Suppress default logging."""
        pass

def create_health_handler(trading_bot):
    """Create a health handler with trading bot reference."""
    def handler(*args, **kwargs):
        return HealthHandler(trading_bot, *args, **kwargs)
    return handler

class TradingBot:
    """Main trading bot service."""

    def __init__(self, user_id: str, strategy_id: str):
        """Initialize trading bot."""
        self.logger = Logger("TradingBot")
        self.user_id = user_id
        self.strategy_id = strategy_id
        self.market_data_provider = None  # Will be initialized based on configuration
        self.strategy: BaseStrategy
        self.trading_engine: TradingEngine
        self.oanda_client: OandaClient
        self.firebase_client: FirebaseClient
        self.fast_update_loop: FastUpdateLoop = None
        self.strategy_controller_url: str = os.getenv("STRATEGY_CONTROLLER_URL", "https://control-strategy-ihjc6tjxia-uc.a.run.app")
        self.is_paused: bool = False
        self.should_exit: bool = False  # Flag to indicate if the bot should exit
        self.subscriber = pubsub_v1.SubscriberClient()
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
        self.command_topic_name = os.getenv('COMMAND_TOPIC_NAME', 'strategy-commands')
        self.command_subscription_name = f"strategy-{self.strategy_id}-commands"
        self.command_subscription_path = self.subscriber.subscription_path(
            self.project_id, self.command_subscription_name
        )
        self.running = True
        self.immediate_stop = False  # Flag for immediate stopping on critical errors
        self.stop_reason = None  # Track why the bot stopped (to preserve status)
        self.command_thread = None
        self.health_server = None
        self.health_thread = None

        # Initialize health client with the correct URL
        # If running locally, use localhost instead of strategy-controller
        controller_url = self.strategy_controller_url
        if "strategy-controller" in controller_url and not os.getenv("KUBERNETES_SERVICE_HOST"):
            controller_url = controller_url.replace("strategy-controller", "localhost")
            self.logger.log_info(f"Using modified URL for health client: {controller_url}")

        self.health_client = HealthClient(self.strategy_id, self.user_id, controller_url)

        # Initialize graceful shutdown handler
        self.graceful_shutdown_handler = None  # Will be initialized after other components

        # Initialize state restoration
        self.state_restoration = StateRestoration(self)

        # Initialize pause timeout manager
        self.pause_timeout_manager = PauseTimeoutManager(self)

        # Initialize recovery manager
        self.recovery_manager = RecoveryManager(self.strategy_id, logger=self.logger)

        # Initialize order tracker
        self.order_tracker = OrderTracker(logger=self.logger)

        # Create a file to indicate if the bot has been explicitly stopped
        self.stop_flag_file = f"/tmp/trade_bot_stopped_{self.strategy_id}"
        self.logger.log_info(f"🔍 Stop flag file path: {self.stop_flag_file}")

        # Check if the stop flag file exists and remove it on startup
        if os.path.exists(self.stop_flag_file):
            try:
                os.remove(self.stop_flag_file)
                self.logger.log_info(f"Removed existing stop flag file: {self.stop_flag_file}")
            except Exception as e:
                self.logger.log_warning(f"Could not remove stop flag file: {str(e)}")
        else:
            self.logger.log_info(f"✅ No existing stop flag file found")

        try:
            # Get user_id from environment
            user_id = os.getenv("USER_ID")
            if not user_id:
                self.logger.log_error("⚙️ USER_ID environment variable not set")
                self.shutdown_with_exit_code(
                    ExitCodes.CONFIGURATION_ERROR,
                    "USER_ID environment variable not set"
                )
            self.user_id = str(user_id)
            self.logger.log_info(f"User ID found: {self.user_id}")

            # Get strategy_id from environment
            strategy_id = os.getenv("STRATEGY_ID")
            if not strategy_id:
                self.logger.log_error("⚙️ STRATEGY_ID environment variable not set")
                self.shutdown_with_exit_code(
                    ExitCodes.CONFIGURATION_ERROR,
                    "STRATEGY_ID environment variable not set"
                )
            self.strategy_id = str(strategy_id)
            self.logger.log_info(f"Strategy ID found: {self.strategy_id}")

            # Initialize Firebase client first
            self.firebase_client = FirebaseClient(user_id=self.user_id, strategy_id=self.strategy_id)

            polygon_api_key = os.getenv("POLYGON_API_KEY")
            if not polygon_api_key:
                raise ValueError("POLYGON_API_KEY environment variable not set")

            # Update bot status in Firebase
            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.INITIALIZING,
                {"message": "Trading bot initializing..."}
            )

            self.market_data_provider = self._create_market_data_provider(str(polygon_api_key))
            self.logger.log_info("Market data provider initialized")

            # Initialize OANDA client
            try:
                self.oanda_client = OandaClient(self.firebase_client, health_client=self.health_client)
                self.logger.log_info("OANDA client initialized")
            except ValueError as e:
                if "credentials" in str(e).lower():
                    self.logger.log_error(f"🔐 OANDA authentication failed: {e}")
                    self.shutdown_with_exit_code(
                        ExitCodes.AUTHENTICATION_ERROR,
                        f"OANDA authentication failed: {e}"
                    )
                else:
                    raise  # Re-raise if not a credential error

            # Load strategy from Firestore
            try:
                strategy_doc = self.firebase_client.db.collection('users').document(self.user_id)\
                    .collection('submittedStrategies').document(self.strategy_id).get()
                if not strategy_doc.exists:
                    self.logger.log_error(f"📄 Strategy {self.strategy_id} not found for user {self.user_id}")
                    self.shutdown_with_exit_code(
                        ExitCodes.INVALID_STRATEGY,
                        f"Strategy {self.strategy_id} not found"
                    )
                strategy_json = strategy_doc.get('strategy_json')
                if not strategy_json:
                    self.logger.log_error(f"📄 Strategy JSON not found for strategy {self.strategy_id}")
                    self.shutdown_with_exit_code(
                        ExitCodes.INVALID_STRATEGY,
                        f"Strategy JSON not found for strategy {self.strategy_id}"
                    )
                self.logger.log_info(f"Strategy fetched from Firestore: {strategy_json}")

                # Initialize strategy
                self.strategy = BaseStrategy(strategy_json)
                self.logger.log_info(f"Strategy initialized with timeframe: {self.strategy.timeframe}")
                self.logger.log_info(f"Strategy instrument: {self.strategy.instrument}")
            except Exception as e:
                if "strategy" in str(e).lower() or "json" in str(e).lower():
                    self.logger.log_error(f"📄 Invalid strategy configuration: {e}")
                    self.shutdown_with_exit_code(
                        ExitCodes.INVALID_STRATEGY,
                        f"Invalid strategy configuration: {e}"
                    )
                else:
                    raise  # Re-raise if not a strategy validation error
            self.logger.log_info(f"Strategy indicators: {[ind.get('type', ind.get('indicator_class')) for ind in self.strategy.indicators]}")

            # Generate and save human-readable rules
            human_readable_rules = self.strategy.generate_human_readable_rules()
            self.firebase_client.update_human_readable_rules(
                human_readable_rules
            )
            self.logger.log_info("Human-readable rules saved to strategy document")

            # Initialize trading engine
            self.trading_engine = TradingEngine(
                strategy=self.strategy,
                market_data_provider=self.market_data_provider,
                oanda_client=self.oanda_client,
                user_id=self.user_id,
                strategy_id=self.strategy_id,
                firebase_client=self.firebase_client,
                health_client=self.health_client
            )
            self.logger.log_info("Trading engine initialized")

            # Initialize fast update loop
            self.fast_update_loop = FastUpdateLoop(
                trading_engine=self.trading_engine,
                oanda_client=self.oanda_client,
                firebase_client=self.firebase_client,
                update_interval=5  # Update every 5 seconds
            )
            self.logger.log_info("Fast update loop initialized")

            # Give trading engine access to fast update loop for duplicate detection
            self.trading_engine.fast_update_loop = self.fast_update_loop

            # Warm up trading engine with proper initialization sequence
            self.logger.log_info("🔥 Warming up trading engine...")
            warmup_result = self.trading_engine.warm_up()
            if warmup_result.get("status") != "success":
                error_msg = f"Trading engine warmup failed: {warmup_result.get('message', 'Unknown error')}"
                self.logger.log_error(Exception(error_msg), error_msg)
                raise Exception(error_msg)
            self.logger.log_info("✅ Trading engine warmed up successfully")

            # Setup signal handlers
            self.logger.log_info("Setting up signal handlers...")
            signal.signal(signal.SIGINT, self._handle_shutdown)
            signal.signal(signal.SIGTERM, self._handle_shutdown)
            self.logger.log_info("Signal handlers configured")

            # Update bot status in Firebase
            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.INITIALIZED,
                {"message": "Trading bot initialized successfully"}
            )

            # Inform the user that the trading bot has been initialized successfully
            self.firebase_client.append_user_log(
                "☀️ Trading bot initialized successfully",
            )

            self.logger.log_info("Trading bot initialized successfully")

        except Exception as e:
            self.logger.log_error(e, "Failed to initialize trading bot")
            raise

    def _create_market_data_provider(self, polygon_api_key: str):
        """
        Create the appropriate market data provider based on configuration.

        Args:
            polygon_api_key: Polygon.io API key

        Returns:
            Market data provider instance
        """
        self.logger.log_info(f"🔍 DEBUG: _create_market_data_provider called")
        # Check environment variable for market data provider type
        provider_type = os.getenv('MARKET_DATA_PROVIDER', 'hybrid').lower()

        if provider_type == 'pubsub':
            self.logger.log_info("🚀 Using Pub/Sub market data provider (real-time via WebSocket ingestion)")

            def on_candle_callback(candle_data):
                """Handle real-time candle data from Pub/Sub."""
                self.logger.log_info(f"📊 Received real-time candle: {candle_data['symbol']} - {candle_data['close']}")

                # Process the real-time candle data
                try:
                    # Parse timestamp from ISO format to Unix timestamp
                    timestamp_str = candle_data.get('timestamp', '')
                    if timestamp_str:
                        from datetime import datetime
                        # Parse ISO format timestamp and convert to Unix timestamp
                        dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        unix_timestamp = int(dt.timestamp())
                    else:
                        unix_timestamp = 0

                    # Convert the minute aggregate to candle format
                    new_candle = {
                        "time": unix_timestamp,
                        "open": float(candle_data.get('open', 0)),
                        "high": float(candle_data.get('high', 0)),
                        "low": float(candle_data.get('low', 0)),
                        "close": float(candle_data.get('close', 0)),
                        "volume": int(candle_data.get('volume', 0))
                    }

                    # Add to candle builder for higher timeframe processing
                    if hasattr(self.trading_engine, 'candle_builder') and self.trading_engine.candle_builder:
                        completed_candle = self.trading_engine.candle_builder.add_1m_candle(new_candle)

                        if completed_candle:
                            self.logger.log_info(f"✅ Completed {self.trading_engine.strategy.timeframe} candle from real-time data")
                            # Trigger strategy update with new completed candle
                            self._process_new_completed_candle(completed_candle)
                    else:
                        self.logger.log_warning("⚠️ No candle builder available for real-time processing")

                except Exception as e:
                    self.logger.log_error(f"❌ Error processing real-time candle: {e}")

            provider = PubSubMarketDataProvider(
                project_id=self.project_id,
                trade_bot_id=f"bot-{self.strategy_id}",
                on_candle_callback=on_candle_callback,
                polygon_api_key=polygon_api_key
            )

            # Start the provider
            provider.start()

            return provider
        else:
            # Default to hybrid provider (polling)
            self.logger.log_info("🔄 Using hybrid market data provider (polling)")
            return HybridMarketDataWrapper(api_key=polygon_api_key)

    def _start_health_server(self):
        """Start the health server for Kubernetes health checks."""
        try:
            health_port = int(os.getenv('HEALTH_API_PORT', '8001'))
            handler = create_health_handler(self)
            self.health_server = HTTPServer(('0.0.0.0', health_port), handler)

            def run_server():
                self.logger.log_info(f"🏥 Health server starting on port {health_port}")
                self.health_server.serve_forever()

            self.health_thread = threading.Thread(target=run_server, daemon=True)
            self.health_thread.start()
            self.logger.log_info(f"✅ Health server started on port {health_port}")

        except Exception as e:
            self.logger.log_error(f"❌ Failed to start health server: {e}")

    def _stop_health_server(self):
        """Stop the health server."""
        try:
            if self.health_server:
                self.logger.log_info("🏥 Stopping health server")
                self.health_server.shutdown()
                self.health_server.server_close()
                self.health_server = None

            if self.health_thread and self.health_thread.is_alive():
                self.health_thread.join(timeout=5)
                self.health_thread = None

            self.logger.log_info("✅ Health server stopped")
        except Exception as e:
            self.logger.log_error(f"❌ Error stopping health server: {e}")

    def notify_strategy_controller_shutdown(self, reason: str, shutdown_type: str = "business_logic"):
        """Notify Strategy Controller of intentional shutdown to prevent restart."""
        try:
            self.logger.log_info(f"📢 Notifying Strategy Controller of shutdown: {reason}")

            # Get auth token for the request
            auth_token = None
            try:
                if hasattr(self, 'firebase_client') and self.firebase_client:
                    auth_token = self.firebase_client.get_auth_token()
            except Exception as e:
                self.logger.log_warning(f"Could not get auth token: {e}")

            # Prepare the shutdown notification
            shutdown_data = {
                "user_id": self.user_id,
                "strategy_id": self.strategy_id,
                "reason": reason,
                "shutdown_type": shutdown_type,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            headers = {"Content-Type": "application/json"}
            if auth_token:
                headers["Authorization"] = f"Bearer {auth_token}"

            # Send notification to Strategy Controller
            response = requests.post(
                f"{self.strategy_controller_url}/strategy-shutdown",
                json=shutdown_data,
                headers=headers,
                timeout=10
            )

            if response.status_code == 200:
                self.logger.log_info("✅ Strategy Controller notified of shutdown")
            else:
                self.logger.log_warning(f"⚠️ Failed to notify Strategy Controller: {response.status_code}")

        except Exception as e:
            self.logger.log_error(f"❌ Error notifying Strategy Controller: {e}")
            # Don't fail the shutdown process if notification fails

    def shutdown_with_exit_code(self, exit_code: int, reason: str):
        """Perform graceful shutdown and exit with specific code."""
        try:
            self.logger.log_info(f"🛑 Shutting down with exit code {exit_code}: {reason}")

            # Notify Strategy Controller for business logic shutdowns
            if exit_code != ExitCodes.SUCCESS and exit_code != ExitCodes.SYSTEM_ERROR:
                self.notify_strategy_controller_shutdown(reason, "business_logic")

            # For system errors, don't update status to stopped since pod will restart
            # For business logic shutdowns, the cleanup process will handle status updates
            if exit_code == ExitCodes.SYSTEM_ERROR:
                self.logger.log_info("🔄 System error shutdown - preserving bot status for restart")
            else:
                self.logger.log_info("🛑 Business logic shutdown - status will be updated to stopped during cleanup")

            # Perform cleanup - only close trades for business logic shutdowns
            # System errors (exit code 1) should preserve trades and status for restart
            should_close_trades = exit_code != ExitCodes.SYSTEM_ERROR
            should_preserve_status = exit_code == ExitCodes.SYSTEM_ERROR
            self.cleanup(close_trades=should_close_trades, preserve_status=should_preserve_status)

            # Exit with the specified code
            self.logger.log_info(f"🚪 Exiting with code {exit_code}")
            sys.exit(exit_code)

        except Exception as e:
            self.logger.log_error(f"❌ Error during shutdown: {e}")
            # If shutdown fails, exit with system error to trigger restart
            sys.exit(ExitCodes.SYSTEM_ERROR)

    def pause_new_trades(self):
        """Pause accepting new trade signals (for graceful shutdown)."""
        try:
            self.is_paused = True
            if hasattr(self, 'trading_engine') and self.trading_engine:
                # Set a flag to prevent new trades
                self.trading_engine.accepting_new_trades = False
            self.logger.log_info("⏸️ New trade signals paused for graceful shutdown")
        except Exception as e:
            self.logger.log_error(f"❌ Error pausing new trades: {e}")

    def get_pause_timeout_status(self) -> dict:
        """Get current pause timeout status for health checks."""
        return self.pause_timeout_manager.get_pause_status()

    def _handle_shutdown(self, signum: int, frame):
        """
        Handle shutdown signals with graceful cleanup.

        Args:
            signum (int): Signal number
            frame: Current stack frame
        """
        signal_name = "SIGINT" if signum == signal.SIGINT else "SIGTERM" if signum == signal.SIGTERM else f"Signal {signum}"

        try:
            self.logger.log_info(f"Received shutdown signal: {signal_name}")

            # Temporarily ignore additional signals during cleanup
            # This prevents multiple cleanup attempts if user presses Ctrl+C multiple times
            signal.signal(signal.SIGINT, signal.SIG_IGN)
            signal.signal(signal.SIGTERM, signal.SIG_IGN)

            # Perform cleanup with a timeout - preserve trades and status during signal-based shutdown
            # Signal-based shutdowns (SIGTERM/SIGINT) are typically from Kubernetes pod restarts
            # so we should preserve trades and status to allow them to continue after restart
            self.cleanup(timeout_seconds=60, close_trades=False, preserve_status=True)

            self.logger.log_info(f"Graceful shutdown completed after {signal_name}")
        except Exception as e:
            self.logger.log_error(e, f"Error during shutdown from {signal_name}")
        finally:
            # Exit with appropriate code (0 for SIGTERM, 130 for SIGINT which is standard for Ctrl+C)
            exit_code = 130 if signum == signal.SIGINT else 0
            sys.exit(exit_code)

    def _get_next_timeframe_interval(self, timeframe: str) -> datetime:
        """
        Calculate the next interval based on the timeframe.

        Args:
            timeframe (str): Timeframe string (e.g., "1h", "4h", "1d")

        Returns:
            datetime: Next interval datetime
        """
        now = datetime.now(timezone.utc)

        # Extract multiplier and unit from timeframe
        multiplier = int(''.join(filter(str.isdigit, timeframe))) if any(c.isdigit() for c in timeframe) else 1
        unit = ''.join([i for i in timeframe if not i.isdigit()]).lower()

        # Map timeframe units to timedelta parameters
        unit_map = {
            'm': 'minutes',
            'h': 'hours',
            'd': 'days',
            'w': 'weeks'
        }

        if unit not in unit_map:
            self.logger.log_error(f"Unsupported timeframe unit: {unit}")
            return now + timedelta(minutes=1)  # Default to 1 minute

        # Calculate the next interval
        delta_param = {unit_map[unit]: multiplier}
        current_interval = now.replace(microsecond=0)

        # Round down to the current interval
        if unit == 'm':
            current_interval = current_interval.replace(second=0)
            # For multi-minute intervals, align to the proper boundary
            if multiplier > 1:
                minutes = (current_interval.minute // multiplier) * multiplier
                current_interval = current_interval.replace(minute=minutes)
        elif unit == 'h':
            current_interval = current_interval.replace(minute=0, second=0)
            # For multi-hour intervals, align to the proper boundary
            if multiplier > 1:
                hours = (current_interval.hour // multiplier) * multiplier
                current_interval = current_interval.replace(hour=hours)
        elif unit == 'd':
            current_interval = current_interval.replace(hour=0, minute=0, second=0)
        elif unit == 'w':
            current_interval = current_interval.replace(hour=0, minute=0, second=0)
            # Round down to the start of the week (Monday)
            current_interval = current_interval - timedelta(days=current_interval.weekday())

        # Add one interval to get the next one
        next_interval = current_interval + timedelta(**delta_param)

        # If we're already past the current interval, use the next one
        if now >= next_interval:
            next_interval = next_interval + timedelta(**delta_param)

        # Add a buffer time based on the timeframe to ensure data is available
        # This is to ensure Polygon has a couple of seconds to update the new candle data
        final_interval = next_interval + timedelta(seconds=POLYGON_BUFFER_SECONDS)

        # Log the calculation for debugging
        self.logger.log_info(f"⏰ Interval calculation for {timeframe}:")
        self.logger.log_info(f"   Current time: {now}")
        self.logger.log_info(f"   Current interval: {current_interval}")
        self.logger.log_info(f"   Next interval: {next_interval}")
        self.logger.log_info(f"   Final (with buffer): {final_interval}")

        return final_interval

    def start_command_listener(self):
        """Start listening for commands in a background thread."""
        def listen_for_commands():
            while self.running:
                try:
                    # Create subscription if it doesn't exist
                    try:
                        self.subscriber.create_subscription(
                            request={
                                "name": self.command_subscription_path,
                                "topic": f"projects/{self.project_id}/topics/{self.command_topic_name}",
                                "filter": f'attributes.strategy_id="{self.strategy_id}"'
                            }
                        )
                    except Exception as e:
                        if "already exists" not in str(e):
                            self.logger.log_error(e, "Failed to create command subscription")
                            time.sleep(5)
                            continue

                    # Pull messages
                    response = self.subscriber.pull(
                        request={
                            "subscription": self.command_subscription_path,
                            "max_messages": 1
                        }
                    )

                    if not response.received_messages:
                        time.sleep(1)
                        continue

                    for received_message in response.received_messages:
                        try:
                            # Process the message
                            message_data = json.loads(received_message.message.data.decode("utf-8"))

                            if message_data.get("user_id") != self.user_id:
                                self.logger.log_warning(f"Received command for different user: {message_data.get('user_id')}")
                                continue

                            command = message_data.get("command")
                            parameters = message_data.get("parameters", {})

                            self.logger.log_info(f"📨 Received command: '{command}' from user: {message_data.get('user_id')}")

                            if command == "pause":
                                self.handle_pause_command()
                            elif command == "resume":
                                self.handle_resume_command()
                            elif command == "stop":
                                self.logger.log_info(f"🛑 Received STOP command via Pub/Sub")
                                self.logger.log_info(f"   Command data: {message_data}")
                                self.logger.log_info(f"   Setting should_exit = True")
                                self.should_exit = True

                                # Create a stop flag file to prevent the bot from restarting
                                try:
                                    with open(self.stop_flag_file, 'w') as f:
                                        f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()}")
                                    self.logger.log_info(f"Created stop flag file: {self.stop_flag_file}")
                                except Exception as e:
                                    self.logger.log_error(f"Could not create stop flag file: {str(e)}")

                                # User-initiated stop should close trades and mark as stopped
                                self.cleanup(close_trades=True, preserve_status=False)
                                sys.exit(0)

                            # Acknowledge the message
                            self.subscriber.acknowledge(
                                request={
                                    "subscription": self.command_subscription_path,
                                    "ack_ids": [received_message.ack_id]
                                }
                            )

                        except Exception as e:
                            self.logger.log_error(e, "Error processing command message")

                except Exception as e:
                    self.logger.log_error(e, "Error in command listener")
                    time.sleep(5)

        self.command_thread = threading.Thread(target=listen_for_commands)
        self.command_thread.daemon = True
        self.command_thread.start()
        self.logger.log_info("Command listener started")

    def handle_pause_command(self):
        """
        Handle the pause command.
        When the bot is paused, we want to do the following:
        - Close all open trades
        - Update the bot status in Firebase
        - Start 24-hour timeout timer
        - Keep the fast update loop running for monitoring
        """
        self.logger.log_info("⏸️ Received pause command")
        self.firebase_client.update_bot_status(
            FirebaseClient.BotStatus.PAUSING,
            {"message": "Trading bot pausing..."}
        )
        self.firebase_client.append_user_log(
            "⏰ Trading bot pausing..."
        )

        # Set paused flag first to prevent any new trades during closing process
        self.is_paused = True

        # Close all open trades if any
        self.trading_engine.close_and_update_open_trades()
        self.firebase_client.append_user_log(
            "✅ All open trades closed"
        )

        # Start the 24-hour pause timeout
        self.pause_timeout_manager.start_pause_timeout()

        self.firebase_client.update_bot_status(
            FirebaseClient.BotStatus.PAUSED,
            {"message": "Trading bot paused (will auto-stop in 24 hours if not resumed)"}
        )
        self.firebase_client.append_user_log(
            "⏸️ Trading bot paused successfully! Will automatically stop in 24 hours if not resumed."
        )

        # Note: We keep the fast update loop running even when paused
        # This allows us to continue monitoring market conditions and updating UI

    def handle_resume_command(self):
        """
        Handle the resume command.
        When the bot is resumed, we want to do the following:
        - Cancel the pause timeout
        - Update the bot status in Firebase
        - Ensure the fast update loop is running
        """
        self.logger.log_info("▶️ Received resume command")
        self.firebase_client.update_bot_status(
            FirebaseClient.BotStatus.RESUMING,
            {"message": "Trading bot resuming..."}
        )

        # Cancel the pause timeout
        self.pause_timeout_manager.cancel_pause_timeout()

        self.is_paused = False

        # Ensure the fast update loop is running
        if self.fast_update_loop and not self.fast_update_loop.running:
            self.fast_update_loop.start()
            self.logger.log_info("Fast update loop restarted")

        self.firebase_client.update_bot_status(
            FirebaseClient.BotStatus.RUNNING,
            {"message": "Trading bot resumed successfully"}
        )
        self.firebase_client.append_user_log(
            "▶️ Trading bot resumed and ready for trading!"
        )

    def _perform_startup_margin_check(self):
        """Perform margin check at bot startup to avoid wasting user's time."""
        try:
            self.logger.log_info("🔍 Checking margin requirements at startup...")

            # Get current market data for price
            market_data = self.trading_engine.market_data_provider.get_candles(
                symbol=self.trading_engine.strategy.instrument,
                timespan=self.trading_engine.strategy.timeframe,
                count=1
            )
            if not market_data or market_data.get("status") != "success" or not market_data.get("candles"):
                self.logger.log_warning("Cannot perform startup margin check - no market data available")
                return None

            current_price = market_data["candles"][-1]["close"]

            # Get account summary
            account_summary = self.trading_engine.oanda_client.get_account_summary()
            if not account_summary:
                self.logger.log_warning("Cannot perform startup margin check - no account data available")
                return None

            account_balance = float(account_summary.get("balance", 0))

            # Calculate indicators first to get accurate stop loss calculation
            self.logger.log_info("🔍 Calculating indicators for accurate stop loss...")

            # Get candles for indicator calculation
            # For PubSub providers, use the already-fetched data from warmup
            if isinstance(self.trading_engine.market_data_provider, PubSubMarketDataProvider):
                # Use the candles that were already fetched during warmup
                if hasattr(self.trading_engine, 'last_market_data') and self.trading_engine.last_market_data:
                    extended_market_data = self.trading_engine.last_market_data
                    self.logger.log_info("🔄 Using cached market data from warmup for indicator calculation")
                else:
                    self.logger.log_warning("⚠️ No cached market data available, skipping startup margin check")
                    return None
            else:
                # For other providers, fetch fresh data
                extended_market_data = self.trading_engine.market_data_provider.get_candles(
                    symbol=self.trading_engine.strategy.instrument,
                    timespan=self.trading_engine.strategy.timeframe,
                    count=1000  # Same as runtime
                )

            if extended_market_data and extended_market_data.get("status") == "success" and extended_market_data.get("candles"):
                # Calculate indicators using the same logic as runtime
                indicators = self.trading_engine.strategy.calculate_indicators(extended_market_data["candles"])
                self.logger.log_info(f"✅ Calculated indicators for startup check: {list(indicators.keys())}")

                # Store indicators in strategy (same as runtime)
                self.trading_engine.strategy.current_indicators = indicators

                # Now calculate position size with accurate stop loss
                position_size = self.trading_engine.strategy.get_position_size(account_balance, current_price)
                self.logger.log_info(f"🔍 Startup position size with real indicators: {position_size} units")
            else:
                self.logger.log_warning("Could not get extended market data for accurate startup check, using fallback")
                position_size = self.trading_engine.strategy.get_position_size(account_balance, current_price)

            margin_check_result = self.trading_engine._check_margin_requirements(position_size, current_price, account_balance)

            if not margin_check_result["sufficient"]:
                # Create margin error thinking data for UI display
                margin_thinking_data = self.trading_engine._create_margin_error_thinking_data(
                    current_price, position_size, margin_check_result, account_balance
                )

                # Update thinking data in Firebase for UI display
                self.firebase_client.update_thinking_data(margin_thinking_data)

                # Enhanced margin shortfall logging and user notification
                self.logger.log_error(None, f"🚨 STARTUP MARGIN CHECK FAILED:")
                self.logger.log_error(None, f"  💰 Required Margin: ${margin_check_result['required_margin']:.2f}")
                self.logger.log_error(None, f"  📊 Available Margin: ${margin_check_result['available_margin']:.2f}")
                self.logger.log_error(None, f"  ❌ Shortfall: ${margin_check_result['shortfall']:.2f}")
                self.logger.log_error(None, f"  🛑 Bot cannot start with current account balance")

                # Send user-friendly notification
                user_message = (
                    f"🛑 BOT STARTUP FAILED - INSUFFICIENT MARGIN\n"
                    f"📊 Required Margin: ${margin_check_result['required_margin']:.2f}\n"
                    f"💰 Available Margin: ${margin_check_result['available_margin']:.2f}\n"
                    f"❌ Shortfall: ${margin_check_result['shortfall']:.2f}\n\n"
                    f"🛠️ SOLUTIONS:\n"
                    f"• Add more funds to your account\n"
                    f"• Reduce risk percentage (currently {self.trading_engine.risk_percentage_pct}%)\n"
                    f"• Use tighter stop losses\n\n"
                    f"🤖 Please fix the margin issue before starting the bot."
                )

                self.firebase_client.append_user_log(user_message)

                return {
                    "status": "insufficient_margin",
                    "message": "Bot startup failed due to insufficient margin",
                    "margin_details": margin_check_result
                }
            else:
                self.logger.log_info(f"✅ Startup margin check passed - Required: ${margin_check_result['required_margin']:.2f}, Available: ${margin_check_result['available_margin']:.2f}")
                return None

        except Exception as e:
            self.logger.log_error(e, "Error during startup margin check")
            return None

    def run(self):
        """Run the trading bot."""
        try:
            # Check if this strategy is in the stopped strategies set
            try:
                # Make a request to the strategy controller to check if this strategy is stopped
                response = requests.get(
                    f"{self.strategy_controller_url}/check-strategy-status/{self.user_id}/{self.strategy_id}",
                    timeout=5
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get("is_stopped", False):
                        self.logger.log_info(f"Strategy {self.strategy_id} was explicitly stopped and will not be restarted")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.STOPPED,
                            {"message": "Trading bot was explicitly stopped and will not be restarted"}
                        )
                        self.firebase_client.append_user_log(
                            "⏹️ Trading bot was explicitly stopped and will not be restarted"
                        )
                        return
            except Exception as e:
                # If we can't check the status, just continue
                self.logger.log_warning(f"Could not check if strategy is stopped: {str(e)}")

            # Start the health client
            self.health_client.start(interval=60)

            # Start the health server for Kubernetes health checks
            self._start_health_server()

            # Initialize graceful shutdown handler (after all components are ready)
            self.graceful_shutdown_handler = GracefulShutdownHandler(self)

            # Try to restore state from previous restart
            state_restored = self.state_restoration.restore_state_if_needed()
            if state_restored:
                self.logger.log_info("🔄 Resumed from previous state after pod restart")
            else:
                self.logger.log_info("🆕 Starting fresh (no previous state to restore)")

            # Start the command listener
            self.start_command_listener()

            # Start the fast update loop
            if self.fast_update_loop:
                self.fast_update_loop.start()
                self.logger.log_info("Fast update loop started")

            # Check for pending operations to recover
            self._recover_pending_operations()

            # Set the bot status to running
            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.RUNNING,
                {"message": "Trading bot is now running"}
            )

            self.logger.log_info("Trading bot initialized successfully and is now running")

            # Add user log
            self.firebase_client.append_user_log(
                "🚀 Trading bot is now running and analyzing the market."
            )

            # STARTUP MARGIN CHECK - Check margin requirements before starting main loop
            self.logger.log_info("🔍 Performing startup margin check...")
            startup_margin_result = self._perform_startup_margin_check()
            if startup_margin_result and startup_margin_result["status"] == "insufficient_margin":
                self.logger.log_error(None, "🛑 Bot stopped during startup due to insufficient margin")
                self.stop_reason = "insufficient_margin"  # Preserve the reason
                self.firebase_client.update_bot_status(
                    FirebaseClient.BotStatus.INSUFFICIENT_MARGIN,
                    {"message": startup_margin_result["message"], "margin_details": startup_margin_result.get("margin_details")}
                )
                # Use business logic shutdown to prevent restart
                self.shutdown_with_exit_code(
                    ExitCodes.INSUFFICIENT_MARGIN,
                    f"Insufficient margin: ${startup_margin_result.get('margin_details', {}).get('shortfall', 0):.2f} shortfall"
                )

            # Get the timeframe from the strategy
            timeframe = self.trading_engine.strategy.get_timeframe()
            self.logger.log_info(f"Using timeframe: {timeframe}")

            # Flag to track if this is the first update cycle (to skip waiting for the next timeframe)
            first_cycle = True

            # Track last candle count to detect when new candles are added
            last_candle_count = 0

            # Flag to log event-driven mode only once
            event_driven_mode_logged = False

            while True:
                try:
                    # Check if the bot should exit
                    should_exit_flag = self.should_exit
                    stop_file_exists = os.path.exists(self.stop_flag_file)

                    if should_exit_flag or stop_file_exists:
                        self.logger.log_info(f"🛑 Bot stop condition triggered:")
                        self.logger.log_info(f"   should_exit flag: {should_exit_flag}")
                        self.logger.log_info(f"   stop_flag_file exists: {stop_file_exists}")
                        self.logger.log_info(f"   stop_flag_file path: {self.stop_flag_file}")
                        self.logger.log_info("Bot has been explicitly stopped, exiting main loop...")
                        break

                    if self.is_paused:
                        self.logger.log_info("Bot is paused, waiting...")

                        # Check for immediate stop even when paused
                        if self.immediate_stop:
                            self.logger.log_info("🛑 Paused state interrupted due to immediate stop flag")
                            break

                        # Check if pause timeout has expired
                        if self.stop_reason == "pause_timeout":
                            self.logger.log_info("🕐 Pause timeout expired, exiting main loop...")
                            break

                        time.sleep(5)
                        continue

                    # For PubSub providers, skip timer-based updates since real-time data triggers updates
                    if isinstance(self.market_data_provider, PubSubMarketDataProvider) and not first_cycle:
                        # Log event-driven mode only once
                        if not event_driven_mode_logged:
                            self.logger.log_info("🔄 PubSub provider: Switched to event-driven mode - trading logic triggered by real-time candle completion")
                            event_driven_mode_logged = True

                        # Just sleep and check for exit conditions - no verbose logging
                        time.sleep(30)  # Check every 30 seconds for exit conditions only
                        continue

                    # Start immediately for the first update, then sync with timeframe intervals
                    wait_seconds = 0
                    if not first_cycle:
                        # We need the trading engine loop to be in sync with real world time
                        # This ensures that the loop always runs right after the previous candle (depending on the timeframe)
                        # closes. Therefore allowing it to immediately react to the previous candle.
                        # Calculate next interval
                        next_interval = self._get_next_timeframe_interval(timeframe)
                        wait_seconds = (next_interval - datetime.now(timezone.utc)).total_seconds()

                        # Also check if new candles are available more frequently
                        self.logger.log_info(f"Next scheduled interval: {next_interval}, waiting {wait_seconds:.2f}s")

                    if wait_seconds > 0:
                        self.logger.log_info(f"Waiting {wait_seconds:.2f} seconds until next interval at {next_interval}")

                        # Wait in smaller increments so we can check for exit conditions
                        wait_increment = 5  # Check every 5 seconds
                        remaining_wait = wait_seconds

                        while remaining_wait > 0:
                            # Check if the bot should exit
                            if self.should_exit or os.path.exists(self.stop_flag_file):
                                self.logger.log_info("Bot has been explicitly stopped during wait, exiting main loop...")
                                return

                            # Check if new candles are available (break early if so)
                            # Skip polling for PubSub provider since it uses real-time data
                            if not isinstance(self.market_data_provider, PubSubMarketDataProvider):
                                try:
                                    current_candles = self.market_data_provider.get_candles(
                                        symbol=self.trading_engine.strategy.instrument,
                                        timespan=timeframe,
                                        count=10  # Just check recent candles
                                    )
                                    current_count = len(current_candles.get("candles", []))
                                    if current_count > last_candle_count:
                                        self.logger.log_info(f"🚀 New candles detected ({current_count} vs {last_candle_count}), breaking wait early")
                                        last_candle_count = current_count
                                        break
                                except Exception as e:
                                    # Don't break the loop for candle check errors
                                    pass
                            else:
                                # For PubSub provider, real-time data will trigger updates automatically
                                # No need to poll for new candles
                                pass

                            # Sleep for the smaller increment or the remaining time
                            sleep_time = min(wait_increment, remaining_wait)

                            # Check for immediate stop flag during sleep
                            if self.immediate_stop:
                                self.logger.log_info("🛑 Sleep interrupted due to immediate stop flag")
                                break

                            time.sleep(sleep_time)
                            remaining_wait -= sleep_time

                    # Check paused state again after waiting
                    if self.is_paused:
                        self.logger.log_info("Bot is paused after waiting interval, skipping update cycle...")
                        continue

                    # Check exit condition again
                    should_exit_flag = self.should_exit
                    stop_file_exists = os.path.exists(self.stop_flag_file)

                    if should_exit_flag or stop_file_exists:
                        self.logger.log_info(f"🛑 Bot stop condition triggered after waiting:")
                        self.logger.log_info(f"   should_exit flag: {should_exit_flag}")
                        self.logger.log_info(f"   stop_flag_file exists: {stop_file_exists}")
                        self.logger.log_info(f"   stop_flag_file path: {self.stop_flag_file}")
                        self.logger.log_info("Bot has been explicitly stopped after waiting, exiting main loop...")
                        break

                    # Update trading engine state
                    self.logger.log_info("Analyzing market conditions and updating trading state...")

                    try:
                        result = self.trading_engine.update()

                        # Update candle count tracking
                        # Skip polling for PubSub provider since it uses real-time data
                        if not isinstance(self.market_data_provider, PubSubMarketDataProvider):
                            try:
                                current_candles = self.market_data_provider.get_candles(
                                    symbol=self.trading_engine.strategy.instrument,
                                    timespan=timeframe,
                                    count=10
                                )
                                last_candle_count = len(current_candles.get("candles", []))
                            except Exception as e:
                                pass
                        else:
                            # For PubSub provider, candle count tracking is handled by real-time updates
                            pass

                        # Set first_cycle to False after the first cycle
                        if first_cycle:
                            first_cycle = False
                            self.logger.log_info("Initial market analysis complete, continuing with regular updates")
                    except SystemExit as e:
                        self.logger.log_info(f"Received SystemExit: {str(e)}")

                        # Use the specific user-friendly message from SystemExit
                        user_friendly_message = str(e) if str(e) else "Trading bot stopped due to critical error"

                        # Update Firebase bot status with the specific error
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.ERROR,
                            {"message": user_friendly_message}
                        )

                        # Add user log with the specific message
                        self.firebase_client.append_user_log(
                            f"🚨 {user_friendly_message}"
                        )

                        # Set the should_exit flag to ensure we exit the main loop
                        self.should_exit = True
                        # Create a stop flag file if it doesn't exist
                        if not os.path.exists(self.stop_flag_file):
                            try:
                                with open(self.stop_flag_file, 'w') as f:
                                    f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()} due to: {str(e)}")
                                self.logger.log_info(f"Created stop flag file: {self.stop_flag_file}")
                            except Exception as ex:
                                self.logger.log_error(f"Could not create stop flag file: {str(ex)}")
                        # Break out of the main loop
                        break

                    if result["status"] == "error":
                        self.logger.log_error(Exception(result["message"]), "Trading engine update failed")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.ERROR,
                            {"message": result["message"]}
                        )
                    elif result["status"] == "insufficient_margin":
                        self.logger.log_error(Exception(result["message"]), "Bot stopped due to insufficient margin")
                        self.stop_reason = "insufficient_margin"  # Preserve the reason
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.INSUFFICIENT_MARGIN,
                            {"message": result["message"], "margin_details": result.get("margin_details")}
                        )
                        # Use business logic shutdown to prevent restart
                        self.shutdown_with_exit_code(
                            ExitCodes.INSUFFICIENT_MARGIN,
                            f"Runtime insufficient margin: {result['message']}"
                        )
                    elif result["status"] == "market_closed":
                        self.logger.log_info("Market is closed, waiting...")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.MARKET_CLOSED,
                            {"message": "Market is closed"}
                        )
                    elif result["status"] == "not_in_session":
                        self.logger.log_info(f"Not in trading session, waiting... {result.get('message', '')}")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.NOT_IN_SESSION,
                            {"message": result.get('message', 'Outside trading session')}
                        )
                    elif result["status"] == "data_stale":
                        self.logger.log_info(f"Data is stale, pausing trading... {result.get('message', '')}")
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.DATA_STALE,
                            {"message": result.get('message', 'Data provider is not providing fresh data')}
                        )
                    elif result["status"] == "market_data_error":
                        # Log the technical error details for debugging
                        technical_error = result.get("technical_details", "Unknown error")
                        self.logger.log_error(Exception(technical_error), "Market data error")

                        # Use the user-friendly message for the UI
                        user_message = result["message"]

                        # Update bot status with user-friendly message
                        self.firebase_client.update_bot_status(
                            FirebaseClient.BotStatus.ERROR,
                            {"message": user_message}
                        )

                        # Add user log with friendly message
                        self.firebase_client.append_user_log(
                            f"❌ Market data issue: {user_message}. Stopping bot."
                        )
                        break
                    else:
                        self.logger.log_info("Update cycle completed successfully")
                        # Only update status to RUNNING if not paused
                        if not self.is_paused:
                            self.firebase_client.update_bot_status(
                                FirebaseClient.BotStatus.RUNNING,
                                {"message": "Trading bot running normally"}
                            )
                        else:
                            # If paused, make sure status reflects this
                            self.firebase_client.update_bot_status(
                                FirebaseClient.BotStatus.PAUSED,
                                {"message": "Trading bot paused"}
                            )

                        # NOTE: Chart data updates removed - indicators now calculated in frontend
                        # This reduces Firestore writes and improves performance
                        if "market_data" in result:
                            self.logger.log_info(f"Market data available: {len(result['market_data']['candles'])} candles")
                            # Chart data is now handled by the frontend via GCS + WebSocket
                        # Update trade history if a trade was executed
                        # Only add LONG and SHORT trades to history - CLOSE trades are handled by updating existing trades
                        if "trade" in result and result["trade"] is not None:
                            from core.trading_engine_types import TradeType
                            if result["trade"].type in [TradeType.LONG, TradeType.SHORT]:
                                # Extract entry conditions from current thinking data
                                entry_conditions = None
                                if hasattr(self.trading_engine, 'current_thinking_data') and self.trading_engine.current_thinking_data:
                                    thinking_data = self.trading_engine.current_thinking_data
                                    trade_type = "long" if result["trade"].type == TradeType.LONG else "short"

                                    # Create entry conditions from the analysis that triggered the trade
                                    if f"{trade_type}_analysis" in thinking_data:
                                        entry_conditions = {
                                            "analysis": thinking_data[f"{trade_type}_analysis"],
                                            "candle_time": thinking_data.get("candle_timing", {}).get("current_candle_time_formatted"),
                                            "current_price": thinking_data.get("current_price"),
                                            "current_indicators": thinking_data.get("current_indicators"),
                                            "timestamp": thinking_data.get("timestamp")
                                        }

                                trade_history_row = self.trading_engine.convert_trade_execution_response_to_trade_history_row(result["trade"], entry_conditions)
                                self.firebase_client.add_to_trade_history(
                                    trade_history_row
                                )
                            else:
                                self.logger.log_info(f"Skipping trade history addition for {result['trade'].type} trade - handled by trade closure process")
                        else:
                            self.logger.log_info("No trade executed")
                            if "open_trades" in result:
                                self.firebase_client.update_open_trades(
                                    result["open_trades"]
                                )

                        # Update account balance if available
                        if "account_summary" in result:
                            if self.firebase_client:
                                self.firebase_client.update_account_balance(
                                    result["account_summary"]["balance"]
                                )

                except Exception as e:
                    self.logger.log_error(e, "Error in trading loop")

                    # Create a user-friendly error message
                    user_message = get_user_friendly_error_message(str(e))

                    self.firebase_client.update_bot_status(
                        FirebaseClient.BotStatus.ERROR,
                        {"message": user_message}
                    )

                    # Add user log with friendly message
                    self.firebase_client.append_user_log(
                        f"⚠️ Trading bot encountered an issue: {user_message}. Attempting to recover..."
                    )

                    # Wait for a short time before retrying
                    time.sleep(5)

        except Exception as e:
            self.logger.log_error(e, "Fatal error in trading bot")

            # Create a user-friendly error message
            user_message = get_user_friendly_error_message(str(e))

            self.firebase_client.update_bot_status(
                FirebaseClient.BotStatus.ERROR,
                {"message": user_message}
            )

            # Add user log with friendly message
            self.firebase_client.append_user_log(
                f"⛔ Trading bot stopped due to an error: {user_message}. Please restart the bot."
            )
        finally:
            # Fatal error cleanup should close trades and mark as stopped
            self.cleanup(close_trades=True, preserve_status=False)
            pass

    def _recover_pending_operations(self):
        """Recover any pending operations from previous runs."""
        try:
            # Define handlers for different operation types
            handlers = {
                "trade_execution": self._recover_trade_execution,
                "position_close": self._recover_position_close,
                "risk_update": self._recover_risk_update
            }

            # Recover pending operations
            recovered_count = self.recovery_manager.recover_pending_operations(handlers)

            if recovered_count > 0:
                self.logger.log_info(f"Recovered {recovered_count} pending operations")
                self.firebase_client.append_user_log(
                    f"🔄 Recovered {recovered_count} pending operations from previous session"
                )

            # Load last checkpoint if available
            checkpoint = self.recovery_manager.get_last_checkpoint()
            if checkpoint:
                self.logger.log_info(f"Found checkpoint {checkpoint['checkpoint_id']} from {checkpoint['last_updated']}")
                # TODO: Implement checkpoint recovery logic if needed
        except Exception as e:
            self.logger.log_error(e, "Error recovering pending operations")

    def _recover_trade_execution(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Recover a trade execution operation."""
        try:
            trade_type = params.get("trade_type")
            instrument = params.get("instrument")
            units = params.get("units")
            order_id = params.get("order_id")

            self.logger.log_info(f"Recovering trade execution: {trade_type} {units} units of {instrument}")

            # Check if the order was already executed
            if order_id:
                # Check with the broker if the order exists
                try:
                    order_status = self.oanda_client.get_order(order_id)
                    self.logger.log_info(f"Order {order_id} already exists with status {order_status['state']}")
                    return {"status": "already_executed", "order_id": order_id, "order_status": order_status}
                except Exception as e:
                    self.logger.log_warning(f"Order {order_id} not found, will re-execute: {str(e)}")

            # Re-execute the trade
            result = self.trading_engine.execute_trade(trade_type, instrument, units)

            self.logger.log_info(f"Re-executed trade: {result}")
            self.firebase_client.append_user_log(
                f"🔄 Recovered interrupted trade: {trade_type} {units} units of {instrument}"
            )

            return result
        except Exception as e:
            self.logger.log_error(e, f"Error recovering trade execution: {params}")
            raise

    def _recover_position_close(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Recover a position close operation."""
        try:
            instrument = params.get("instrument")
            trade_id = params.get("trade_id")

            self.logger.log_info(f"Recovering position close for {instrument} (trade ID: {trade_id})")

            # Check if the position is still open
            open_trades = self.oanda_client.get_open_trades()

            # If trade_id is provided, check if that specific trade is still open
            if trade_id:
                trade_still_open = any(trade["id"] == trade_id for trade in open_trades)
                if not trade_still_open:
                    self.logger.log_info(f"Trade {trade_id} is already closed")
                    return {"status": "already_closed", "trade_id": trade_id}

                # Close the specific trade
                result = self.trading_engine.close_trade(trade_id)

                self.logger.log_info(f"Closed trade {trade_id}: {result}")
                self.firebase_client.append_user_log(
                    f"🔄 Recovered interrupted position close for trade {trade_id}"
                )

                return result
            else:
                # Close all trades for the instrument if no specific trade_id
                instrument_trades = [trade for trade in open_trades if trade["instrument"] == instrument]

                if not instrument_trades:
                    self.logger.log_info(f"No open trades for {instrument}")
                    return {"status": "no_open_trades", "instrument": instrument}

                results = []
                for trade in instrument_trades:
                    result = self.trading_engine.close_trade(trade["id"])
                    results.append(result)

                self.logger.log_info(f"Closed {len(results)} trades for {instrument}")
                self.firebase_client.append_user_log(
                    f"🔄 Recovered interrupted position close for {len(results)} trades of {instrument}"
                )

                return {"status": "success", "results": results}
        except Exception as e:
            self.logger.log_error(e, f"Error recovering position close: {params}")
            raise

    def _recover_risk_update(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Recover a risk update operation."""
        try:
            risk_params = params.get("risk_params", {})

            self.logger.log_info(f"Recovering risk update: {risk_params}")

            # Update risk parameters
            self.trading_engine.update_risk_parameters(risk_params)

            self.logger.log_info("Updated risk parameters")
            self.firebase_client.append_user_log(
                "🔄 Recovered interrupted risk parameter update"
            )

            return {"status": "success", "risk_params": risk_params}
        except Exception as e:
            self.logger.log_error(e, f"Error recovering risk update: {params}")
            raise

    def _process_new_completed_candle(self, completed_candle):
        """
        Process a new completed candle from real-time data.

        Args:
            completed_candle: The completed candle data
        """
        try:
            self.logger.log_info(f"🔄 Processing new completed candle: {completed_candle.get('time', 'N/A')}")

            # Update the main candle buffer with FIFO logic
            # This should trigger a strategy update with the new candle
            if hasattr(self.trading_engine, 'update_with_new_candle'):
                self.trading_engine.update_with_new_candle(completed_candle)
            else:
                self.logger.log_warning("⚠️ Trading engine doesn't support real-time candle updates")

        except Exception as e:
            self.logger.log_error(f"❌ Error processing completed candle: {e}")

    def cleanup(self, timeout_seconds: int = 30, close_trades: bool = True, preserve_status: bool = False):
        """
        Clean up resources and optionally close positions with timeout protection.

        This function is called:
        - When the trading bot is stopped
        - When the trading bot is stopped due to an error
        - When the trading bot receives a SIGINT or SIGTERM signal

        Args:
            timeout_seconds (int): Maximum time to spend on cleanup before giving up
            close_trades (bool): Whether to close open trades during cleanup.
                                Set to False for system error restarts to preserve trades.
                                Set to True for business logic shutdowns to close trades.
            preserve_status (bool): Whether to preserve the current bot status instead of marking as STOPPED.
                                   Set to True for system error restarts to preserve status.
                                   Set to False for business logic shutdowns to mark as stopped.
        """
        # Set a flag to prevent multiple cleanup calls
        if hasattr(self, '_cleanup_in_progress') and self._cleanup_in_progress:
            self.logger.log_info("Cleanup already in progress, skipping duplicate call")
            return

        self._cleanup_in_progress = True
        self.running = False

        # Start timing the cleanup process
        start_time = time.time()

        # Log the start of cleanup
        self.logger.log_info(f"Starting cleanup process with {timeout_seconds}s timeout")

        try:
            # Step 1: Gracefully stop the command listener thread
            self._cleanup_command_thread(timeout_seconds=5)

            # Check if we've exceeded our timeout
            if time.time() - start_time > timeout_seconds:
                self.logger.log_warning("Cleanup timeout exceeded after command thread shutdown")
                self._finalize_cleanup(success=False, error_message="Cleanup timed out", preserve_status=preserve_status)
                return

            # Step 2: Update status to stopping (unless we need to preserve insufficient margin status)
            try:
                if self.stop_reason == "insufficient_margin":
                    self.logger.log_info("🛑 Preserving insufficient margin status during cleanup - skipping STOPPING status")
                    # Don't update status to STOPPING - keep INSUFFICIENT_MARGIN
                else:
                    self.logger.log_info("Updating bot status to STOPPING...")
                    self.firebase_client.update_bot_status(
                        FirebaseClient.BotStatus.STOPPING,
                        {"message": "Trading bot stopping..."}
                    )
                    self.firebase_client.append_user_log(
                        "⏰ Trading bot stopping..."
                    )
            except Exception as firebase_error:
                # Don't let Firebase errors stop the cleanup process
                self.logger.log_error(firebase_error, "Error updating bot status to STOPPING")

            # Step 3: Close all open trades (only if close_trades is True)
            if self.trading_engine and close_trades:
                # Calculate remaining time for trade closing
                remaining_time = timeout_seconds - (time.time() - start_time)
                if remaining_time <= 0:
                    self.logger.log_warning("No time remaining for trade closing")
                else:
                    self.logger.log_info(f"Closing open trades with {remaining_time:.1f}s remaining")
                    try:
                        # Use the is_shutdown flag to ensure we continue even if some trades fail to close
                        trades_closed = self.trading_engine.close_and_update_open_trades(is_shutdown=True)
                        if not trades_closed:
                            self.logger.log_warning("Some trades could not be closed during shutdown")
                    except Exception as trade_error:
                        self.logger.log_error(trade_error, "Error closing trades during shutdown")
            elif self.trading_engine and not close_trades:
                self.logger.log_info("🔄 Preserving open trades for system restart - trades will continue after pod restart")

            # Step 4: Final status update
            self._finalize_cleanup(success=True, preserve_status=preserve_status)

            # Step 5: Clean up subscription
            try:
                # Check if the subscription exists before trying to delete it
                try:
                    self.subscriber.get_subscription(
                        request={"subscription": self.command_subscription_path}
                    )
                    # If we get here, the subscription exists and we can delete it
                    self.subscriber.delete_subscription(
                        request={"subscription": self.command_subscription_path}
                    )
                    self.logger.log_info("Command subscription deleted")
                except google.api_core.exceptions.NotFound:
                    # Subscription doesn't exist, which is fine
                    self.logger.log_info("Command subscription already deleted or doesn't exist")
                except Exception as get_error:
                    # If we can't check if the subscription exists, try to delete it anyway
                    self.logger.log_warning(f"Error checking if subscription exists: {str(get_error)}")
                    self.subscriber.delete_subscription(
                        request={"subscription": self.command_subscription_path}
                    )
                    self.logger.log_info("Command subscription deleted")
            except Exception as sub_error:
                # This is not a critical error, so just log it
                self.logger.log_warning(f"Error deleting command subscription: {str(sub_error)}")

            # Step 6: Stop the health client
            try:
                if hasattr(self, 'health_client'):
                    self.logger.log_info("Stopping health client")
                    self.health_client.update_status("stopped")
                    self.health_client.send_health_data()  # Send final health update
                    self.health_client.stop()
                    self.logger.log_info("Health client stopped successfully")
            except Exception as health_error:
                self.logger.log_error(health_error, "Error stopping health client")

            # Step 6.5: Stop the health server
            try:
                self._stop_health_server()
            except Exception as health_server_error:
                self.logger.log_error(health_server_error, "Error stopping health server")

            # Step 7: Stop the fast update loop
            try:
                if hasattr(self, 'fast_update_loop') and self.fast_update_loop:
                    self.logger.log_info("Stopping fast update loop")
                    self.fast_update_loop.stop()
                    self.logger.log_info("Fast update loop stopped successfully")
            except Exception as fast_update_error:
                self.logger.log_error(fast_update_error, "Error stopping fast update loop")

            # Step 8: Clean up market data provider
            try:
                if hasattr(self, 'market_data_provider') and self.market_data_provider:
                    self.logger.log_info("Cleaning up market data provider")
                    if hasattr(self.market_data_provider, 'cleanup'):
                        self.market_data_provider.cleanup()
                    self.logger.log_info("Market data provider cleaned up successfully")
            except Exception as market_data_error:
                self.logger.log_error(market_data_error, "Error cleaning up market data provider")

        except Exception as e:
            self.logger.log_error(e, "Error during cleanup")
            self._finalize_cleanup(success=False, error_message=str(e), preserve_status=preserve_status)
        finally:
            # Always clear the cleanup flag
            self._cleanup_in_progress = False

    def _cleanup_command_thread(self, timeout_seconds: int = 5):
        """
        Gracefully stop the command listener thread.

        Args:
            timeout_seconds (int): Maximum time to wait for the thread to stop
        """
        try:
            # Check if we're not in the command thread before trying to join it
            current_thread = threading.current_thread()
            if self.command_thread is not None and current_thread != self.command_thread:
                self.logger.log_info(f"Waiting up to {timeout_seconds}s for command thread to stop...")
                self.command_thread.join(timeout=timeout_seconds)
                if self.command_thread.is_alive():
                    self.logger.log_warning("Command thread did not stop within the timeout period")
                else:
                    self.logger.log_info("Command thread stopped successfully")
        except Exception as e:
            self.logger.log_error(e, "Error stopping command thread")

    def _finalize_cleanup(self, success: bool, error_message: str = None, preserve_status: bool = False):
        """
        Finalize the cleanup process by updating the bot status.

        Args:
            success (bool): Whether the cleanup was successful
            error_message (str, optional): Error message if cleanup failed
            preserve_status (bool): Whether to preserve the current bot status instead of marking as STOPPED
        """
        try:
            # Create a stop flag file to prevent the bot from restarting
            # This is a backup in case the flag wasn't created during the stop command
            if not os.path.exists(self.stop_flag_file):
                try:
                    with open(self.stop_flag_file, 'w') as f:
                        f.write(f"Stopped at {datetime.now(timezone.utc).isoformat()}")
                    self.logger.log_info(f"Created stop flag file during cleanup: {self.stop_flag_file}")
                except Exception as e:
                    self.logger.log_error(f"Could not create stop flag file during cleanup: {str(e)}")

            # Set the should_exit flag
            self.should_exit = True

            if success:
                # Check if we should preserve status (for system restarts)
                if preserve_status:
                    self.logger.log_info("🔄 Preserving bot status for system restart - not marking as stopped")
                    self.firebase_client.append_user_log(
                        "🔄 Trading bot restarting due to system maintenance - will resume automatically"
                    )
                # Preserve insufficient margin status instead of overwriting with STOPPED
                elif self.stop_reason == "insufficient_margin":
                    self.logger.log_info("🛑 Preserving insufficient margin status during cleanup")
                    # Don't update status - keep the INSUFFICIENT_MARGIN status
                    self.firebase_client.append_user_log(
                        "🛑 Trading bot stopped due to insufficient margin. Please check the analysis for details."
                    )
                else:
                    # Normal stop - update to STOPPED status
                    self.firebase_client.update_bot_status(
                        FirebaseClient.BotStatus.STOPPED,
                        {"message": "Trading bot stopped"}
                    )
                    self.firebase_client.append_user_log(
                        "⏹️ Trading bot stopped successfully!"
                    )
                self.logger.log_info("Cleanup completed successfully")
            else:
                # Create a user-friendly error message
                user_message = get_user_friendly_error_message(error_message or "Unknown error during shutdown")

                self.firebase_client.update_bot_status(
                    FirebaseClient.BotStatus.ERROR,
                    {"message": user_message}
                )
                self.firebase_client.append_user_log(
                    f"⚠️ Trading bot stopped with some issues: {user_message}"
                )
                self.logger.log_warning(f"Cleanup completed with errors: {error_message}")
        except Exception as e:
            # Last resort logging if even the status update fails
            self.logger.log_error(e, "Error during final cleanup status update")

def main():
    """Main entry point for the trading bot service."""
    try:
        # Check required environment variables
        required_vars = [
            "POLYGON_API_KEY",
            "USER_ID",  # This will be set by the Strategy Controller
            "STRATEGY_ID"  # This will be set by the Strategy Controller
        ]

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            print(f"⚙️ Error: Missing required environment variables: {', '.join(missing_vars)}")
            sys.exit(ExitCodes.CONFIGURATION_ERROR)

        # Create and start trading bot
        bot = TradingBot(os.getenv("USER_ID"), os.getenv("STRATEGY_ID"))
        bot.run()

        # Normal completion
        print("✅ Trade-bot completed successfully")
        sys.exit(ExitCodes.SUCCESS)

    except KeyboardInterrupt:
        print("🛑 Trade-bot interrupted by user")
        sys.exit(ExitCodes.SUCCESS)

    except Exception as e:
        print(f"❌ Unexpected error in trade-bot: {e}")
        # Unknown errors should restart for investigation
        sys.exit(ExitCodes.SYSTEM_ERROR)

if __name__ == "__main__":
    main()