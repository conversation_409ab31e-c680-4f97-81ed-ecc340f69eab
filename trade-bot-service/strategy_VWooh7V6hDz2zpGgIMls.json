{"name": "MACD + RSI Momentum", "description": "", "instruments": "EUR/USD", "timeframe": "1m", "tradingSession": ["All"], "indicators": [{"id": "1750635494107l2wbx8urxw", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 9}, "source": "price"}, {"id": "1750635503528hpe7e2mi3a", "type": "MACD", "indicator_class": "MACD", "parameters": {"fast": 12, "slow": 26, "signal": 9}, "source": "price"}, {"id": "1751010718683vvi0upibpoj", "type": "BollingerBands", "indicator_class": "BollingerBands", "parameters": {"period": 20, "devfactor": 2, "offset": 0}, "source": "close"}], "entryRules": [{"tradeType": "long", "indicator1": "1750635503528hpe7e2mi3a", "operator": "Crossing above", "compareType": "indicator", "indicator2": "1750635503528hpe7e2mi3a", "value": "", "logicalOperator": "AND", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal", "id": "1750635527854avhp6lzjii"}, {"id": "175063558661102l6vf3wi08h", "tradeType": "short", "indicator1": "1750635503528hpe7e2mi3a", "operator": "Crossing below", "compareType": "indicator", "indicator2": "1750635503528hpe7e2mi3a", "value": "", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}, {"id": "175105619540491sspdfyvqh", "tradeType": "long", "indicator1": "1750635494107l2wbx8urxw", "operator": "<", "compareType": "value", "indicator2": "", "value": "35", "barRef": "close"}, {"id": "1751056235412smuzkx24e0m", "tradeType": "short", "indicator1": "1750635494107l2wbx8urxw", "operator": ">", "compareType": "value", "indicator2": "", "value": "65", "barRef": "close"}], "exitRules": [{"tradeType": "long", "indicator1": "1750635503528hpe7e2mi3a", "operator": "Crossing below", "compareType": "indicator", "indicator2": "1750635503528hpe7e2mi3a", "value": "", "logicalOperator": "AND", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal", "id": "1750635618532fk2xyp4vldc"}, {"id": "1750635639998cw1rfo1dknf", "tradeType": "short", "indicator1": "1750635503528hpe7e2mi3a", "operator": "Crossing above", "compareType": "indicator", "indicator2": "1750635503528hpe7e2mi3a", "value": "", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}, {"id": "1751056245844q83ep71z01k", "tradeType": "long", "indicator1": "1750635494107l2wbx8urxw", "operator": ">", "compareType": "value", "indicator2": "", "value": "65", "barRef": "close"}, {"id": "1751056254148i00neyb6ide", "tradeType": "short", "indicator1": "1750635494107l2wbx8urxw", "operator": "<", "compareType": "value", "indicator2": "", "value": "35", "barRef": "close"}], "riskManagement": {"riskPercentage": "1", "riskRewardRatio": "3", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "bollinger", "parameters": {}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "50%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "entryLongGroupOperator": "AND", "entryShortGroupOperator": "AND", "exitLongGroupOperator": "OR", "exitShortGroupOperator": "OR", "user_id": "2tixoSldb3K4Fmz015AZQbTm2QfJ", "id": "VWooh7V6hDz2zpGgIMls"}