#!/usr/bin/env python3
"""
Trade Synchronization Fix Script

This script diagnoses and fixes trade data synchronization issues between OANDA and Firebase.
It will:
1. Check current open trades in OANDA
2. Check trade data in Firebase
3. Identify and fix inconsistencies
4. Update Firebase with correct trade statuses
"""

import os
import sys
import json
from datetime import datetime, timezone
from typing import List, Dict, Any

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from execution.oanda_client import OandaClient
from execution.firebase_client import FirebaseClient
from core.logger import Logger
from models.trade_history import TradeHistoryRow, TradeStatus

class TradeSyncFixer:
    def __init__(self, user_id: str, strategy_id: str, oanda_api_key: str, oanda_account_id: str):
        self.user_id = user_id
        self.strategy_id = strategy_id
        self.logger = Logger("TradeSyncFixer")
        
        # Initialize clients
        self.oanda_client = OandaClient(
            api_key=oanda_api_key,
            account_id=oanda_account_id,
            logger=self.logger
        )
        
        self.firebase_client = FirebaseClient(
            user_id=user_id,
            strategy_id=strategy_id,
            logger=self.logger
        )
    
    def diagnose_and_fix(self) -> Dict[str, Any]:
        """
        Diagnose and fix trade synchronization issues.
        
        Returns:
            Dict with diagnosis and fix results
        """
        results = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": self.user_id,
            "strategy_id": self.strategy_id,
            "oanda_open_trades": [],
            "firebase_open_trades": [],
            "inconsistencies": [],
            "fixes_applied": []
        }
        
        try:
            # Step 1: Get current open trades from OANDA
            self.logger.log_info("🔍 Checking OANDA for open trades...")
            oanda_open_trades = self.oanda_client.get_open_trades()
            results["oanda_open_trades"] = [
                {
                    "tradeID": trade.tradeID,
                    "instrument": trade.instrument,
                    "units": trade.units,
                    "price": trade.price,
                    "unrealizedPL": trade.unrealizedPL,
                    "status": trade.status.value
                }
                for trade in oanda_open_trades
            ]
            
            self.logger.log_info(f"📊 OANDA shows {len(oanda_open_trades)} open trades")
            
            # Step 2: Get trade data from Firebase
            self.logger.log_info("🔍 Checking Firebase for trade data...")
            firebase_trades = self._get_firebase_trades()
            
            # Filter for trades that Firebase considers "open"
            firebase_open_trades = [
                trade for trade in firebase_trades
                if (trade.get('status') in ['OPEN', 'open'] or 
                    (trade.get('status') not in ['CLOSED', 'closed'] and not trade.get('closeTime')))
            ]
            
            results["firebase_open_trades"] = [
                {
                    "tradeID": trade.get('tradeID'),
                    "instrument": trade.get('instrument'),
                    "units": trade.get('units'),
                    "price": trade.get('price'),
                    "status": trade.get('status'),
                    "closeTime": trade.get('closeTime'),
                    "realizedPL": trade.get('realizedPL')
                }
                for trade in firebase_open_trades
            ]
            
            self.logger.log_info(f"📊 Firebase shows {len(firebase_open_trades)} open trades")
            
            # Step 3: Identify inconsistencies
            oanda_trade_ids = {trade.tradeID for trade in oanda_open_trades}
            firebase_trade_ids = {trade.get('tradeID') for trade in firebase_open_trades}
            
            # Trades that Firebase thinks are open but OANDA says are closed
            orphaned_trades = firebase_trade_ids - oanda_trade_ids
            
            if orphaned_trades:
                self.logger.log_warning(f"🚨 Found {len(orphaned_trades)} orphaned trades in Firebase: {orphaned_trades}")
                results["inconsistencies"].append({
                    "type": "orphaned_trades",
                    "count": len(orphaned_trades),
                    "trade_ids": list(orphaned_trades),
                    "description": "Trades that Firebase considers open but OANDA shows as closed"
                })
                
                # Step 4: Fix orphaned trades
                for trade_id in orphaned_trades:
                    try:
                        self.logger.log_info(f"🔧 Fixing orphaned trade: {trade_id}")
                        
                        # Get closed trade details from OANDA
                        closed_trade_details = self.oanda_client.get_closed_trade_details(trade_id)
                        
                        if closed_trade_details:
                            # Update the trade in Firebase as closed
                            self._update_trade_as_closed(trade_id, closed_trade_details)
                            results["fixes_applied"].append({
                                "trade_id": trade_id,
                                "action": "marked_as_closed",
                                "realized_pl": closed_trade_details.get('realized_pl'),
                                "close_time": closed_trade_details.get('close_time')
                            })
                            self.logger.log_info(f"✅ Successfully fixed trade {trade_id}")
                        else:
                            # If we can't get details, mark as closed with unknown P&L
                            self._mark_trade_as_closed_unknown(trade_id)
                            results["fixes_applied"].append({
                                "trade_id": trade_id,
                                "action": "marked_as_closed_unknown",
                                "note": "Could not retrieve close details from OANDA"
                            })
                            self.logger.log_warning(f"⚠️ Marked trade {trade_id} as closed but could not get details")
                            
                    except Exception as e:
                        self.logger.log_error(f"❌ Error fixing trade {trade_id}: {e}")
                        results["inconsistencies"].append({
                            "type": "fix_error",
                            "trade_id": trade_id,
                            "error": str(e)
                        })
            
            # Check for trades that OANDA has but Firebase doesn't (shouldn't happen but worth checking)
            missing_trades = oanda_trade_ids - firebase_trade_ids
            if missing_trades:
                self.logger.log_warning(f"🚨 Found {len(missing_trades)} trades in OANDA but not in Firebase: {missing_trades}")
                results["inconsistencies"].append({
                    "type": "missing_trades",
                    "count": len(missing_trades),
                    "trade_ids": list(missing_trades),
                    "description": "Trades that OANDA shows as open but Firebase doesn't have"
                })
            
            # Step 5: Clear OANDA cache to ensure fresh data
            if hasattr(self.oanda_client, 'cache'):
                self.oanda_client.cache.clear()
                self.logger.log_info("🧹 Cleared OANDA cache")
                results["fixes_applied"].append({
                    "action": "cleared_oanda_cache",
                    "description": "Cleared cached data to ensure fresh reads"
                })
            
            self.logger.log_info("✅ Trade synchronization diagnosis and fix completed")
            
        except Exception as e:
            self.logger.log_error(f"❌ Error during diagnosis and fix: {e}")
            results["error"] = str(e)
        
        return results
    
    def _get_firebase_trades(self) -> List[Dict[str, Any]]:
        """Get all trades from Firebase for this strategy."""
        try:
            # Access Firebase directly to get all trade documents
            trade_collection = (self.firebase_client.db
                              .collection('users').document(self.user_id)
                              .collection('submittedStrategies').document(self.strategy_id)
                              .collection('tradeHistory'))
            
            docs = trade_collection.get()
            trades = []
            
            for doc in docs:
                trade_data = doc.to_dict()
                trade_data['doc_id'] = doc.id
                trades.append(trade_data)
            
            return trades
            
        except Exception as e:
            self.logger.log_error(f"Error getting Firebase trades: {e}")
            return []
    
    def _update_trade_as_closed(self, trade_id: str, closed_trade_details: Dict[str, Any]):
        """Update a trade in Firebase as closed with proper details."""
        try:
            trade_ref = (self.firebase_client.db
                        .collection('users').document(self.user_id)
                        .collection('submittedStrategies').document(self.strategy_id)
                        .collection('tradeHistory').document(trade_id))
            
            # Update with closed status and details
            update_data = {
                'status': 'CLOSED',
                'closeTime': closed_trade_details.get('close_time', datetime.now(timezone.utc).isoformat()),
                'realizedPL': closed_trade_details.get('realized_pl', 0.0)
            }
            
            # Add commission and spread cost if available
            if 'commission' in closed_trade_details:
                update_data['commission'] = closed_trade_details['commission']
            if 'half_spread_cost' in closed_trade_details:
                update_data['halfSpreadCost'] = closed_trade_details['half_spread_cost']
            
            trade_ref.update(update_data)
            self.logger.log_info(f"Updated trade {trade_id} as closed in Firebase")
            
        except Exception as e:
            self.logger.log_error(f"Error updating trade {trade_id} as closed: {e}")
            raise
    
    def _mark_trade_as_closed_unknown(self, trade_id: str):
        """Mark a trade as closed when we can't get details from OANDA."""
        try:
            trade_ref = (self.firebase_client.db
                        .collection('users').document(self.user_id)
                        .collection('submittedStrategies').document(self.strategy_id)
                        .collection('tradeHistory').document(trade_id))
            
            # Update with closed status but unknown details
            update_data = {
                'status': 'CLOSED',
                'closeTime': datetime.now(timezone.utc).isoformat(),
                'realizedPL': 0.0,  # Unknown P&L
                'note': 'Trade closed but details unavailable from OANDA'
            }
            
            trade_ref.update(update_data)
            self.logger.log_info(f"Marked trade {trade_id} as closed (unknown details) in Firebase")
            
        except Exception as e:
            self.logger.log_error(f"Error marking trade {trade_id} as closed: {e}")
            raise


def main():
    """Main function to run the trade sync fixer."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Fix trade synchronization between OANDA and Firebase')
    parser.add_argument('--user-id', required=True, help='Firebase user ID')
    parser.add_argument('--strategy-id', required=True, help='Strategy ID')
    parser.add_argument('--oanda-api-key', required=True, help='OANDA API key')
    parser.add_argument('--oanda-account-id', required=True, help='OANDA account ID')
    parser.add_argument('--output-file', help='Output file for results (optional)')
    
    args = parser.parse_args()
    
    # Create fixer and run diagnosis
    fixer = TradeSyncFixer(
        user_id=args.user_id,
        strategy_id=args.strategy_id,
        oanda_api_key=args.oanda_api_key,
        oanda_account_id=args.oanda_account_id
    )
    
    results = fixer.diagnose_and_fix()
    
    # Print results
    print("\n" + "="*60)
    print("TRADE SYNCHRONIZATION DIAGNOSIS AND FIX RESULTS")
    print("="*60)
    print(f"Timestamp: {results['timestamp']}")
    print(f"User ID: {results['user_id']}")
    print(f"Strategy ID: {results['strategy_id']}")
    print(f"\nOANDA Open Trades: {len(results['oanda_open_trades'])}")
    print(f"Firebase Open Trades: {len(results['firebase_open_trades'])}")
    print(f"Inconsistencies Found: {len(results['inconsistencies'])}")
    print(f"Fixes Applied: {len(results['fixes_applied'])}")
    
    if results['inconsistencies']:
        print("\n🚨 INCONSISTENCIES:")
        for inconsistency in results['inconsistencies']:
            print(f"  - {inconsistency['type']}: {inconsistency.get('description', 'No description')}")
    
    if results['fixes_applied']:
        print("\n✅ FIXES APPLIED:")
        for fix in results['fixes_applied']:
            print(f"  - {fix.get('action', 'Unknown action')}: {fix.get('trade_id', 'N/A')}")
    
    # Save to file if requested
    if args.output_file:
        with open(args.output_file, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n📄 Results saved to: {args.output_file}")
    
    print("\n" + "="*60)


if __name__ == "__main__":
    main()
