{"name": "Multi-Indicator Scalping Strategy ", "description": "", "instruments": "EUR/USD", "timeframe": "5m", "tradingSession": ["All"], "indicators": [{"id": "17513163649847hmqt9pxrqh", "type": "RSI", "indicator_class": "RSI", "parameters": {"period": 14}, "source": "close"}, {"id": "1751316375724coo0r15arz", "type": "MACD", "indicator_class": "MACD", "parameters": {"fast": 12, "slow": 26, "signal": 9}, "source": "close"}, {"id": "17513163834112anips072d7", "type": "BollingerBands", "indicator_class": "BollingerBands", "parameters": {"period": 20, "devfactor": 2, "offset": 0}, "source": "close"}, {"id": "1751316396013losrog1og2d", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 9}, "source": "close"}, {"id": "17513164006234m066qly9nh", "type": "EMA", "indicator_class": "EMA", "parameters": {"period": 21}, "source": "close"}, {"id": "1751316717326puzih9oez7k", "type": "ATR", "indicator_class": "ATR", "parameters": {"period": 14, "multiplier": 2}, "source": "price"}], "entryRules": [{"tradeType": "long", "indicator1": "17513163649847hmqt9pxrqh", "operator": "Crossing above", "compareType": "value", "indicator2": "", "value": "30", "logicalOperator": "AND", "barRef": "close", "id": "1751316445284lxr9qhx058"}, {"id": "1751316458474jmxify3roh", "tradeType": "long", "indicator1": "1751316375724coo0r15arz", "operator": "Crossing above", "compareType": "indicator", "indicator2": "1751316375724coo0r15arz", "value": "", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}, {"id": "1751316493880yu2fhbjvv9", "tradeType": "long", "indicator1": "price", "operator": ">=", "compareType": "indicator", "indicator2": "17513163834112anips072d7", "value": "", "barRef": "close", "band2": "lower"}, {"id": "175131650974507b1z8orlss9", "tradeType": "long", "indicator1": "1751316396013losrog1og2d", "operator": ">", "compareType": "indicator", "indicator2": "17513164006234m066qly9nh", "value": "", "barRef": "close"}, {"id": "1751316537658zsgsf12c4j", "tradeType": "short", "indicator1": "17513163649847hmqt9pxrqh", "operator": "Crossing below", "compareType": "value", "indicator2": "", "value": "70", "barRef": "close"}, {"id": "1751316552975z3yujtsqdrm", "tradeType": "short", "indicator1": "1751316375724coo0r15arz", "operator": "Crossing below", "compareType": "indicator", "indicator2": "1751316375724coo0r15arz", "value": "", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}, {"id": "1751316582815a6vey0i55cl", "tradeType": "short", "indicator1": "price", "operator": "<=", "compareType": "indicator", "indicator2": "17513163834112anips072d7", "value": "", "barRef": "close", "band2": "upper"}, {"id": "17513166040199nzq9gdlpum", "tradeType": "short", "indicator1": "1751316396013losrog1og2d", "operator": "<", "compareType": "indicator", "indicator2": "17513164006234m066qly9nh", "value": "", "barRef": "close"}], "exitRules": [{"id": "1751316623585y3tpy27hf3", "tradeType": "long", "indicator1": "17513163649847hmqt9pxrqh", "operator": ">=", "compareType": "value", "indicator2": "", "value": "70", "barRef": "close"}, {"id": "1751316636726f6oeo1qfhga", "tradeType": "long", "indicator1": "price", "operator": ">=", "compareType": "indicator", "indicator2": "17513163834112anips072d7", "value": "", "barRef": "close", "band2": "upper"}, {"id": "1751316649465p8cy0a79tzb", "tradeType": "long", "indicator1": "1751316375724coo0r15arz", "operator": "Crossing below", "compareType": "indicator", "indicator2": "1751316375724coo0r15arz", "value": "", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}, {"id": "175131666558332mtodq9ogq", "tradeType": "short", "indicator1": "17513163649847hmqt9pxrqh", "operator": "<=", "compareType": "value", "indicator2": "", "value": "30", "barRef": "close"}, {"id": "1751316678975sov6gb5jgio", "tradeType": "short", "indicator1": "price", "operator": "<=", "compareType": "indicator", "indicator2": "17513163834112anips072d7", "value": "", "barRef": "close", "band2": "lower"}, {"id": "1751316697045q6lfhvaply7", "tradeType": "short", "indicator1": "1751316375724coo0r15arz", "operator": "Crossing above", "compareType": "indicator", "indicator2": "1751316375724coo0r15arz", "value": "", "barRef": "close", "macdComponent": "macd", "macdComponent2": "signal"}], "riskManagement": {"riskPercentage": "0.5", "riskRewardRatio": "2", "stopLossMethod": "indicator", "fixedPips": "", "indicatorBasedSL": {"indicator": "atr", "parameters": {"period": 14, "multiplier": 1.5}}, "lotSize": "", "maxDailyLoss": "5%", "maxPositionSize": "10%", "runtime": 7, "totalProfitTarget": "20%", "totalLossLimit": "10%", "avoidHighSpread": false, "stopLoss": "1", "stopLossUnit": "percentage", "takeProfit": "2", "takeProfitUnit": "percentage"}, "entryLongGroupOperator": "AND", "entryShortGroupOperator": "AND", "exitLongGroupOperator": "OR", "exitShortGroupOperator": "OR", "user_id": "2tixoSldb3K4Fmz015AZQbTm2QfJ", "id": "v68tEzIw3VmFQhbSdkDt"}