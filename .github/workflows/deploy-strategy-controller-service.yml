name: Deploy Strategy Controller Service

on:
  push:
    branches: [ main, master ]
    paths:
      - 'strategy-controller-service/**'
  workflow_dispatch:

env:
  PROJECT_ID: oryntrade
  GKE_CLUSTER: oryn-trading-cluster
  GKE_ZONE: us-central1
  USE_GKE_GCLOUD_AUTH_PLUGIN: True
  SERVICE_NAME: strategy-controller

jobs:
  deploy:
    name: Deploy Strategy Controller Service to Kubernetes
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker us-central1-docker.pkg.dev

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --zone ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_ID }}

    - name: Verify kubectl connection
      run: |
        kubectl cluster-info
        kubectl get nodes

    - name: Deploy Strategy Controller Service
      working-directory: ./strategy-controller-service
      run: |
        echo "🚀 Starting Strategy Controller Service deployment..."
        chmod +x deploy-to-k8s.sh
        ./deploy-to-k8s.sh

    - name: Verify deployment
      run: |
        echo "🔍 Verifying Strategy Controller deployment..."
        kubectl get deployment strategy-controller
        kubectl get pods -l app=strategy-controller
        kubectl rollout status deployment/strategy-controller --timeout=300s

    - name: Verify service endpoints
      run: |
        echo "🔍 Checking service endpoints..."
        kubectl get service strategy-controller-service
        echo ""
        echo "📊 Testing health endpoint (if accessible)..."
        kubectl get pods -l app=strategy-controller -o jsonpath='{.items[0].metadata.name}' | xargs -I {} kubectl port-forward pod/{} 8080:8080 &
        sleep 5
        curl -f http://localhost:8080/ || echo "Health check endpoint not accessible from CI"
        pkill -f "kubectl port-forward" || true

    - name: Post deployment summary
      run: |
        echo "✅ Strategy Controller Service deployment completed successfully!"
        echo "📊 Deployment Status:"
        kubectl get deployment strategy-controller -o wide
        echo ""
        echo "🔍 Pod Status:"
        kubectl get pods -l app=strategy-controller -o wide
        echo ""
        echo "🌐 Service Status:"
        kubectl get service strategy-controller-service -o wide
