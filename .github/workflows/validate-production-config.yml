name: Validate Production Configuration

on:
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  check-development-mode:
    name: Check DEVELOPMENT_MODE Flag
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check DEVELOPMENT_MODE flag
      run: |
        echo "🔍 Checking DEVELOPMENT_MODE flag in config/config_loader.py..."

        if [ ! -f "config/config_loader.py" ]; then
          echo "❌ config/config_loader.py not found"
          exit 1
        fi

        if grep -q "DEVELOPMENT_MODE.*:.*True" config/config_loader.py; then
          echo "❌ DEVELOPMENT_MODE is set to True"
          echo "   This will cause services to use local emulators instead of production resources"
          echo "   Please run: ./use-prod.sh to switch to production mode"
          echo "   Or manually set: 'DEVELOPMENT_MODE': False in config/config_loader.py"
          exit 1
        elif grep -q "DEVELOPMENT_MODE.*:.*False" config/config_loader.py; then
          echo "✅ DEVELOPMENT_MODE is correctly set to False"
        else
          echo "⚠️ DEVELOPMENT_MODE flag not found or unclear"
          echo "   Please ensure 'DEVELOPMENT_MODE': False is set in config/config_loader.py"
          exit 1
        fi

  check-firebase-emulator:
    name: Check Firebase Emulator Flag
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check Firebase emulator flag
      run: |
        echo "🔍 Checking Firebase emulator flag in frontend/src/config.js..."

        if [ ! -f "frontend/src/config.js" ]; then
          echo "❌ frontend/src/config.js not found"
          exit 1
        fi

        if grep -q "USE_FIREBASE_EMULATOR = true" frontend/src/config.js; then
          echo "❌ USE_FIREBASE_EMULATOR is set to true"
          echo "   This will cause frontend to use Firebase emulator instead of production"
          echo "   Please run: ./use-prod.sh to switch to production mode"
          echo "   Or manually set: USE_FIREBASE_EMULATOR = false in frontend/src/config.js"
          exit 1
        elif grep -q "USE_FIREBASE_EMULATOR = false" frontend/src/config.js; then
          echo "✅ USE_FIREBASE_EMULATOR is correctly set to false"
        else
          echo "⚠️ USE_FIREBASE_EMULATOR flag not found or unclear"
          echo "   Please ensure USE_FIREBASE_EMULATOR = false is set in frontend/src/config.js"
          exit 1
        fi

  check-websocket-production:
    name: Check WebSocket Production Flag
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check WebSocket production flag
      run: |
        echo "🔍 Checking WebSocket production flag..."

        # Check frontend .env.local if it exists
        if [ -f "frontend/.env.local" ]; then
          if grep -q "NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET=false" frontend/.env.local; then
            echo "⚠️ NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET is set to false in frontend/.env.local"
            echo "   This will use local WebSocket service instead of production"
            echo "   Consider setting: NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET=true"
            exit 1
          elif grep -q "NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET=true" frontend/.env.local; then
            echo "✅ NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET is correctly set to true"
          else
            echo "ℹ️ NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET not found in frontend/.env.local (this is optional)"
          fi
        else
          echo "ℹ️ frontend/.env.local not found (this is optional)"
        fi

  check-oanda-practice-mode:
    name: Check OANDA Practice Mode
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check OANDA practice mode flags
      run: |
        echo "🔍 Checking OANDA practice mode flags..."

        # Check trade-bot-service Dockerfile
        if [ -f "trade-bot-service/Dockerfile" ]; then
          if grep -q "OANDA_PRACTICE_MODE=true" trade-bot-service/Dockerfile; then
            echo "⚠️ OANDA_PRACTICE_MODE=true in trade-bot-service/Dockerfile"
            echo "   This will use OANDA demo account instead of live trading"
            echo "   Set to false for live trading: OANDA_PRACTICE_MODE=false"
            # This is a warning, not an error - don't exit 1
          elif grep -q "OANDA_PRACTICE_MODE=false" trade-bot-service/Dockerfile; then
            echo "✅ OANDA_PRACTICE_MODE is set to false (live trading mode)"
          else
            echo "ℹ️ OANDA_PRACTICE_MODE not found in Dockerfile"
          fi
        fi

        # Check .env.example for reference
        if [ -f "trade-bot-service/.env.example" ]; then
          if grep -q "OANDA_PRACTICE_MODE=true" trade-bot-service/.env.example; then
            echo "ℹ️ .env.example shows OANDA_PRACTICE_MODE=true (demo mode)"
          fi
        fi

  check-market-data-provider:
    name: Check Market Data Provider
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check market data provider configuration
      run: |
        echo "🔍 Checking market data provider configuration..."

        # Check trade-bot-service Dockerfile
        if [ -f "trade-bot-service/Dockerfile" ]; then
          if grep -q "MARKET_DATA_PROVIDER=pubsub" trade-bot-service/Dockerfile; then
            echo "✅ MARKET_DATA_PROVIDER is set to pubsub (production mode)"
          elif grep -q "MARKET_DATA_PROVIDER=hybrid" trade-bot-service/Dockerfile; then
            echo "⚠️ MARKET_DATA_PROVIDER is set to hybrid (polling mode)"
            echo "   Consider using 'pubsub' for production real-time data"
            # This is a warning, not an error - don't exit 1
          else
            echo "ℹ️ MARKET_DATA_PROVIDER not found in Dockerfile"
          fi
        fi

  check-bypass-market-closed:
    name: Check Bypass Market Closed Flag
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check bypass market closed flag
      run: |
        echo "🔍 Checking bypass market closed flag..."

        # Check trade-bot-service Dockerfile
        if [ -f "trade-bot-service/Dockerfile" ]; then
          if grep -q "BYPASS_MARKET_IS_CLOSED=true" trade-bot-service/Dockerfile; then
            echo "❌ BYPASS_MARKET_IS_CLOSED=true in trade-bot-service/Dockerfile"
            echo "   This will allow trading when markets are closed (development mode)"
            echo "   Should be false for production: BYPASS_MARKET_IS_CLOSED=false"
            exit 1
          elif grep -q "BYPASS_MARKET_IS_CLOSED=false" trade-bot-service/Dockerfile; then
            echo "✅ BYPASS_MARKET_IS_CLOSED is correctly set to false"
          else
            echo "ℹ️ BYPASS_MARKET_IS_CLOSED not found in Dockerfile"
          fi
        fi

  summary:
    name: Configuration Summary
    runs-on: ubuntu-latest
    needs: [check-development-mode, check-firebase-emulator, check-websocket-production, check-oanda-practice-mode, check-market-data-provider, check-bypass-market-closed]
    if: always()

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configuration Summary
      run: |
        echo ""
        echo "📊 Production Configuration Summary"
        echo "=================================="
        echo ""

        # Check job results
        echo "🔧 Critical Production Flags:"

        # DEVELOPMENT_MODE
        if [ "${{ needs.check-development-mode.result }}" == "success" ]; then
          echo "   ✅ DEVELOPMENT_MODE: Correctly configured"
        else
          echo "   ❌ DEVELOPMENT_MODE: Failed validation"
        fi

        # Firebase emulator
        if [ "${{ needs.check-firebase-emulator.result }}" == "success" ]; then
          echo "   ✅ USE_FIREBASE_EMULATOR: Correctly configured"
        else
          echo "   ❌ USE_FIREBASE_EMULATOR: Failed validation"
        fi

        # WebSocket production
        if [ "${{ needs.check-websocket-production.result }}" == "success" ]; then
          echo "   ✅ WEBSOCKET_PRODUCTION: Correctly configured"
        else
          echo "   ⚠️ WEBSOCKET_PRODUCTION: Check warnings"
        fi

        # Bypass market closed
        if [ "${{ needs.check-bypass-market-closed.result }}" == "success" ]; then
          echo "   ✅ BYPASS_MARKET_IS_CLOSED: Correctly configured"
        else
          echo "   ❌ BYPASS_MARKET_IS_CLOSED: Failed validation"
        fi

        echo ""
        echo "⚠️ Optional Production Settings:"

        # OANDA mode
        if [ "${{ needs.check-oanda-practice-mode.result }}" == "success" ]; then
          echo "   ✅ OANDA_PRACTICE_MODE: Checked"
        else
          echo "   ⚠️ OANDA_PRACTICE_MODE: Check warnings"
        fi

        # Market data provider
        if [ "${{ needs.check-market-data-provider.result }}" == "success" ]; then
          echo "   ✅ MARKET_DATA_PROVIDER: Checked"
        else
          echo "   ⚠️ MARKET_DATA_PROVIDER: Check warnings"
        fi

        echo ""
        echo "💡 To switch to production mode, run: ./use-prod.sh"
        echo "💡 To switch to development mode, run: ./use-dev.sh"

        # Fail if any critical checks failed
        if [ "${{ needs.check-development-mode.result }}" != "success" ] || [ "${{ needs.check-firebase-emulator.result }}" != "success" ] || [ "${{ needs.check-bypass-market-closed.result }}" != "success" ]; then
          echo ""
          echo "❌ Critical production configuration checks failed!"
          echo "Please fix the issues above before merging to main/master"
          exit 1
        fi
