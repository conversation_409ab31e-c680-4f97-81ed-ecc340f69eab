name: Deploy Polygon WebSocket Service

on:
  push:
    branches: [ main, master ]
    paths:
      - 'polygon-websocket-service/**'
  workflow_dispatch:

env:
  PROJECT_ID: oryntrade
  SERVICE_NAME: polygon-websocket-ingestion
  REGION: us-central1

jobs:
  deploy:
    name: Deploy Polygon WebSocket Service to Cloud Run
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker gcr.io

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Set up Python for PubSub setup
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Python dependencies for setup
      working-directory: ./polygon-websocket-service
      run: |
        pip install google-cloud-pubsub

    - name: Verify Polygon API key secret
      run: |
        echo "🔍 Verifying Polygon API key secret exists..."
        if gcloud secrets describe polygon-api-key >/dev/null 2>&1; then
          echo "✅ Polygon API key secret found"
        else
          echo "❌ Polygon API key secret not found"
          echo "Please create it with: gcloud secrets create polygon-api-key --data-file=<path-to-api-key-file>"
          exit 1
        fi

    - name: Deploy Polygon WebSocket Service
      working-directory: ./polygon-websocket-service
      run: |
        echo "🚀 Starting Polygon WebSocket Service deployment..."
        chmod +x deploy.sh
        ./deploy.sh

    - name: Verify deployment
      run: |
        echo "🔍 Verifying Polygon WebSocket Service deployment..."
        
        # Get service URL
        SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format="value(status.url)")
        
        echo "📊 Service URL: $SERVICE_URL"
        
        # Wait for service to be ready
        echo "⏳ Waiting for service to be ready..."
        sleep 30
        
        # Test health endpoint
        if curl -f "$SERVICE_URL/health" 2>/dev/null; then
          echo "✅ Health check passed"
        else
          echo "⚠️ Health check endpoint not available or failed"
        fi

    - name: Verify PubSub topics
      run: |
        echo "🔍 Verifying PubSub topics..."
        
        # Check for required topics
        topics=("polygon.subscription_commands" "polygon.minute_aggregates")
        
        for topic in "${topics[@]}"; do
          if gcloud pubsub topics describe "$topic" >/dev/null 2>&1; then
            echo "✅ Topic $topic exists"
          else
            echo "⚠️ Topic $topic not found"
          fi
        done

    - name: Post deployment summary
      run: |
        echo "✅ Polygon WebSocket Service deployment completed successfully!"
        echo ""
        echo "📊 Service Information:"
        gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format="table(metadata.name,status.url,status.conditions[0].type,status.conditions[0].status)"
        echo ""
        echo "🌐 Service URL:"
        gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format="value(status.url)"
        echo ""
        echo "📡 PubSub Topics:"
        gcloud pubsub topics list --filter="name:polygon" --format="table(name)"
        echo ""
        echo "📈 To check logs:"
        echo "   gcloud logs tail --follow --resource-type=cloud_run_revision --resource-labels=service_name=${{ env.SERVICE_NAME }}"
