name: Deploy WebSocket Data Distribution Service

on:
  push:
    branches: [ main, master ]
    paths:
      - 'websocket-data-distribution-service/**'
  workflow_dispatch:

env:
  PROJECT_ID: oryntrade
  SERVICE_NAME: websocket-data-distribution-service
  REGION: us-central1

jobs:
  deploy:
    name: Deploy WebSocket Data Distribution Service to Cloud Run
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker gcr.io

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Verify Polygon API key secret
      run: |
        echo "🔍 Verifying Polygon API key secret exists..."
        if gcloud secrets describe polygon-api-key >/dev/null 2>&1; then
          echo "✅ Polygon API key secret found"
        else
          echo "❌ Polygon API key secret not found"
          echo "Please create it with: gcloud secrets create polygon-api-key --data-file=<path-to-api-key-file>"
          exit 1
        fi

    - name: Deploy WebSocket Data Distribution Service
      working-directory: ./websocket-data-distribution-service
      run: |
        echo "🚀 Starting WebSocket Data Distribution Service deployment..."
        chmod +x deploy-cloudrun.sh
        ./deploy-cloudrun.sh

    - name: Verify deployment
      run: |
        echo "🔍 Verifying WebSocket Data Distribution Service deployment..."
        
        # Get service URL
        SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format="value(status.url)")
        
        echo "📊 Service URL: $SERVICE_URL"
        
        # Wait for service to be ready
        echo "⏳ Waiting for service to be ready..."
        sleep 30
        
        # Test health endpoint (if available)
        if curl -f "$SERVICE_URL/health" 2>/dev/null; then
          echo "✅ Health check passed"
        else
          echo "⚠️ Health check endpoint not available or failed"
        fi

    - name: Post deployment summary
      run: |
        echo "✅ WebSocket Data Distribution Service deployment completed successfully!"
        echo ""
        echo "📊 Service Information:"
        gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format="table(metadata.name,status.url,status.conditions[0].type,status.conditions[0].status)"
        echo ""
        echo "🌐 Service URL:"
        gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format="value(status.url)"
        echo ""
        echo "📈 To check logs:"
        echo "   gcloud logs tail --follow --resource-type=cloud_run_revision --resource-labels=service_name=${{ env.SERVICE_NAME }}"
