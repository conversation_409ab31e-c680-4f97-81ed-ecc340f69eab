name: Deploy Trade Bot Service

on:
  push:
    branches: [ main, master ]
    paths:
      - 'trade-bot-service/**'
  workflow_dispatch:

env:
  PROJECT_ID: oryntrade
  GKE_CLUSTER: oryn-trading-cluster
  GKE_ZONE: us-central1
  USE_GKE_GCLOUD_AUTH_PLUGIN: True
  SERVICE_NAME: trade-bot

jobs:
  deploy:
    name: Deploy Trade Bot Service to Kubernetes
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Check for GCP Service Account Key
      run: |
        if [ -z "$GCP_SA_KEY" ]; then
          echo "❌ GCP_SA_KEY secret is not configured"
          echo "📋 To fix this:"
          echo "   1. Go to Settings → Secrets and variables → Actions"
          echo "   2. Add a new repository secret named 'GCP_SA_KEY'"
          echo "   3. Use the content from github-actions-key.json"
          echo ""
          echo "⚠️ Deployment will fail without this secret"
          exit 1
        else
          echo "✅ GCP_SA_KEY secret is configured"
        fi
      env:
        GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        project_id: ${{ env.PROJECT_ID }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker us-central1-docker.pkg.dev

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --zone ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_ID }}

    - name: Verify kubectl connection
      run: |
        kubectl cluster-info
        kubectl get nodes

    - name: Deploy Trade Bot Service
      working-directory: ./trade-bot-service
      run: |
        echo "🚀 Starting Trade Bot Service deployment..."
        chmod +x deploy-to-k8s.sh
        ./deploy-to-k8s.sh

    - name: Verify deployment
      run: |
        echo "🔍 Verifying Trade Bot deployment..."
        kubectl get deployment trade-bot-base
        kubectl get pods -l app=trade-bot
        kubectl rollout status deployment/trade-bot-base --timeout=300s

    - name: Post deployment summary
      run: |
        echo "✅ Trade Bot Service deployment completed successfully!"
        echo "📊 Deployment Status:"
        kubectl get deployment trade-bot-base -o wide
        echo ""
        echo "🔍 Pod Status:"
        kubectl get pods -l app=trade-bot -o wide
