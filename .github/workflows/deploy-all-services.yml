name: Deploy All Services

on:
  push:
    branches: [ main, master ]
    paths:
      - 'k8s-manifests/**'
      - 'config/**'
      - '.github/workflows/deploy-all-services.yml'
  workflow_dispatch:
    inputs:
      deploy_trade_bot:
        description: 'Deploy Trade Bot Service'
        required: false
        default: 'true'
        type: boolean
      deploy_strategy_controller:
        description: 'Deploy Strategy Controller Service'
        required: false
        default: 'true'
        type: boolean
      deploy_websocket_data_distribution:
        description: 'Deploy WebSocket Data Distribution Service'
        required: false
        default: 'true'
        type: boolean
      deploy_polygon_websocket:
        description: 'Deploy Polygon WebSocket Service'
        required: false
        default: 'true'
        type: boolean

env:
  PROJECT_ID: oryntrade
  GKE_CLUSTER: oryn-trading-cluster
  GKE_ZONE: us-central1
  USE_GKE_GCLOUD_AUTH_PLUGIN: True

jobs:
  deploy-trade-bot:
    name: Deploy Trade Bot Service
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'push' && github.ref == 'refs/heads/master') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_trade_bot == 'true')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker us-central1-docker.pkg.dev

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --zone ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_ID }}

    - name: Deploy Trade Bot Service
      working-directory: ./trade-bot-service
      run: |
        echo "🚀 Starting Trade Bot Service deployment..."
        chmod +x deploy-to-k8s.sh
        ./deploy-to-k8s.sh

    - name: Verify Trade Bot deployment
      run: |
        echo "🔍 Verifying Trade Bot deployment..."
        kubectl rollout status deployment/trade-bot-base --timeout=300s

  deploy-strategy-controller:
    name: Deploy Strategy Controller Service
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'push' && github.ref == 'refs/heads/master') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_strategy_controller == 'true')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker us-central1-docker.pkg.dev

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --zone ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_ID }}

    - name: Deploy Strategy Controller Service
      working-directory: ./strategy-controller-service
      run: |
        echo "🚀 Starting Strategy Controller Service deployment..."
        chmod +x deploy-to-k8s.sh
        ./deploy-to-k8s.sh

    - name: Verify Strategy Controller deployment
      run: |
        echo "🔍 Verifying Strategy Controller deployment..."
        kubectl rollout status deployment/strategy-controller --timeout=300s

  deploy-websocket-data-distribution:
    name: Deploy WebSocket Data Distribution Service
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'push' && github.ref == 'refs/heads/master') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_websocket_data_distribution == 'true')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker gcr.io

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Deploy WebSocket Data Distribution Service
      working-directory: ./websocket-data-distribution-service
      run: |
        echo "🚀 Starting WebSocket Data Distribution Service deployment..."
        chmod +x deploy-cloudrun.sh
        ./deploy-cloudrun.sh

  deploy-polygon-websocket:
    name: Deploy Polygon WebSocket Service
    runs-on: ubuntu-latest
    if: |
      (github.event_name == 'push' && github.ref == 'refs/heads/main') ||
      (github.event_name == 'push' && github.ref == 'refs/heads/master') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_polygon_websocket == 'true')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Google Container Registry
      run: |
        gcloud auth configure-docker gcr.io

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Set up Python for PubSub setup
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Python dependencies for setup
      working-directory: ./polygon-websocket-service
      run: |
        pip install google-cloud-pubsub

    - name: Deploy Polygon WebSocket Service
      working-directory: ./polygon-websocket-service
      run: |
        echo "🚀 Starting Polygon WebSocket Service deployment..."
        chmod +x deploy.sh
        ./deploy.sh

  post-deployment:
    name: Post-Deployment Summary
    runs-on: ubuntu-latest
    needs: [deploy-trade-bot, deploy-strategy-controller, deploy-websocket-data-distribution, deploy-polygon-websocket]
    if: always()
    
    steps:
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Google Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        install_components: 'gke-gcloud-auth-plugin'

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --zone ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_ID }}

    - name: Deployment Summary
      run: |
        echo "📊 Deployment Summary"
        echo "===================="
        echo ""
        echo "🤖 Trade Bot Service:"
        kubectl get deployment trade-bot-base -o wide || echo "❌ Trade Bot deployment not found"
        echo ""
        echo "🎯 Strategy Controller Service:"
        kubectl get deployment strategy-controller -o wide || echo "❌ Strategy Controller deployment not found"
        echo ""
        echo "📋 All Pods:"
        kubectl get pods -l 'app in (trade-bot,strategy-controller)' -o wide
        echo ""
        echo "🌐 Kubernetes Services:"
        kubectl get services -l 'app in (trade-bot,strategy-controller)' -o wide
        echo ""
        echo "☁️ Cloud Run Services:"
        gcloud run services list --filter="metadata.name:(websocket-data-distribution-service OR polygon-websocket-ingestion)" --format="table(metadata.name,status.url,status.conditions[0].type)" --region=us-central1 || echo "No Cloud Run services found"
