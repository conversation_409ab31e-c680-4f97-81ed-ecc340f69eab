# GitHub Actions CI/CD Workflows

This directory contains GitHub Actions workflows for automated deployment of services to the Kubernetes cluster.

## Workflows

### 1. Deploy Trade Bot Service (`deploy-trade-bot-service.yml`)
- **Triggers**: Push to main/master with changes to `trade-bot-service/**` directory
- **Target**: Kubernetes deployment using `trade-bot-service/deploy-to-k8s.sh`
- **Deployment**: Creates/updates `trade-bot-base` deployment

### 2. Deploy Strategy Controller Service (`deploy-strategy-controller-service.yml`)
- **Triggers**: Push to main/master with changes to `strategy-controller-service/**` directory
- **Target**: Kubernetes deployment using `strategy-controller-service/deploy-to-k8s.sh`
- **Deployment**: Creates/updates `strategy-controller` deployment

### 3. Deploy WebSocket Data Distribution Service (`deploy-websocket-data-distribution-service.yml`)
- **Triggers**: Push to main/master with changes to `websocket-data-distribution-service/**` directory
- **Target**: Cloud Run deployment using `websocket-data-distribution-service/deploy-cloudrun.sh`
- **Deployment**: Creates/updates `websocket-data-distribution-service` Cloud Run service

### 4. Deploy Polygon WebSocket Service (`deploy-polygon-websocket-service.yml`)
- **Triggers**: Push to main/master with changes to `polygon-websocket-service/**` directory
- **Target**: Cloud Run deployment using `polygon-websocket-service/deploy.sh`
- **Deployment**: Creates/updates `polygon-websocket-ingestion` Cloud Run service

## Setup Requirements

### 1. GitHub Repository Secrets

You need to configure the following secrets in your GitHub repository:

#### `GCP_SA_KEY`
A Google Cloud Service Account key with the following permissions:
- **Kubernetes Engine Admin** (`roles/container.admin`)
- **Storage Admin** (`roles/storage.admin`) 
- **Artifact Registry Administrator** (`roles/artifactregistry.admin`)
- **Cloud Build Editor** (`roles/cloudbuild.builds.editor`)

**To create the service account:**

```bash
# Create service account
gcloud iam service-accounts create github-actions-sa \
    --display-name="GitHub Actions Service Account" \
    --project=oryntrade

# Grant necessary roles
gcloud projects add-iam-policy-binding oryntrade \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/container.admin"

gcloud projects add-iam-policy-binding oryntrade \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding oryntrade \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/artifactregistry.admin"

gcloud projects add-iam-policy-binding oryntrade \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/cloudbuild.builds.editor"

# Create and download key
gcloud iam service-accounts keys create github-actions-key.json \
    --iam-account=<EMAIL>
```

**Add the key to GitHub:**
1. Go to your repository → Settings → Secrets and variables → Actions
2. Click "New repository secret"
3. Name: `GCP_SA_KEY`
4. Value: Copy the entire contents of `github-actions-key.json`

### 2. Kubernetes Cluster Configuration

Ensure your GKE cluster is properly configured:

```bash
# Verify cluster exists and is accessible
gcloud container clusters get-credentials oryn-trading-cluster \
    --zone us-central1 --project oryntrade

# Verify kubectl access
kubectl cluster-info
kubectl get nodes
```

### 3. Docker Registry Access

The workflows use Google Artifact Registry. Ensure the registry exists:

```bash
# Create repository if it doesn't exist
gcloud artifacts repositories create oryn-containers \
    --repository-format=docker \
    --location=us-central1 \
    --project=oryntrade
```

## Workflow Behavior

### Trigger Conditions
- **Push to main/master**: Deploys automatically
- **Pull Request**: Runs validation only (no deployment)
- **Manual Trigger**: Can be triggered manually via GitHub Actions UI

### Path-based Triggering
- Trade Bot workflow only runs on main/master push when files in `trade-bot-service/` change
- Strategy Controller workflow only runs on main/master push when files in `strategy-controller-service/` change
- WebSocket Data Distribution workflow only runs on main/master push when files in `websocket-data-distribution-service/` change
- Polygon WebSocket workflow only runs on main/master push when files in `polygon-websocket-service/` change

### Deployment Process
1. **Authentication**: Authenticate with Google Cloud using service account
2. **Docker Setup**: Configure Docker for Artifact Registry access
3. **Kubernetes Access**: Get GKE cluster credentials
4. **Deployment**: Execute the respective `deploy-to-k8s.sh` script
5. **Verification**: Verify deployment status and pod health

## Monitoring Deployments

### GitHub Actions UI
- Go to your repository → Actions tab
- View workflow runs, logs, and status

### Kubernetes Monitoring
```bash
# Check deployment status
kubectl get deployments
kubectl get pods

# View logs
kubectl logs -l app=trade-bot
kubectl logs -l app=strategy-controller

# Check rollout status
kubectl rollout status deployment/trade-bot-base
kubectl rollout status deployment/strategy-controller
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify `GCP_SA_KEY` secret is correctly set
   - Ensure service account has required permissions

2. **Docker Build Failures**
   - Check Dockerfile syntax in respective service directories
   - Verify base images are accessible

3. **Kubernetes Deployment Failures**
   - Check cluster connectivity
   - Verify deployment YAML files are valid
   - Ensure required secrets exist in cluster

4. **Script Execution Errors**
   - Verify `deploy-to-k8s.sh` scripts have execute permissions
   - Check script dependencies and requirements

### Debug Commands
```bash
# Test service account locally
gcloud auth activate-service-account --key-file=github-actions-key.json
gcloud auth list

# Test cluster access
kubectl cluster-info
kubectl get namespaces

# Test Docker registry access
docker pull us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest
```

## Manual Deployment

If you need to deploy manually, you can still use the existing scripts:

```bash
# Trade Bot Service
cd trade-bot-service
./deploy-to-k8s.sh

# Strategy Controller Service  
cd strategy-controller-service
./deploy-to-k8s.sh
```
