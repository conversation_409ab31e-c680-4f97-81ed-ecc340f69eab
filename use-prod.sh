#!/bin/bash
# Switch to production mode

echo "🚀 Switching to PRODUCTION mode..."
echo ""

# Update the configuration in config_loader.py
echo "📝 Updating config/config_loader.py..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/'DEVELOPMENT_MODE': True/'DEVELOPMENT_MODE': False/" config/config_loader.py
else
    # Linux
    sed -i "s/'DEVELOPMENT_MODE': True/'DEVELOPMENT_MODE': False/" config/config_loader.py
fi
echo "✅ Backend configuration updated to PRODUCTION mode"

# Update frontend configuration
echo "📝 Updating frontend/src/config.js..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' 's/export const USE_FIREBASE_EMULATOR = true/export const USE_FIREBASE_EMULATOR = false/' frontend/src/config.js
else
    # Linux
    sed -i 's/export const USE_FIREBASE_EMULATOR = true/export const USE_FIREBASE_EMULATOR = false/' frontend/src/config.js
fi
echo "✅ Frontend configuration updated to PRODUCTION mode"

echo ""
echo "✅ Switched to PRODUCTION mode"
echo ""
echo "📋 Production Configuration:"
echo "   - Firebase: Using production Firestore"
echo "   - PubSub: Using production PubSub"
echo "   - Strategy Controller: Kubernetes cluster"
echo "   - Frontend: localhost:3000 (with port forwarding)"
echo ""
echo "🔧 To use production environment:"
echo ""
echo "   Terminal 1 - Port Forward to Kubernetes:"
echo "   kubectl port-forward svc/strategy-controller-service 8080:80"
echo ""
echo "   Terminal 2 - Frontend:"
echo "   cd frontend && npm run dev"
echo ""
echo "🌐 Access URLs:"
echo "   • Frontend: http://localhost:3000"
echo "   • Strategy Controller: http://localhost:8080 (via port forward)"
echo "   • Monitoring Dashboard: http://localhost:8080/monitoring"
echo ""
echo "🧪 Test production pipeline:"
echo "   python3 publish-strategy-message.py"
echo ""
echo "⚠️  Note: Strategy controller and trade bots run in Kubernetes cluster"
echo ""
