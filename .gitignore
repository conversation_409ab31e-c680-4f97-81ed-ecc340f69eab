# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
firebase-debug.log*
firebase-debug.*.log*

# Firebase cache
.firebase/

# Firebase config

# Uncomment this if you'd like others to create their own Firebase project.
# For a team working on the same Firebase project(s), it is recommended to leave
# it commented so all members can deploy to the same project(s) in .firebaserc.
# .firebaserc

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

/functions/venv
/functions/__pycache__
/functions/algebra

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Firebase
.firebase/
firebase-debug.log
firebase-debug.*.log
ui-debug.log
ui-debug.*.log
.firebaserc

# Service Account Keys
*-service-account*.json
service-account*.json
*.json.key
github-actions-key.json

# Security - API Keys and Secrets
scripts/*.key
*.pem
*.p12
.env.local
.env.production
functions/env-vars.yaml

# Node.js
node_modules/
.npm/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
