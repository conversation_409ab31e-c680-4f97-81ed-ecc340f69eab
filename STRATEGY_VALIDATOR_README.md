# Universal Strategy JSON Validator for Trade Bot

A comprehensive validation tool that tests any strategy JSON to ensure it can be run successfully on the trade bot. This validator checks all components including JSON parsing, indicator calculations, signal detection, risk management, and position sizing.

## 🎯 What It Validates

### ✅ **Core Components**
- **JSON Structure**: Validates required fields and format
- **Strategy Initialization**: Tests strategy object creation
- **Indicator Calculations**: Verifies all indicators compute correctly
- **Signal Detection**: Tests entry/exit signal generation logic
- **Risk Management**: Validates stop loss and take profit configuration
- **Position Sizing**: Tests position size calculations across different account sizes

### 📊 **Supported Indicators**
- **RSI** (Relative Strength Index)
- **SMA** (Simple Moving Average)
- **EMA** (Exponential Moving Average)
- **MACD** (Moving Average Convergence Divergence)
- **Bollinger Bands** (Upper, Middle, Lower)
- **ATR** (Average True Range)
- **Support & Resistance** (Dynamic levels)

### 🎯 **Risk Management Methods**
- **Fixed Pips**: Fixed stop loss in pips
- **Indicator-Based**: ATR, Bollinger Bands, Support/Resistance
- **Risk-Based**: User-specified lot size with calculated stop loss

## 🚀 Usage

### **Method 1: JSON File**
```bash
python test_strategy_validator.py strategy.json
```

### **Method 2: JSON String**
```bash
python test_strategy_validator.py --json '{"name":"MyStrategy","instruments":"EUR/USD",...}'
```

### **Method 3: Interactive Mode**
```bash
python test_strategy_validator.py --interactive
# Then paste your JSON and press Ctrl+D
```

## 📋 Example Output

```
🧪 Universal Strategy JSON Validator
============================================================
📋 Step 1: Validating JSON structure...
   ✅ JSON structure valid
📋 Step 2: Initializing strategy...
   ✅ Strategy initialized successfully
📋 Step 3: Validating indicators...
   ✅ Calculated 4 indicator types
   📊 RSI: 136 valid values
   📊 SMA: 131 valid values
📋 Step 4: Testing signal detection...
   ⚠️  No signals generated (may be normal depending on strategy)
📋 Step 5: Validating risk management...
   ✅ Risk management configured: fixed
   📊 Risk: 2%, SL: 20, TP: 40.0
📋 Step 6: Testing position sizing...
   💰 $1,000 account → 10,000 units
   💰 $10,000 account → 100,000 units
   💰 $50,000 account → 500,000 units
   ✅ Position sizing working

🎉 Strategy validation PASSED!

============================================================
📋 VALIDATION SUMMARY
============================================================
Overall Status: ✅ PASSED

📊 Component Status:
   ✅ Json Parsing
   ✅ Strategy Initialization
   ✅ Indicators
   ✅ Signal Detection
   ✅ Risk Management
   ✅ Position Sizing

💡 Recommendations:
   ✅ Strategy is ready for trade bot deployment!
   ✅ All components are working correctly
```

## 📝 Required JSON Structure

Your strategy JSON must include these required fields:

```json
{
  "name": "Strategy Name",
  "instruments": "EUR/USD",
  "timeframe": "5m",
  "indicators": [
    {
      "id": "unique_id",
      "indicator_class": "RSI",
      "parameters": {"period": 14},
      "source": "price"
    }
  ],
  "entryRules": [
    {
      "tradeType": "long",
      "indicator1": "unique_id",
      "operator": "Crossing above",
      "compareType": "value",
      "value": "30"
    }
  ],
  "riskManagement": {
    "riskPercentage": "2",
    "riskRewardRatio": "2",
    "stopLossMethod": "fixed",
    "fixedPips": "20"
  }
}
```

## 🔍 Validation Steps Explained

### **Step 1: JSON Structure**
- Checks for required fields: `name`, `instruments`, `timeframe`, `indicators`, `entryRules`, `riskManagement`
- Validates JSON format and syntax

### **Step 2: Strategy Initialization**
- Creates BaseStrategy object from JSON
- Tests parameter extraction and validation
- Verifies risk management configuration

### **Step 3: Indicator Validation**
- Generates realistic test market data (150 candles)
- Calculates all indicators specified in strategy
- Verifies indicator values are generated correctly
- Checks for proper indicator dependencies

### **Step 4: Signal Detection**
- Tests signal generation across multiple market scenarios
- Validates entry and exit rule logic
- Checks indicator value comparisons (crossing above/below, etc.)
- Tests logical operators (AND/OR)

### **Step 5: Risk Management**
- Validates stop loss method configuration
- Tests risk percentage and reward ratio calculations
- Verifies indicator-based stop loss parameters
- Checks take profit calculations

### **Step 6: Position Sizing**
- Tests position size calculations for different account balances
- Validates pip value calculations for different instruments
- Checks risk-based position sizing logic
- Verifies position size scaling

## ⚠️ Common Issues & Solutions

### **❌ "Missing required fields"**
**Solution**: Ensure your JSON includes all required fields listed above.

### **❌ "Strategy initialization failed"**
**Solution**: Check indicator IDs match between `indicators` array and `entryRules`/`exitRules`.

### **❌ "Indicator calculation failed"**
**Solution**: Verify indicator parameters are correct (e.g., period > 0).

### **❌ "No signals generated"**
**Solution**: This is often normal - signals depend on market conditions. The validator uses test data that may not trigger your specific conditions.

### **❌ "Position sizing failed"**
**Solution**: Check risk management configuration, especially `riskPercentage` and stop loss settings.

## 🎯 Exit Codes

- **0**: Validation passed - strategy is ready for deployment
- **1**: Validation failed - fix errors before deployment

## 📚 Integration with Trade Bot

Once your strategy passes validation:

1. ✅ **Deploy to Trade Bot**: Your strategy JSON is compatible
2. ✅ **All Indicators Work**: Calculations will run correctly
3. ✅ **Signals Will Generate**: Entry/exit logic is functional
4. ✅ **Risk Management Active**: Stop loss and take profit will work
5. ✅ **Position Sizing Correct**: Trade sizes will be calculated properly

## 🔧 Advanced Usage

### **Custom Test Data**
The validator generates realistic test data automatically, but you can modify the `generate_realistic_test_candles()` function to create specific market conditions for testing.

### **Verbose Logging**
The validator includes detailed logging from the BaseStrategy class, showing exactly how indicators are calculated and signals are generated.

### **Batch Testing**
You can create a script to test multiple strategies:

```bash
for strategy in *.json; do
    echo "Testing $strategy..."
    python test_strategy_validator.py "$strategy"
done
```

## 🎉 Success Criteria

Your strategy is ready for trade bot deployment when:

- ✅ All 6 validation steps pass
- ✅ No critical errors reported
- ✅ Position sizing works for different account sizes
- ✅ Indicators calculate without warnings
- ✅ Risk management is properly configured

**Happy Trading!** 🚀
