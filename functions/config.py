import os
from dotenv import load_dotenv

# Load environment variables from .env file in development
load_dotenv()

def is_running_in_firebase():
    """Detects if the code is running in the Firebase Functions environment."""
    return 'FIREBASE_CONFIG' in os.environ

def get_config():
    # Check if we're running in Firebase Functions
    if is_running_in_firebase():
        # Production environment
        return {
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
            'OANDA_API_KEY': os.getenv('OANDA_API_KEY'),
            'OANDA_API_URL': os.getenv('OANDA_API_URL'),
            'BUCKET_NAME': os.getenv('BUCKET_NAME'),
            'CSV_FILENAME': os.getenv('CSV_FILENAME'),
            'DEFAULT_SYMBOL': os.getenv('DEFAULT_SYMBOL'),
            'DEFAULT_TIMEFRAME': os.getenv('DEFAULT_TIMEFRAME'),
            'STRIPE_SECRET_KEY': os.getenv('STRIPE_SECRET_KEY'),
            'STRIPE_PUBLISHABLE_KEY': os.getenv('STRIPE_PUBLISHABLE_KEY'),
            'USE_FIREBASE_EMULATOR': False,
            'LOCAL_FOREX_DATA_DIR': os.getenv('LOCAL_FOREX_DATA_DIR', 'forex_data')
        }
    else:
        # Development environment
        return {
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
            'OANDA_API_KEY': os.getenv('OANDA_API_KEY'),
            'OANDA_API_URL': os.getenv('OANDA_API_URL', 'https://api-fxpractice.oanda.com/v3'),
            'BUCKET_NAME': os.getenv('BUCKET_NAME', 'your-backtest-data-bucket'),
            'CSV_FILENAME': os.getenv('CSV_FILENAME', 'eur_usd_hourly.csv'),
            'DEFAULT_SYMBOL': os.getenv('DEFAULT_SYMBOL', 'EUR_USD'),
            'DEFAULT_TIMEFRAME': os.getenv('DEFAULT_TIMEFRAME', 'H1'),
            'STRIPE_SECRET_KEY': os.getenv('STRIPE_SECRET_KEY'),
            'STRIPE_PUBLISHABLE_KEY': os.getenv('STRIPE_PUBLISHABLE_KEY'),
            'USE_FIREBASE_EMULATOR': os.getenv('USE_FIREBASE_EMULATOR', 'false').lower() == 'true',
            'LOCAL_FOREX_DATA_DIR': os.getenv('LOCAL_FOREX_DATA_DIR', 'forex_data')
        }

# Load configuration
config = get_config()

# OpenAI API Key
OPENAI_API_KEY = config['OPENAI_API_KEY']
if OPENAI_API_KEY:
    OPENAI_API_KEY = OPENAI_API_KEY.strip()  # Remove any trailing whitespace/newlines
if not OPENAI_API_KEY:
    print("Warning: OPENAI_API_KEY is not set. Please set it in your environment variables or .env file")

# OANDA Configuration
OANDA_API_KEY = config['OANDA_API_KEY']
OANDA_API_URL = config['OANDA_API_URL']

# Storage Configuration
BUCKET_NAME = config['BUCKET_NAME']
CSV_FILENAME = config['CSV_FILENAME']

# Default Trading Parameters
DEFAULT_SYMBOL = config['DEFAULT_SYMBOL']
DEFAULT_TIMEFRAME = config['DEFAULT_TIMEFRAME']

# Stripe Configuration
STRIPE_SECRET_KEY = config['STRIPE_SECRET_KEY']
STRIPE_PUBLISHABLE_KEY = config['STRIPE_PUBLISHABLE_KEY']

# Firebase Configuration
USE_FIREBASE_EMULATOR = config['USE_FIREBASE_EMULATOR']

# Local Data Directory
LOCAL_FOREX_DATA_DIR = config['LOCAL_FOREX_DATA_DIR'] 