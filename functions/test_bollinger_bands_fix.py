#!/usr/bin/env python3
"""
Test script to verify Bollinger Bands calculation fix and PnL calculation fix.
This script tests the user's reported strategy: RSI(14) > 30 AND price(close) crossing above lower Bollinger Band.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oryn_backtest_engine.engine import OrynBacktestEngine
from oryn_backtest_engine.models.strategy import (
    Strategy, IndicatorConfig, RuleCondition, RiskManagement, TradingCosts,
    TradeType, CompareType, Operator, StopLossMethod
)

def create_sample_data():
    """Create sample EUR/USD data for testing."""
    # Generate 1000 data points with realistic EUR/USD prices
    np.random.seed(42)  # For reproducible results
    
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='h')
    
    # Start with a base price around 1.1000
    base_price = 1.1000
    prices = []
    current_price = base_price
    
    for i in range(1000):
        # Add some realistic price movement
        change = np.random.normal(0, 0.0005)  # Small random changes
        current_price += change
        
        # Keep price in reasonable range
        current_price = max(1.0500, min(1.1500, current_price))
        prices.append(current_price)
    
    # Create OHLC data
    data = []
    for i, price in enumerate(prices):
        # Create realistic OHLC from the close price
        close = price
        open_price = close + np.random.normal(0, 0.0002)
        high = max(open_price, close) + abs(np.random.normal(0, 0.0003))
        low = min(open_price, close) - abs(np.random.normal(0, 0.0003))
        
        data.append({
            'timestamp': dates[i],
            'open': round(open_price, 5),
            'high': round(high, 5),
            'low': round(low, 5),
            'close': round(close, 5),
            'volume': np.random.randint(1000, 10000)
        })
    
    df = pd.DataFrame(data)
    # The backtest engine needs both: timestamp column for validation AND datetime index for processing
    df.set_index('timestamp', inplace=True)
    # Add timestamp column back for validation
    df['timestamp'] = df.index
    return df

def create_test_strategy():
    """Create the user's strategy: RSI(14) > 30 AND price(close) crossing above lower Bollinger Band."""
    
    # Create indicators
    rsi_indicator = IndicatorConfig(
        id="rsi_14",
        type="RSI",
        parameters={"period": 14},
        source="close"
    )
    
    bb_indicator = IndicatorConfig(
        id="bb_20_2",
        type="BollingerBands", 
        parameters={"period": 20, "devfactor": 2},
        source="close"
    )
    
    # Create entry rules
    # Rule 1: RSI(14) > 30
    rsi_rule = RuleCondition(
        trade_type=TradeType.LONG,
        indicator1="rsi_14",
        operator=Operator.GREATER_THAN,
        compare_type=CompareType.VALUE,
        value=30,
        bar_ref="close"
    )

    # Rule 2: price(close) crossing above lower Bollinger Band
    bb_cross_rule = RuleCondition(
        trade_type=TradeType.LONG,
        indicator1="price",
        operator=Operator.CROSSING_ABOVE,
        compare_type=CompareType.INDICATOR,
        indicator2="bb_20_2",
        band2="lower",
        bar_ref="close"
    )

    # Create exit rules (simple RSI overbought exit)
    exit_rule = RuleCondition(
        trade_type=TradeType.LONG,
        indicator1="rsi_14",
        operator=Operator.GREATER_THAN,
        compare_type=CompareType.VALUE,
        value=70,
        bar_ref="close"
    )
    
    # Risk management
    risk_management = RiskManagement(
        risk_percentage="1",
        risk_reward_ratio="2",
        stop_loss_method=StopLossMethod.FIXED,
        fixed_pips="20"
    )
    
    # Trading costs (realistic for testing)
    trading_costs = TradingCosts(
        spread_pips=0.2,  # Realistic EUR/USD spread
        commission_percentage=0.0,
        commission_fixed=0.0
    )
    
    return Strategy(
        indicators=[rsi_indicator, bb_indicator],
        entry_rules=[rsi_rule, bb_cross_rule],
        exit_rules=[exit_rule],
        risk_management=risk_management,
        trading_costs=trading_costs,
        entry_buy_group_operator="AND"
    )

def test_bollinger_bands_calculation():
    """Test that Bollinger Bands are calculated correctly."""
    print("🔍 Testing Bollinger Bands calculation...")
    
    # Create simple test data
    test_data = pd.DataFrame({
        'timestamp': pd.date_range('2024-01-01', periods=50, freq='h'),
        'open': [1.1000] * 50,
        'high': [1.1010] * 50,
        'low': [1.0990] * 50,
        'close': [1.1000 + i * 0.0001 for i in range(50)],  # Trending up
        'volume': [1000] * 50
    })
    # Set timestamp as index for backtest engine and keep column for validation
    test_data.set_index('timestamp', inplace=True)
    test_data['timestamp'] = test_data.index
    
    # Create engine and strategy
    engine = OrynBacktestEngine(test_data)
    strategy = create_test_strategy()
    
    # Setup indicators
    engine._setup_indicators(strategy)
    
    # Check if Bollinger Bands indicator exists
    bb_indicator = engine.indicators.get("bb_20_2")
    if bb_indicator is None:
        print("❌ Bollinger Bands indicator not found!")
        return False
    
    # Test Bollinger Bands values at index 25 (after warm-up period)
    lower, middle, upper = bb_indicator.get_bands(25)
    
    print(f"   BB values at index 25: Lower={lower}, Middle={middle}, Upper={upper}")
    
    if lower is None or middle is None or upper is None:
        print("❌ Bollinger Bands returned None values!")
        return False
    
    # Verify that lower < middle < upper
    if not (lower < middle < upper):
        print(f"❌ Bollinger Bands order incorrect: {lower} < {middle} < {upper}")
        return False
    
    print("✅ Bollinger Bands calculation test passed!")
    return True

def run_backtest_test():
    """Run a full backtest with the user's strategy."""
    print("\n🚀 Running backtest test...")
    
    # Create test data
    data = create_sample_data()
    print(f"   Created {len(data)} data points from {data.index[0]} to {data.index[-1]}")
    
    # Create strategy
    strategy = create_test_strategy()
    print(f"   Strategy: RSI(14) > 30 AND price crossing above lower BB(20,2)")
    
    # Run backtest
    engine = OrynBacktestEngine(data, initial_capital=100000)
    results = engine.run(strategy)
    
    # Print results
    print(f"\n📊 Backtest Results:")
    print(f"   Total trades: {results['total_trades']}")
    print(f"   Winning trades: {results['winning_trades']}")
    print(f"   Losing trades: {results['losing_trades']}")
    print(f"   Win rate: {results['win_rate']:.2%}")
    print(f"   Gross profit: ${results['gross_profit']:.2f}")
    print(f"   Gross loss: ${results['gross_loss']:.2f}")
    print(f"   Net PnL: ${results['net_pnl']:.2f}")
    print(f"   Final capital: ${results['final_value']:.2f}")
    print(f"   Total return: {results['total_return']:.2%}")
    
    # Check for trades with detailed PnL info
    if results['total_trades'] > 0:
        print(f"\n📋 Trade Details:")
        for i, trade in enumerate(results['trades'][:5]):  # Show first 5 trades
            entry_time = datetime.fromtimestamp(trade['entry_time'])
            exit_time = datetime.fromtimestamp(trade['exit_time']) if trade.get('exit_time') else 'Open'
            trade_type = trade.get('type', trade.get('trade_type', 'UNKNOWN'))
            exit_price_str = f"{trade['exit_price']:.5f}" if trade.get('exit_price') else 'Open'
            print(f"   Trade {i+1}: {trade_type.upper()} @ {trade['entry_price']:.5f} -> {exit_price_str}")
            print(f"            Entry: {entry_time}, Exit: {exit_time}")
            if trade.get('gross_pnl') is not None:
                print(f"            Gross PnL: ${trade['gross_pnl']:.2f}, Costs: ${trade.get('costs', 0):.2f}, Net PnL: ${trade.get('net_pnl', 0):.2f}")
    
    return results

def main():
    """Main test function."""
    print("🧪 Testing Bollinger Bands and PnL calculation fixes...")
    print("=" * 60)
    
    # Test 1: Bollinger Bands calculation
    bb_test_passed = test_bollinger_bands_calculation()
    
    # Test 2: Full backtest
    if bb_test_passed:
        results = run_backtest_test()
        
        # Verify that we have some trades and they make sense
        if results['total_trades'] > 0:
            print(f"\n✅ Backtest completed successfully with {results['total_trades']} trades!")
            
            # Check for any obvious issues
            if results['total_trades'] > 0 and results['winning_trades'] == 0 and results['losing_trades'] == 0:
                print("⚠️  Warning: No winning or losing trades classified - check PnL calculation")
            else:
                print("✅ PnL calculation appears to be working correctly!")
        else:
            print("⚠️  No trades generated - this might be normal depending on the data and strategy")
    else:
        print("❌ Bollinger Bands test failed - skipping backtest")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
