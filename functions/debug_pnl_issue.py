#!/usr/bin/env python3
"""
Debug script to identify the exact PnL calculation issue.
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pnl_calculation():
    """Test PnL calculation with simple numbers."""
    print("🔍 Testing PnL Calculation Logic")
    print("=" * 50)
    
    # Test case: LONG trade that should be profitable
    entry_price = 1.10000
    exit_price = 1.10050  # 5 pips profit
    size = 10000  # 10k units
    
    # Calculate gross PnL for LONG trade
    gross_pnl = (exit_price - entry_price) * size
    print(f"LONG Trade Test:")
    print(f"  Entry Price: {entry_price}")
    print(f"  Exit Price: {exit_price}")
    print(f"  Size: {size}")
    print(f"  Price Difference: {exit_price - entry_price} ({(exit_price - entry_price) * 10000} pips)")
    print(f"  Gross PnL: {gross_pnl}")
    
    # Calculate trading costs
    spread_pips = 1.0
    commission_percentage = 0.0
    commission_fixed = 0.0
    
    # Spread cost
    spread_cost = spread_pips * 0.0001 * size
    
    # Commission (on position value, not PnL)
    position_value = (entry_price + exit_price) * size / 2
    commission = (commission_percentage / 100) * position_value
    
    total_costs = spread_cost + commission + commission_fixed
    
    print(f"  Trading Costs:")
    print(f"    Spread Cost: {spread_cost} (1 pip * {size} units)")
    print(f"    Position Value: {position_value}")
    print(f"    Commission: {commission} ({commission_percentage}%)")
    print(f"    Total Costs: {total_costs}")
    
    # Net PnL
    net_pnl = gross_pnl - total_costs
    print(f"  Net PnL: {net_pnl}")
    print(f"  Should be: {'PROFIT' if net_pnl > 0 else 'LOSS'}")
    
    print("\n" + "=" * 50)
    
    # Test the issue: What if the size is very large?
    large_size = 50000  # 50k units
    gross_pnl_large = (exit_price - entry_price) * large_size
    spread_cost_large = spread_pips * 0.0001 * large_size
    position_value_large = (entry_price + exit_price) * large_size / 2
    commission_large = (commission_percentage / 100) * position_value_large
    total_costs_large = spread_cost_large + commission_large + commission_fixed
    net_pnl_large = gross_pnl_large - total_costs_large
    
    print(f"Large Size Test (50k units):")
    print(f"  Gross PnL: {gross_pnl_large}")
    print(f"  Spread Cost: {spread_cost_large}")
    print(f"  Total Costs: {total_costs_large}")
    print(f"  Net PnL: {net_pnl_large}")
    print(f"  Should be: {'PROFIT' if net_pnl_large > 0 else 'LOSS'}")
    
    return net_pnl > 0 and net_pnl_large > 0

def test_trade_type_conversion():
    """Test trade type conversion issues."""
    print("\n🔍 Testing Trade Type Conversion")
    print("=" * 50)
    
    from oryn_backtest_engine.models.strategy import TradeType
    
    # Test TradeType enum values
    print(f"TradeType.LONG: {TradeType.LONG}")
    print(f"TradeType.LONG value: {TradeType.LONG.value}")
    print(f"TradeType.SHORT: {TradeType.SHORT}")
    print(f"TradeType.SHORT value: {TradeType.SHORT.value}")
    
    # Test string comparisons
    test_type = TradeType.LONG
    print(f"\nTesting comparisons:")
    print(f"test_type == TradeType.LONG: {test_type == TradeType.LONG}")
    print(f"test_type == 'long': {test_type == 'long'}")
    print(f"test_type.value == 'long': {test_type.value == 'long'}")
    print(f"str(test_type): {str(test_type)}")
    
    # Test what happens in frontend
    trade_data = {'trade_type': TradeType.LONG}
    frontend_type = trade_data['trade_type']
    print(f"\nFrontend receives: {frontend_type}")
    print(f"Frontend type check (== 'long'): {frontend_type == 'long'}")
    print(f"Frontend type check (.value == 'long'): {frontend_type.value == 'long'}")

def main():
    """Main debug function."""
    print("🐛 Debugging PnL and Entry Rule Issues")
    print("=" * 60)
    
    # Test 1: PnL Calculation
    pnl_correct = test_pnl_calculation()
    
    # Test 2: Trade Type Conversion
    test_trade_type_conversion()
    
    print("\n" + "=" * 60)
    print("🏁 Debug Summary:")
    print(f"  PnL Calculation: {'✅ CORRECT' if pnl_correct else '❌ ISSUE FOUND'}")
    
    if not pnl_correct:
        print("  ⚠️  PnL calculation shows unexpected results")
    
    print("\n💡 Recommendations:")
    print("  1. Check if trade type conversion is causing frontend display issues")
    print("  2. Verify that position sizing limits aren't affecting PnL calculations")
    print("  3. Ensure spread costs are reasonable for the position sizes")

if __name__ == "__main__":
    main()
