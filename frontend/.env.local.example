# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51RfCEZQsbQLpVn2DqZqljzKWv5gk5AGKm7rIFCipCP0m24nibtmyXkZYPK66C2UYpicMrPSPxzNpScgfky7s9qCJ00V6me2ogl
STRIPE_SECRET_KEY=sk_test_51RfCEZQsbQLpVn2DAm9qLEib1N7Z4swryhXEaTfJnlqGUudy4WxdickRE91sdpFsrPYdyFCY9HxS7sB12IC1kFBo00fbRvCO6T
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Firebase Configuration (if needed)
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id

# WebSocket Data Service Configuration
# Set to true to use production WebSocket service, false for local
NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET=false
NEXT_PUBLIC_WEBSOCKET_DATA_SERVICE_URL_LOCAL=ws://localhost:8765
NEXT_PUBLIC_WEBSOCKET_DATA_SERVICE_URL_PRODUCTION=wss://websocket-data-distribution-service-ihjc6tjxia-uc.a.run.app

# Other environment variables
NODE_ENV=development
