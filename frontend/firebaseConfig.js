// firebase/firebaseConfig.js
import { initializeApp, getApps } from "firebase/app";
import { getAuth, connectAuthEmulator, setPersistence, browserLocalPersistence } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";
import { USE_FIREBASE_EMULATOR } from "./src/config";

const firebaseConfig = {
  apiKey: "AIzaSyCDi1iqSKY0BXcIefwEIceBdiUbbKd-wFg",
  authDomain: "oryntrade.firebaseapp.com",
  projectId: "oryntrade",
  storageBucket: "oryntrade.firebasestorage.app",
  messagingSenderId: "87400455587",
  appId: "1:87400455587:web:f0f9530d3a93a0ca52ce16",
  measurementId: "G-12NLTNJ3GY",
};

const app = !getApps().length ? initializeApp(firebaseConfig) : getApps()[0];
const auth = getAuth(app);
const db = getFirestore(app);
const functions = getFunctions(app);

// Set authentication persistence to LOCAL to prevent random logouts
if (typeof window !== 'undefined') {
  setPersistence(auth, browserLocalPersistence).catch((error) => {
    console.error("Error setting auth persistence:", error);
  });
}

if (USE_FIREBASE_EMULATOR) {
  console.log("Connecting to Firebase Emulators...");
  connectAuthEmulator(auth, "http://127.0.0.1:9099", { disableWarnings: true });
  connectFirestoreEmulator(db, "127.0.0.1", 8082);
  connectFunctionsEmulator(functions, "127.0.0.1", 5001);
}

export { app, auth, db, functions };
