import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Image from 'next/image';
import {
  CheckIcon,
  ArrowLeftIcon,
  ShieldCheckIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import { createUserWithEmailAndPassword, signInWithPopup, GoogleAuthProvider } from 'firebase/auth';
import { auth } from '../../firebaseConfig';
import LoadingSpinner from '../components/LoadingSpinner';
import ComprehensiveTermsModal from '../components/ComprehensiveTermsModal';
import { Elements } from '@stripe/react-stripe-js';
import getStripe from '../lib/stripe';
import StripePaymentForm from '../components/StripePaymentForm';

export default function PaymentPage() {
  const router = useRouter();
  const { plan, period } = router.query;
  
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [paymentIntentId, setPaymentIntentId] = useState('');
  const [showTermsModal, setShowTermsModal] = useState(false);
  
  // User signup state
  const [signupData, setSignupData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: ''
  });

  // Plan details
  const planDetails = {
    trial: {
      name: 'Trial',
      price: 29,
      period: 'week',
      description: '1-week trial to experience the platform',
      features: [
        'All Technical Indicators',
        'No-Code Strategy Generation',
        'Up to 1 Custom Strategy',
        '1 Automated Trading Agent',
        'Professional Backtesting',
        'Real-time Market Data',
        'Email Support'
      ]
    },
    starter: {
      name: 'Starter',
      price: 79,
      period: 'month',
      description: 'Perfect for individual traders getting started',
      features: [
        'All Technical Indicators',
        'No-Code Strategy Generation',
        'Up to 2 Custom Strategies',
        '1 Automated Trading Agent',
        'Professional Backtesting',
        'Real-time Market Data',
        'Email Support',
        'Risk Management Tools'
      ]
    },
    professional: {
      name: 'Professional',
      price: 199,
      period: 'month',
      description: 'For serious traders scaling their operations',
      features: [
        'Everything in Starter',
        'Unlimited Custom Strategies',
        'Up to 5 Trading Agents',
        'Advanced Portfolio Analytics',
        'Priority Support',
        'Custom Indicator Builder',
        'Multi-timeframe Analysis',
        'Advanced Risk Controls'
      ]
    },
    'pro+': {
      name: 'Pro+',
      price: 499,
      period: 'month',
      description: 'For advanced traders with multiple strategies',
      features: [
        'Everything in Professional',
        'Up to 12 Trading Agents',
        'Advanced Market Scanner',
        'Portfolio Optimization Tools',
        'Advanced Backtesting Suite',
        'Priority Phone Support',
        'Custom Alerts & Notifications',
        'Enhanced Risk Analytics',
        'Multi-Account Management'
      ]
    },
    enterprise: {
      name: 'Enterprise',
      price: 999,
      period: 'month',
      description: 'For professional traders and institutions',
      features: [
        'Everything in Pro+',
        'Up to 25 Trading Agents',
        'Advanced Reporting & Analytics',
        '24/7 Priority Phone Support',
        'Enhanced Security Features',
        'Advanced Portfolio Management',
        'Premium Market Data Access',
        'Extended Trading Hours Support',
        'Dedicated Account Manager',
        'Custom Integrations & API Access',
        'Priority Feature Requests',
        'SLA Guarantee (99.9% uptime)'
      ]
    }
  };

  const selectedPlan = planDetails[plan] || planDetails.trial;
  
  // Calculate price based on period with launch pricing (50% off, except Starter)
  const calculatePrice = () => {
    if (plan === 'trial') {
      // Launch pricing: 50% off trial
      return Math.round(29 * 0.5);
    }

    const basePrice = selectedPlan.price;
    const isStarterPlan = plan === 'starter';
    const launchPrice = isStarterPlan ? basePrice : Math.round(basePrice * 0.5); // No launch discount for Starter

    switch (period) {
      case 'yearly':
        if (isStarterPlan) {
          // Starter: Only yearly discount, no launch discount
          return Math.round(basePrice * 12 * 0.7);
        } else {
          // Other plans: Launch + yearly discount
          return Math.round(launchPrice * 12 * 0.7);
        }
      case 'sixmonth':
        if (isStarterPlan) {
          // Starter: Only 6-month discount, no launch discount
          return Math.round(basePrice * 6 * 0.85);
        } else {
          // Other plans: Launch + 6-month discount
          return Math.round(launchPrice * 6 * 0.85);
        }
      default:
        // Monthly: Launch discount for non-Starter plans
        return launchPrice;
    }
  };

  // Calculate original price (without launch discount) for display
  const calculateOriginalPrice = () => {
    if (plan === 'trial') return 29;

    const basePrice = selectedPlan.price;
    switch (period) {
      case 'yearly':
        return Math.round(basePrice * 12 * 0.7);
      case 'sixmonth':
        return Math.round(basePrice * 6 * 0.85);
      default:
        return basePrice;
    }
  };

  const finalPrice = calculatePrice();
  const originalPrice = calculateOriginalPrice();
  const periodText = plan === 'trial' ? 'week' : (period === 'yearly' ? 'year' : period === 'sixmonth' ? '6 months' : 'month');
  const isLaunchPricing = plan !== 'starter'; // Enable launch pricing for all users except Starter

  const createPaymentIntent = async (userEmail, firebaseUid = null) => {
    try {
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: finalPrice,
          plan: plan,
          period: period,
          customerEmail: userEmail,
          firebaseUid: firebaseUid,
          userData: {
            firstName: signupData.firstName,
            lastName: signupData.lastName,
            phone: signupData.phone,
            email: userEmail,
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      const data = await response.json();
      setClientSecret(data.clientSecret);
      setPaymentIntentId(data.paymentIntentId);
      return data;
    } catch (error) {
      setError('Failed to initialize payment. Please try again.');
      throw error;
    }
  };

  const handleEmailSignup = async (e) => {
    e.preventDefault();
    setError('');

    if (signupData.password !== signupData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (!signupData.email || !signupData.password || !signupData.firstName || !signupData.lastName) {
      setError('Please fill in all required fields');
      return;
    }

    // Show terms modal before proceeding
    setShowTermsModal(true);
  };

  const handleGoogleSignup = async () => {
    setError('');
    setIsLoading(true);

    try {
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);

      // Extract name from Google profile
      const displayName = result.user.displayName || '';
      const [firstName, ...lastNameParts] = displayName.split(' ');
      const lastName = lastNameParts.join(' ');

      // Update signup data with Google info
      setSignupData({
        ...signupData,
        email: result.user.email,
        firstName: firstName || '',
        lastName: lastName || '',
        googleUser: result.user, // Store Google user for later use
      });

      setIsLoading(false);
      // Show terms modal before proceeding
      setShowTermsModal(true);
    } catch (error) {
      setError(error.message);
      setIsLoading(false);
    }
  };

  const handlePaymentSuccess = async (paymentIntent) => {
    try {
      setIsLoading(true);

      // Firebase user is already created in the signup step
      // Account creation will be handled by the webhook
      // Just redirect to dashboard
      router.push('/dashboard?payment_success=true');
    } catch (error) {
      console.error('Payment success handling error:', error);
      setError('Payment completed but there was an issue. Please contact support.');
      setIsLoading(false);
    }
  };

  const handlePaymentError = (error) => {
    setError(`Payment failed: ${error.message}`);
    setIsLoading(false);
  };

  const handleTermsAccept = async () => {
    setShowTermsModal(false);
    setIsLoading(true);

    try {
      let firebaseUser = null;

      // Create Firebase user based on signup method
      if (signupData.googleUser) {
        // Google user is already created
        firebaseUser = signupData.googleUser;
      } else {
        // Create email/password user
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          signupData.email,
          signupData.password
        );
        firebaseUser = userCredential.user;
      }

      // Create payment intent with Firebase UID
      await createPaymentIntent(signupData.email, firebaseUser.uid);
      setCurrentStep(3); // Move to payment step
      setIsLoading(false);
    } catch (error) {
      console.error('Terms acceptance error:', error);
      setError(error.message || 'Failed to proceed. Please try again.');
      setIsLoading(false);
    }
  };

  const handleTermsDecline = () => {
    setShowTermsModal(false);
    setError('You must accept the Terms of Service to continue with your purchase.');

    // If Google user was created, we should sign them out
    if (signupData.googleUser) {
      auth.signOut();
      setSignupData({
        ...signupData,
        googleUser: null,
        email: '',
        firstName: '',
        lastName: '',
      });
    }
  };

  return (
    <div className="min-h-screen bg-[#0A0B0B] text-[#FEFEFF]">
      {/* Header */}
      <header className="border-b border-[#1a1a1a] px-4 py-4">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <Image
              src="/logo.png"
              alt="Oryn Logo"
              width={32}
              height={32}
              quality={100}
            />
            <span className="text-lg font-bold">OrynTrade</span>
          </Link>
          
          <Link
            href="/"
            className="flex items-center space-x-2 text-[#FEFEFF]/70 hover:text-[#EFBD3A] transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left Column - Plan Summary & Trust Signals */}
          <div className="space-y-6">
            {/* Plan Summary */}
            <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#EFBD3A]/20">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-[#EFBD3A]">
                  {selectedPlan.name} Plan
                </h2>
                {selectedPlan.highlighted && (
                  <span className="bg-[#EFBD3A] text-black px-3 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                )}
              </div>
              <p className="text-[#FEFEFF]/70 mb-6">{selectedPlan.description}</p>

              {/* Launch Pricing Display */}
              {isLaunchPricing && plan !== 'trial' ? (
                <div className="mb-6">
                  {/* Launch Special Badge */}
                  <div className="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-lg text-center mb-4">
                    <span className="font-bold">🚀 LAUNCH SPECIAL - 50% OFF!</span>
                  </div>

                  {/* Price Display */}
                  <div className="flex items-center justify-center gap-4 mb-2">
                    <span className="text-2xl text-[#FEFEFF]/40 line-through">${originalPrice}</span>
                    <span className="text-4xl font-bold text-[#EFBD3A]">${finalPrice}</span>
                    <span className="text-[#FEFEFF]/60">/{periodText}</span>
                  </div>

                  {/* Savings Display */}
                  <div className="flex justify-center gap-2">
                    <span className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                      50% OFF LAUNCH
                    </span>
                    {period === 'yearly' && (
                      <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                        + 30% YEARLY
                      </span>
                    )}
                    {period === 'sixmonth' && (
                      <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-bold">
                        + 15% BIANNUAL
                      </span>
                    )}
                  </div>

                  {/* Billing Note */}
                  <p className="text-center text-[#FEFEFF]/60 text-sm mt-3">
                    {period === 'yearly' && `First year: $${finalPrice}, then $${originalPrice}/year`}
                    {period === 'sixmonth' && `First 6 months: $${finalPrice}, then $${originalPrice}/6 months`}
                    {period === 'monthly' && `First month: $${finalPrice}, then $${originalPrice}/month`}
                  </p>
                </div>
              ) : (
                <div className="flex items-baseline mb-6">
                  <span className="text-4xl font-bold text-[#EFBD3A]">${finalPrice}</span>
                  <span className="text-[#FEFEFF]/60 ml-2">/{periodText}</span>
                  {period === 'yearly' && (
                    <span className="ml-3 bg-green-500 text-white px-2 py-1 rounded text-xs">
                      Save 30%
                    </span>
                  )}
                  {period === 'sixmonth' && (
                    <span className="ml-3 bg-green-500 text-white px-2 py-1 rounded text-xs">
                      Save 15%
                    </span>
                  )}
                </div>
              )}

              <div className="space-y-3">
                <h3 className="font-semibold text-[#FEFEFF]">What's Included:</h3>
                {selectedPlan.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckIcon className="w-5 h-5 text-[#EFBD3A] flex-shrink-0" />
                    <span className="text-[#FEFEFF]/80">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Value Proposition */}
            <div className="bg-gradient-to-r from-[#EFBD3A]/10 to-[#EFBD3A]/5 rounded-xl p-6 border border-[#EFBD3A]/20">
              <h3 className="text-lg font-bold text-[#EFBD3A] mb-4">🚀 Why Choose OrynTrade?</h3>
              <div className="space-y-3 text-[#FEFEFF]/80">
                <div className="flex items-start space-x-3">
                  <span className="text-[#EFBD3A] mt-1">✓</span>
                  <span>No TradingView or MT5 required - everything built-in</span>
                </div>
                <div className="flex items-start space-x-3">
                  <span className="text-[#EFBD3A] mt-1">✓</span>
                  <span>No coding knowledge needed - visual strategy builder</span>
                </div>
                <div className="flex items-start space-x-3">
                  <span className="text-[#EFBD3A] mt-1">✓</span>
                  <span>24/7 automated trading with advanced risk management</span>
                </div>
                <div className="flex items-start space-x-3">
                  <span className="text-[#EFBD3A] mt-1">✓</span>
                  <span>Professional backtesting with years of historical data</span>
                </div>
              </div>
            </div>

            {/* Customer Testimonials */}
            <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#EFBD3A]/20">
              <h3 className="text-lg font-bold text-[#FEFEFF] mb-4">💬 What Our Users Say</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-[#EFBD3A] pl-4">
                  <p className="text-[#FEFEFF]/80 italic mb-2">
                    "Finally, a platform that doesn't require me to learn coding or pay for multiple subscriptions. OrynTrade has everything I need in one place."
                  </p>
                  <p className="text-[#EFBD3A] text-sm font-semibold">- Sarah M., Professional Trader</p>
                </div>
                <div className="border-l-4 border-[#EFBD3A] pl-4">
                  <p className="text-[#FEFEFF]/80 italic mb-2">
                    "The automated trading agents have been running my strategies 24/7. I've saved hundreds on TradingView subscriptions alone."
                  </p>
                  <p className="text-[#EFBD3A] text-sm font-semibold">- Michael R., Forex Trader</p>
                </div>
              </div>
            </div>

            {/* Money Back Guarantee */}
            <div className="bg-green-900/20 border border-green-500/30 rounded-xl p-6">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckIcon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-lg font-bold text-green-400">30-Day Money-Back Guarantee</h3>
              </div>
              <p className="text-green-300">
                Not satisfied? Get a full refund within 30 days, no questions asked.
                We're confident you'll love the platform.
              </p>
            </div>

            {/* Security & Trust Badges */}
            <div className="space-y-4">
              <div className="flex items-center justify-center space-x-6 text-[#FEFEFF]/60">
                <div className="flex items-center space-x-2">
                  <ShieldCheckIcon className="w-5 h-5" />
                  <span className="text-sm">SSL Secured</span>
                </div>
                <div className="flex items-center space-x-2">
                  <LockClosedIcon className="w-5 h-5" />
                  <span className="text-sm">256-bit Encryption</span>
                </div>
              </div>

              <div className="bg-[#1a1a1a] rounded-lg p-4 border border-[#EFBD3A]/20">
                <div className="flex items-center justify-center space-x-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#EFBD3A]">99.9%</div>
                    <div className="text-xs text-[#FEFEFF]/60">Uptime</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#EFBD3A]">10k+</div>
                    <div className="text-xs text-[#FEFEFF]/60">Active Users</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[#EFBD3A]">24/7</div>
                    <div className="text-xs text-[#FEFEFF]/60">Support</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Payment & Signup Forms */}
          <div className="space-y-6">
            {/* Header */}
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-[#FEFEFF] mb-2">Complete Your Purchase</h1>
              <p className="text-[#FEFEFF]/70">Join thousands of traders already using OrynTrade</p>
            </div>

            {/* Progress Steps */}
            <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#EFBD3A]/20 mb-6">
              <div className="flex items-center space-x-4 mb-4">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                  currentStep >= 1 ? 'bg-[#EFBD3A] text-black' : 'bg-[#333] text-[#FEFEFF]/60'
                }`}>
                  {currentStep > 1 ? <CheckIcon className="w-6 h-6" /> : '1'}
                </div>
                <div className={`h-1 flex-1 ${
                  currentStep >= 2 ? 'bg-[#EFBD3A]' : 'bg-[#333]'
                }`}></div>
                <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                  currentStep >= 2 ? 'bg-[#EFBD3A] text-black' : 'bg-[#333] text-[#FEFEFF]/60'
                }`}>
                  {currentStep > 2 ? <CheckIcon className="w-6 h-6" /> : '2'}
                </div>
                <div className={`h-1 flex-1 ${
                  currentStep >= 3 ? 'bg-[#EFBD3A]' : 'bg-[#333]'
                }`}></div>
                <div className={`flex items-center justify-center w-10 h-10 rounded-full ${
                  currentStep >= 3 ? 'bg-[#EFBD3A] text-black' : 'bg-[#333] text-[#FEFEFF]/60'
                }`}>
                  {currentStep > 3 ? <CheckIcon className="w-6 h-6" /> : '3'}
                </div>
              </div>

              <div className="text-center">
                <h3 className="text-lg font-semibold text-[#FEFEFF] mb-1">
                  {currentStep === 1 ? 'Account Information' : currentStep === 2 ? 'Create Your Account' : 'Secure Payment'}
                </h3>
                <p className="text-[#FEFEFF]/60 text-sm">
                  {currentStep === 1 ? 'Tell us about yourself' : currentStep === 2 ? 'Choose your signup method' : 'Complete your purchase securely'}
                </p>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="bg-[#1a1a1a] rounded-xl p-4 border border-[#EFBD3A]/20 mb-6">
              <div className="flex items-center justify-center space-x-6 text-sm">
                <div className="flex items-center space-x-2 text-green-400">
                  <ShieldCheckIcon className="w-4 h-4" />
                  <span>Secure Checkout</span>
                </div>
                <div className="flex items-center space-x-2 text-blue-400">
                  <LockClosedIcon className="w-4 h-4" />
                  <span>Encrypted</span>
                </div>
                <div className="flex items-center space-x-2 text-[#EFBD3A]">
                  <CheckIcon className="w-4 h-4" />
                  <span>Instant Access</span>
                </div>
              </div>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 mb-4">
                <p className="text-red-400 text-sm text-center">{error}</p>
              </div>
            )}

            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div
                  key="account-info"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-[#1a1a1a] rounded-xl p-6 border border-[#EFBD3A]/20"
                >
                  <form onSubmit={(e) => { e.preventDefault(); setCurrentStep(2); }} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">First Name</label>
                        <input
                          type="text"
                          value={signupData.firstName}
                          onChange={(e) => setSignupData({...signupData, firstName: e.target.value})}
                          className="w-full px-4 py-3 bg-[#0A0B0B] border border-[#333] rounded-lg text-[#FEFEFF] focus:border-[#EFBD3A] focus:outline-none"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Last Name</label>
                        <input
                          type="text"
                          value={signupData.lastName}
                          onChange={(e) => setSignupData({...signupData, lastName: e.target.value})}
                          className="w-full px-4 py-3 bg-[#0A0B0B] border border-[#333] rounded-lg text-[#FEFEFF] focus:border-[#EFBD3A] focus:outline-none"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Email</label>
                      <input
                        type="email"
                        value={signupData.email}
                        onChange={(e) => setSignupData({...signupData, email: e.target.value})}
                        className="w-full px-4 py-3 bg-[#0A0B0B] border border-[#333] rounded-lg text-[#FEFEFF] focus:border-[#EFBD3A] focus:outline-none"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Phone (Optional)</label>
                      <input
                        type="tel"
                        value={signupData.phone}
                        onChange={(e) => setSignupData({...signupData, phone: e.target.value})}
                        className="w-full px-4 py-3 bg-[#0A0B0B] border border-[#333] rounded-lg text-[#FEFEFF] focus:border-[#EFBD3A] focus:outline-none"
                      />
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-black py-3 rounded-lg font-semibold transition-colors"
                    >
                      Continue
                    </button>
                  </form>
                </motion.div>
              )}

              {currentStep === 2 && (
                <motion.div
                  key="signup"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-[#1a1a1a] rounded-xl p-6 border border-[#EFBD3A]/20"
                >
                  <div className="space-y-4">
                    {/* Google Signup */}
                    <button
                      onClick={handleGoogleSignup}
                      disabled={isLoading}
                      className="w-full flex items-center justify-center space-x-3 bg-white hover:bg-gray-100 text-black py-3 rounded-lg font-semibold transition-colors disabled:opacity-50"
                    >
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      <span>Continue with Google</span>
                    </button>

                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-[#333]"></div>
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="px-2 bg-[#1a1a1a] text-[#FEFEFF]/60">or</span>
                      </div>
                    </div>

                    {/* Email Signup Form */}
                    <form onSubmit={handleEmailSignup} className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">Password</label>
                        <input
                          type="password"
                          value={signupData.password}
                          onChange={(e) => setSignupData({...signupData, password: e.target.value})}
                          className="w-full px-4 py-3 bg-[#0A0B0B] border border-[#333] rounded-lg text-[#FEFEFF] focus:border-[#EFBD3A] focus:outline-none"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium mb-2">Confirm Password</label>
                        <input
                          type="password"
                          value={signupData.confirmPassword}
                          onChange={(e) => setSignupData({...signupData, confirmPassword: e.target.value})}
                          className="w-full px-4 py-3 bg-[#0A0B0B] border border-[#333] rounded-lg text-[#FEFEFF] focus:border-[#EFBD3A] focus:outline-none"
                          required
                        />
                      </div>

                      <button
                        type="submit"
                        disabled={isLoading}
                        className="w-full bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-black py-3 rounded-lg font-semibold transition-colors disabled:opacity-50"
                      >
                        {isLoading ? <LoadingSpinner size="small" /> : 'Continue to Payment'}
                      </button>
                    </form>
                  </div>
                </motion.div>
              )}

              {currentStep === 3 && clientSecret && (
                <motion.div
                  key="payment"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-[#1a1a1a] rounded-xl p-6 border border-[#EFBD3A]/20"
                >
                  <Elements stripe={getStripe()} options={{ clientSecret }}>
                    <StripePaymentForm
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                      isLoading={isLoading}
                      setIsLoading={setIsLoading}
                    />
                  </Elements>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Footer Section */}
        <div className="mt-16 border-t border-[#333] pt-8">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            {/* Support */}
            <div className="space-y-3">
              <div className="w-12 h-12 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                </svg>
              </div>
              <h4 className="font-semibold text-[#FEFEFF]">24/7 Support</h4>
              <p className="text-[#FEFEFF]/60 text-sm">
                Get help anytime via email at <br />
                <span className="text-[#EFBD3A]"><EMAIL></span>
              </p>
            </div>

            {/* Security */}
            <div className="space-y-3">
              <div className="w-12 h-12 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center mx-auto">
                <ShieldCheckIcon className="w-6 h-6 text-[#EFBD3A]" />
              </div>
              <h4 className="font-semibold text-[#FEFEFF]">Bank-Level Security</h4>
              <p className="text-[#FEFEFF]/60 text-sm">
                Your payment is protected by Stripe's <br />
                enterprise-grade security infrastructure
              </p>
            </div>

            {/* Guarantee */}
            <div className="space-y-3">
              <div className="w-12 h-12 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-6 h-6 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h4 className="font-semibold text-[#FEFEFF]">Instant Activation</h4>
              <p className="text-[#FEFEFF]/60 text-sm">
                Your account is activated immediately <br />
                after successful payment
              </p>
            </div>
          </div>

          {/* Payment Methods */}
          <div className="mt-8 pt-6 border-t border-[#333]">
            <p className="text-center text-[#FEFEFF]/60 text-sm mb-4">Accepted Payment Methods</p>
            <div className="flex items-center justify-center space-x-6">
              <div className="bg-white rounded px-3 py-2">
                <span className="text-blue-600 font-bold text-sm">VISA</span>
              </div>
              <div className="bg-white rounded px-3 py-2">
                <span className="text-orange-500 font-bold text-sm">Mastercard</span>
              </div>
              <div className="bg-white rounded px-3 py-2">
                <span className="text-blue-500 font-bold text-sm">American Express</span>
              </div>
              <div className="bg-white rounded px-3 py-2">
                <span className="text-purple-600 font-bold text-sm">Discover</span>
              </div>
            </div>
          </div>

          {/* Final Trust Message */}
          <div className="mt-8 text-center">
            <p className="text-[#FEFEFF]/60 text-sm">
              Join <span className="text-[#EFBD3A] font-semibold">10,000+</span> traders who trust OrynTrade with their automated trading
            </p>
          </div>
        </div>
      </div>

      {isLoading && <LoadingSpinner fullScreen text="Processing your payment..." />}

      <ComprehensiveTermsModal
        isOpen={showTermsModal}
        onAccept={handleTermsAccept}
        onDecline={handleTermsDecline}
      />
    </div>
  );
}
