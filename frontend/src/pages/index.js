import { useState, useEffect, useCallback } from 'react';
import { motion, useReducedMotion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { auth } from '../../firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';
import Head from 'next/head';
import {
  ChartBarIcon,
  CpuChipIcon,
  BeakerIcon,
  BoltIcon,
  ShieldCheckIcon,
  ClockIcon,
  GlobeAltIcon,
  CheckIcon,
  StarIcon
} from '@heroicons/react/24/outline';

export default function LandingPage() {
  const router = useRouter();
  const [activeFeature, setActiveFeature] = useState(0);
  const [billingPeriod, setBillingPeriod] = useState('sixmonth');
  const [user, setUser] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);

  // Performance: Respect user's motion preferences
  const prefersReducedMotion = useReducedMotion();

  // Performance: Optimize auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      setAuthLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Performance: Optimize scroll handlers
  const handleMyAccountClick = useCallback(() => {
    if (user) {
      router.push('/dashboard');
    } else {
      router.push('/login');
    }
  }, [user, router]);

  // Performance: Optimize feature rotation
  useEffect(() => {
    if (prefersReducedMotion) return;

    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [prefersReducedMotion]);

  const features = [
    {
      icon: ChartBarIcon,
      title: "Advanced Technical Analysis",
      description: "Professional indicators including RSI, MACD, Bollinger Bands, Support & Resistance levels, and custom indicators.",
      color: "#EFBD3A"
    },
    {
      icon: CpuChipIcon,
      title: "No-Code Strategy Builder",
      description: "Create sophisticated trading strategies without programming. Visual interface with complex logic support.",
      color: "#EFBD3A"
    },
    {
      icon: BoltIcon,
      title: "Automated Trading Agents",
      description: "Deploy AI-powered trading agents that execute your strategies 24/7 with real-time market data and risk management.",
      color: "#EFBD3A"
    },
    {
      icon: BeakerIcon,
      title: "Professional Backtesting",
      description: "Test strategies against years of historical data with detailed performance analytics and risk metrics.",
      color: "#EFBD3A"
    },
    {
      icon: ShieldCheckIcon,
      title: "Enterprise Risk Management",
      description: "Advanced position sizing, indicator-based dynamic stop loss for trading agents, and portfolio risk controls to protect your capital.",
      color: "#EFBD3A"
    },
    {
      icon: GlobeAltIcon,
      title: "Real-Time Market Data",
      description: "Live forex data from Polygon.io with millisecond precision and comprehensive market coverage.",
      color: "#EFBD3A"
    }
  ];

  const getPricingPlans = () => {
    const basePlans = [
      {
        name: "Trial",
        monthlyPrice: 29,
        description: "1-week trial to experience the platform",
        features: [
          "All Technical Indicators",
          "No-Code Strategy Generation",
          "Up to 1 Custom Strategy",
          "1 Automated Trading Agent",
          "Professional Backtesting",
          "Real-time Market Data",
          "Email Support",
          "Risk Management Tools"
        ],
        highlighted: false,
        buttonText: "Start 1-Week Trial",
        isTrial: true
      },
      {
        name: "Starter",
        monthlyPrice: 79,
        description: "Perfect for individual traders getting started",
        features: [
          "All Technical Indicators",
          "No-Code Strategy Generation",
          "Up to 2 Custom Strategies",
          "1 Automated Trading Agent",
          "Professional Backtesting",
          "Real-time Market Data",
          "Email Support",
          "Risk Management Tools"
        ],
        highlighted: false,
        buttonText: "Start Trading"
      },
      {
        name: "Professional",
        monthlyPrice: 199,
        description: "For serious traders scaling their operations",
        features: [
          "Everything in Starter",
          "Unlimited Custom Strategies",
          "Up to 5 Trading Agents",
          "Advanced Portfolio Analytics",
          "Priority Support",
          "Custom Indicator Builder",
          "Multi-timeframe Analysis",
          "Advanced Risk Controls"
        ],
        highlighted: true,
        buttonText: "Go Professional"
      },
      {
        name: "Pro+",
        monthlyPrice: 499,
        description: "For advanced traders with multiple strategies",
        features: [
          "Everything in Professional",
          "Up to 12 Trading Agents",
          "Advanced Market Scanner",
          "Portfolio Optimization Tools",
          "Advanced Backtesting Suite",
          "Priority Phone Support",
          "Custom Alerts & Notifications",
          "Enhanced Risk Analytics",
          "Multi-Account Management"
        ],
        highlighted: false,
        buttonText: "Upgrade to Pro+"
      },
      {
        name: "Enterprise",
        monthlyPrice: 999,
        description: "For professional traders and institutions",
        features: [
          "Everything in Pro+",
          "Up to 25 Trading Agents",
          "Advanced Reporting & Analytics",
          "24/7 Priority Phone Support",
          "Enhanced Security Features",
          "Advanced Portfolio Management",
          "Premium Market Data Access",
          "Extended Trading Hours Support",
          "Dedicated Account Manager",
          "Custom Integrations & API Access",
          "Priority Feature Requests",
          "SLA Guarantee (99.9% uptime)"
        ],
        highlighted: false,
        buttonText: "Contact Sales"
      }
    ];

    return basePlans.map(plan => {
      if (plan.isTrial) {
        return {
          ...plan,
          price: "$29",
          period: "/week",
          savings: null
        };
      }

      let price, period, savings, billingNote, totalPrice, originalPrice, launchDiscount;

      // Launch pricing: 50% off for first billing period (except Starter)
      const isStarterPlan = plan.name === "Starter";
      const launchPrice = isStarterPlan ? plan.monthlyPrice : Math.round(plan.monthlyPrice * 0.5);

      switch (billingPeriod) {
        case 'yearly':
          const yearlyMonthlyRate = Math.round(plan.monthlyPrice * 0.7);
          const yearlyTotal = Math.round(plan.monthlyPrice * 12 * 0.7);

          if (isStarterPlan) {
            // Starter: No launch discount, only yearly discount
            price = `$${yearlyMonthlyRate}`;
            originalPrice = null;
            period = "/month";
            savings = "Save 30%";
            billingNote = `Billed annually at $${yearlyTotal}`;
            totalPrice = yearlyTotal;
            launchDiscount = null;
          } else {
            // Other plans: Launch + yearly discount
            const launchYearlyRate = Math.round(launchPrice * 0.7);
            const launchYearlyTotal = Math.round(launchPrice * 12 * 0.7);

            price = `$${launchYearlyRate}`;
            originalPrice = `$${yearlyMonthlyRate}`;
            period = "/month";
            savings = "Save 30% + Launch 50%";
            billingNote = `First year: $${launchYearlyTotal}, then $${yearlyTotal}/year`;
            totalPrice = launchYearlyTotal;
            launchDiscount = "50% OFF FIRST YEAR";
          }
          break;
        case 'sixmonth':
          const sixMonthMonthlyRate = Math.round(plan.monthlyPrice * 0.85);
          const sixMonthTotal = Math.round(plan.monthlyPrice * 6 * 0.85);

          if (isStarterPlan) {
            // Starter: No launch discount, only 6-month discount
            price = `$${sixMonthMonthlyRate}`;
            originalPrice = null;
            period = "/month";
            savings = "Save 15%";
            billingNote = `Billed every 6 months at $${sixMonthTotal}`;
            totalPrice = sixMonthTotal;
            launchDiscount = null;
          } else {
            // Other plans: Launch + 6-month discount
            const launchSixMonthRate = Math.round(launchPrice * 0.85);
            const launchSixMonthTotal = Math.round(launchPrice * 6 * 0.85);

            price = `$${launchSixMonthRate}`;
            originalPrice = `$${sixMonthMonthlyRate}`;
            period = "/month";
            savings = "Save 15% + Launch 50%";
            billingNote = `First 6 months: $${launchSixMonthTotal}, then $${sixMonthTotal}/6 months`;
            totalPrice = launchSixMonthTotal;
            launchDiscount = "50% OFF FIRST 6 MONTHS";
          }
          break;
        default:
          if (isStarterPlan) {
            // Starter: No launch discount
            price = `$${plan.monthlyPrice}`;
            originalPrice = null;
            period = "/month";
            savings = null;
            billingNote = "Billed monthly";
            totalPrice = plan.monthlyPrice;
            launchDiscount = null;
          } else {
            // Other plans: Launch discount
            price = `$${launchPrice}`;
            originalPrice = `$${plan.monthlyPrice}`;
            period = "/month";
            savings = "Launch 50% OFF";
            billingNote = `First month: $${launchPrice}, then $${plan.monthlyPrice}/month`;
            totalPrice = launchPrice;
            launchDiscount = "50% OFF FIRST MONTH";
          }
      }

      return {
        ...plan,
        price,
        originalPrice,
        period,
        savings,
        billingNote,
        totalPrice,
        launchDiscount
      };
    });
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <Head>
        {/* Primary Meta Tags */}
        <title>OrynTrade - No-Code Automated Trading Platform | Professional Forex Trading</title>
        <meta name="title" content="OrynTrade - No-Code Automated Trading Platform | Professional Forex Trading" />
        <meta name="description" content="No-code automated trading platform built by Ex-Google Engineer & Day Trader. No TradingView or MT5 required. Early access available." />
        <meta name="keywords" content="automated trading, forex trading, no-code trading, trading agent, algorithmic trading, trading platform, backtesting, technical analysis" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="English" />
        <meta name="author" content="OrynTrade" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://oryntrade.com/" />
        <meta property="og:title" content="OrynTrade - No-Code Automated Trading Platform" />
        <meta property="og:description" content="No-code automated trading platform built by Ex-Google Engineer & Day Trader. Early access available." />
        <meta property="og:image" content="https://oryntrade.com/og-image.jpg" />
        <meta property="og:site_name" content="OrynTrade" />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="https://oryntrade.com/" />
        <meta property="twitter:title" content="OrynTrade - Built by Ex-Google Engineer & Day Trader" />
        <meta property="twitter:description" content="No-code automated trading platform built by Ex-Google Engineer & Day Trader. Early access available." />
        <meta property="twitter:image" content="https://oryntrade.com/twitter-image.jpg" />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />

        {/* Performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="//api.oryntrade.com" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "SoftwareApplication",
              "name": "OrynTrade",
              "description": "No-code automated trading platform for professional forex trading",
              "url": "https://oryntrade.com",
              "applicationCategory": "FinanceApplication",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "49",
                "priceCurrency": "USD",
                "priceValidUntil": "2024-12-31"
              }
            })
          }}
        />
      </Head>

      <div className="min-h-screen bg-[#0A0B0B] text-[#FEFEFF]">
        {/* Navigation */}
        <nav className="fixed top-0 w-full z-50 bg-[#0A0B0B]/95 backdrop-blur-sm border-b border-[#1a1a1a]">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Image
                src="/logo.png"
                alt="Oryn Logo"
                width={40}
                height={40}
                quality={100}
                priority
              />
              <span className="text-xl font-bold text-[#FEFEFF]">OrynTrade</span>
            </div>
            <div className="flex items-center space-x-6">
              <button
                onClick={() => document.getElementById('features').scrollIntoView({ behavior: 'smooth' })}
                className="text-[#FEFEFF] hover:text-[#EFBD3A] transition-colors font-medium"
              >
                Features
              </button>
              <button
                onClick={() => document.getElementById('pricing').scrollIntoView({ behavior: 'smooth' })}
                className="text-[#FEFEFF] hover:text-[#EFBD3A] transition-colors font-medium"
              >
                Pricing
              </button>

              {/* User Authentication Section */}
              {authLoading ? (
                <div className="bg-[#EFBD3A]/20 text-[#EFBD3A] px-6 py-2 rounded-lg font-semibold">
                  Loading...
                </div>
              ) : user ? (
                <div className="flex items-center space-x-3">
                  <div className="text-[#FEFEFF]/70 text-sm">
                    Welcome, {user.displayName || user.email?.split('@')[0] || 'User'}
                  </div>
                  <button
                    onClick={handleMyAccountClick}
                    className="bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-black px-6 py-2 rounded-lg font-semibold transition-all duration-200 hover:scale-105"
                  >
                    Dashboard
                  </button>
                </div>
              ) : (
                <button
                  onClick={handleMyAccountClick}
                  className="bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-black px-6 py-2 rounded-lg font-semibold transition-all duration-200 hover:scale-105"
                >
                  My Account
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="relative pt-24 pb-16 px-4 sm:px-6 lg:px-8 min-h-screen flex items-center overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#EFBD3A]/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#EFBD3A]/3 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-[#EFBD3A]/10 to-transparent rounded-full"></div>

          {/* Floating Elements */}
          <motion.div
            className="absolute top-20 right-20 w-4 h-4 bg-[#EFBD3A] rounded-full opacity-60"
            animate={{ y: [0, -20, 0], opacity: [0.6, 1, 0.6] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.div
            className="absolute bottom-32 left-16 w-6 h-6 bg-[#EFBD3A]/40 rounded-full"
            animate={{ y: [0, 15, 0], opacity: [0.4, 0.8, 0.4] }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
          />
        </div>

        <div className="w-full mx-auto relative z-10">
          <div className="text-center">
            {/* Founder Credibility Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-[#EFBD3A]/10 to-blue-500/10 border border-[#EFBD3A]/20 rounded-full px-6 py-3 mb-8 backdrop-blur-sm"
            >
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">G</span>
                </div>
                <span className="text-[#FEFEFF] font-semibold">Built by Ex-Google Engineer</span>
              </div>
              <div className="w-px h-6 bg-[#EFBD3A]/30"></div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl">📈</span>
                <span className="text-[#EFBD3A] font-semibold">Active Day Trader</span>
              </div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.3 : 0.8 }}
              className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight"
            >
              <motion.span
                className="block text-[#FEFEFF] mb-2"
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                Professional
              </motion.span>
              <motion.span
                className="block bg-gradient-to-r from-[#EFBD3A] via-[#FFD700] to-[#EFBD3A] bg-clip-text text-transparent mb-2"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5, duration: 0.6 }}
                style={{
                  backgroundSize: "200% 100%",
                  animation: "gradient-shift 3s ease-in-out infinite"
                }}
              >
                Automated Trading
              </motion.span>
              <motion.span
                className="block text-[#FEFEFF]"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7, duration: 0.6 }}
              >
                Without Code
              </motion.span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.3 : 0.8, delay: 0.9 }}
              className="text-xl md:text-2xl text-[#FEFEFF]/80 mb-10 max-w-4xl mx-auto leading-relaxed"
            >
              Build, backtest, and deploy sophisticated trading strategies with our no-code platform.{' '}
              <span className="text-[#EFBD3A] font-semibold">No TradingView subscriptions</span>,{' '}
              <span className="text-[#EFBD3A] font-semibold">no MT5 setup</span>,{' '}
              <span className="text-[#EFBD3A] font-semibold">no programming required</span>.
            </motion.p>

            {/* Key Benefits Pills */}
            <motion.div
              className="flex flex-wrap justify-center gap-3 mb-10 max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1, duration: 0.6 }}
            >
              {[
                { icon: "🚀", text: "Deploy in Minutes" },
                { icon: "📊", text: "Professional Analytics" },
                { icon: "🛡️", text: "Advanced Risk Management" },
                { icon: "⚡", text: "24/7 Automation" },
                { icon: "💰", text: "Save $100s/Month" },
                { icon: "🎯", text: "No Coding Required" }
              ].map((benefit, index) => (
                <motion.div
                  key={benefit.text}
                  className="bg-[#1a1a1a]/60 backdrop-blur-sm border border-[#EFBD3A]/20 rounded-full px-4 py-2 hover:bg-[#EFBD3A]/10 transition-all duration-300 cursor-default"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.2 + index * 0.1, duration: 0.4 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  <span className="text-[#FEFEFF]/90 text-sm font-medium">
                    {benefit.icon} {benefit.text}
                  </span>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: prefersReducedMotion ? 0.3 : 0.8, delay: 1.3 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12"
            >
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link
                  href="/payment?plan=trial"
                  className="group relative bg-gradient-to-r from-[#EFBD3A] to-[#FFD700] hover:from-[#FFD700] hover:to-[#EFBD3A] text-black px-10 py-5 rounded-xl font-bold text-lg transition-all duration-300 shadow-2xl hover:shadow-[#EFBD3A]/25 flex items-center space-x-3 overflow-hidden"
                >
                  <span className="relative z-10">🚀 Start 1-Week Trial - $29</span>
                  <motion.div
                    className="absolute inset-0 bg-white/20"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: "100%" }}
                    transition={{ duration: 0.6 }}
                  />
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <button className="group border-2 border-[#EFBD3A] text-[#EFBD3A] hover:bg-[#EFBD3A] hover:text-black px-10 py-5 rounded-xl font-semibold text-lg transition-all duration-300 flex items-center space-x-3 backdrop-blur-sm">
                  <span>📹 Watch Demo</span>
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                  >
                    →
                  </motion.div>
                </button>
              </motion.div>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 1.5 }}
              className="flex flex-wrap justify-center items-center gap-8 text-[#FEFEFF]/70 max-w-4xl mx-auto mb-16"
            >
              {[
                { icon: CheckIcon, text: "No Code Required", color: "text-green-400" },
                { icon: ClockIcon, text: "24/7 Automation", color: "text-blue-400" },
                { icon: ShieldCheckIcon, text: "Enterprise Security", color: "text-purple-400" },
                { icon: BoltIcon, text: "Instant Setup", color: "text-[#EFBD3A]" }
              ].map((item, index) => (
                <motion.div
                  key={item.text}
                  className="flex items-center space-x-2 group cursor-default"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.6 + index * 0.1, duration: 0.4 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <item.icon className={`w-5 h-5 ${item.color} group-hover:scale-110 transition-transform duration-200`} />
                  <span className="font-medium group-hover:text-[#FEFEFF] transition-colors duration-200">{item.text}</span>
                </motion.div>
              ))}
            </motion.div>

            {/* Startup Value Props */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.8, duration: 0.6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
            >
              {[
                { number: "Beta", label: "Early Access", icon: "🚀" },
                { number: "100%", label: "No-Code", icon: "⚡" },
                { number: "$29", label: "Trial Week", icon: "💰" },
                { number: "24/7", label: "Platform", icon: "🛟" }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  className="text-center group"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.9 + index * 0.1, duration: 0.4 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="text-3xl mb-2">{stat.icon}</div>
                  <div className="text-3xl md:text-4xl font-bold text-[#EFBD3A] mb-1 group-hover:scale-110 transition-transform duration-200">
                    {stat.number}
                  </div>
                  <div className="text-[#FEFEFF]/60 font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Independence Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-[#EFBD3A]/10 via-[#EFBD3A]/5 to-[#EFBD3A]/10">
        <div className="w-full mx-auto">
          <div className="text-center max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#FEFEFF]">
                <span className="text-[#EFBD3A]">Complete Independence</span> from External Platforms
              </h2>
              <p className="text-xl md:text-2xl text-[#FEFEFF]/90 mb-8 leading-relaxed">
                Unlike other platforms that require <span className="text-[#EFBD3A] font-semibold">TradingView subscriptions</span>,
                <span className="text-[#EFBD3A] font-semibold"> MT5 installations</span>, or
                <span className="text-[#EFBD3A] font-semibold"> coding knowledge</span> —
                OrynTrade provides everything you need in one unified platform.
              </p>

              <div className="grid md:grid-cols-3 gap-8 mt-12">
                <div className="bg-[#0A0B0B]/50 rounded-xl p-6 border border-[#EFBD3A]/20">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-red-500/20 rounded-full flex items-center justify-center">
                      <span className="text-2xl">❌</span>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF] mb-2">Other Platforms</h3>
                    <ul className="text-[#FEFEFF]/70 space-y-2 text-sm">
                      <li>• TradingView Pro ($15-60/mo)</li>
                      <li>• MT5 Setup & Learning</li>
                      <li>• Coding Required</li>
                      <li>• Multiple Subscriptions</li>
                      <li>• Complex Integrations</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-[#EFBD3A]/10 rounded-xl p-6 border border-[#EFBD3A] relative">
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#EFBD3A] text-black px-3 py-1 rounded-full text-sm font-semibold">
                      OrynTrade
                    </span>
                  </div>
                  <div className="text-center mt-2">
                    <div className="w-16 h-16 mx-auto mb-4 bg-[#EFBD3A] rounded-full flex items-center justify-center">
                      <span className="text-2xl">✅</span>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF] mb-2">All-in-One Solution</h3>
                    <ul className="text-[#FEFEFF]/90 space-y-2 text-sm">
                      <li>• Built-in Charts & Analysis</li>
                      <li>• No-Code Strategy Builder</li>
                      <li>• Automated Deployment</li>
                      <li>• Single Platform</li>
                      <li>• Everything Included</li>
                    </ul>
                  </div>
                </div>

                <div className="bg-[#0A0B0B]/50 rounded-xl p-6 border border-[#EFBD3A]/20">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-green-500/20 rounded-full flex items-center justify-center">
                      <span className="text-2xl">💰</span>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF] mb-2">Total Savings</h3>
                    <ul className="text-[#FEFEFF]/70 space-y-2 text-sm">
                      <li>• No TradingView fees</li>
                      <li>• No additional software</li>
                      <li>• No developer costs</li>
                      <li>• No learning curve</li>
                      <li className="text-[#EFBD3A] font-semibold">• Save $100s/month</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-6 bg-[#EFBD3A]/5 rounded-xl border border-[#EFBD3A]/30">
                <p className="text-lg text-[#FEFEFF]/90">
                  <span className="text-[#EFBD3A] font-semibold">Start trading in minutes</span> —
                  Create strategies, backtest, and deploy automated trading agents
                  all within our platform at one transparent price.
                </p>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Founder Story Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-[#0A0B0B] to-[#1a1a1a]/20">
        <div className="w-full mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-8">
              <span className="text-[#FEFEFF]">Built by a </span>
              <span className="text-[#EFBD3A]">Trader</span>
              <span className="text-[#FEFEFF]">, for </span>
              <span className="text-[#EFBD3A]">Traders</span>
            </h2>

            <div className="bg-[#1a1a1a]/60 backdrop-blur-sm rounded-2xl p-8 border border-[#EFBD3A]/20">
              <div className="flex flex-col md:flex-row items-center gap-8">
                {/* Founder Avatar/Logo */}
                <div className="flex-shrink-0">
                  <div className="w-24 h-24 bg-gradient-to-r from-blue-500 via-purple-500 to-[#EFBD3A] rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-2xl">
                    👨‍💻
                  </div>
                </div>

                {/* Founder Story */}
                <div className="flex-1 text-left">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-bold">G</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-[#FEFEFF]">Ex-Google Engineer</h3>
                          <p className="text-[#FEFEFF]/70 text-sm">Built scalable systems for millions of users</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-[#EFBD3A] rounded-full flex items-center justify-center">
                          <span className="text-black text-sm">📈</span>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-[#FEFEFF]">Active Day Trader</h3>
                          <p className="text-[#FEFEFF]/70 text-sm">Years of hands-on trading experience</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <blockquote className="text-[#FEFEFF]/90 italic border-l-4 border-[#EFBD3A] pl-4">
                        "I was frustrated paying for multiple platforms, learning complex tools, and writing code just to automate my strategies. So I built the platform I wished existed."
                      </blockquote>

                      <div className="flex flex-wrap gap-2">
                        {[
                          "Google-Scale Engineering",
                          "Real Trading Experience",
                          "Problem-First Approach",
                          "Trader's Perspective"
                        ].map((tag) => (
                          <span key={tag} className="bg-[#EFBD3A]/20 text-[#EFBD3A] px-3 py-1 rounded-full text-xs font-medium">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Why This Matters */}
              <div className="mt-8 pt-6 border-t border-[#EFBD3A]/20">
                <div className="grid md:grid-cols-3 gap-6 text-center">
                  <div>
                    <div className="text-2xl mb-2">🏗️</div>
                    <h4 className="font-semibold text-[#FEFEFF] mb-1">Engineering Excellence</h4>
                    <p className="text-[#FEFEFF]/70 text-sm">Google-level architecture and reliability</p>
                  </div>
                  <div>
                    <div className="text-2xl mb-2">📊</div>
                    <h4 className="font-semibold text-[#FEFEFF] mb-1">Real Trading Needs</h4>
                    <p className="text-[#FEFEFF]/70 text-sm">Built by someone who trades daily</p>
                  </div>
                  <div>
                    <div className="text-2xl mb-2">🎯</div>
                    <h4 className="font-semibold text-[#FEFEFF] mb-1">Problem-Focused</h4>
                    <p className="text-[#FEFEFF]/70 text-sm">Solves actual pain points, not imaginary ones</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Showcase */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-[#0A0B0B]/50">
        <div className="w-full mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-[#EFBD3A]">Powerful Features</span>
              <br />
              <span className="text-[#FEFEFF]">Built for Professionals</span>
            </h2>
            <p className="text-xl text-[#FEFEFF]/80 max-w-3xl mx-auto">
              Everything you need to build, test, and deploy sophisticated trading strategies
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className={`p-6 rounded-xl border transition-all duration-300 cursor-pointer ${
                    activeFeature === index
                      ? 'border-[#EFBD3A] bg-[#EFBD3A]/5'
                      : 'border-[#1a1a1a] hover:border-[#EFBD3A]/50'
                  }`}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg ${
                      activeFeature === index ? 'bg-[#EFBD3A]' : 'bg-[#1a1a1a]'
                    }`}>
                      {(() => {
                        const IconComponent = feature.icon;
                        return <IconComponent className={`w-6 h-6 ${
                          activeFeature === index ? 'text-black' : 'text-[#EFBD3A]'
                        }`} />;
                      })()}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2 text-[#FEFEFF]">
                        {feature.title}
                      </h3>
                      <p className="text-[#FEFEFF]/70">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="relative">
              <div className="bg-gradient-to-br from-[#EFBD3A]/20 to-[#EFBD3A]/5 rounded-2xl p-8 border border-[#EFBD3A]/20">
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-6 bg-[#EFBD3A] rounded-full flex items-center justify-center">
                    {features[activeFeature] && (() => {
                      const IconComponent = features[activeFeature].icon;
                      return <IconComponent className="w-10 h-10 text-black" />;
                    })()}
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-[#FEFEFF]">
                    {features[activeFeature]?.title}
                  </h3>
                  <p className="text-[#FEFEFF]/80 text-lg">
                    {features[activeFeature]?.description}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="w-full mx-auto">
          {/* Launch Special Banner */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-red-600 via-red-500 to-orange-500 rounded-2xl p-6 mb-12 text-center border-2 border-red-400 shadow-2xl"
          >
            <div className="flex items-center justify-center gap-3 mb-3">
              <span className="text-3xl animate-bounce">🚀</span>
              <h3 className="text-2xl md:text-3xl font-bold text-white">
                LAUNCH SPECIAL - 50% OFF!
              </h3>
              <span className="text-3xl animate-bounce">🔥</span>
            </div>
            <p className="text-white/90 text-lg mb-4">
              Get 50% off your first billing period as an early adopter!
            </p>
            <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 inline-block">
              <div className="text-white font-bold text-lg mb-1">
                ⏰ Limited Time Offer
              </div>
              <div className="text-yellow-200 font-semibold">
                Valid for the first 1,000 users only • Expires in 30 days
              </div>
            </div>
          </motion.div>

          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-[#EFBD3A]">Launch Pricing</span>
              <br />
              <span className="text-[#FEFEFF]">50% Off Everything</span>
            </h2>
            <p className="text-xl text-[#FEFEFF]/80 max-w-3xl mx-auto mb-8">
              Choose the perfect plan for your trading needs. All plans include our core features with no hidden fees.
            </p>

            {/* Billing Period Toggle */}
            <div className="flex justify-center mb-8">
              <div className="bg-[#1a1a1a] rounded-xl p-1.5 flex border border-[#EFBD3A]/20">
                <button
                  onClick={() => setBillingPeriod('monthly')}
                  className={`px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
                    billingPeriod === 'monthly'
                      ? 'bg-[#EFBD3A] text-black shadow-lg'
                      : 'text-[#FEFEFF]/70 hover:text-[#FEFEFF] hover:bg-[#EFBD3A]/10'
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingPeriod('sixmonth')}
                  className={`relative px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
                    billingPeriod === 'sixmonth'
                      ? 'bg-gradient-to-r from-[#EFBD3A] to-[#FFD700] text-black shadow-lg'
                      : 'text-[#FEFEFF] hover:bg-[#EFBD3A]/10 border border-green-500/30'
                  }`}
                >
                  6 Months
                  <span className={`ml-2 text-xs px-2 py-0.5 rounded-full font-bold ${
                    billingPeriod === 'sixmonth'
                      ? 'bg-green-600 text-white'
                      : 'bg-green-500 text-white animate-pulse'
                  }`}>
                    SAVE 15%
                  </span>
                  {billingPeriod !== 'sixmonth' && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
                  )}
                </button>
                <button
                  onClick={() => setBillingPeriod('yearly')}
                  className={`relative px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${
                    billingPeriod === 'yearly'
                      ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg'
                      : 'text-[#FEFEFF] hover:bg-[#EFBD3A]/10 border border-green-500/30'
                  }`}
                >
                  Yearly
                  <span className={`ml-2 text-xs px-2 py-0.5 rounded-full font-bold ${
                    billingPeriod === 'yearly'
                      ? 'bg-green-700 text-white'
                      : 'bg-green-500 text-white animate-pulse'
                  }`}>
                    SAVE 30%
                  </span>
                  {billingPeriod !== 'yearly' && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
                  )}
                </button>
              </div>
            </div>

            {/* Billing Explanation */}
            <div className="text-center mb-8">
              <p className="text-[#FEFEFF]/60 text-sm">
                {billingPeriod === 'monthly' && "💳 Billed monthly - Cancel anytime"}
                {billingPeriod === 'sixmonth' && "💰 Pay every 6 months - Save 15% vs monthly"}
                {billingPeriod === 'yearly' && "🎉 Pay annually - Best value with 30% savings"}
              </p>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-6 w-full mx-auto">
            {getPricingPlans().map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`relative rounded-2xl p-8 border transition-all duration-300 hover:scale-105 ${
                  plan.highlighted
                    ? 'border-[#EFBD3A] bg-gradient-to-br from-[#EFBD3A]/10 to-[#EFBD3A]/5'
                    : 'border-[#1a1a1a] hover:border-[#EFBD3A]/50'
                }`}
              >
                {plan.highlighted && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-[#EFBD3A] text-black px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </div>
                  </div>
                )}



                {plan.savings && (
                  <div className="absolute -top-2 -left-2">
                    <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                      {plan.savings}
                    </div>
                  </div>
                )}

                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-[#FEFEFF] mb-2">{plan.name}</h3>
                  <p className="text-[#FEFEFF]/60 mb-4">{plan.description}</p>

                  {/* Launch Pricing Display */}
                  {plan.originalPrice && !plan.isTrial ? (
                    <div className="mb-3">
                      <div className="flex items-center justify-center gap-3 mb-1">
                        <span className="text-2xl text-[#FEFEFF]/40 line-through">{plan.originalPrice}</span>
                        <span className="text-5xl font-bold text-[#EFBD3A]">{plan.price}</span>
                        <span className="text-[#FEFEFF]/60 ml-1">{plan.period}</span>
                      </div>
                      <div className="text-red-400 font-bold text-sm mb-2">
                        🔥 LAUNCH SPECIAL - 50% OFF!
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-baseline justify-center mb-2">
                      <span className="text-5xl font-bold text-[#EFBD3A]">{plan.price}</span>
                      <span className="text-[#FEFEFF]/60 ml-1">{plan.period}</span>
                    </div>
                  )}

                  {plan.billingNote && (
                    <p className="text-[#FEFEFF]/50 text-sm">
                      {plan.billingNote}
                    </p>
                  )}
                </div>

                <ul className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start space-x-3">
                      <CheckIcon className="w-5 h-5 text-[#EFBD3A] mt-0.5 flex-shrink-0" />
                      <span className="text-[#FEFEFF]/80">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  href={`/payment?plan=${plan.name.toLowerCase()}${plan.isTrial ? '' : `&period=${billingPeriod}`}`}
                  className={`w-full block text-center py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                    plan.highlighted
                      ? 'bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-black'
                      : 'border border-[#EFBD3A] text-[#EFBD3A] hover:bg-[#EFBD3A]/10'
                  }`}
                >
                  {plan.buttonText}
                </Link>

                {/* Launch Badge - Bottom Right */}
                {plan.launchDiscount && (
                  <div className="absolute -bottom-3 -right-3">
                    <div className="bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-2 rounded-full text-xs font-bold shadow-lg animate-pulse">
                      🚀 {plan.launchDiscount}
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-[#FEFEFF]/60 mb-4">
              Start with our 1-week trial for $29 • No setup fees • Cancel anytime
            </p>
            <div className="flex justify-center items-center space-x-6 text-sm text-[#FEFEFF]/40">
              <div className="flex items-center space-x-2">
                <ShieldCheckIcon className="w-4 h-4" />
                <span>Enterprise Security</span>
              </div>
              <div className="flex items-center space-x-2">
                <ClockIcon className="w-4 h-4" />
                <span>24/7 Uptime</span>
              </div>
              <div className="flex items-center space-x-2">
                <StarIcon className="w-4 h-4" />
                <span>Premium Support</span>
              </div>
            </div>
          </div>
        </div>
      </section>
      </div>

      {/* Global Styles */}
      <style jsx global>{`
        @keyframes gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }

        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }

        .animate-float {
          animation: float 3s ease-in-out infinite;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
          width: 8px;
        }

        ::-webkit-scrollbar-track {
          background: #0A0B0B;
        }

        ::-webkit-scrollbar-thumb {
          background: #EFBD3A;
          border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: #FFD700;
        }

        /* Smooth scroll behavior */
        html {
          scroll-behavior: smooth;
        }

        /* Focus styles for accessibility */
        button:focus-visible,
        a:focus-visible {
          outline: 2px solid #EFBD3A;
          outline-offset: 2px;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          *,
          *::before,
          *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `}</style>
    </>
  );
}