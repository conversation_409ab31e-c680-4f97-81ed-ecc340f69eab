import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from "framer-motion";
import DashboardLayout from "../components/DashboardLayout";
import StrategyCard from "../components/StrategyCard";
import { auth } from "../../firebaseConfig";
import { onAuthStateChanged } from "firebase/auth";
import { USE_FIREBASE_EMULATOR } from "../config";
import { getStrategyControllerUrl } from '../config/services';
import axios from 'axios';
import { useRouter } from 'next/router';
import dynamic from "next/dynamic";

const RiskManagementModal = dynamic(() => import("../components/RiskManagementModal"), {
  ssr: false,
});

const BacktestDialog = dynamic(() => import("../components/BacktestDialog"), {
  ssr: false,
});

const BacktestResults = dynamic(() => import("../components/BacktestResults"), {
  ssr: false,
});

const StrategyChart = dynamic(() => import("../components/StrategyChart"), {
  ssr: false,
});

const GET_STRATEGIES_URL = USE_FIREBASE_EMULATOR 
  ? "http://127.0.0.1:5001/oryntrade/us-central1/get_strategies"
  : "https://get-strategies-ihjc6tjxia-uc.a.run.app";

const DELETE_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/delete_strategy"
  : "https://delete-strategy-ihjc6tjxia-uc.a.run.app";

const PUBLISH_STRATEGY_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/publish_strategy"
    : "https://publish-strategy-ihjc6tjxia-uc.a.run.app";

const CONTROL_STRATEGY_URL = getStrategyControllerUrl();

// Helper function to make authenticated requests to Strategy Controller
const makeAuthenticatedRequest = async (method, url, data = null) => {
  const currentUser = auth.currentUser;
  if (!currentUser) {
    throw new Error("No user logged in");
  }

  const idToken = await currentUser.getIdToken(true);
  const config = {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${idToken}`,
    },
  };

  if (method.toLowerCase() === 'get') {
    return axios.get(url, config);
  } else if (method.toLowerCase() === 'post') {
    return axios.post(url, data, config);
  } else if (method.toLowerCase() === 'delete') {
    return axios.delete(url, { ...config, data });
  }
};

const GET_COMMUNITY_STRATEGIES_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/get_community_strategies"
    : "https://get-community-strategies-ihjc6tjxia-uc.a.run.app";

const ADD_STRATEGY_TO_USER_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/add_strategy_to_user"
    : "https://add-strategy-to-user-ihjc6tjxia-uc.a.run.app";

const DELETE_COMMUNITY_STRATEGY_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/delete_community_strategy"
    : "https://delete-community-strategy-ihjc6tjxia-uc.a.run.app";

const CHECK_USER_PRIVILEGES_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/check_user_privileges"
    : "https://check-user-privileges-ihjc6tjxia-uc.a.run.app";

// Helper function to generate unique IDs
const generateId = () => {
  return Math.random().toString(36).substring(2, 11);
};

export default function StrategyLibrary() {
  const [activeTab, setActiveTab] = useState('strategies');
  const [strategies, setStrategies] = useState([]);
  const [communityStrategies, setCommunityStrategies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [communityLoading, setCommunityLoading] = useState(false);
  const [deployedStrategyIds, setDeployedStrategyIds] = useState(new Set());
  const [selectedStrategy, setSelectedStrategy] = useState(null);
  const [showRiskModal, setShowRiskModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState(null);
  const [communityError, setCommunityError] = useState(null);
  const [hasPrivileges, setHasPrivileges] = useState(false);
  const [isDeletingCommunityStrategy, setIsDeletingCommunityStrategy] = useState(false);

  // Backtest related state
  const [isBacktestDialogOpen, setIsBacktestDialogOpen] = useState(false);
  const [backtestResults, setBacktestResults] = useState(null);
  const [selectedStrategyForBacktest, setSelectedStrategyForBacktest] = useState(null);

  // Chart related state for backtest results
  const [forexData, setForexData] = useState(null);
  const [isLoadingForexData, setIsLoadingForexData] = useState(false);
  const [forexDataError, setForexDataError] = useState(null);

  const router = useRouter();

  // Debug: Monitor selectedStrategyForBacktest changes
  useEffect(() => {
    console.log('🔍 selectedStrategyForBacktest changed:', {
      hasStrategy: !!selectedStrategyForBacktest,
      instruments: selectedStrategyForBacktest?.instruments,
      timeframe: selectedStrategyForBacktest?.timeframe,
      name: selectedStrategyForBacktest?.name
    });
  }, [selectedStrategyForBacktest]);

  const fetchStrategies = async () => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      setError("No user logged in");
      setLoading(false);
      return;
    }
    
    setLoading(true);
    try {
      const response = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${currentUser.uid}`);
      // Instead of querying Firestore directly, check the is_deployed field in the strategies we already have
      const deployedIds = new Set(
          response.data
              .filter(strategy => strategy.is_deployed === true)
              .map(strategy => strategy.id)
      );
      setDeployedStrategyIds(deployedIds);

      console.log("Deployed strategy IDs:", Array.from(deployedIds));

      // Sort strategies by creation date, newest first
      const sortedStrategies = response.data.sort((a, b) => {
        const dateA = new Date(a.created_at || 0);
        const dateB = new Date(b.created_at || 0);
        return dateB - dateA;
      });

      // Process each strategy to ensure proper data structure
      const processedStrategies = sortedStrategies.map(strategy => {
        let strategyData;
        try {
          strategyData = typeof strategy.strategy_json === 'string' 
            ? JSON.parse(strategy.strategy_json)
            : strategy.strategy_json;

          // Ensure risk management has the correct structure
          if (strategyData.riskManagement) {
            strategyData.riskManagement = {
              riskPercentage: strategyData.riskManagement.riskPercentage || "",
              riskRewardRatio: strategyData.riskManagement.riskRewardRatio || "",
              stopLossMethod: strategyData.riskManagement.stopLossMethod || "fixed",
              fixedPips: strategyData.riskManagement.fixedPips || "",
              indicatorBasedSL: strategyData.riskManagement.indicatorBasedSL || {
                indicator: "",
                parameters: {}
              },
              lotSize: strategyData.riskManagement.lotSize || ""
            };
          }

          // Ensure indicators have the correct structure
          if (strategyData.indicators) {
            strategyData.indicators = strategyData.indicators.map(indicator => ({
              id: indicator.id || generateId(),
              type: indicator.type || indicator.indicator_class,
              indicator_class: indicator.type || indicator.indicator_class,
              parameters: indicator.parameters || {},
              source: indicator.source || "price"
            }));
          }

          return {
            ...strategy,
            strategy_json: strategyData
          };
        } catch (e) {
          console.error("Error processing strategy:", e);
          return strategy;
        }
      });

      setStrategies(processedStrategies);
      setError(null);
    } catch (error) {
      console.error("Error fetching strategies:", error);
      setError(error.message || "Failed to fetch strategies");
    } finally {
      setLoading(false);
    }
  };

  const fetchCommunityStrategies = async () => {
    setCommunityLoading(true);
    try {
      const response = await axios.get(GET_COMMUNITY_STRATEGIES_URL);
      setCommunityStrategies(response.data);
      setCommunityError(null);
    } catch (error) {
      console.error("Error fetching community strategies:", error);
      setCommunityError(error.message || "Failed to fetch community strategies");
    } finally {
      setCommunityLoading(false);
    }
  };

  // Function to check if user has community strategy privileges
  const checkUserPrivileges = async (user) => {
    try {
      if (!user || !user.email) {
        setHasPrivileges(false);
        return;
      }

      const response = await axios.get(`${CHECK_USER_PRIVILEGES_URL}?email=${encodeURIComponent(user.email)}`);
      setHasPrivileges(response.data.hasPrivileges || false);
    } catch (error) {
      console.error("Error checking user privileges:", error);
      setHasPrivileges(false);
    }
  };

  useEffect(() => {
    fetchStrategies();
    fetchCommunityStrategies();

    // Check user privileges
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        checkUserPrivileges(user);
      } else {
        setHasPrivileges(false);
      }
    });

    return () => unsubscribe();
  }, []);

  const handleDeleteStrategy = async (strategyId) => {
    const currentUser = auth.currentUser;
    if (!currentUser) return;
    
    try {
      await axios.delete(DELETE_STRATEGY_URL, {
        data: {
          firebase_uid: currentUser.uid,
          strategy_id: strategyId
        }
      });
      
      // Refresh the strategies list after successful deletion
      await fetchStrategies();
    } catch (error) {
      console.error("Error deleting strategy:", error);
    }
  };

  const handleStrategySelect = (strategy) => {
    console.log('🎯 handleStrategySelect called with strategy:', {
      id: strategy.id,
      hasStrategyJson: !!strategy.strategy_json,
      strategyJsonType: typeof strategy.strategy_json
    });

    // Transform the strategy data to match the format expected by BacktestDialog
    // (same format as generatedStrategy from strategy-generation.js)
    let transformedStrategy;

    try {
      // Parse the strategy_json if it's a string
      const strategyData = typeof strategy.strategy_json === 'string'
        ? JSON.parse(strategy.strategy_json)
        : strategy.strategy_json;

      console.log('📊 Parsed strategy data:', {
        name: strategyData.name,
        instruments: strategyData.instruments,
        timeframe: strategyData.timeframe,
        hasIndicators: !!strategyData.indicators,
        hasEntryRules: !!strategyData.entryRules,
        hasExitRules: !!strategyData.exitRules
      });

      // Transform to match generatedStrategy format from strategy-generation.js
      transformedStrategy = {
        name: strategyData.name,
        description: strategyData.description,
        instruments: strategyData.instruments,
        timeframe: strategyData.timeframe,
        tradingSession: strategyData.tradingSession || [],
        indicators: strategyData.indicators || [],
        entryRules: strategyData.entryRules || [],
        exitRules: strategyData.exitRules || [],
        riskManagement: strategyData.riskManagement || {}
      };

      console.log('✅ Transformed strategy for backtest:', {
        name: transformedStrategy.name,
        instruments: transformedStrategy.instruments,
        timeframe: transformedStrategy.timeframe
      });
    } catch (error) {
      console.error('❌ Error transforming strategy for backtest:', error);
      transformedStrategy = strategy; // Fallback to original
    }

    // Set the transformed strategy for backtesting
    console.log('🔄 Setting selectedStrategyForBacktest...');
    setSelectedStrategyForBacktest(transformedStrategy);

    // Switch to the backtest results tab
    console.log('🔄 Switching to results tab...');
    setActiveTab('results');

    // Open the backtest dialog
    console.log('🔄 Opening backtest dialog...');
    setIsBacktestDialogOpen(true);
  };

  const handleDeployStrategy = (strategy) => {
    // Check if strategy is already deployed
    if (deployedStrategyIds.has(strategy.id)) {
      alert(
        "This strategy is already deployed as a trading agent. " +
        "Please go to the Trading Agents page to delete the existing agent before deploying again."
      );
      return;
    }

    setSelectedStrategy(strategy);
    setShowRiskModal(true);
  };

  // Backtest dialog handlers
  const handleCloseBacktestDialog = (clearStrategy = true) => {
    console.log('🔄 handleCloseBacktestDialog called with clearStrategy:', clearStrategy);
    setIsBacktestDialogOpen(false);
    // Only clear strategy data if explicitly requested (e.g., user cancels)
    // Don't clear when backtest completes successfully - we need the data for chart
    if (clearStrategy) {
      console.log('🗑️ Clearing selectedStrategyForBacktest');
      setSelectedStrategyForBacktest(null);
    } else {
      console.log('✅ Keeping selectedStrategyForBacktest for chart display');
    }
  };

  const handleBacktestComplete = async (results) => {
    console.log('🎯 handleBacktestComplete called with results:', {
      hasResults: !!results,
      tradesCount: results?.trades?.length || 0,
      selectedStrategy: !!selectedStrategyForBacktest,
      instruments: selectedStrategyForBacktest?.instruments,
      timeframe: selectedStrategyForBacktest?.timeframe,
      sampleRawTrade: results?.trades?.[0]
    });

    // Format trades data for chart compatibility and include strategy data (same as strategy-generation.js)
    const formattedResults = {
      ...results,
      strategy: selectedStrategyForBacktest, // Include strategy data for chart
      trades: results.trades?.map(trade => ({
        ...trade,
        // Map backend field names to chart expected field names
        type: trade.trade_type || trade.type, // Backend uses 'trade_type', chart expects 'type'
        entry_time: trade.entry_time ? (
          // Handle both timestamp integers and date strings
          typeof trade.entry_time === 'number'
            ? new Date(trade.entry_time * 1000) // Convert Unix timestamp to Date
            : new Date(trade.entry_time + 'Z') // Add 'Z' to parse as UTC
        ) : null,
        exit_time: trade.exit_time ? (
          // Handle both timestamp integers and date strings
          typeof trade.exit_time === 'number'
            ? new Date(trade.exit_time * 1000) // Convert Unix timestamp to Date
            : new Date(trade.exit_time + 'Z') // Add 'Z' to parse as UTC
        ) : null
      })) || []
    };

    console.log('✅ Formatted backtest results for chart:', {
      totalTrades: formattedResults.trades.length,
      sampleTrade: formattedResults.trades[0],
      hasStrategy: !!formattedResults.strategy,
      strategyInstruments: formattedResults.strategy?.instruments,
      strategyTimeframe: formattedResults.strategy?.timeframe
    });

    setBacktestResults(formattedResults);
    // Close dialog but keep strategy data for chart display
    handleCloseBacktestDialog(false);

    // Fetch forex data for the chart using strategy data from results
    if (formattedResults.strategy?.instruments) {
      console.log('🚀 Starting chart data fetch...');
      try {
        await fetchForexDataForChart(
          formattedResults.strategy.instruments,
          formattedResults.strategy.timeframe || '1h'
        );
        console.log('✅ Chart data fetch completed successfully');
      } catch (error) {
        console.error('❌ Chart data fetch failed in handleBacktestComplete:', error);
      }
    } else {
      console.warn('⚠️ No strategy data available for chart, skipping chart data fetch');
    }

    // Keep the results tab active to show the results
  };

  // Function to fetch forex data for chart display (same approach as strategy-generation.js)
  const fetchForexDataForChart = async (forexPair, timeframe) => {
    console.log('🔍 fetchForexDataForChart called with:', { forexPair, timeframe });

    if (!forexPair || !timeframe) {
      console.error('❌ Missing required parameters:', { forexPair, timeframe });
      return;
    }

    setIsLoadingForexData(true);
    setForexDataError(null);

    try {
      console.log(`📊 Starting forex data fetch: ${forexPair}, ${timeframe}`);

      // Use the same endpoint and approach as strategy-generation.js
      const GET_HISTORICAL_DATA_URL = USE_FIREBASE_EMULATOR
        ? "http://127.0.0.1:5001/oryntrade/us-central1/fetch_historical_data_from_polygon"
        : "https://fetch-historical-data-from-polygon-ihjc6tjxia-uc.a.run.app";

      console.log('🌐 Using endpoint:', GET_HISTORICAL_DATA_URL);

      // Import getDateRange function for proper date handling
      console.log('📅 Importing getDateRange utility...');
      const { getDateRange } = await import('../utils/dateUtils');
      const { startDate, endDate, displayRange, chunkSize } = getDateRange(timeframe);

      console.log('📅 Date range calculated:', {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        chunkSize
      });

      let allData = [];
      let currentStart = new Date(startDate);
      let chunkCount = 0;

      console.log('🔄 Starting chunked data fetching...');

      while (currentStart < endDate) {
        chunkCount++;
        let chunkEnd = new Date(currentStart);
        chunkEnd.setDate(chunkEnd.getDate() + chunkSize);

        if (chunkEnd > endDate) {
          chunkEnd = endDate;
        }

        const requestParams = {
          symbol: forexPair,
          timeframe: timeframe,
          start_date: currentStart.toISOString().split('T')[0],
          end_date: chunkEnd.toISOString().split('T')[0]
        };

        console.log(`📦 Fetching chunk ${chunkCount}:`, requestParams);

        try {
          // Use GET request with query parameters (same as strategy-generation.js)
          const response = await axios.get(GET_HISTORICAL_DATA_URL, {
            params: requestParams
          });

          console.log(`✅ Chunk ${chunkCount} response:`, {
            status: response.status,
            hasData: !!response.data,
            dataType: typeof response.data,
            hasDataArray: !!(response.data && response.data.data),
            dataLength: response.data?.data?.length || 0
          });

          if (response.data && response.data.data) {
            allData = [...allData, ...response.data.data];
            console.log(`📈 Added ${response.data.data.length} candles, total: ${allData.length}`);
          } else {
            console.warn(`⚠️ Chunk ${chunkCount} returned no data:`, response.data);
          }
        } catch (chunkError) {
          console.error(`❌ Error fetching chunk ${chunkCount}:`, chunkError);
          throw chunkError; // Re-throw to be caught by outer try-catch
        }

        currentStart = chunkEnd;
        await new Promise(resolve => setTimeout(resolve, 300)); // Rate limiting
      }

      console.log(`🎯 Completed chunked fetching: ${chunkCount} chunks, ${allData.length} total candles`);

      console.log('🔄 Processing raw data...');
      console.log('📊 Raw data sample:', allData.slice(0, 2));

      // Format and process the data (same as strategy-generation.js)
      const processedData = allData
        .filter(item => {
          const isValid = item && typeof item === 'object';
          if (!isValid) console.warn('⚠️ Invalid item filtered out:', item);
          return isValid;
        })
        .map((item, index) => {
          try {
            // Parse the ISO datetime string and ensure it's treated as UTC
            const utcDateString = item.datetime.endsWith('Z') ?
              item.datetime :
              item.datetime + 'Z';

            // Create date object from UTC string
            const dateObj = new Date(utcDateString);

            // Get timestamp in seconds
            const timestamp = dateObj.getTime() / 1000;

            const processed = {
              time: Math.floor(timestamp),
              open: Number(item.open) || 0,
              high: Number(item.high) || 0,
              low: Number(item.low) || 0,
              close: Number(item.close) || 0,
              volume: Number(item.volume) || 0
            };

            if (index < 2) {
              console.log(`📈 Processed sample ${index}:`, { original: item, processed });
            }

            return processed;
          } catch (error) {
            console.error('❌ Error processing item:', item, error);
            return null;
          }
        })
        .filter(item => {
          if (!item) return false;

          const isValid = !isNaN(item.time) &&
            !isNaN(item.open) &&
            !isNaN(item.high) &&
            !isNaN(item.low) &&
            !isNaN(item.close) &&
            item.high >= item.low;

          if (!isValid) {
            console.warn('⚠️ Invalid processed item filtered out:', item);
          }

          return isValid;
        });

      console.log(`✅ Data processing complete: ${allData.length} → ${processedData.length} candles`);

      // Remove duplicates and sort
      const uniqueData = Array.from(new Map(
        processedData.map(item => [item.time, item])
      ).values()).sort((a, b) => a.time - b.time);

      console.log(`🎯 Final data: ${processedData.length} → ${uniqueData.length} unique candles`);
      console.log('📊 Final data sample:', uniqueData.slice(0, 2));

      setForexData(uniqueData);
      console.log(`✅ Successfully loaded ${uniqueData.length} candles for chart`);

    } catch (error) {
      console.error('❌ Error fetching forex data for chart:', {
        message: error.message,
        stack: error.stack,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config
      });

      // Set a more detailed error message
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.message ||
                          error.message ||
                          'Unknown error occurred';

      setForexDataError(`Chart data fetch failed: ${errorMessage}`);
    } finally {
      console.log('🏁 Forex data fetch completed, loading state cleared');
      setIsLoadingForexData(false);
    }
  };

  // Function to check if a community strategy is already in user's collection
  const isStrategyAlreadyAdded = (communityStrategy) => {
    if (!communityStrategy || !communityStrategy.strategy_json) return false;

    try {
      const communityStrategyData = typeof communityStrategy.strategy_json === 'string'
        ? JSON.parse(communityStrategy.strategy_json)
        : communityStrategy.strategy_json;

      return strategies.some(userStrategy => {
        try {
          const userStrategyData = typeof userStrategy.strategy_json === 'string'
            ? JSON.parse(userStrategy.strategy_json)
            : userStrategy.strategy_json;

          // Check if this strategy was copied from community (has source field)
          // or if the strategy content is identical
          return userStrategy.source === 'community' &&
                 userStrategyData.name === communityStrategyData.name &&
                 userStrategyData.instruments === communityStrategyData.instruments &&
                 userStrategyData.timeframe === communityStrategyData.timeframe;
        } catch (e) {
          return false;
        }
      });
    } catch (e) {
      return false;
    }
  };

  const handleAddCommunityStrategy = async (strategy) => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      setError("No user logged in");
      return;
    }

    // Check if already added
    if (isStrategyAlreadyAdded(strategy)) {
      alert("This strategy is already in your collection!");
      return;
    }

    setActionLoading(true);
    try {
      const response = await axios.post(ADD_STRATEGY_TO_USER_URL, {
        firebase_uid: currentUser.uid,
        strategy: strategy
      });

      if (response.data.success) {
        // Refresh the user's strategies list
        await fetchStrategies();
        // Show success message or toast
        alert("Strategy added to your collection successfully!");
      }
    } catch (error) {
      console.error("Error adding community strategy:", error);
      setError(error.message || "Failed to add strategy to your collection");
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteCommunityStrategy = async (strategyId) => {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      setError("No user logged in");
      return;
    }

    if (!hasPrivileges) {
      alert("You don't have permission to delete community strategies.");
      return;
    }

    // Confirm deletion
    if (!confirm("Are you sure you want to delete this community strategy? This action cannot be undone.")) {
      return;
    }

    setIsDeletingCommunityStrategy(true);
    try {
      const response = await axios.delete(DELETE_COMMUNITY_STRATEGY_URL, {
        data: {
          user_email: currentUser.email,
          strategy_id: strategyId
        }
      });

      if (response.data.success) {
        // Refresh the community strategies list
        await fetchCommunityStrategies();
        alert("Community strategy deleted successfully!");
      } else {
        alert("Failed to delete community strategy: " + response.data.message);
      }
    } catch (error) {
      console.error("Error deleting community strategy:", error);
      setError(error.message || "Failed to delete community strategy");
    } finally {
      setIsDeletingCommunityStrategy(false);
    }
  };

  const handleRiskConfirm = async (riskParams) => {
    if (!selectedStrategy || !auth.currentUser) return;

    setActionLoading(true);
    try {
      // Get a fresh token
      const idToken = await auth.currentUser.getIdToken(true);

      // First, publish the strategy with risk parameters
      console.log("Publishing strategy with ID:", selectedStrategy.id);

      // Prepare the strategy JSON with risk parameters
      let strategyJson = selectedStrategy.strategy_json;
      if (typeof strategyJson === 'string') {
        try {
          strategyJson = JSON.parse(strategyJson);
        } catch (e) {
          console.error('Error parsing strategy JSON:', e);
          strategyJson = {};
        }
      }

      // Add risk management parameters to the strategy JSON
      // Make sure we preserve the existing stopLoss and takeProfit if they exist
      if (strategyJson.riskManagement) {
        // Merge the existing risk management with the new parameters
        strategyJson.riskManagement = {
          ...strategyJson.riskManagement,
          ...riskParams
        };
      } else {
        // Just use the new parameters
        strategyJson.riskManagement = riskParams;
      }

      // Ensure stopLoss and takeProfit are always present
      if (!strategyJson.riskManagement.stopLoss) {
        console.warn("stopLoss is missing from risk management, adding default value");
        strategyJson.riskManagement.stopLoss = "1";
        strategyJson.riskManagement.stopLossUnit = "percentage";
      }

      if (!strategyJson.riskManagement.takeProfit) {
        console.warn("takeProfit is missing from risk management, adding default value");
        strategyJson.riskManagement.takeProfit = "2";
        strategyJson.riskManagement.takeProfitUnit = "percentage";
      }

      // Log the final risk management configuration
      console.log("Final risk management configuration:", strategyJson.riskManagement);

      // Convert the strategy JSON to a string
      const strategyJsonString = JSON.stringify(strategyJson);
      console.log("Stringified strategy JSON:", strategyJsonString);

      const publishResponse = await axios.post(
          PUBLISH_STRATEGY_URL,
          {
            strategy_id: selectedStrategy.id,
            user_id: auth.currentUser.uid,
            strategy_json: strategyJsonString,  // Send as a string
            risk_management: riskParams
          },
          {
            headers: {
              Authorization: `Bearer ${idToken}`,
            },
          }
      );

      // Then, start the agent
      await axios.post(
          `${CONTROL_STRATEGY_URL}/control-strategy/${auth.currentUser.uid}/${selectedStrategy.id}`,
          { command: "start" },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${idToken}`,
            },
          }
      );

      // Log the success response
      console.log("Strategy published successfully:", publishResponse.data);

      // Close the modal
      setShowRiskModal(false);

      // Show success message (you can add a toast notification here if you have one)
      console.log("Strategy deployed successfully, redirecting to Trade Bots page...");

      // Navigate to the trade bots page
      window.location.href = "http://localhost:3000/trade-bots";
    } catch (error) {
      console.error("Error deploying strategy:", error);

      // Extract the error message from the response if available
      let errorMessage = "Failed to deploy strategy";

      if (error.response && error.response.data) {
        console.log("Server error response:", error.response.data);

        if (error.response.data.message) {
          errorMessage = `Deployment failed: ${error.response.data.message}`;
          console.log("Error message details:", error.response.data.message);
        }
      } else {
        console.log("No response data available, raw error:", error.message);
      }

      // Log the request payload for debugging
      if (error.config && error.config.data) {
        try {
          const requestData = JSON.parse(error.config.data);
          console.log("Request payload that caused the error:", requestData);
        } catch (e) {
          console.log("Could not parse request data:", error.config.data);
        }
      }

      setError(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen text-white p-6"
      >
        <div className="w-full">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-[#FEFEFF] mb-2">Strategy Library</h1>
            <p className="text-[#FEFEFF]/60">
              View, test, and manage your trading strategies
            </p>
          </div>

          {/* Tabs */}
          <div className="mb-8">
            <div className="border-b border-white/10">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => {
                    setActiveTab('strategies');
                    // Clear backtest data when switching away from results
                    if (activeTab === 'results') {
                      setBacktestResults(null);
                      setSelectedStrategyForBacktest(null);
                      setForexData(null);
                    }
                  }}
                  className={`${
                    activeTab === 'strategies'
                      ? 'border-[#EFBD3A] text-[#EFBD3A]'
                      : 'border-transparent text-[#FEFEFF]/60 hover:text-[#FEFEFF] hover:border-[#FEFEFF]/40'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  My Strategies
                </button>
                <button
                  onClick={() => setActiveTab('results')}
                  className={`${
                    activeTab === 'results'
                      ? 'border-[#EFBD3A] text-[#EFBD3A]'
                      : 'border-transparent text-[#FEFEFF]/60 hover:text-[#FEFEFF] hover:border-[#FEFEFF]/40'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Backtest Results
                </button>
                <button
                  onClick={() => {
                    setActiveTab('community');
                    // Clear backtest data when switching away from results
                    if (activeTab === 'results') {
                      setBacktestResults(null);
                      setSelectedStrategyForBacktest(null);
                      setForexData(null);
                    }
                  }}
                  className={`${
                    activeTab === 'community'
                      ? 'border-[#EFBD3A] text-[#EFBD3A]'
                      : 'border-transparent text-[#FEFEFF]/60 hover:text-[#FEFEFF] hover:border-[#FEFEFF]/40'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                >
                  Community Strategies
                </button>
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="mt-8">
            {activeTab === 'strategies' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FEFEFF]"></div>
                  </div>
                ) : error ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-red-500 mb-2">Error Loading Strategies</h3>
                    <p className="text-[#FEFEFF]/60">{error}</p>
                  </div>
                ) : strategies.length === 0 ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-[#FEFEFF] mb-2">No Strategies Found</h3>
                    <p className="text-[#FEFEFF]/60 mb-4">
                      Create your first strategy in the Strategy Generation page to get started
                    </p>
                    <button
                      onClick={() => router.push('/strategy-generation')}
                      className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-blue-500/30 border border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all duration-200"
                    >
                      Create Strategy
                    </button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {strategies.map((strategy) => (
                      <StrategyCard
                        key={strategy.id}
                        strategy={strategy}
                        onSelect={handleStrategySelect}
                        onDelete={handleDeleteStrategy}
                        onDeploy={handleDeployStrategy}
                        isDeployed={deployedStrategyIds.has(strategy.id)}
                      />
                    ))}
                  </div>
                )}
              </motion.div>
            )}

            {activeTab === 'results' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="mb-6">
                  <h2 className="text-xl font-semibold text-[#FEFEFF] mb-2">Backtest Results</h2>
                  <p className="text-[#FEFEFF]/60">
                    View and analyze the results of your backtests
                  </p>
                </div>

                {backtestResults ? (
                  <div className="space-y-6">
                    {/* Chart Section - Simplified to match strategy-generation.js exactly */}
                    <div className="bg-[#1a1a1a] rounded-xl p-4 md:p-6 shadow-lg overflow-hidden min-w-[320px]">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                        <h3 className="text-lg font-semibold text-[#FEFEFF]">Backtest Chart</h3>
                        <div className="flex items-center gap-2 md:gap-4">
                          <div className="text-xs md:text-sm text-[#FEFEFF]/60">
                            {backtestResults.strategy?.instruments || 'Unknown'} • {backtestResults.strategy?.timeframe || 'Unknown'} • {backtestResults.trades?.length || 0} trades
                          </div>
                        </div>
                      </div>
                      <div className="w-full overflow-x-auto min-w-[300px]" style={{ minWidth: '300px', maxWidth: '100%' }}>
                        {(() => {
                          console.log('📊 StrategyChart props for backtest results:', {
                            forexPair: backtestResults.strategy?.instruments,
                            timeframe: backtestResults.strategy?.timeframe,
                            dataLength: forexData?.length,
                            indicatorsCount: backtestResults.strategy?.indicators?.length,
                            indicators: backtestResults.strategy?.indicators,
                            tradesCount: backtestResults.trades?.length
                          });

                          return (
                            <StrategyChart
                              forexPair={backtestResults.strategy?.instruments || 'EUR/USD'}
                              timeframe={backtestResults.strategy?.timeframe || '1h'}
                              userTimezone="UTC"
                              selectedTradingSessions={[]}
                              data={forexData}
                              isLoading={isLoadingForexData}
                              error={forexDataError}
                              indicators={backtestResults.strategy?.indicators || []}
                              trades={backtestResults.trades || []}
                              onIndicatorAdd={() => {}} // No-op for backtest results
                            />
                          );
                        })()}
                      </div>
                    </div>

                    {/* Results Section */}
                    <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                      <BacktestResults results={backtestResults} />
                    </div>
                  </div>
                ) : (
                  <div className="p-6 bg-white/5 border border-white/10 rounded-xl text-center">
                    <div className="mb-4">
                      <svg className="w-16 h-16 mx-auto text-[#FEFEFF]/30 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <h3 className="text-lg font-semibold text-[#FEFEFF] mb-2">No Backtest Results</h3>
                      <p className="text-[#FEFEFF]/60 mb-4">
                        Run a backtest on one of your strategies to see the results here.
                      </p>
                      <p className="text-sm text-[#FEFEFF]/40">
                        Go to "My Strategies" tab and click "Run Backtest" on any strategy card.
                      </p>
                    </div>
                  </div>
                )}
              </motion.div>
            )}

            {activeTab === 'community' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {communityLoading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FEFEFF]"></div>
                  </div>
                ) : communityError ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-red-500 mb-2">Error Loading Community Strategies</h3>
                    <p className="text-[#FEFEFF]/60">{communityError}</p>
                  </div>
                ) : communityStrategies.length === 0 ? (
                  <div className="text-center py-12">
                    <h3 className="text-xl font-semibold text-[#FEFEFF] mb-2">No Community Strategies Available</h3>
                    <p className="text-[#FEFEFF]/60 mb-4">
                      Community strategies will be available soon. Check back later!
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {communityStrategies.map((strategy) => (
                      <StrategyCard
                        key={strategy.id}
                        strategy={strategy}
                        onSelect={null}
                        onDelete={hasPrivileges ? handleDeleteCommunityStrategy : null}
                        onDeploy={null}
                        onAddToMyStrategies={handleAddCommunityStrategy}
                        isDeployed={false}
                        isCommunityStrategy={true}
                        isAddingToCollection={actionLoading}
                        isAlreadyAdded={isStrategyAlreadyAdded(strategy)}
                        hasPrivileges={hasPrivileges}
                        isDeletingCommunityStrategy={isDeletingCommunityStrategy}
                      />
                    ))}
                  </div>
                )}
              </motion.div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Risk Management Modal */}
      <AnimatePresence>
        {showRiskModal && (
            <RiskManagementModal
                isOpen={showRiskModal}
                onClose={() => setShowRiskModal(false)}
                onConfirm={handleRiskConfirm}
                strategy={selectedStrategy}
            />
        )}
      </AnimatePresence>

      {/* Backtest Dialog */}
      <AnimatePresence>
        {isBacktestDialogOpen && selectedStrategyForBacktest && (
          <BacktestDialog
            isOpen={isBacktestDialogOpen}
            onClose={handleCloseBacktestDialog}
            strategy={selectedStrategyForBacktest}
            onBacktestComplete={handleBacktestComplete}
          />
        )}
      </AnimatePresence>
    </DashboardLayout>
  );
}