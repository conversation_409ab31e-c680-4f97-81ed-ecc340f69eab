import { useEffect, useState, useRef, useMemo, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "react-hot-toast";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../../../firebaseConfig";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import MetricCard from "../../components/MetricCard";
import TradeHistoryRow from "../../components/TradeHistoryRow";
import StrategyRules from "../../components/StrategyRules";
import StrategyVisualization from "../../components/StrategyVisualization";
import RiskManagementPanel from "../../components/RiskManagementPanel";
import StatusNotification from "../../components/StatusNotification";
import UserLogs from "../../components/UserLogs";
import PerformanceDashboard from "../../components/PerformanceDashboard";
import TradeNotification from "../../components/TradeNotification";
import MarketConditionsPanel from "../../components/MarketConditionsPanel";
import TradeSyncFixerButton from "../../components/TradeSyncFixerButton";
import TradeBotHeader from "../../components/TradeBotHeader";
import PerformanceMetricsRow from "../../components/PerformanceMetricsRow";
import RiskManagementDashboard from "../../components/RiskManagementDashboard";
import TradeBotTabs from "../../components/TradeBotTabs";
import { PauseConfirmationDialog, StopConfirmationDialog } from "../../components/ConfirmationDialog";
import {
  getDoc,
  doc,
  getDocs,
  query,
  where,
  collection,
  updateDoc,
} from "firebase/firestore";
import { db } from "../../../firebaseConfig";
import PolygonTradingChart from "../../components/PolygonTradingChart";
import SmoothLoadingOverlay, { SmoothContentTransition, ChartSkeleton } from "../../components/SmoothLoadingOverlay";
import { USE_FIREBASE_EMULATOR } from "../../config";
import polygonApiService from "../../services/polygonApiService";
import { getFunctions, httpsCallable } from 'firebase/functions';

// Utility function to calculate trading period from historical trades
const calculateTradingPeriod = (historicalTrades) => {
  if (!historicalTrades || historicalTrades.length === 0) {
    return null;
  }

  // Get all trades with valid timestamps
  const tradesWithTimes = historicalTrades.filter(trade => {
    return trade.openTime || trade.closeTime;
  });

  if (tradesWithTimes.length === 0) {
    return null;
  }

  // Extract all timestamps (both open and close times)
  const timestamps = [];

  tradesWithTimes.forEach(trade => {
    // Add open time
    if (trade.openTime) {
      let openTime;
      if (trade.openTime instanceof Date) {
        openTime = trade.openTime;
      } else if (typeof trade.openTime === 'string') {
        openTime = new Date(trade.openTime);
      } else if (typeof trade.openTime === 'object' && trade.openTime.seconds) {
        openTime = new Date(trade.openTime.seconds * 1000);
      }
      if (openTime && !isNaN(openTime.getTime())) {
        timestamps.push(openTime);
      }
    }

    // Add close time if available
    if (trade.closeTime) {
      let closeTime;
      if (trade.closeTime instanceof Date) {
        closeTime = trade.closeTime;
      } else if (typeof trade.closeTime === 'string') {
        closeTime = new Date(trade.closeTime);
      } else if (typeof trade.closeTime === 'object' && trade.closeTime.seconds) {
        closeTime = new Date(trade.closeTime.seconds * 1000);
      }
      if (closeTime && !isNaN(closeTime.getTime())) {
        timestamps.push(closeTime);
      }
    }
  });

  if (timestamps.length === 0) {
    return null;
  }

  // Sort timestamps and get first and last
  timestamps.sort((a, b) => a.getTime() - b.getTime());
  const firstTradeTime = timestamps[0];
  const lastTradeTime = timestamps[timestamps.length - 1];

  // Add some buffer around the trading period (e.g., 1 day before and after)
  const bufferMs = 24 * 60 * 60 * 1000; // 1 day in milliseconds
  const startTime = new Date(firstTradeTime.getTime() - bufferMs);
  const endTime = new Date(lastTradeTime.getTime() + bufferMs);

  return {
    startTime,
    endTime,
    firstTradeTime,
    lastTradeTime,
    totalTrades: tradesWithTimes.length,
    tradingDuration: lastTradeTime.getTime() - firstTradeTime.getTime()
  };
};

// Global cache to persist across component remounts
const globalCandleCache = new Map();

// Stable empty indicators object to prevent unnecessary re-renders
const EMPTY_INDICATORS = {};

// Performance logging utility - only log in development
const perfLog = (message, ...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(message, ...args);
  }
};

// Chart state persistence for smooth refreshes
const chartStateCache = new Map();

const DashboardLayout = dynamic(
  () => import("../../components/DashboardLayout"),
  {
    ssr: false,
  }
);

import { getStrategyControllerUrl } from '../../config/services';

// Define control strategy URL
const CONTROL_STRATEGY_URL = getStrategyControllerUrl();

export default function TradeBotDetail() {
  const router = useRouter();
  const { id } = router.query;

  // Core state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [botStatus, setBotStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({
    show: false,
    type: null
  });

  // Data state
  const [strategy, setStrategy] = useState({});
  const [tradeLogs, setTradeLogs] = useState([]);
  const [openTrades, setOpenTrades] = useState([]);
  const [historicalTrades, setHistoricalTrades] = useState([]);
  const [candleData, setCandleData] = useState([]);
  const [indicators, setIndicators] = useState(EMPTY_INDICATORS);

  // Performance optimized state
  const [candleDataLoading, setCandleDataLoading] = useState(false);
  const [candleDataCache, setCandleDataCache] = useState(new Map());
  const [historicalDataLoaded, setHistoricalDataLoaded] = useState(() => {
    // Check if historical data was already loaded for this agent in this session
    try {
      return sessionStorage.getItem(`historicalDataLoaded_${id}`) === 'true';
    } catch {
      return false;
    }
  });
  // UI state
  const [currentTime, setCurrentTime] = useState(new Date());
  const [userTimezone] = useState(Intl.DateTimeFormat().resolvedOptions().timeZone);
  const [isColumnMenuOpen, setIsColumnMenuOpen] = useState(false);

  // Performance metrics state
  const [botPerformance, setBotPerformance] = useState({
    totalPnL: 0,
    winRate: 0,
    totalTrades: 0,
  });
  const [strategySummary, setStrategySummary] = useState({
    totalTrades: 0,
    totalRealizedPL: 0,
    totalUnrealizedPL: 0,
    winRate: 0,
    maxDrawdown: 0,
    sharpeRatio: 0,
    profitFactor: 0,
    averageWin: 0,
    averageLoss: 0,
    largestWin: 0,
    largestLoss: 0,
  });

  // Market data state
  const [lastHeartbeat, setLastHeartbeat] = useState(null);
  const [marketStatus, setMarketStatus] = useState(null);
  const [accountBalance, setAccountBalance] = useState(null);
  const [riskManagement, setRiskManagement] = useState(null);

  // Refs for performance optimization
  const columnButtonRef = useRef(null);
  const columnMenuRef = useRef(null);
  const fetchTimeoutRef = useRef(null);
  const lastFetchRef = useRef(0);

  // Handle click outside to close the column menu
  useEffect(() => {
    function handleClickOutside(event) {
      if (columnMenuRef.current && !columnMenuRef.current.contains(event.target) &&
          columnButtonRef.current && !columnButtonRef.current.contains(event.target)) {
        setIsColumnMenuOpen(false);
      }
    }

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // New state variables for enhanced user experience
  const [previousStatus, setPreviousStatus] = useState(null);
  const [showStatusNotification, setShowStatusNotification] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [lastTradeTimestamp, setLastTradeTimestamp] = useState(null);
  const notificationsRef = useRef([]);

  // Handle authentication
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        router.push("/login");
      } else {
        setIsAuthenticated(true);
        setFirebaseUser(user);
      }
    });
    return () => unsubscribe();
  }, [router]);

  // Update running clock every second
  useEffect(() => {
    const clockInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(clockInterval);
  }, []);

  // Auto-refresh state with performance optimization
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(60000); // 60 seconds default
  const [chartZoomState, setChartZoomState] = useState(null); // Preserve chart zoom during refreshes

  // Function to save current chart zoom state
  const saveCurrentZoomState = useCallback(() => {
    try {
      if (window.chartInstance) {
        const timeScale = window.chartInstance.timeScale();
        const currentRange = timeScale.getVisibleLogicalRange();
        if (currentRange && currentRange.from !== undefined && currentRange.to !== undefined) {
          const newZoomState = {
            from: currentRange.from,
            to: currentRange.to,
            timestamp: Date.now()
          };
          console.log('💾 Saving current zoom state:', newZoomState);
          setChartZoomState(newZoomState);

          // Also store in sessionStorage as backup
          try {
            sessionStorage.setItem(`chartZoomState_${id}`, JSON.stringify(newZoomState));
          } catch (e) {
            console.warn('⚠️ Could not save zoom state to sessionStorage:', e);
          }

          return newZoomState;
        }
      }
    } catch (error) {
      console.warn('⚠️ Error saving zoom state:', error);
    }
    return null;
  }, [id]);

  // Function to restore zoom state from sessionStorage if needed
  const restoreZoomStateFromStorage = useCallback(() => {
    try {
      const stored = sessionStorage.getItem(`chartZoomState_${id}`);
      if (stored) {
        const parsedState = JSON.parse(stored);
        if (parsedState.from !== undefined && parsedState.to !== undefined) {
          console.log('💾 Restoring zoom state from sessionStorage:', parsedState);
          setChartZoomState(parsedState);
          return parsedState;
        }
      }
    } catch (error) {
      console.warn('⚠️ Error restoring zoom state from storage:', error);
    }
    return null;
  }, [id]);

  // Initialize zoom state from storage on component mount
  useEffect(() => {
    if (!chartZoomState) {
      restoreZoomStateFromStorage();
    }
  }, [chartZoomState, restoreZoomStateFromStorage]);

  // Periodically save zoom state to prevent loss during auto-refresh
  useEffect(() => {
    const saveZoomInterval = setInterval(() => {
      if (window.chartInstance) {
        saveCurrentZoomState();
      }
    }, 10000); // Save every 10 seconds

    return () => clearInterval(saveZoomInterval);
  }, [saveCurrentZoomState]);

  // Check if current time is in trading session
  const isInTradingSession = () => {
    const tradingSessions = strategy?.strategy_json?.tradingSession || [];

    // If no trading sessions specified, assume all sessions are valid
    if (!tradingSessions || tradingSessions.length === 0) {
      return true;
    }

    const currentHour = new Date().getUTCHours();

    // Check if current time is in any of the specified trading sessions
    return tradingSessions.some(session => {
      switch(session) {
        case 'New York':
          // New York session: 8:00-17:00 EST (13:00-22:00 UTC)
          return currentHour >= 13 && currentHour < 22;
        case 'London':
          // London session: 8:00-17:00 GMT (8:00-17:00 UTC)
          return currentHour >= 8 && currentHour < 17;
        case 'Tokyo':
          // Tokyo session: 9:00-18:00 JST (0:00-9:00 UTC)
          return currentHour >= 0 && currentHour < 9;
        case 'Sydney':
          // Sydney session: 8:00-17:00 AEST (22:00-7:00 UTC)
          return currentHour >= 22 || currentHour < 7;
        case 'All':
          return true;
        default:
          return false;
      }
    });
  };

  // Memoized performance calculation to prevent unnecessary recalculations
  const calculatePerformance = useCallback((trades) => {
    if (!trades || trades.length === 0) {
      return { totalPnL: 0, winRate: 0, totalTrades: 0 };
    }

    const closedTrades = trades.filter(
      (trade) => trade.status?.toUpperCase() === "CLOSED"
    );
    const winningTrades = closedTrades.filter(
      (trade) => parseFloat(trade.realizedPL) > 0
    );

    // Calculate total P/L: sum of realized P/L from all closed trades + unrealized P/L from open trades
    const totalPnL = trades.reduce((sum, trade) => {
      if (trade.status?.toUpperCase() === "CLOSED") {
        return sum + (parseFloat(trade.realizedPL) || 0);
      } else {
        return sum + (parseFloat(trade.unrealizedPL) || 0);
      }
    }, 0);

    return {
      totalPnL,
      winRate:
        closedTrades.length > 0
          ? winningTrades.length / closedTrades.length
          : 0,
      totalTrades: trades.length,
    };
  }, []);

  // Reset historical data loaded flag when agent ID changes
  useEffect(() => {
    // Clear the flag for the previous agent and check for the new agent
    const newFlag = sessionStorage.getItem(`historicalDataLoaded_${id}`) === 'true';
    setHistoricalDataLoaded(newFlag);
    perfLog(`🔄 Agent ID changed to ${id}, historical data flag: ${newFlag}`);
  }, [id]);

  // Optimized data fetching with debouncing
  const fetchBotDataOptimized = useCallback(async () => {
    // Early return if user is not authenticated
    if (!firebaseUser?.uid || !id) {
      perfLog('❌ Cannot fetch agent data: missing firebaseUser or id');
      return;
    }

    const now = Date.now();

    // Debounce rapid calls
    if (now - lastFetchRef.current < 1000) {
      perfLog('🚫 Debouncing fetchBotData call');
      return;
    }

    lastFetchRef.current = now;

    // Clear any pending timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }

    // Store chart state before refresh to preserve zoom/pan
    saveCurrentZoomState();

    return fetchBotData();
  }, [firebaseUser?.uid, id, historicalTrades, saveCurrentZoomState]);

  // Poll agent status and data at the specified interval
  useEffect(() => {
    if (firebaseUser?.uid && id) {
      fetchBotDataOptimized();

      let interval;
      if (autoRefresh) {
        perfLog(`Setting up auto-refresh every ${refreshInterval/1000} seconds (historical data will only load once)`);
        interval = setInterval(fetchBotDataOptimized, refreshInterval);
      }

      return () => {
        if (interval) clearInterval(interval);
        if (fetchTimeoutRef.current) clearTimeout(fetchTimeoutRef.current);
      };
    }
  }, [firebaseUser?.uid, id, autoRefresh, refreshInterval, fetchBotDataOptimized]);

  // Debug effect to track data availability for chart
  useEffect(() => {
    console.log('🔍 Chart Data Availability Changed:', {
      candleDataLength: candleData.length,
      hasStrategy: !!strategy,
      hasStrategyJson: !!strategy?.strategy_json,
      hasIndicators: !!strategy?.strategy_json?.indicators,
      indicatorCount: strategy?.strategy_json?.indicators?.length || 0,
      indicatorTypes: strategy?.strategy_json?.indicators?.map(ind => ind.type || ind.indicator_class) || []
    });
  }, [candleData, strategy]);

  // Trading period data loading effect - triggers when agent status changes
  useEffect(() => {
    console.log('🔄 TRADING PERIOD EFFECT TRIGGERED:', {
      botStatus,
      historicalTradesLength: historicalTrades?.length || 0,
      strategyAvailable: !!strategy,
      candleDataLength: candleData.length
    });

    if (!botStatus || !historicalTrades || historicalTrades.length === 0) {
      console.log('⏭️ Skipping trading period effect - missing data:', {
        botStatus: !!botStatus,
        historicalTrades: !!historicalTrades,
        historicalTradesLength: historicalTrades?.length || 0
      });
      return;
    }

    const tradingPeriod = calculateTradingPeriod(historicalTrades);
    const shouldUseTradingPeriod = ['stopped', 'paused', 'market_closed', 'not_in_session'].includes(botStatus) && tradingPeriod;

    console.log('🔄 Agent status changed - checking trading period data:', {
      botStatus,
      tradingPeriod: !!tradingPeriod,
      shouldUseTradingPeriod,
      currentCandleDataLength: candleData.length,
      historicalTradesLength: historicalTrades.length
    });

    if (shouldUseTradingPeriod) {
      console.log('🚀 Should load trading period data - checking conditions:', {
        shouldUseTradingPeriod,
        candleDataLength: candleData.length,
        willLoad: true
      });

      // Clear any existing cache for stopped bots to force trading period data
      const instrument = strategy?.human_readable_rules?.strategy_info?.instrument || "EUR/USD";
      const timeframe = strategy?.human_readable_rules?.strategy_info?.timeframe || "1h";
      const forexPair = instrument.replace("/", "");
      const cacheKey = `${forexPair}_${timeframe}`;

      console.log('🧹 Clearing cache for stopped agent to load trading period data:', cacheKey);
      try {
        sessionStorage.removeItem(`candleData_${cacheKey}`);
        sessionStorage.removeItem(`candleDataTimestamp_${cacheKey}`);
        sessionStorage.removeItem(`historicalDataLoaded_${id}`);
      } catch (e) {
        console.warn('Failed to clear cache:', e);
      }

      // Always load trading period data for stopped bots, even if candle data exists
      console.log('🚀 Loading trading period data for stopped/paused agent...');

      // Load trading period data
      loadInitialHistoricalData(forexPair, timeframe, tradingPeriod)
        .then(() => {
          console.log('✅ Trading period data loaded successfully');
        })
        .catch((error) => {
          console.error('❌ Error loading trading period data:', error);
        });
    }
  }, [botStatus, historicalTrades, strategy]);

  // Calculate chart container height based on indicators
  const calculateChartHeight = useMemo(() => {
    if (!strategy?.strategy_json?.indicators) return 400; // Default height

    let height = 400; // Base height for main price chart

    // Add height for each indicator type that creates a separate chart
    const indicatorTypes = strategy.strategy_json.indicators.map(ind => ind.type || ind.indicator_class);

    // RSI indicators (200px each)
    const rsiCount = indicatorTypes.filter(type => type === 'RSI').length;
    if (rsiCount > 0) {
      height += 200; // Single RSI chart regardless of count (combined mode)
    }

    // MACD indicators (200px each)
    const macdCount = indicatorTypes.filter(type => type === 'MACD').length;
    if (macdCount > 0) {
      height += 200;
    }

    // ATR indicators (200px each)
    const atrCount = indicatorTypes.filter(type => type === 'ATR').length;
    if (atrCount > 0) {
      height += 200;
    }

    // Add some padding
    height += 50;

    return height;
  }, [strategy?.strategy_json?.indicators]);

  // Optimized cache status check with reduced logging
  const checkCacheStatus = (cacheKey) => {
    const maxAge = 5 * 60 * 1000; // 5 minutes

    // Check session storage
    let sessionCacheValid = false;
    let sessionCacheAge = null;
    try {
      const sessionData = sessionStorage.getItem(`candles_${cacheKey}`);
      if (sessionData) {
        const cachedData = JSON.parse(sessionData);
        sessionCacheAge = Date.now() - cachedData.timestamp;
        sessionCacheValid = sessionCacheAge < maxAge && cachedData.candles && cachedData.candles.length > 0;
      }
    } catch (e) {
      // Silently handle errors in production
      if (process.env.NODE_ENV === 'development') {
        console.warn('Session cache check error:', e);
      }
    }

    // Check component memory cache
    let memoryCacheValid = false;
    let memoryCacheAge = null;
    if (candleDataCache.has(cacheKey)) {
      const cachedEntry = candleDataCache.get(cacheKey);
      memoryCacheAge = Date.now() - cachedEntry.timestamp;
      memoryCacheValid = memoryCacheAge < maxAge && cachedEntry.candles && cachedEntry.candles.length > 0;
    }

    // Check global memory cache
    let globalCacheValid = false;
    let globalCacheAge = null;
    if (globalCandleCache.has(cacheKey)) {
      const cachedEntry = globalCandleCache.get(cacheKey);
      globalCacheAge = Date.now() - cachedEntry.timestamp;
      globalCacheValid = globalCacheAge < maxAge && cachedEntry.candles && cachedEntry.candles.length > 0;
    }

    return {
      sessionCache: { valid: sessionCacheValid, age: sessionCacheAge },
      memoryCache: { valid: memoryCacheValid, age: memoryCacheAge },
      globalCache: { valid: globalCacheValid, age: globalCacheAge }
    };
  };

  // Function to load historical candles ONCE when page loads
  const loadInitialHistoricalData = async (forexPair, timeframe, tradingPeriodOrDateRange = null) => {
    perfLog(`🚀 Loading initial historical data for ${forexPair} ${timeframe} (one-time load)`);

    // If we have a trading period (for stopped bots), fetch candles for that specific range
    if (tradingPeriodOrDateRange && tradingPeriodOrDateRange.startTime && tradingPeriodOrDateRange.endTime) {
      return await fetchCandlesForTradingPeriod(forexPair, timeframe, tradingPeriodOrDateRange);
    }

    // If we have a date range (for live bots), fetch candles for that date range
    if (tradingPeriodOrDateRange && tradingPeriodOrDateRange.startDate && tradingPeriodOrDateRange.endDate) {
      return await fetchCandlesForDateRange(forexPair, timeframe, tradingPeriodOrDateRange);
    }

    // Otherwise, fetch the default 1000 candles
    return await fetchHistoricalCandles(forexPair, timeframe);
  };

  // Function to fetch candles for a specific trading period (stopped bots)
  const fetchCandlesForTradingPeriod = async (forexPair, timeframe, tradingPeriod) => {
    const cacheKey = `${forexPair}_${timeframe}_period`;
    perfLog(`📊 Frontend: Fetching candles for trading period: ${cacheKey}`);

    try {
      setCandleDataLoading(true);

      console.log(`📡 Frontend: Fetching trading period candles from Polygon API`);
      console.log(`📅 Trading period: ${tradingPeriod.startTime.toISOString()} to ${tradingPeriod.endTime.toISOString()}`);

      const result = await polygonApiService.fetchCandlesForDateRange(
        forexPair,
        timeframe,
        tradingPeriod.startTime,
        tradingPeriod.endTime
      );

      if (result.status === 'success' && result.candles && result.candles.length > 0) {
        console.log(`✅ Frontend: Fetched ${result.candles.length} candles for trading period`);
        console.log(`📊 Frontend: Period range: ${new Date(result.candles[0].time * 1000).toISOString()} to ${new Date(result.candles[result.candles.length - 1].time * 1000).toISOString()}`);
        console.log(`🔍 Frontend: Trading period candles sample:`, result.candles.slice(0, 5).map(c => ({
          time: new Date(c.time * 1000).toISOString(),
          close: c.close
        })));

        setCandleData(result.candles);
        return result.candles;
      } else {
        console.warn(`⚠️ Frontend: Failed to fetch trading period candles: ${result.message || 'No data'}`);
        setCandleData([]);
        return [];
      }
    } catch (error) {
      console.error(`❌ Frontend: Error fetching trading period candles:`, error);
      setCandleData([]);
      return [];
    } finally {
      setCandleDataLoading(false);
    }
  };

  // Function to fetch candles for a specific date range (live bots)
  const fetchCandlesForDateRange = async (forexPair, timeframe, dateRange) => {
    const cacheKey = `${forexPair}_${timeframe}_live`;
    perfLog(`📊 Frontend: Fetching candles for live agent date range: ${cacheKey}`);

    try {
      setCandleDataLoading(true);

      console.log(`📡 Frontend: Fetching live agent candles from Polygon API`);
      console.log(`📅 Date range: ${dateRange.startDate} to ${dateRange.endDate}`);

      // Convert date strings to Date objects
      const startDate = new Date(dateRange.startDate + 'T00:00:00Z');
      const endDate = new Date(dateRange.endDate + 'T23:59:59Z');

      const result = await polygonApiService.fetchCandlesForDateRange(
        forexPair,
        timeframe,
        startDate,
        endDate
      );

      if (result.status === 'success' && result.candles && result.candles.length > 0) {
        console.log(`✅ Frontend: Fetched ${result.candles.length} candles for live agent date range`);
        console.log(`📊 Frontend: Range: ${new Date(result.candles[0].time * 1000).toISOString()} to ${new Date(result.candles[result.candles.length - 1].time * 1000).toISOString()}`);
        console.log(`🔍 Frontend: Live agent candles sample:`, result.candles.slice(0, 5).map(c => ({
          time: new Date(c.time * 1000).toISOString(),
          close: c.close
        })));

        setCandleData(result.candles);
        return result.candles;
      } else {
        console.warn(`⚠️ Frontend: Failed to fetch live agent candles: ${result.message || 'No data'}`);
        setCandleData([]);
        return [];
      }
    } catch (error) {
      console.error(`❌ Frontend: Error fetching live agent candles:`, error);
      setCandleData([]);
      return [];
    } finally {
      setCandleDataLoading(false);
    }
  };

  // Simplified function to fetch candles using Polygon API (same as backend approach)
  const fetchHistoricalCandles = async (forexPair, timeframe) => {
    const cacheKey = `${forexPair}_${timeframe}`;
    perfLog(`📊 Frontend: Fetching candles using Polygon API for ${cacheKey}`);

    try {
      setCandleDataLoading(true);

      // Use the simplified Polygon API service (same as backend approach)
      console.log(`📡 Frontend: Fetching from Polygon API for ${forexPair} ${timeframe}`);
      const result = await polygonApiService.getCandles(forexPair, timeframe, 1000);

      if (result.status === 'success' && result.candles && result.candles.length > 0) {
        console.log(`✅ Frontend: Fetched ${result.candles.length} candles from Polygon API`);
        console.log(`📊 Frontend: Latest candle: ${new Date(result.candles[result.candles.length - 1].time * 1000).toISOString()}`);

        setCandleData(result.candles);
        return result.candles;
      } else {
        console.warn(`⚠️ Frontend: Failed to fetch candles: ${result.message || 'No data'}`);
        setCandleData([]);
        return [];
      }

    } catch (error) {
      console.error("❌ Frontend: Error fetching candles from Polygon API:", error);
      toast.error(`Failed to load chart data: ${error.message}`);
      setCandleData([]);
      return [];
    } finally {
      setCandleDataLoading(false);
    }
  };

  const fetchBotData = async () => {
    try {
      // Early return if user is not authenticated
      if (!firebaseUser?.uid || !id) {
        perfLog('❌ Cannot fetch agent data: missing firebaseUser or id');
        return;
      }

      setLoading(true);

      const botDoc = await getDoc(
        doc(db, "users", firebaseUser.uid, "submittedStrategies", id)
      );

      if (!botDoc.exists()) {
        console.error("Agent not found");
        router.push("/trade-bots");
        return;
      }

      const strategyData = botDoc.data();
      console.log("Strategy data from Firestore:", strategyData);
      console.log("Human readable rules:", strategyData.human_readable_rules);
      console.log("Strategy info:", strategyData.human_readable_rules?.strategy_info);
      console.log("Strategy timeframe:", strategyData.human_readable_rules?.strategy_info?.timeframe);

      // Parse strategy_json if it's a string
      if (strategyData.strategy_json && typeof strategyData.strategy_json === 'string') {
        try {
          strategyData.strategy_json = JSON.parse(strategyData.strategy_json);
          console.log("Parsed strategy_json:", strategyData.strategy_json);

          // Check if risk management data exists in the strategy JSON
          if (strategyData.strategy_json.riskManagement) {
            console.log("Found risk management in strategy_json:", strategyData.strategy_json.riskManagement);
          } else if (strategyData.strategy_json.risk_management) {
            console.log("Found risk_management in strategy_json:", strategyData.strategy_json.risk_management);
            // Convert risk_management to riskManagement for consistency
            strategyData.strategy_json.riskManagement = strategyData.strategy_json.risk_management;
            delete strategyData.strategy_json.risk_management;
          }
        } catch (e) {
          console.error("Error parsing strategy_json:", e);
        }
      }

      setStrategy(strategyData);

      // Get agent status
      const status = strategyData.status || "unknown";

      // Check if status has changed
      if (botStatus !== status && botStatus !== null) {
        setPreviousStatus(botStatus);
        setShowStatusNotification(true);

        // Add status change to notifications
        const statusNotification = {
          id: Date.now(),
          type: 'status',
          message: `Agent status changed from ${botStatus.toUpperCase()} to ${status.toUpperCase()}`,
          timestamp: new Date().toISOString(),
          details: { previousStatus: botStatus, newStatus: status }
        };

        const updatedNotifications = [statusNotification, ...notificationsRef.current].slice(0, 10);
        setNotifications(updatedNotifications);
        notificationsRef.current = updatedNotifications;
      }

      // Check if we're in a trading session
      const inTradingSession = isInTradingSession();

      // If we have trading sessions defined and we're not in a session, override the status
      const tradingSessions = strategyData.strategy_json?.tradingSession || [];
      if (tradingSessions.length > 0 && !inTradingSession && status === "running") {
        // Only override if the agent is running
        setBotStatus("not_in_session");
        setIsRunning(false);
      } else {
        // Normal status handling
        setBotStatus(status);
        setIsRunning(status === "running");
      }

      setLastHeartbeat(strategyData.last_heartbeat);
      setTradeLogs(strategyData.user_logs || []);
      setMarketStatus(strategyData.marketStatus);

      const accountBalanceData = strategyData.accountBalance;
      if (
        accountBalanceData &&
        typeof accountBalanceData.balance === "number"
      ) {
        setAccountBalance(accountBalanceData.balance);
      } else {
        setAccountBalance(0);
      }

      // Get risk management data
      const riskManagementData = strategyData.riskManagement;
      console.log("Raw strategy data:", strategyData);
      console.log("Risk management data available:", !!riskManagementData);
      if (riskManagementData) {
        console.log("Risk management data:", riskManagementData);
        console.log("Risk management parameters:", riskManagementData.parameters);
        console.log("Risk management metrics:", riskManagementData.metrics);

        // Ensure metrics are properly initialized
        if (!riskManagementData.metrics) {
          riskManagementData.metrics = {
            dailyLoss: 0,
            totalProfit: 0,
            totalLoss: 0,
            runtimeProgress: 0,
            elapsedDays: 0,
            daysRemaining: riskManagementData.parameters?.runtime || 7,
            accountBalance: accountBalance,
            lastUpdated: new Date().toISOString()
          };
        }

        // Note: Runtime calculation will be done after trade history is fetched
        // to ensure we have access to fresh trade data

        // Note: Risk management metrics calculation will be done after trade history is fetched

        setRiskManagement(riskManagementData);
      } else {
        // Create a default risk management object for testing
        // Remove this in production
        const defaultRiskManagement = {
          parameters: {
            riskPerTrade: "1%",
            maxDailyLoss: "5%",
            stopLoss: "2%",
            takeProfit: "4%",
            maxPositionSize: "10%",
            totalProfitTarget: "20%",
            totalLossLimit: "10%",
            runtime: 7
          },
          metrics: {
            dailyLoss: 0,
            totalProfit: 0,
            totalLoss: 0,
            runtimeProgress: 0,
            elapsedDays: 0,
            daysRemaining: 7,
            accountBalance: accountBalance,
            totalProfitTargetAbs: accountBalance * 0.2,
            totalLossLimitAbs: accountBalance * 0.1,
            dailyLossLimitAbs: accountBalance * 0.05,
            lastUpdated: new Date().toISOString()
          }
        };
        console.log("Using default risk management data for testing:", defaultRiskManagement);
        setRiskManagement(defaultRiskManagement);
      }

      const summary = strategyData.summary || {
        totalTrades: 0,
        totalRealizedPL: 0,
        totalUnrealizedPL: 0,
        winRate: 0,
        maxDrawdown: 0,
        sharpeRatio: 0,
        profitFactor: 0,
        averageWin: 0,
        averageLoss: 0,
        largestWin: 0,
        largestLoss: 0,
      };
      setStrategySummary(summary);

      // Always fetch trade history regardless of agent status
      const tradeHistoryRef = collection(
        db,
        "users",
        firebaseUser.uid,
        "submittedStrategies",
        id,
        "tradeHistory"
      );
      const tradeHistorySnapshot = await getDocs(tradeHistoryRef);

      // Get the current strategy's instrument for filtering trades
      const currentInstrument = strategyData.human_readable_rules?.strategy_info?.instrument || "EUR/USD";
      console.log(`🔍 Filtering trades for current instrument: ${currentInstrument}`);

      const trades = tradeHistorySnapshot.docs.map((doc) => {
        const data = doc.data();
        console.log("Trade data from Firestore:", doc.id, data);
        console.log("closeTime type:", typeof data.closeTime, data.closeTime);
        return {
          id: doc.id,
          ...data,
          openTime:
            data.openTime?.toDate?.() ||
            new Date(
              data.openTime?.seconds * 1000 +
                (data.openTime?.nanoseconds || 0) / 1000000
            ),
          closeTime:
            data.closeTime?.toDate?.() ||
            (data.closeTime
              ? typeof data.closeTime === 'string'
                ? new Date(data.closeTime)
                : new Date(
                    data.closeTime.seconds * 1000 +
                      (data.closeTime.nanoseconds || 0) / 1000000
                  )
              : null),
          price: parseFloat(data.price),
          units: parseFloat(data.units),
          initialMarginRequired: parseFloat(data.initialMarginRequired),
          halfSpreadCost: parseFloat(data.halfSpreadCost || 0),
          commission: parseFloat(data.commission || 0),
          realizedPL: parseFloat(data.realizedPL || 0),
          unrealizedPL: parseFloat(data.unrealizedPL || 0),
          takeProfitPrice: data.takeProfitPrice ? parseFloat(data.takeProfitPrice) : null,
          stopLossPrice: data.stopLossPrice ? parseFloat(data.stopLossPrice) : null,
        };
      }).filter((trade) => {
        // Filter trades to only include those for the current instrument/forex pair
        const tradeInstrument = trade.instrument;
        const isMatch = tradeInstrument === currentInstrument;

        if (!isMatch) {
          console.log(`🚫 Filtering out trade for ${tradeInstrument} (current bot is for ${currentInstrument})`);
        } else {
          console.log(`✅ Including trade for ${tradeInstrument}`);
        }

        return isMatch;
      });

      // Set both open trades and historical trades
      const openTradesFiltered = trades.filter(trade =>
        trade.status === 'OPEN' || trade.status === 'open' ||
        (trade.status !== 'CLOSED' && trade.status !== 'closed' && !trade.closeTime)
      );

      // Add take profit and stop loss prices from risk management if available
      if (riskManagement && riskManagement.parameters) {
        const { stopLoss, takeProfit } = riskManagement.parameters;

        // Process each open trade to add TP and SL if not already present
        const processedOpenTrades = openTradesFiltered.map(trade => {
          if (!trade.takeProfitPrice && takeProfit && trade.price) {
            const tpPercent = parseFloat(takeProfit) / 100;
            // Calculate TP based on direction (long/short)
            if (trade.type === 'long' || trade.units > 0) {
              trade.takeProfitPrice = trade.price * (1 + tpPercent);
            } else {
              trade.takeProfitPrice = trade.price * (1 - tpPercent);
            }
          }

          if (!trade.stopLossPrice && stopLoss && trade.price) {
            const slPercent = parseFloat(stopLoss) / 100;
            // Calculate SL based on direction (long/short)
            if (trade.type === 'long' || trade.units > 0) {
              trade.stopLossPrice = trade.price * (1 - slPercent);
            } else {
              trade.stopLossPrice = trade.price * (1 + slPercent);
            }
          }

          return trade;
        });

        console.log("Open trades with TP/SL added:", processedOpenTrades);
        setOpenTrades(processedOpenTrades);
      } else {
        console.log("Open trades filtered (no risk management data):", openTradesFiltered);
        setOpenTrades(openTradesFiltered);
      }

      setHistoricalTrades(trades);

      console.log(`📊 Fetched ${trades.length} trades for ${currentInstrument}:`, trades);
      console.log("Trades with realized losses:", trades.filter(trade => trade.status === 'CLOSED' && parseFloat(trade.realizedPL) < 0));

      // Log summary of filtered trades
      const totalTradesInFirestore = tradeHistorySnapshot.docs.length;
      const filteredTradesCount = trades.length;
      console.log(`🔍 Trade filtering summary: ${filteredTradesCount}/${totalTradesInFirestore} trades match instrument ${currentInstrument}`);

      // Update risk management metrics with fresh trade data
      if (riskManagement && trades && trades.length > 0) {
        console.log('📊 Updating risk management metrics with fresh trade data');

        // Calculate runtime metrics using actual agent start time
        const configuredRuntime = parseInt(riskManagement.parameters?.runtime || 7);
        if (configuredRuntime > 0) {
          let botStartTime = null;

          // First, try to use the actual agent start time from strategy document
          if (strategyData.started_at) {
            if (strategyData.started_at.seconds) {
              // Firestore timestamp
              botStartTime = new Date(strategyData.started_at.seconds * 1000);
            } else {
              // ISO string or Date object
              botStartTime = new Date(strategyData.started_at);
            }
          }

          // Fallback: use first trade time if no started_at timestamp
          if (!botStartTime && trades.length > 0) {
            const tradeTimes = trades
              .map(trade => {
                if (trade.openTime) {
                  return trade.openTime instanceof Date ? trade.openTime : new Date(trade.openTime);
                }
                return null;
              })
              .filter(time => time && !isNaN(time.getTime()))
              .sort((a, b) => a.getTime() - b.getTime());

            if (tradeTimes.length > 0) {
              botStartTime = tradeTimes[0];
              console.log('⚠️ Using first trade time as fallback for agent start time');
            }
          }

          if (botStartTime && !isNaN(botStartTime.getTime())) {
            const currentTime = new Date();
            const elapsedMs = currentTime.getTime() - botStartTime.getTime();
            const elapsedDays = elapsedMs / (1000 * 60 * 60 * 24); // Convert to days
            const runtimeProgress = Math.min((elapsedDays / configuredRuntime) * 100, 100);
            const daysRemaining = Math.max(configuredRuntime - elapsedDays, 0);

            // Update runtime metrics
            riskManagement.metrics.elapsedDays = elapsedDays;
            riskManagement.metrics.runtimeProgress = runtimeProgress;
            riskManagement.metrics.daysRemaining = daysRemaining;

            console.log('📊 Calculated runtime metrics:', {
              botStartTime: botStartTime.toISOString(),
              elapsedDays: elapsedDays.toFixed(2),
              runtimeProgress: runtimeProgress.toFixed(1) + '%',
              daysRemaining: daysRemaining.toFixed(1),
              configuredRuntime,
              source: strategyData.started_at ? 'actual_start_time' : 'first_trade_fallback'
            });
          }
        }

        // Calculate profit/loss metrics from fresh trade data
        let calculatedTotalProfit = 0;
        let calculatedTotalLoss = 0;
        let calculatedDailyLoss = 0;

        // Get today's date for daily loss calculation
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

        // Calculate total profit, loss, and daily loss from fresh trade data
        trades.forEach(trade => {
          if (trade.status === 'CLOSED' && trade.realizedPL) {
            const realizedPL = parseFloat(trade.realizedPL);

            // Calculate total profit/loss
            if (realizedPL > 0) {
              calculatedTotalProfit += realizedPL;
            } else if (realizedPL < 0) {
              calculatedTotalLoss += Math.abs(realizedPL);
            }

            // Calculate daily loss (only for today's trades)
            if (realizedPL < 0 && trade.closeTime) {
              let tradeCloseTime;
              if (trade.closeTime instanceof Date) {
                tradeCloseTime = trade.closeTime;
              } else if (trade.closeTime.seconds) {
                tradeCloseTime = new Date(trade.closeTime.seconds * 1000);
              } else {
                tradeCloseTime = new Date(trade.closeTime);
              }

              if (tradeCloseTime >= todayStart && tradeCloseTime < todayEnd) {
                calculatedDailyLoss += Math.abs(realizedPL);
              }
            }
          }
        });

        console.log("Calculated from fresh trade data:", {
          totalProfit: calculatedTotalProfit,
          totalLoss: calculatedTotalLoss,
          dailyLoss: calculatedDailyLoss
        });

        // Update metrics with calculated values from fresh trade data
        riskManagement.metrics.totalProfit = calculatedTotalProfit;
        riskManagement.metrics.totalLoss = calculatedTotalLoss;
        riskManagement.metrics.dailyLoss = calculatedDailyLoss;

        // Calculate absolute values if they're missing
        if (!riskManagement.metrics.totalProfitTargetAbs) {
          const params = riskManagement.parameters || {};
          const balance = riskManagement.metrics.accountBalance || accountBalance;

          // Parse percentage values
          const parsePercentage = (value) => {
            if (!value) return 0;
            if (typeof value === 'string') {
              return parseFloat(value.replace('%', ''));
            }
            return value;
          };

          // Calculate absolute values
          const totalProfitTarget = parsePercentage(params.totalProfitTarget);
          const totalLossLimit = parsePercentage(params.totalLossLimit);
          const maxDailyLoss = parsePercentage(params.maxDailyLoss);

          riskManagement.metrics.totalProfitTargetAbs = balance * (totalProfitTarget / 100);
          riskManagement.metrics.totalLossLimitAbs = balance * (totalLossLimit / 100);
          riskManagement.metrics.dailyLossLimitAbs = balance * (maxDailyLoss / 100);

          console.log("Calculated absolute values:", {
            totalProfitTargetAbs: riskManagement.metrics.totalProfitTargetAbs,
            totalLossLimitAbs: riskManagement.metrics.totalLossLimitAbs,
            dailyLossLimitAbs: riskManagement.metrics.dailyLossLimitAbs
          });
        }

        console.log("Final updated risk management metrics:", riskManagement.metrics);
      }

      // Calculate performance metrics from trade history
      const closedTrades = trades.filter(trade =>
        trade.status === 'CLOSED' ||
        (trade.closeTime && trade.realizedPL !== undefined)
      );

      if (closedTrades.length > 0) {
        // Calculate total realized P&L
        const totalRealizedPL = closedTrades.reduce((sum, trade) => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return sum + realizedPL;
        }, 0);

        // Calculate winning and losing trades
        const winningTradesData = closedTrades.filter(trade => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
          return realizedPL > 0;
        });

        const losingTradesData = closedTrades.filter(trade => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
          return realizedPL < 0;
        });

        const winningTrades = winningTradesData.length;
        const losingTrades = losingTradesData.length;

        // Calculate win rate (as percentage)
        const winRate = closedTrades.length > 0 ? (winningTrades / closedTrades.length) * 100 : 0;

        // Calculate profit factor
        const totalWins = winningTradesData.reduce((sum, trade) => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return sum + realizedPL;
        }, 0);

        const totalLosses = Math.abs(losingTradesData.reduce((sum, trade) => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return sum + realizedPL;
        }, 0));

        const profitFactor = totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? 999 : 0;

        console.log('📊 Calculated metrics:', {
          closedTrades: closedTrades.length,
          winningTrades,
          losingTrades,
          winRate: winRate.toFixed(2) + '%',
          totalWins: totalWins.toFixed(2),
          totalLosses: totalLosses.toFixed(2),
          profitFactor: profitFactor.toFixed(2)
        });

        // Update strategy summary with calculated metrics
        const updatedSummary = {
          ...summary,
          totalTrades: closedTrades.length,
          totalRealizedPL: totalRealizedPL,
          winRate: winRate,
          profitFactor: profitFactor,
          winningTrades: winningTrades,
          losingTrades: losingTrades
        };

        console.log("Updated strategy summary with calculated metrics:", updatedSummary);
        setStrategySummary(updatedSummary);
      }

      // Check for new trades since last fetch
      if (lastTradeTimestamp) {
        const newTrades = trades.filter(trade => {
          const tradeTime = trade.openTime instanceof Date ? trade.openTime : new Date(trade.openTime);
          return tradeTime > new Date(lastTradeTimestamp);
        });

        // Create notifications for new trades
        if (newTrades.length > 0) {
          const tradeNotifications = newTrades.map(trade => {
            const isOpen = trade.status === 'OPEN';
            const hasProfitLoss = trade.realizedPL !== undefined && trade.realizedPL !== null;
            const isProfitable = hasProfitLoss && parseFloat(trade.realizedPL) > 0;

            let notificationType = isOpen ? 'trade_executed' : 'trade_closed';
            if (!isOpen && hasProfitLoss) {
              notificationType = isProfitable ? 'profit' : 'loss';
            }

            return {
              id: Date.now() + Math.random(),
              type: notificationType,
              message: isOpen
                ? `New ${trade.type.toUpperCase()} trade opened for ${trade.instrument}`
                : `${trade.type.toUpperCase()} trade closed for ${trade.instrument} with ${isProfitable ? 'profit' : 'loss'}`,
              timestamp: new Date().toISOString(),
              details: {
                tradeID: trade.id,
                instrument: trade.instrument,
                type: trade.type.toUpperCase(),
                units: trade.units,
                price: trade.price,
                realizedPL: trade.realizedPL
              }
            };
          });

          const updatedNotifications = [...tradeNotifications, ...notificationsRef.current].slice(0, 10);
          setNotifications(updatedNotifications);
          notificationsRef.current = updatedNotifications;
        }
      }

      // Update last trade timestamp
      setLastTradeTimestamp(new Date().toISOString());

      const performance = calculatePerformance(trades);
      setBotPerformance(performance);

      // Extract forex pair and timeframe from strategy
      const instrument = strategyData.human_readable_rules?.strategy_info?.instrument || "EUR/USD";
      const timeframe = strategyData.human_readable_rules?.strategy_info?.timeframe || "1h";
      const forexPair = instrument.replace("/", "");
      const cacheKey = `${forexPair}_${timeframe}`;

      // Calculate trading period from historical trades
      const tradingPeriod = calculateTradingPeriod(trades);
      console.log('📊 Trading period calculated:', tradingPeriod);
      console.log('🔍 Sample trade timestamps:', trades.slice(0, 3).map(trade => ({
        openTime: trade.openTime,
        closeTime: trade.closeTime,
        openTimeParsed: trade.openTime ? new Date(trade.openTime).toISOString() : null,
        closeTimeParsed: trade.closeTime ? new Date(trade.closeTime).toISOString() : null
      })));

      // Determine if we should use trading period for chart data
      const shouldUseTradingPeriod = ['stopped', 'paused', 'market_closed', 'not_in_session'].includes(botStatus) && tradingPeriod;

      // Also check if we have historical trades but agent status is not yet available
      // Use the local trades variable since historicalTrades state hasn't been updated yet
      const hasLocalTrades = trades && trades.length > 0;
      const shouldWaitForBotStatus = hasLocalTrades && !botStatus;

      console.log('🔍 Trading period decision:', {
        botStatus,
        tradingPeriod: !!tradingPeriod,
        shouldUseTradingPeriod,
        hasLocalTrades,
        localTradesLength: trades?.length || 0,
        shouldWaitForBotStatus,
        historicalDataLoaded,
        candleDataLength: candleData.length
      });

      // Skip regular data loading if we should use trading period OR if we should wait for agent status
      if (shouldUseTradingPeriod || shouldWaitForBotStatus) {
        console.log('⏭️ Skipping regular data fetch - will use trading period data or waiting for agent status');
        return;
      }

      // For live bots, determine date range based on trades
      let dateRange = null;
      if (botStatus === 'running' && trades && trades.length > 0) {
        // Find first trade time
        const firstTradeTime = Math.min(...trades.map(trade => {
          const openTime = trade.openTime ? new Date(trade.openTime).getTime() : null;
          const closeTime = trade.closeTime ? new Date(trade.closeTime).getTime() : null;
          return Math.min(openTime || Infinity, closeTime || Infinity);
        }).filter(time => time !== Infinity));

        if (firstTradeTime && firstTradeTime !== Infinity) {
          const startDate = new Date(firstTradeTime);
          const endDate = new Date(); // Current time

          dateRange = {
            startDate: startDate.toISOString().split('T')[0], // YYYY-MM-DD format
            endDate: endDate.toISOString().split('T')[0]
          };

          console.log('📊 Live agent date range from trades:', {
            firstTradeTime: startDate.toISOString(),
            currentTime: endDate.toISOString(),
            dateRange,
            tradesCount: trades.length
          });
        }
      }

      perfLog(`📊 Checking data for ${forexPair} ${timeframe}`);
      perfLog(`📊 Historical data loaded flag: ${historicalDataLoaded}`);
      perfLog(`📊 Current candle data length: ${candleData.length}`);

      // Debug: Log current state for troubleshooting
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Chart Data Debug:', {
          candleDataLength: candleData.length,
          indicatorsKeys: Object.keys(indicators || {}),
          botStatus,
          historicalDataLoaded,
          candleDataLoading,
          forexPair,
          timeframe
        });
      }

      const cachedData = candleDataCache.get(cacheKey);
      console.log(`📊 Cached data available: ${!!cachedData}, Cache data length: ${cachedData?.candles?.length || 0}`);
      console.log(`📊 Cache keys available:`, Array.from(candleDataCache.keys()));
      console.log(`📊 Looking for cache key: ${cacheKey}`);

      // Determine if we need to fetch data
      // Check if we have data in current state OR in cache (using correct property name)
      const hasCurrentData = candleData.length > 0;
      const hasCachedData = !!(cachedData && cachedData.candles && cachedData.candles.length > 0);
      const hasAnyData = hasCurrentData || hasCachedData;

      // Only fetch if we have no data AND haven't loaded before (respect the flag when we have data)
      const needsDataFetch = !hasAnyData && !historicalDataLoaded;

      console.log(`📊 Data availability check:`, {
        hasCurrentData,
        hasCachedData,
        hasAnyData,
        needsDataFetch,
        historicalDataLoaded
      });

      if (needsDataFetch) {
        console.log("🔄 Fetching historical candles - no data available...");
        setCandleDataLoading(true);

        console.log(`📊 Loading initial chart data for ${forexPair} ${timeframe}`);

        try {
          // Fetch historical candles - use trading period if available and agent is not running
          const tradingPeriodToUse = shouldUseTradingPeriod ? tradingPeriod : null;
          await loadInitialHistoricalData(forexPair, timeframe, tradingPeriodToUse);
          setHistoricalDataLoaded(true);

          // Persist the flag in sessionStorage so it survives page refreshes
          try {
            sessionStorage.setItem(`historicalDataLoaded_${id}`, 'true');
          } catch (e) {
            console.warn('Failed to save historical data flag to sessionStorage:', e);
          }

          console.log("✅ Finished loading initial historical data. Future updates will come from polling.");
        } catch (error) {
          console.error("❌ Error loading initial historical data:", error);
        } finally {
          setCandleDataLoading(false);
        }
      } else {
        console.log("⚡ Skipping historical data fetch - data already available");

        // If we have cached data but no current data, restore from cache
        if (cachedData && cachedData.candles && candleData.length === 0) {
          console.log(`📊 Restoring cached candle data for ${cacheKey} (${cachedData.candles.length} candles)`);
          console.log(`📊 Sample cached data:`, cachedData.candles.slice(0, 3));

          // Set loading state while restoring data
          setCandleDataLoading(true);

          // Use setTimeout to ensure the loading state is set before data restoration
          setTimeout(() => {
            setCandleData(cachedData.candles);
            console.log(`📊 After setCandleData, current length should be: ${cachedData.candles.length}`);

            // Clear loading state after data is set
            setTimeout(() => {
              setCandleDataLoading(false);
            }, 100);
          }, 50);
        } else {
          console.log(`📊 Not restoring cache because:`, {
            hasCachedData: !!cachedData,
            hasCachedDataArray: !!(cachedData?.candles),
            cachedDataLength: cachedData?.candles?.length || 0,
            currentCandleDataLength: candleData.length
          });

          // Only clear loading state if we actually have data to show
          if (candleData.length > 0) {
            setCandleDataLoading(false);
          } else {
            // If no data available, try to fetch it
            console.log("🔄 No data available, attempting to fetch...");
            setCandleDataLoading(true);

            // Use date range for live bots with trades, otherwise use default 1000 candles
            const loadPromise = dateRange
              ? (() => {
                  console.log("🚀 Loading data for live agent from first trade to current time");
                  return loadInitialHistoricalData(forexPair, timeframe, dateRange);
                })()
              : (() => {
                  console.log("🚀 Loading default 1000 candles (no trades or not a live agent)");
                  return loadInitialHistoricalData(forexPair, timeframe);
                })();

            loadPromise
              .then(() => {
                setHistoricalDataLoaded(true);
                try {
                  sessionStorage.setItem(`historicalDataLoaded_${id}`, 'true');
                } catch (e) {
                  console.warn('Failed to save historical data flag to sessionStorage:', e);
                }
              })
              .catch((error) => {
                console.error("❌ Error loading historical data:", error);
              })
              .finally(() => {
                setCandleDataLoading(false);
              });
          }
        }
      }

      try {
        // NOTE: Indicators are now calculated in real-time from candle data in RealTimeOptimizedTradingChart
        // This eliminates the need for complex Firestore indicator processing
        console.log("📊 Indicators will be calculated in real-time from candle data");

        // Set empty indicators object - RealTimeOptimizedTradingChart will calculate them
        // Use a stable reference to prevent unnecessary re-renders
        if (Object.keys(indicators).length > 0) {
          setIndicators(EMPTY_INDICATORS);
        }
      } catch (indicatorError) {
        console.error("❌ Error processing indicators:", indicatorError);
        // Only set empty object if indicators is not already empty
        if (Object.keys(indicators).length > 0) {
          setIndicators(EMPTY_INDICATORS);
        }
      }
    } catch (error) {
      console.error("Error fetching agent data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePauseBot = async () => {
    setConfirmDialog({ show: true, type: 'pause' });
  };

  const executePauseBot = async () => {
    if (!firebaseUser?.uid) return;
    setActionLoading(true);
    try {
      // Get a fresh token each time
      const idToken = await firebaseUser.getIdToken(true);

      const response = await fetch(
        `${CONTROL_STRATEGY_URL}/control-strategy/${firebaseUser.uid}/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            command: "pause",
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to pause agent");
      }
      setBotStatus("paused");
      setIsRunning(false);
    } catch (err) {
      console.error("Error pausing agent:", err);
      alert("Failed to pause agent: " + (err.message || "Unknown error"));
    } finally {
      setActionLoading(false);
    }
  };

  const handleResumeBot = async () => {
    if (!firebaseUser?.uid) return;
    setActionLoading(true);
    try {
      // Get a fresh token each time
      const idToken = await firebaseUser.getIdToken(true);

      const response = await fetch(
        `${CONTROL_STRATEGY_URL}/control-strategy/${firebaseUser.uid}/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            command: "resume",
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || "Failed to resume agent");
      }
      setBotStatus("running");
      setIsRunning(true);
    } catch (err) {
      console.error("Error resuming agent:", err);
      alert("Failed to resume agent: " + (err.message || "Unknown error"));
    } finally {
      setActionLoading(false);
    }
  };

  const handleStopBot = async () => {
    setConfirmDialog({ show: true, type: 'stop' });
  };

  const executeStopBot = async () => {
    if (!firebaseUser?.uid) return;
    setActionLoading(true);
    try {
      // Get a fresh token each time
      const idToken = await firebaseUser.getIdToken(true);

      const response = await fetch(
        `${CONTROL_STRATEGY_URL}/control-strategy/${firebaseUser.uid}/${id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${idToken}`,
          },
          body: JSON.stringify({
            command: "stop",
          }),
        }
      );

      // Even if the API returns an error, the agent might still be stopping
      // So we'll check the status after a delay
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.warn("Warning stopping agent:", errorData);
        toast.warning("Agent stop command sent, but there might be issues. Checking status...");

        // Wait a moment and then check the agent status
        setTimeout(() => {
          fetchBotData();
        }, 3000);
      } else {
        toast.success("Agent stopped successfully");
      }

      // Assume the agent is stopping/stopped even if there was an error
      // The fetchBotData call will update the status if needed
      setBotStatus("stopping");
      setIsRunning(false);
    } catch (err) {
      console.error("Error stopping agent:", err);
      toast.error("Failed to stop agent: " + (err.message || "Unknown error"));

      // Still try to fetch the latest status
      setTimeout(() => {
        fetchBotData();
      }, 3000);
    } finally {
      setActionLoading(false);
    }
  };

  const handleConfirmAction = async () => {
    const { type } = confirmDialog;
    setConfirmDialog({ show: false, type: null });

    if (type === 'pause') {
      await executePauseBot();
    } else if (type === 'stop') {
      await executeStopBot();
    }
  };

  const handleCancelAction = () => {
    setConfirmDialog({ show: false, type: null });
  };

  const formatTime = (date) => {
    if (!date) return "N/A";
    try {
      let dateToFormat;

      if (date.seconds && date.nanoseconds) {
        dateToFormat = new Date(
          date.seconds * 1000 + date.nanoseconds / 1000000
        );
      } else if (date.toDate) {
        dateToFormat = date.toDate();
      } else if (date instanceof Date) {
        dateToFormat = date;
      } else if (typeof date === "string") {
        dateToFormat = new Date(date.replace("Z", "+00:00"));
      }

      if (!dateToFormat || isNaN(dateToFormat.getTime())) {
        console.warn("Invalid date value:", date);
        return "Invalid Date";
      }

      return dateToFormat.toLocaleString(undefined, {
        timeZone: userTimezone,
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      });
    } catch (error) {
      console.error("Error formatting time:", error);
      return "Invalid Date";
    }
  };

  const LivePulse = ({ label }) => (
    <div className="flex items-center space-x-2">
      <motion.div
        className="w-2 h-2 bg-green-500 rounded-full"
        animate={{
          scale: [1, 1.5, 1],
          opacity: [1, 0.5, 1],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <span className="text-sm text-green-400 font-medium">{label}</span>
    </div>
  );

  const tableHeaders = [
    { key: "openTime", label: "Open Time", visible: true },
    { key: "tradeID", label: "Trade ID", visible: false },
    { key: "instrument", label: "Instrument", visible: true },
    { key: "type", label: "Type", visible: true },
    { key: "units", label: "Units", visible: true },
    { key: "price", label: "Entry Price", visible: true },
    { key: "takeProfitPrice", label: "Take Profit Price", visible: false },
    { key: "stopLossPrice", label: "Stop Loss Price", visible: false },
    { key: "initialMarginRequired", label: "Margin", visible: false },
    { key: "halfSpreadCost", label: "Spread Cost", visible: false },
    { key: "commission", label: "Commission", visible: false },
    { key: "realizedPL", label: "Realized P/L", visible: true },
    { key: "unrealizedPL", label: "Unrealized P/L", visible: true },
    { key: "status", label: "Status", visible: true },
    { key: "closeTime", label: "Close Time", visible: true },
  ];

  const [visibleColumns, setVisibleColumns] = useState(
    tableHeaders.reduce((acc, header) => {
      acc[header.key] = header.visible;
      return acc;
    }, {})
  );

  const toggleColumn = (key) => {
    setVisibleColumns((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  // Show loading state while authentication is being determined
  if (firebaseUser === null && !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#0c0f1c] to-[#0a0b10] text-white p-6">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h1 className="text-xl font-semibold text-gray-300">
              Loading...
            </h1>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#0c0f1c] to-[#0a0b10] text-white p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">
            Please log in to access the trade agent
          </h1>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#EFBD3A]"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!strategy) {
    return (
      <DashboardLayout>
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Agent not found</h1>
          <button
            onClick={() => router.push("/trade-bots")}
            className="px-4 py-2 bg-[#EFBD3A] text-[#0A0B0B] rounded-lg hover:bg-[#EFBD3A]/90"
          >
            Back to Bots
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* Status Notification */}
      <AnimatePresence>
        {showStatusNotification && (
          <StatusNotification
            status={botStatus}
            previousStatus={previousStatus}
            onClose={() => setShowStatusNotification(false)}
          />
        )}
      </AnimatePresence>

      {/* Trade Notifications */}
      <div className="fixed top-4 right-4 z-50 space-y-2 w-full max-w-md">
        <AnimatePresence>
          {notifications.slice(0, 3).map((notification, index) => (
            <TradeNotification
              key={notification.id}
              notification={notification}
              onClose={() => {
                const updatedNotifications = notifications.filter(n => n.id !== notification.id);
                setNotifications(updatedNotifications);
                notificationsRef.current = updatedNotifications;
              }}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* New Header Component */}
      <TradeBotHeader
        strategyName={strategy.human_readable_rules?.strategy_info?.name || "AI Trading Agent"}
        instrument={strategy.human_readable_rules?.strategy_info?.instrument || "EUR/USD"}
        timeframe={strategy.human_readable_rules?.strategy_info?.timeframe || "2m"}
        status={botStatus}
        autoRefresh={autoRefresh}
        refreshInterval={refreshInterval}
        onAutoRefreshToggle={() => setAutoRefresh(!autoRefresh)}
        onRefreshIntervalChange={setRefreshInterval}
        onRefresh={fetchBotDataOptimized}
        onPause={handlePauseBot}
        onResume={handleResumeBot}
        onStop={handleStopBot}
        actionLoading={actionLoading}
      />

      {/* Performance Metrics Row */}
      <PerformanceMetricsRow
        accountBalance={accountBalance}
        totalPnL={strategySummary.totalRealizedPL || 0}
        todaysTradeCount={strategySummary.totalTrades || 0}
        totalTrades={strategySummary.totalTrades || 0}
        winningTrades={historicalTrades.filter(trade => trade.realizedPL > 0).length}
        losingTrades={historicalTrades.filter(trade => trade.realizedPL < 0).length}
        winRate={strategySummary.winRate || 0}
        profitFactor={strategySummary.profitFactor || 0}
      />

      {/* Risk Management Dashboard */}
      <RiskManagementDashboard
        accountBalance={accountBalance}
        totalProfit={riskManagement?.metrics?.totalProfit || 0}
        totalLoss={riskManagement?.metrics?.totalLoss || 0}
        dailyLoss={riskManagement?.metrics?.dailyLoss || 0}
        riskManagement={riskManagement || {}}
        tradingPeriod="Trading period"
      />

      {/* Tab Layout */}
      <TradeBotTabs
        currentPrice={candleData.length > 0 ? candleData[candleData.length - 1]?.close : null}
        indicators={indicators}
        strategy={strategy}
        historicalTrades={historicalTrades}
        candleData={candleData}
        marketStatus={marketStatus}
        chartZoomState={chartZoomState}
        setChartZoomState={setChartZoomState}
        accountBalance={accountBalance}
        openTrades={openTrades}
        strategySummary={strategySummary}
        riskManagement={riskManagement}
        tradeLogs={tradeLogs}
        botStatus={botStatus}
        userId={firebaseUser?.uid}
        strategyId={id}
      />

      <div className="space-y-6 w-full overflow-x-hidden px-6" style={{ display: 'none' }}>
        {/* These sections will be moved into tabs - temporarily hidden */}

        {/* Strategy Visualization - Will move to Strategy tab */}
        <div className="mb-6 overflow-x-auto w-full" style={{ maxWidth: '100%', overflowX: 'auto' }}>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-2xl font-bold text-[#FEFEFF]">Strategy Review</h3>
          </div>
          {strategy?.strategy_json && (
            <>
              {console.log("Strategy JSON before passing to StrategyVisualization:", strategy.strategy_json)}
              {console.log("Risk management data before passing to StrategyVisualization:", riskManagement)}
              <StrategyVisualization
                strategy={{
                  ...strategy.strategy_json,
                  riskManagement: {
                    ...strategy.strategy_json.riskManagement,
                    ...riskManagement?.parameters
                  }
                }}
              />
            </>
          )}
          {!strategy?.strategy_json && strategy?.human_readable_rules && (
            <StrategyRules rules={strategy.human_readable_rules} />
          )}
        </div>



        {/* Chart - Performance Optimized */}
        <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] p-4 relative chart-container mb-16 overflow-hidden">
          {/* Smooth loading overlay for data refreshes */}
          <SmoothLoadingOverlay
            isLoading={candleDataLoading && candleData.length > 0}
            message="Refreshing chart data..."
            minimal={true}
          />

          {/* Overlay for paused agent */}
          {botStatus === "paused" && (
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
              <div className="text-center">
                <div className="bg-yellow-600/20 p-4 rounded-lg inline-flex mb-4">
                  <svg
                    className="w-12 h-12 text-yellow-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">
                  Agent Paused
                </h3>
                <p className="text-gray-300">
                  Chart updates are paused while the agent is inactive
                </p>
              </div>
            </div>
          )}

          {botStatus === "not_in_session" && (
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
              <div className="text-center">
                <div className="bg-blue-600/20 p-4 rounded-lg inline-flex mb-4">
                  <svg
                    className="w-12 h-12 text-blue-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">
                  Outside Trading Session
                </h3>
                <p className="text-gray-300">
                  Chart updates are paused while outside the trading session
                </p>
              </div>
            </div>
          )}

          {/* Smooth content transition for chart loading */}
          <SmoothContentTransition
            isLoading={candleDataLoading && candleData.length === 0}
            loadingComponent={
              <div className="h-96">
                <ChartSkeleton className="h-full" />
              </div>
            }
            className="min-h-96"
          >
            {/* Hide chart for stopped, market closed, or not in session agent (but only if no candle data) */}
            {(botStatus === "stopped" || botStatus === "market_closed" || botStatus === "not_in_session") && candleData.length === 0 ? (
              <div className="p-12 text-center">
                <div
                  className={`${
                    botStatus === "market_closed"
                      ? "bg-purple-600/20"
                      : botStatus === "not_in_session"
                      ? "bg-blue-600/20"
                      : "bg-gray-700/20"
                  } p-4 rounded-lg inline-flex mb-4`}
                >
                  <svg
                    className={`w-12 h-12 ${
                      botStatus === "market_closed"
                        ? "text-purple-500"
                        : botStatus === "not_in_session"
                        ? "text-blue-500"
                        : "text-gray-500"
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    {botStatus === "market_closed" ? (
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    ) : botStatus === "not_in_session" ? (
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    ) : (
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 715.636 5.636m12.728 12.728L5.636 5.636"
                      />
                    )}
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">
                  {botStatus === "market_closed"
                    ? "Market Closed"
                    : botStatus === "not_in_session"
                    ? "Outside Trading Session"
                    : "Agent Stopped"}
                </h3>
                <p className="text-gray-300">
                  {botStatus === "market_closed"
                    ? "Trading is unavailable while the market is closed"
                    : botStatus === "not_in_session"
                    ? "Trading is paused until the next trading session begins"
                    : "Chart data is not available"}
                </p>
              </div>
            ) : (
              <div className="gpu-accelerated" style={{ minHeight: `${calculateChartHeight}px` }}>

                <PolygonTradingChart
                  candleData={candleData}
                  indicators={indicators}
                  trades={historicalTrades}
                  timeframe={
                    strategy?.human_readable_rules?.strategy_info?.timeframe || "1h"
                  }
                  preserveZoom={true}
                  onZoomChange={setChartZoomState}
                  initialZoomState={chartZoomState}
                  chartTimeframe={
                    strategy?.human_readable_rules?.strategy_info?.timeframe || "1h"
                  }
                  instrument={
                    strategy?.human_readable_rules?.strategy_info?.instrument ||
                    "EUR/USD"
                  }
                  marketStatus={marketStatus}
                  strategy={strategy}
                  strategyInfo={strategy?.human_readable_rules?.strategy_info}
                  enablePolling={true}
                  onDataUpdate={(newCandles) => {
                    console.log(`📊 Frontend: Chart received ${newCandles.length} updated candles`);
                    // Save zoom state before data update to preserve user's position
                    saveCurrentZoomState();
                  }}
                  onIndicatorsCalculated={(calculatedIndicators) => {
                    console.log('📊 Frontend: Indicators calculated by chart component', {
                      indicatorKeys: Object.keys(calculatedIndicators),
                      indicatorCount: Object.keys(calculatedIndicators).length
                    });
                  }}
                />
              </div>
            )}
          </SmoothContentTransition>
        </div>

        {/* Trade History */}
        <div className="mt-20 overflow-x-auto w-full" style={{ maxWidth: '100%', overflowX: 'auto' }} onClick={() => isColumnMenuOpen && setIsColumnMenuOpen(false)}>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-gray-300">Trade History <span className="text-sm font-normal text-gray-400 ml-2">({historicalTrades.length} trades)</span></h2>
            <div>
              <button
                ref={columnButtonRef}
                className="columns-button px-4 py-2 bg-[#1a1a1a] text-gray-300 rounded-lg hover:bg-[#2a2a2a] transition-colors flex items-center space-x-2"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsColumnMenuOpen(!isColumnMenuOpen);
                }}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16m-7 6h7"
                  />
                </svg>
                <span>Customize Columns</span>
                <svg
                  className={`w-4 h-4 transition-transform duration-200 ${isColumnMenuOpen ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>





            </div>
          </div>
          <div className="bg-[#0A0B0B] shadow sm:rounded-lg border border-[#1a1a1a] w-full">
            <div className="overflow-x-scroll w-full" style={{ maxWidth: '100%', overflowX: 'scroll', WebkitOverflowScrolling: 'touch' }}>
              <table className="w-full divide-y divide-gray-700 border-collapse" style={{ minWidth: '1200px' }}>
                <thead className="bg-[#1a1a1a]">
                  <tr>
                    {tableHeaders.map(
                      (header) =>
                        visibleColumns[header.key] && (
                          <th
                            key={header.key}
                            className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider whitespace-nowrap overflow-hidden text-ellipsis border-b border-gray-700 group"
                            style={{ maxWidth: '150px' }}
                            onClick={() => {
                              // Add sorting functionality here if needed
                            }}
                          >
                            {header.label}
                          </th>
                        )
                    )}
                  </tr>
                </thead>
                <tbody className="bg-[#0A0B0B] divide-y divide-gray-700">
                  {historicalTrades.length === 0 ? (
                    <tr>
                      <td colSpan={Object.values(visibleColumns).filter(Boolean).length} className="px-6 py-8 text-center text-gray-400">
                        No trades found. Trades will appear here when the agent executes them.
                      </td>
                    </tr>
                  ) : (
                    historicalTrades.map((trade) => {
                      return (
                        <tr
                          key={trade.id}
                          className="border-b border-gray-700 hover:bg-gray-800"
                        >
                          {tableHeaders.map(
                            (header) => {
                              if (!visibleColumns[header.key]) return null;

                              return (
                                <td
                                  key={header.key}
                                  className={`px-6 py-4 whitespace-nowrap text-sm overflow-hidden text-ellipsis border-b border-gray-800 ${header.key === 'realizedPL' || header.key === 'unrealizedPL' ?
                                    (trade[header.key] >= 0 ? 'text-green-400' : 'text-red-400') :
                                    'text-gray-300'}`}
                                  style={{ maxWidth: '150px' }}
                                >
                                  {header.key === "type" ? (
                                    <span
                                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        trade.units > 0
                                          ? "bg-green-900/40 text-green-300 border border-green-700/50"
                                          : "bg-red-900/40 text-red-300 border border-red-700/50"
                                      }`}
                                    >
                                      {trade.type === "long" ? "LONG" : "SHORT"}
                                    </span>
                                  ) : header.key === "status" ? (
                                    <span
                                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                        trade.status === "OPEN" || trade.status === "open"
                                          ? "bg-blue-900/40 text-blue-300 border border-blue-700/50"
                                          : "bg-gray-800/80 text-gray-300 border border-gray-700/50"
                                      }`}
                                    >
                                      {trade.status.toUpperCase()}
                                    </span>
                                  ) : header.key === "realizedPL" ||
                                    header.key === "unrealizedPL" ? (
                                    <span
                                      className={
                                        trade[header.key] >= 0
                                          ? "text-green-400 font-medium"
                                          : "text-red-400 font-medium"
                                      }
                                    >
                                      {trade[header.key] >= 0 ? '+' : ''}
                                      ${parseFloat(trade[header.key]).toFixed(2)}
                                    </span>
                                  ) : header.key === "openTime" ? (
                                    formatTime(trade.openTime)
                                  ) : header.key === "closeTime" ? (
                                    trade.status === "CLOSED" || trade.status === "closed" ? (
                                      formatTime(trade.closeTime)
                                    ) : (
                                      "N/A"
                                    )
                                  ) : header.key === "units" ? (
                                    Math.abs(trade.units).toLocaleString()
                                  ) : header.key === "price" ||
                                    header.key === "takeProfitPrice" ||
                                    header.key === "stopLossPrice" ? (
                                    trade[header.key]?.toFixed(5) || "N/A"
                                  ) : (
                                    trade[header.key]
                                  )}
                                </td>
                              );
                            }
                          )}
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Trade Sync Fixer */}
        <TradeSyncFixerButton
          userId={firebaseUser?.uid}
          strategyId={id}
          onFixComplete={(results) => {
            console.log('Trade sync fix completed:', results);
            // Refresh the agent data to show updated trade status
            fetchBotDataOptimized();
          }}
        />

        {/* Performance Dashboard */}
        <PerformanceDashboard tradeHistory={historicalTrades} accountBalance={accountBalance} openTrades={openTrades} />

        {/* Market Conditions */}
        <MarketConditionsPanel marketStatus={marketStatus} strategy={strategy} />

        {/* User Logs */}
        <UserLogs logs={tradeLogs} />
      </div>

      {/* Column Menu Modal */}
      {isColumnMenuOpen && (
        <div
          className="fixed inset-0 z-[9999]"
          aria-labelledby="column-menu-title"
          role="dialog"
          aria-modal="true"
        >
          {/* Background overlay */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={() => setIsColumnMenuOpen(false)}
          ></div>

          {/* Menu panel */}
          <div
            className="fixed right-4 top-20 w-64 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#2a2a2a] py-2 z-50"
            style={{
              maxHeight: '80vh',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)',
              overflow: 'hidden'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="px-4 py-2 border-b border-[#2a2a2a] flex justify-between items-center">
              <h3 className="font-medium text-[#EFBD3A]">Table Columns</h3>
              <div className="flex space-x-2">
                <button
                  className="text-xs text-gray-400 hover:text-white transition-colors"
                  onClick={() => {
                    const allVisible = Object.values(visibleColumns).every(v => v);
                    const newState = {};
                    tableHeaders.forEach(h => newState[h.key] = !allVisible);
                    setVisibleColumns(newState);
                  }}
                >
                  {Object.values(visibleColumns).every(v => v) ? 'Hide All' : 'Show All'}
                </button>
                <button
                  className="text-xs text-gray-400 hover:text-white transition-colors"
                  onClick={() => {
                    const defaultState = {};
                    tableHeaders.forEach(h => defaultState[h.key] = h.visible);
                    setVisibleColumns(defaultState);
                  }}
                >
                  Reset
                </button>
              </div>
            </div>
            <div className="max-h-[50vh] overflow-y-auto py-1">
              {tableHeaders.map((header) => (
                <label
                  key={header.key}
                  className="flex items-center px-4 py-2 hover:bg-[#2a2a2a] cursor-pointer transition-colors"
                >
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      id={`column-${header.key}`}
                      checked={visibleColumns[header.key]}
                      onChange={() => toggleColumn(header.key)}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded border ${visibleColumns[header.key] ? 'bg-[#EFBD3A] border-[#EFBD3A]' : 'border-gray-500'} mr-3 flex items-center justify-center transition-colors`}>
                      {visibleColumns[header.key] && (
                        <svg className="w-3 h-3 text-black" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                  <span className="text-gray-300">{header.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialogs */}
      <PauseConfirmationDialog
        isOpen={confirmDialog.show && confirmDialog.type === 'pause'}
        onClose={handleCancelAction}
        onConfirm={handleConfirmAction}
        loading={actionLoading}
      />

      <StopConfirmationDialog
        isOpen={confirmDialog.show && confirmDialog.type === 'stop'}
        onClose={handleCancelAction}
        onConfirm={handleConfirmAction}
        loading={actionLoading}
      />
    </DashboardLayout>
  );
}

