import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import axios from "axios";
import { motion } from "framer-motion";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../../firebaseConfig";
import { doc, updateDoc, getFirestore, collection, getDocs, setDoc } from "firebase/firestore";
import { app, db } from "../../firebaseConfig";
import { USE_FIREBASE_EMULATOR } from "../config";
import LoadingSpinner from "../components/LoadingSpinner";

const DashboardLayout = dynamic(() => import("../components/DashboardLayout"), { ssr: false });
const RegisterForm = dynamic(() => import("../components/RegisterForm"), { ssr: false });

export default function Dashboard({ isSidebarOpen }) {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [hasApiKey, setHasApiKey] = useState(null);
  const [accountData, setAccountData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [transitionComplete, setTransitionComplete] = useState(false);
  const [activeStrategies, setActiveStrategies] = useState([]);
  const [recentTrades, setRecentTrades] = useState([]);

  // Temporary state for historical data testing
  const [forexPair, setForexPair] = useState('EURUSD');
  const [timeframe, setTimeframe] = useState('15m');
  const [daysBack, setDaysBack] = useState(30);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processResult, setProcessResult] = useState(null);

  const GET_USER_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/get_user"
    : "https://get-user-ihjc6tjxia-uc.a.run.app";
  const GET_BROKER_ACCOUNT_URL = USE_FIREBASE_EMULATOR
    ? "http://127.0.0.1:5001/oryntrade/us-central1/get_oanda_account"
    : "https://get-oanda-account-ihjc6tjxia-uc.a.run.app";

  // const FETCH_HISTORICAL_DATA_URL = USE_FIREBASE_EMULATOR
  //   ? "http://127.0.0.1:5001/oryntrade/us-central1/fetch_and_store_historical_data"
  //   : "https://fetch-and-store-historical-data-ihjc6tjxia-uc.a.run.app";

  const FETCH_HISTORICAL_DATA_URL = "https://fetch-and-store-historical-data-ihjc6tjxia-uc.a.run.app";

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log("Auth state changed:", user);
      if (!user) {
        console.log("User not authenticated, redirecting to login");
        router.push("/login");
      } else {
        setIsAuthenticated(true);
        setFirebaseUser(user);
        console.log("User authenticated:", user);
      }
    });
    return () => unsubscribe();
  }, [router]);

  useEffect(() => {
    if (firebaseUser?.uid) {
      console.log("Fetching user data for UID:", firebaseUser.uid);
      axios
        .get(`${GET_USER_URL}?firebase_uid=${firebaseUser.uid}`)
        .then((response) => {
          console.log("User data fetched:", response.data);
          setHasApiKey(response.data.has_api_key);
          if (response.data.has_api_key) {
            fetchBrokerAccountData(firebaseUser.uid);
          } else {
            setLoading(false);
          }
        })
        .catch((error) => {
          console.error("Error fetching user data:", error);
          setHasApiKey(false);
          setLoading(false);
        });
    }
  }, [firebaseUser?.uid]);

  const fetchBrokerAccountData = (uid) => {
    console.log("Fetching account data for UID:", uid);
    axios
      .get(`${GET_BROKER_ACCOUNT_URL}?firebase_uid=${uid}`)
      .then((res) => {
        console.log("Account data fetched:", res.data);
        setAccountData(res.data);
        setTimeout(() => setTransitionComplete(true), 1000);
        setLoading(false);
        // Fetch active strategies after account data is loaded
        fetchActiveStrategies(uid);
      })
      .catch((err) => {
        console.error("Error fetching account data:", err);
        setLoading(false);
      });
  };

  const fetchActiveStrategies = async (uid) => {
    try {
      console.log("Fetching active strategies for UID:", uid);

      // Get the user document reference
      const userDocRef = doc(db, "users", uid);
      const submittedStrategiesCollRef = collection(userDocRef, "submittedStrategies");

      // Make sure the user document exists
      await setDoc(userDocRef, { lastAccess: new Date() }, { merge: true });

      // Fetch submitted strategies
      const strategiesSnapshot = await getDocs(submittedStrategiesCollRef);
      console.log("Found submitted strategies:", strategiesSnapshot.docs.length);

      const strategies = await Promise.all(
        strategiesSnapshot.docs.map(async (doc) => {
          try {
            const data = doc.data();
            console.log("Processing strategy data:", doc.id, data);

            // Fetch trade history for P&L calculation
            const tradeHistoryRef = collection(
              db,
              "users",
              uid,
              "submittedStrategies",
              doc.id,
              "tradeHistory"
            );
            const tradeHistorySnapshot = await getDocs(tradeHistoryRef);
            const trades = tradeHistorySnapshot.docs.map((tradeDoc) => tradeDoc.data());

            // Calculate total P&L
            const totalPnL = trades.reduce((sum, trade) => {
              if (trade.status?.toUpperCase() === "CLOSED") {
                return sum + (parseFloat(trade.realizedPL) || 0);
              } else {
                return sum + (parseFloat(trade.unrealizedPL) || 0);
              }
            }, 0);

            // Parse strategy JSON if it's a string
            let strategyData = data.strategy_json || data.strategy_data;
            if (typeof strategyData === 'string') {
              try {
                strategyData = JSON.parse(strategyData);
              } catch (e) {
                console.error('Error parsing strategy JSON:', e);
                strategyData = {};
              }
            }

            return {
              id: doc.id,
              name: data.name ||
                    data.strategy_data?.name ||
                    data.human_readable_rules?.strategy_info?.name ||
                    "Unnamed Strategy",
              status: data.status || "stopped",
              instrument: data.instrument ||
                         data.strategy_data?.instruments ||
                         strategyData?.instruments ||
                         data.human_readable_rules?.strategy_info?.instrument ||
                         "Unknown",
              timeframe: data.timeframe ||
                        data.strategy_data?.timeframe ||
                        strategyData?.timeframe ||
                        data.human_readable_rules?.strategy_info?.timeframe ||
                        "Unknown",
              totalPnL: isNaN(totalPnL) ? 0 : totalPnL,
              totalTrades: trades.length || 0,
              lastHeartbeat: data.last_heartbeat,
            };
          } catch (error) {
            console.error(`Error processing strategy ${doc.id}:`, error);
            return null;
          }
        })
      );

      // Filter out null values and only include active strategies (running, paused, etc.)
      const activeStrategies = strategies
        .filter((strategy) => strategy !== null &&
                strategy.status !== "stopped" &&
                strategy.status !== "unknown")
        .slice(0, 3); // Limit to 3 for dashboard display

      console.log("Active strategies:", activeStrategies);
      setActiveStrategies(activeStrategies);

    } catch (error) {
      console.error("Error fetching active strategies:", error);
      setActiveStrategies([]);
    }
  };

  const handleApiKeyRegistered = (apiKey) => {
    console.log("API key registered:", apiKey);
    setHasApiKey(true);
    if (firebaseUser) {
      fetchBrokerAccountData(firebaseUser.uid);

      const db = getFirestore(app);
      const userDocRef = doc(db, "users", firebaseUser.uid);

      updateDoc(userDocRef, {
        api_key: apiKey,
        has_api_key: true,
      })
        .then(() => {
          console.log("API key and has_api_key updated in Firestore.");
        })
        .catch((error) => {
          console.error("Error updating Firestore document:", error);
        });
    }
  };

  // Temporary function to test historical data processing
  const handleProcessHistoricalData = async () => {
    setIsProcessing(true);
    setProcessResult(null);

    try {
      console.log(`Processing historical data for ${forexPair} ${timeframe}`);

      const response = await axios.post(FETCH_HISTORICAL_DATA_URL, {
        forex_pair: forexPair,
        timeframe: timeframe,
        days_back: daysBack
      });

      console.log('Historical data processing result:', response.data);
      setProcessResult(response.data);
    } catch (error) {
      console.error('Error processing historical data:', error);
      setProcessResult({
        success: false,
        error: error.response?.data?.error || error.message
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isAuthenticated) {
    return <LoadingSpinner fullScreen text="Loading dashboard..." />;
  }

  if (loading) {
    return <LoadingSpinner fullScreen text="Loading account data..." />;
  }

  return (
    <DashboardLayout>
      <div className={`w-full transition-all duration-200 ${isSidebarOpen ? 'lg:pl-0' : 'lg:pl-0'}`}>
        <div className="w-full space-y-6">
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-[#0A0B0B] rounded-xl p-6 text-[#FEFEFF] shadow-lg border border-[#1a1a1a] relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#EFBD3A]/10 via-[#EFBD3A]/5 to-[#EFBD3A]/10 pointer-events-none"></div>
            <h1 className="text-3xl font-bold mb-2">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">
                Welcome back,
              </span>
              <span className="text-[#FEFEFF] ml-2">{firebaseUser?.email?.split('@')[0]}!</span>
            </h1>
            <p className="text-gray-300">Here's what's happening with your trading account today.</p>
          </motion.div>

          {/* Show RegisterForm if API key is not registered */}
          {!hasApiKey && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="w-full"
            >
              <RegisterForm firebaseUser={firebaseUser} onApiKeyRegistered={handleApiKeyRegistered} />
            </motion.div>
          )}

          {hasApiKey === true && (
            <>
              <div className="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Account Overview Card */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] relative group hover:border-[#EFBD3A]/40 transition-all duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#EFBD3A]/5 via-transparent to-[#EFBD3A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Account Overview</h2>
                <div className="space-y-4 relative">
                  <div>
                    <p className="text-gray-400 text-sm">Balance</p>
                    <p className="text-3xl font-bold text-[#FEFEFF]">
                      ${accountData ? parseFloat(accountData.balance).toFixed(2) : "0.00"}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">Account ID</p>
                    <p className="text-lg text-[#FEFEFF]">{accountData?.account_id || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">Last Updated</p>
                    <p className="text-sm text-gray-300">
                      {new Date().toLocaleString()}
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Active Strategies Card */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] relative group hover:border-[#EFBD3A]/40 transition-all duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#EFBD3A]/5 via-transparent to-[#EFBD3A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>
                <div className="relative flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Active Strategies</h2>
                  {activeStrategies.length > 0 && (
                    <span className="text-sm text-gray-400">{activeStrategies.length} running</span>
                  )}
                </div>
                {activeStrategies.length > 0 ? (
                  <div className="relative space-y-3 mb-4">
                    {activeStrategies.map((strategy) => {
                      const getStatusColor = (status) => {
                        switch (status) {
                          case "running":
                            return "bg-green-500/20 text-green-400 border-green-500/30";
                          case "paused":
                            return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
                          case "market_closed":
                            return "bg-purple-500/20 text-purple-400 border-purple-500/30";
                          case "not_in_session":
                            return "bg-[#EFBD3A]/20 text-[#EFBD3A] border-[#EFBD3A]/30";
                          case "error":
                            return "bg-red-500/20 text-red-400 border-red-500/30";
                          default:
                            return "bg-gray-500/20 text-gray-400 border-gray-500/30";
                        }
                      };

                      const getStatusText = (status) => {
                        switch (status) {
                          case "running":
                            return "Running";
                          case "paused":
                            return "Paused";
                          case "market_closed":
                            return "Market Closed";
                          case "not_in_session":
                            return "Outside Session";
                          case "error":
                            return "Error";
                          default:
                            return status ? status.charAt(0).toUpperCase() + status.slice(1) : "Unknown";
                        }
                      };

                      return (
                        <div key={strategy.id} className="bg-[#141516] rounded-lg p-4 border border-[#1a1a1a]/50 hover:border-[#2a2a2a] transition-all duration-200">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1 min-w-0">
                              <h3 className="text-[#FEFEFF] font-medium text-sm truncate mb-1">{strategy.name}</h3>
                              <div className="flex items-center text-xs text-gray-400 space-x-2">
                                <span>{strategy.instrument}</span>
                                <span>•</span>
                                <span>{strategy.timeframe}</span>
                              </div>
                            </div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(strategy.status)}`}>
                              {getStatusText(strategy.status)}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div>
                                <div className="text-xs text-gray-400">P&L</div>
                                <div className={`text-sm font-bold ${parseFloat(strategy.totalPnL) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                  {parseFloat(strategy.totalPnL) >= 0 ? '+' : ''}${strategy.totalPnL.toFixed(2)}
                                </div>
                              </div>
                              <div>
                                <div className="text-xs text-gray-400">Trades</div>
                                <div className="text-sm font-medium text-[#FEFEFF]">{strategy.totalTrades}</div>
                              </div>
                            </div>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                console.log("View Details button clicked for strategy:", strategy.id);
                                router.push(`/trade-bots/${strategy.id}`);
                              }}
                              className="text-xs text-[#EFBD3A] hover:text-[#EFBD3A]/80 font-medium transition-colors cursor-pointer"
                            >
                              View Details →
                            </button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="relative text-center py-6">
                    <div className="w-12 h-12 mx-auto mb-3 bg-[#141516] rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                      </svg>
                    </div>
                    <p className="text-gray-400 text-sm mb-4">No active strategies</p>
                  </div>
                )}
                <div className="relative flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log("Create Strategy button clicked");
                      router.push('/strategy-generation');
                    }}
                    className="flex-1 bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A] text-[#0A0B0B] font-medium py-2 px-4 rounded-lg hover:from-[#EFBD3A] hover:to-[#EFBD3A] transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:ring-offset-2 focus:ring-offset-[#0A0B0B] text-sm cursor-pointer"
                  >
                    Create Strategy
                  </button>
                  {activeStrategies.length > 0 && (
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log("View All button clicked - navigating to trade-bots page");
                        router.push('/trade-bots');
                      }}
                      className="px-4 py-2 bg-[#141516] hover:bg-[#1a1a1a] text-[#FEFEFF] font-medium rounded-lg transition-all duration-300 border border-[#2a2a2a] hover:border-[#3a3a3a] text-sm cursor-pointer"
                    >
                      View All
                    </button>
                  )}
                </div>
              </motion.div>

              {/* Recent Trades Card */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="bg-[#0A0B0B] rounded-xl p-6 shadow-lg border border-[#1a1a1a] relative group hover:border-[#EFBD3A]/40 transition-all duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-[#EFBD3A]/5 via-transparent to-[#EFBD3A]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                <h2 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]">Recent Trades</h2>
                {recentTrades.length > 0 ? (
                  <div className="space-y-3">
                    {recentTrades.map((trade, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-[#1a1a1a] rounded-lg border border-[#1a1a1a]/50">
                        <div>
                          <p className="text-[#FEFEFF] font-medium">{trade.pair}</p>
                          <p className="text-sm text-gray-400">{trade.type}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          trade.profit > 0 ? 'bg-[#EFBD3A]/20 text-[#EFBD3A]' : 'bg-red-500/20 text-red-400'
                        }`}>
                          {trade.profit > 0 ? '+' : ''}{trade.profit}%
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400">No recent trades</p>
                )}
                <button
                  onClick={() => router.push('/trades')}
                  className="mt-4 w-full bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A] text-[#0A0B0B] font-medium py-2 rounded-lg hover:from-[#EFBD3A] hover:to-[#EFBD3A] transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:ring-offset-2 focus:ring-offset-[#0A0B0B]"
                >
                  View All Trades
                </button>
              </motion.div>
            </div>
            </>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}