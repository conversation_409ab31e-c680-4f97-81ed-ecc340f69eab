import React, { useState, useEffect, useCallback, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import dynamic from "next/dynamic";
import axios from "axios";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { USE_FIREBASE_EMULATOR } from "../config";
import { onAuthStateChanged } from "firebase/auth";
import { getFirestore, doc, getDoc } from 'firebase/firestore';
import { auth } from "../../firebaseConfig";
import { useRouter } from "next/router";
import StrategyDescriptionDialog from "../components/StrategyDescriptionDialog";
import BacktestResults from '../components/BacktestResults';
import SaveStrategyNotification from '../components/SaveStrategyNotification';
import { useInView } from "react-intersection-observer";
import { toast } from 'react-toastify';
import StrategyChart from '../components/StrategyChart';
import { getDateRange } from '../utils/dateUtils';

// API URLs
const GET_HISTORICAL_DATA_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/fetch_historical_data_from_polygon"
  : "https://fetch-historical-data-from-polygon-ihjc6tjxia-uc.a.run.app";

const SAVE_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/save_strategy"
  : "https://save-strategy-ihjc6tjxia-uc.a.run.app";

const GET_STRATEGIES_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/get_strategies"
  : "https://get-strategies-ihjc6tjxia-uc.a.run.app";

const UPDATE_STRATEGY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/update_strategy"
  : "https://update-strategy-ihjc6tjxia-uc.a.run.app";

const SAVE_TO_COMMUNITY_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/save_to_community_strategies"
  : "https://save-to-community-strategies-ihjc6tjxia-uc.a.run.app";

const CHECK_USER_PRIVILEGES_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/check_user_privileges"
  : "https://check-user-privileges-ihjc6tjxia-uc.a.run.app";

const GET_FOREX_PAIRS_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/get_forex_pairs_oanda"
  : "https://get-forex-pairs-oanda-ihjc6tjxia-uc.a.run.app";

const RUN_BACKTEST_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/run_backtest"
  : "https://run-backtest-ihjc6tjxia-uc.a.run.app";

// Constants
const timeframeOptions = ["1m", "5m", "15m", "30m", "1h", "4h", "1d", "1w"];

const tradingSessions = [
  {
    name: "All",
    timezone: "All",
    description: "All Trading Sessions"
  },
  {
    name: "London",
    timezone: "Europe/London",
    description: "London Session"
  },
  {
    name: "New York",
    timezone: "America/New_York",
    description: "New York Session"
  },
  {
    name: "Tokyo",
    timezone: "Asia/Tokyo",
    description: "Tokyo Session"
  },
  {
    name: "Sydney",
    timezone: "Australia/Sydney",
    description: "Sydney Session"
  }
];

// Button styling constants
const buttonStyles = {
  primary: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-blue-500/30 border border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50",
  success: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-green-500/30 border border-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50",
  danger: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white font-medium rounded-lg shadow-lg hover:shadow-red-500/30 border border-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50",
  purple: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-purple-500/30 border border-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50",
  secondary: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white font-medium rounded-lg shadow-lg hover:shadow-gray-500/30 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50",
  add: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-lg shadow-md hover:shadow-blue-400/30 border border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50",
  remove: "transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-1 p-1 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-md shadow-md hover:shadow-red-400/30 border border-red-500 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50",
};

// Dynamic imports
const DashboardLayout = dynamic(() => import("../components/DashboardLayout"), {
  ssr: false,
  loading: () => (
    <div className="min-h-screen bg-[#0A0B0B] flex items-center justify-center">
      <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
    </div>
  ),
});

const BacktestDialogComponent = dynamic(
  () => import("../components/BacktestDialog").then(mod => {
    const { default: Component } = mod;
    return props => <Component {...props} />;
  }),
  {
    ssr: false,
    loading: () => (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-[#0c0f1c] p-6 rounded-xl border border-white/10">
          <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    )
  }
);

const ClientOnlyDropdown = dynamic(() => import("../components/ClientOnlyDropdown"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-12 bg-gray-800 rounded-lg animate-pulse"></div>
  ),
});

// Helper functions
const generateId = () =>
  Date.now().toString() + Math.random().toString(36).substring(2);

const getSessionNameFromTimezone = (timezone) => {
  switch(timezone) {
    case "Europe/London":
      return "London";
    case "America/New_York":
      return "New York";
    case "Asia/Tokyo":
      return "Tokyo";
    case "Australia/Sydney":
      return "Sydney";
    case "All":
      return "All";
    default:
      return null;
  }
};

// Helper function to get session volatility based on forex market knowledge
const getSessionVolatility = (sessionName) => {
  switch(sessionName) {
    case 'New York':
      // Highest volatility - major economic releases, largest trading volume
      // Especially during London-NY overlap (13:00-17:00 UTC)
      return 'High';
    case 'London':
      // High volatility - European economic data, major currency pairs
      // Second most active session after New York
      return 'High';
    case 'Tokyo':
      // Medium volatility - Asian market activity, JPY pairs most active
      // Less volatile than Western sessions but still significant
      return 'Medium';
    case 'Sydney':
      // Lowest volatility - smallest of the major sessions
      // AUD and NZD pairs most active, but overall lower volume
      return 'Low';
    default:
      return 'Low';
  }
};

// Helper function to get volatility color
const getVolatilityColor = (volatility) => {
  switch(volatility) {
    case 'High':
      return 'text-red-400';
    case 'Medium':
      return 'text-yellow-400';
    case 'Low':
      return 'text-gray-400';
    default:
      return 'text-gray-400';
  }
};

// Helper function to get timeframe information
const getTimeframeInfo = (timeframe) => {
  switch(timeframe) {
    case '1m':
      return { description: 'Scalping', category: 'short' };
    case '5m':
      return { description: 'Scalping', category: 'short' };
    case '15m':
      return { description: 'Day Trading', category: 'short' };
    case '30m':
      return { description: 'Day Trading', category: 'medium' };
    case '1h':
      return { description: 'Intraday', category: 'medium' };
    case '4h':
      return { description: 'Swing', category: 'long' };
    case '1d':
      return { description: 'Position', category: 'long' };
    case '1w':
      return { description: 'Long-term', category: 'long' };
    default:
      return { description: 'Standard', category: 'medium' };
  }
};

// Helper function to get indicator information
const getIndicatorInfo = (indicatorName) => {
  switch(indicatorName) {
    case 'SMA':
      return {
        description: 'Simple Moving Average',
        category: 'Trend',
        icon: '📈',
        usage: 'Trend following and support/resistance'
      };
    case 'EMA':
      return {
        description: 'Exponential Moving Average',
        category: 'Trend',
        icon: '📊',
        usage: 'More responsive trend following'
      };
    case 'RSI':
      return {
        description: 'Relative Strength Index',
        category: 'Momentum',
        icon: '⚡',
        usage: 'Overbought/oversold conditions'
      };
    case 'MACD':
      return {
        description: 'Moving Average Convergence Divergence',
        category: 'Momentum',
        icon: '🌊',
        usage: 'Trend changes and momentum'
      };
    case 'BollingerBands':
      return {
        description: 'Bollinger Bands',
        category: 'Volatility',
        icon: '📏',
        usage: 'Volatility and price extremes'
      };
    default:
      return {
        description: 'Technical Indicator',
        category: 'General',
        icon: '📊',
        usage: 'Market analysis'
      };
  }
};

// Helper function to get category color
const getCategoryColor = (category) => {
  switch(category) {
    case 'Trend':
      return 'text-blue-400';
    case 'Momentum':
      return 'text-purple-400';
    case 'Volatility':
      return 'text-orange-400';
    default:
      return 'text-gray-400';
  }
};

// Components
const StepNavigation = ({ steps, currentStep, onStepClick }) => {
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className={`flex flex-col items-center ${
              step.id === currentStep ? 'text-[#EFBD3A]' : 'text-[#FEFEFF]/60'
            }`}
          >
            <motion.button
              onClick={() => onStepClick(step.id)}
              whileHover={step.id < currentStep ? { scale: 1.1 } : {}}
              whileTap={step.id < currentStep ? { scale: 0.9 } : {}}
              className={`w-16 h-16 rounded-full flex items-center justify-center text-lg font-bold transition-all duration-300 ${
                currentStep > step.id
                  ? 'bg-gradient-to-b from-white/20 to-white/5 text-[#FEFEFF] shadow-lg cursor-pointer hover:from-white/30'
                  : currentStep === step.id
                  ? 'bg-gradient-to-b from-[#EFBD3A]/20 to-[#EFBD3A]/5 text-[#EFBD3A] ring-2 ring-[#EFBD3A]/20 shadow-lg'
                  : 'bg-gradient-to-b from-white/[0.08] to-transparent text-[#FEFEFF]/40 cursor-not-allowed opacity-50'
              }`}
              disabled={step.id >= currentStep}
            >
              {currentStep > step.id ? (
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                step.id
              )}
            </motion.button>
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className={`mt-2 text-sm font-medium ${
                currentStep >= step.id ? 'text-[#FEFEFF]' : 'text-[#FEFEFF]/40'
              }`}
            >
              {step.title}
            </motion.span>
          </div>
        ))}
      </div>
      <div className="h-1 bg-white/10 rounded-full mt-4">
        <motion.div
          className="h-full bg-[#EFBD3A] rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  );
};

const BacktestDialogWrapper = ({ isOpen, onClose, strategy, onBacktestComplete }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <BacktestDialogComponent
      isOpen={isOpen}
      onClose={onClose}
      strategy={strategy}
      onBacktestComplete={onBacktestComplete}
    />
  );
};

// Constants for form validation
const timezoneOptions = ["UTC", "EST", "PST", "CET", "IST", "GMT"];
const conditionOperators = ["Crossing above", "Crossing below", ">", "<", ">=", "<=", "=="];

// Supported indicators
const supportedIndicators = [
  { name: "SMA", defaultParameters: { period: 50 } },
  { name: "EMA", defaultParameters: { period: 20 } },
  { name: "RSI", defaultParameters: { period: 14 } },
  { name: "MACD", defaultParameters: { fast: 12, slow: 26, signal: 9 } },
  { name: "BollingerBands", defaultParameters: { period: 20, devfactor: 2, offset: 0 } }
];

// Main component
export default function StrategyGeneration() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [strategyName, setStrategyName] = useState('');
  const [forexPair, setForexPair] = useState('');
  const [isForexPairValid, setIsForexPairValid] = useState(true);
  const [isForexPairExists, setIsForexPairExists] = useState(true);
  const [availableForexPairs, setAvailableForexPairs] = useState([]);
  const [isLoadingForexPairs, setIsLoadingForexPairs] = useState(true);
  const [forexPairError, setForexPairError] = useState('');
  const [strategyDescription, setStrategyDescription] = useState('');
  const [tradingSession, setTradingSession] = useState('London');
  const [timeframe, setTimeframe] = useLocalStorage("timeframe", "1h");
  const [timezone, setTimezone] = useLocalStorage("timezone", "America/New_York");
  const [entryLongGroupOperator, setEntryLongGroupOperator] = useState('AND');
  const [exitLongGroupOperator, setExitLongGroupOperator] = useState('OR');
  const [hasBeenModified, setHasBeenModified] = useLocalStorage("hasBeenModified", false);
  const [isStrategySaved, setIsStrategySaved] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isStrategyDescriptionOpen, setIsStrategyDescriptionOpen] = useState(false);
  const [saveSuccessMessage, setSaveSuccessMessage] = useState("");
  const [userTimezone, setUserTimezone] = useState('UTC');
  const [firebaseUser, setFirebaseUser] = useState(null);
  const [hasPrivileges, setHasPrivileges] = useState(false);
  const [isSavingToCommunity, setIsSavingToCommunity] = useState(false);
  const [isSavedToCommunity, setIsSavedToCommunity] = useState(false);
  const [riskPercentage, setRiskPercentage] = useState('');
  const [riskRewardRatio, setRiskRewardRatio] = useState('');
  const [stopLossMethod, setStopLossMethod] = useState('fixed');
  const [fixedPips, setFixedPips] = useState('');
  const [indicatorBasedSL, setIndicatorBasedSL] = useState('');
  const [indicatorParams, setIndicatorParams] = useState({});
  const [lotSize, setLotSize] = useState('');
  const [riskWarning, setRiskWarning] = useState('');
  const [isAtrAddedToChart, setIsAtrAddedToChart] = useState(false);
  const [isAddingAtrToChart, setIsAddingAtrToChart] = useState(false);
  const [pendingAtrIndicator, setPendingAtrIndicator] = useState(null);

  // State for Bollinger Bands indicator
  const [isBollingerAddedToChart, setIsBollingerAddedToChart] = useState(false);
  const [isAddingBollingerToChart, setIsAddingBollingerToChart] = useState(false);
  const [pendingBollingerIndicator, setPendingBollingerIndicator] = useState(null);

  // State for Support & Resistance indicator
  const [isSRAddedToChart, setIsSRAddedToChart] = useState(false);
  const [isAddingSRToChart, setIsAddingSRToChart] = useState(false);
  const [pendingSRIndicator, setPendingSRIndicator] = useState(null);

  // Add new state for forex data
  const [forexData, setForexData] = useState(null);
  const [isLoadingForexData, setIsLoadingForexData] = useState(false);
  const [forexDataError, setForexDataError] = useState(null);

  // Function to fetch forex data
  const fetchForexData = async (pair, tf) => {
    if (!pair || !tf) return;

    setIsLoadingForexData(true);
    setForexDataError(null);

    try {
      const { startDate, endDate, displayRange, chunkSize } = getDateRange(tf);
      let allData = [];
      let currentStart = new Date(startDate);

      while (currentStart < endDate) {
        let chunkEnd = new Date(currentStart);
        chunkEnd.setDate(chunkEnd.getDate() + chunkSize);

        if (chunkEnd > endDate) {
          chunkEnd = endDate;
        }

        const response = await axios.get(GET_HISTORICAL_DATA_URL, {
          params: {
            symbol: pair,
            timeframe: tf,
            start_date: currentStart.toISOString().split('T')[0],
            end_date: chunkEnd.toISOString().split('T')[0]
          }
        });

        if (response.data && response.data.data) {
          allData = [...allData, ...response.data.data];
        }

        currentStart = chunkEnd;
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Format and process the data (optimized - removed excessive logging)
      const processedData = allData
        .filter(item => item && typeof item === 'object')
        .map(item => {
          // Parse the ISO datetime string and ensure it's treated as UTC
          const utcDateString = item.datetime.endsWith('Z') ?
            item.datetime :
            item.datetime + 'Z';

          // Create date object from UTC string
          const dateObj = new Date(utcDateString);

          // Get timestamp in seconds
          const timestamp = dateObj.getTime() / 1000;

          return {
            time: Math.floor(timestamp),
            open: Number(item.open) || 0,
            high: Number(item.high) || 0,
            low: Number(item.low) || 0,
            close: Number(item.close) || 0,
            volume: Number(item.volume) || 0
          };
        })
        .filter(item =>
          !isNaN(item.time) &&
          !isNaN(item.open) &&
          !isNaN(item.high) &&
          !isNaN(item.low) &&
          !isNaN(item.close) &&
          item.high >= item.low
        );

      // Remove duplicates and sort
      const uniqueData = Array.from(new Map(
        processedData.map(item => [item.time, item])
      ).values()).sort((a, b) => a.time - b.time);

      setForexData(uniqueData);
      // Minimal logging for performance (only in development)
      if (process.env.NODE_ENV === 'development' && uniqueData && uniqueData.length > 0) {
        console.log('Forex data loaded:', uniqueData.length, 'candles');
      }
    } catch (error) {
      console.error('Error fetching forex data:', error);
      setForexDataError(error.message);
    } finally {
      setIsLoadingForexData(false);
    }
  };

  // OPTIMIZED: Fetch data when forex pair is selected or timeframe changes
  // Added debouncing and memoization to prevent excessive API calls
  useEffect(() => {
    if (!forexPair || !isForexPairValid || !isForexPairExists) return;

    // Debounce API calls to prevent excessive requests
    const timeoutId = setTimeout(() => {
      fetchForexData(forexPair, timeframe);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [forexPair, timeframe, isForexPairValid, isForexPairExists]);

  // Fetch user timezone from Firestore
  useEffect(() => {
    const fetchUserTimezone = async () => {
      if (!firebaseUser?.uid) return;

      try {
        const db = getFirestore();
        const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));

        if (userDoc.exists()) {
          const userData = userDoc.data();
          setUserTimezone(userData.timezone || 'UTC');
        }
      } catch (error) {
        console.error('Error fetching user timezone:', error);
      }
    };

    fetchUserTimezone();
  }, [firebaseUser]);

  // Function to check if user has community strategy privileges
  const checkUserPrivileges = async (user) => {
    try {
      if (!user || !user.email) {
        setHasPrivileges(false);
        return;
      }

      const response = await axios.get(`${CHECK_USER_PRIVILEGES_URL}?email=${encodeURIComponent(user.email)}`);
      setHasPrivileges(response.data.hasPrivileges || false);
    } catch (error) {
      console.error("Error checking user privileges:", error);
      setHasPrivileges(false);
    }
  };

  // Update the Firebase auth effect
  useEffect(() => {
    let authTimeout = null;

    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log("Auth state changed in strategy-generation:", user ? user.uid : "null");

      // Clear any existing timeout
      if (authTimeout) {
        clearTimeout(authTimeout);
        authTimeout = null;
      }

      if (user) {
        console.log("User authenticated in strategy-generation:", user.uid);
        setFirebaseUser(user);
        setIsMounted(true);

        // Update localStorage with current auth info
        localStorage.setItem("user_id", user.uid);
        localStorage.setItem("auth_timestamp", Date.now().toString());

        // Check user privileges
        checkUserPrivileges(user);

        try {
          const db = getFirestore();
          const userDoc = await getDoc(doc(db, 'users', user.uid));

          if (userDoc.exists()) {
            const userData = userDoc.data();
            setUserTimezone(userData.timezone || 'UTC');
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          // Don't redirect on data fetch error, just set default timezone
          setUserTimezone('UTC');
        }
      } else {
        console.log("No user in strategy-generation");

        // Check if we recently had a user
        const lastAuthTimestamp = localStorage.getItem("auth_timestamp");
        const timeSinceLastAuth = lastAuthTimestamp ? Date.now() - parseInt(lastAuthTimestamp) : Infinity;

        if (timeSinceLastAuth < 5000) {
          console.log("Recent auth detected in strategy-generation, waiting...");
          // Wait before redirecting if we recently had auth
          authTimeout = setTimeout(() => {
            if (!auth.currentUser) {
              console.log("Still no user after delay in strategy-generation, redirecting to login");
              router.push("/login");
            }
          }, 3000);
        } else {
          // No recent auth, redirect immediately
          console.log("No recent auth in strategy-generation, redirecting to login");
          router.push("/login");
        }

        setFirebaseUser(null);
        setHasPrivileges(false);
        setIsMounted(true);
      }
    });

    // Cleanup subscription on unmount
    return () => {
      if (authTimeout) {
        clearTimeout(authTimeout);
      }
      unsubscribe();
    };
  }, [router]);

  // OPTIMIZED: Simplified navigation warning without heavy form clearing
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasBeenModified && !isStrategySaved) {
        const message = "You have unsaved changes. Are you sure you want to leave?";
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
      // REMOVED: clearStrategyForm() - too expensive and causes crashes
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasBeenModified, isStrategySaved]);

  useEffect(() => {
    const handleRouteChange = (url) => {
      if (hasBeenModified && !isStrategySaved) {
        const confirm = window.confirm("You have unsaved changes. Are you sure you want to leave?");
        if (!confirm) {
          router.events.emit('routeChangeError');
          throw 'Navigation cancelled by user';
        }
      }
      // REMOVED: clearStrategyForm() - too expensive and causes crashes
    };

    const handleRouteChangeComplete = () => {
      // REMOVED: clearStrategyForm() - too expensive and causes crashes
    };

    router.events.on('routeChangeStart', handleRouteChange);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);

    return () => {
      router.events.off('routeChangeStart', handleRouteChange);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
    };
  }, [hasBeenModified, isStrategySaved, router]);

  // DISABLED: Heavy form clearing operations that cause crashes
  // These operations are too expensive and cause browser crashes
  // useEffect(() => {
  //   const handlePopState = () => clearStrategyForm();
  //   const handleVisibilityChange = () => {
  //     if (document.hidden) clearStrategyForm();
  //   };
  //   const handlePageHide = () => clearStrategyForm();
  //
  //   window.addEventListener('popstate', handlePopState);
  //   document.addEventListener('visibilitychange', handleVisibilityChange);
  //   window.addEventListener('pagehide', handlePageHide);
  //
  //   return () => {
  //     window.removeEventListener('popstate', handlePopState);
  //     document.removeEventListener('visibilitychange', handleVisibilityChange);
  //     window.removeEventListener('pagehide', handlePageHide);
  //   };
  // }, []);

  // DISABLED: Component unmount cleanup - too expensive and causes crashes
  // useEffect(() => {
  //   return () => {
  //     console.log("Strategy generation component unmounting, resetting form");
  //     clearStrategyForm();
  //   };
  // }, []);

  // Update the fetch saved strategies effect
  useEffect(() => {
    const fetchSavedStrategies = async () => {
      if (!firebaseUser?.uid) return;

      try {
        const response = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${firebaseUser.uid}`);
        setSavedStrategies(response.data);
      } catch (error) {
        console.error("Error loading saved strategies:", error);
        // Set empty array on error to prevent undefined errors
        setSavedStrategies([]);
      }
    };

    if (firebaseUser?.uid) {
      fetchSavedStrategies();
    }
  }, [firebaseUser]);

  // Update the fetch forex pairs effect
  useEffect(() => {
    const fetchForexPairs = async () => {
      if (!firebaseUser?.uid) {
        setIsLoadingForexPairs(false);
        return;
      }

      try {
        const response = await axios.get(
          `${GET_FOREX_PAIRS_URL}?firebase_uid=${firebaseUser.uid}`
        );
        setAvailableForexPairs(response.data.forex_pairs);
      } catch (error) {
        console.error("Error fetching forex pairs:", error);
        setForexPairError("Failed to load available forex pairs");
        setAvailableForexPairs([]);
      } finally {
        setIsLoadingForexPairs(false);
      }
    };

    if (firebaseUser?.uid) {
      fetchForexPairs();
    }
  }, [firebaseUser]);

  // Validate forex pair format and existence
  const validateForexPair = (pair) => {
    if (isLoadingForexPairs || !availableForexPairs.length) {
      setIsForexPairValid(true); // Don't show error while loading
      setIsForexPairExists(true);
      setForexPairError("");
      return;
    }
    // Format validation: Should be in the format "XXX/XXX" or "XXX_XXX"
    const formatRegex = /^[A-Z]{3}[\/_][A-Z]{3}$/;
    const isValidFormat = formatRegex.test(pair);
    setIsForexPairValid(isValidFormat);

    // Normalize the pair format for comparison (convert / to _)
    const normalizedPair = pair.replace('/', '_');

    // Existence validation: Check if the pair exists in available pairs
    const exists = availableForexPairs.includes(normalizedPair);
    setIsForexPairExists(exists);

    // Set error message
    if (!isValidFormat) {
      setForexPairError("Please enter a valid forex pair format (e.g., EUR/USD)");
    } else if (!exists) {
      setForexPairError("This forex pair is not available for trading");
    } else {
      setForexPairError("");
    }
  };

  // Re-validate forex pair when pairs are loaded or changed
  useEffect(() => {
    if (!isLoadingForexPairs && forexPair) {
      validateForexPair(forexPair);
    }
  }, [isLoadingForexPairs, availableForexPairs]);

  const handleForexPairChange = (e) => {
    handleStrategyModification(); // Mark strategy as modified when forex pair changes
    const pair = e.target.value.toUpperCase();
    setForexPair(pair);
    validateForexPair(pair);
  };

  // Define the steps
  const steps = [
    { id: 1, title: 'Basic Information', description: 'Set your strategy name and trading pair' },
    { id: 2, title: 'Time Settings', description: 'Configure timeframe and timezone' },
    { id: 3, title: 'Indicators', description: 'Add technical indicators to your strategy' },
    { id: 4, title: 'Entry Rules', description: 'Define when to enter trades' },
    { id: 5, title: 'Exit Rules', description: 'Define when to exit trades' },
    { id: 6, title: "Risk Management", description: 'Set stop loss and take profit levels' },
    { id: 7, title: 'Review', description: 'Review and finalize your strategy' }
  ];

  // Continue with the rest of your state declarations and effects
  const [stopLoss, setStopLoss] = useState("");
  const [takeProfit, setTakeProfit] = useState("");
  const [stopLossUnit, setStopLossUnit] = useLocalStorage("stopLossUnit", "pips");
  const [takeProfitUnit, setTakeProfitUnit] = useLocalStorage("takeProfitUnit", "pips");
  const [selectedIndicators, setSelectedIndicators] = useLocalStorage("selectedIndicators", []);

  // Function to check if a specific indicator type is already in the selectedIndicators array
  const isIndicatorTypeAlreadyAdded = useCallback((indicatorType) => {
    return selectedIndicators.some(ind => ind.type === indicatorType);
  }, [selectedIndicators]);

  // CRITICAL: Heavily optimized indicator state management to prevent crashes
  // Added aggressive throttling and limits to prevent browser crashes
  useEffect(() => {
    if (!selectedIndicators.length) return;

    // CRITICAL: Limit the number of indicators to prevent crashes
    const maxIndicators = 8; // Hard limit to prevent browser crashes
    if (selectedIndicators.length > maxIndicators) {
      console.warn(`Too many indicators (${selectedIndicators.length}). Limiting to ${maxIndicators} to prevent crashes.`);
      setSelectedIndicators(prev => prev.slice(0, maxIndicators));
      return;
    }

    // Aggressive throttling to prevent rapid state changes
    const timeoutId = setTimeout(() => {
      const indicatorTypes = new Set(selectedIndicators.map(ind => ind.type));

      // Batch all updates in a single requestAnimationFrame to prevent multiple re-renders
      requestAnimationFrame(() => {
        if (indicatorTypes.has('ATR') && !isAtrAddedToChart) {
          setIsAtrAddedToChart(true);
          setIsAddingAtrToChart(false);
        }

        if (indicatorTypes.has('BollingerBands') && !isBollingerAddedToChart) {
          setIsBollingerAddedToChart(true);
          setIsAddingBollingerToChart(false);
        }

        if (indicatorTypes.has('SupportResistance') && !isSRAddedToChart) {
          setIsSRAddedToChart(true);
          setIsAddingSRToChart(false);
        }
      });
    }, 200); // Increased throttling delay

    return () => clearTimeout(timeoutId);
  }, [selectedIndicators, isAtrAddedToChart, isBollingerAddedToChart, isSRAddedToChart]); // More specific dependencies

  const [entryRules, setEntryRules] = useLocalStorage("entryRules", []);
  const [exitRules, setExitRules] = useLocalStorage("exitRules", []);
  const [entryBuyGroupOperator, setEntryBuyGroupOperator] = useLocalStorage("entryBuyGroupOperator", "AND");
  const [entrySellGroupOperator, setEntrySellGroupOperator] = useLocalStorage("entrySellGroupOperator", "AND");
  const [exitBuyGroupOperator, setExitBuyGroupOperator] = useLocalStorage("exitBuyGroupOperator", "AND");
  const [exitSellGroupOperator, setExitSellGroupOperator] = useLocalStorage("exitSellGroupOperator", "AND");
  const [isStrategyFinalized, setIsStrategyFinalized] = useLocalStorage("isStrategyFinalized", false);
  const [savedStrategies, setSavedStrategies] = useLocalStorage("savedStrategies", []);
  const [selectedStoredStrategyId, setSelectedStoredStrategyId] = useLocalStorage("selectedStoredStrategyId", null);
  const [backtestResults, setBacktestResults] = useLocalStorage("backtestResults", null);
  const [backtestStatus, setBacktestStatus] = useState(null);
  const [backtestError, setBacktestError] = useState(null);
  const [loadingBacktest, setLoadingBacktest] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTimeframeBT, setSelectedTimeframeBT] = useState("1h");
  const [selectedTimezoneBT, setSelectedTimezoneBT] = useState("America/New_York");
  const [startingBalance, setStartingBalance] = useState(10000);
  const [showDuplicatePopup, setShowDuplicatePopup] = useState(false);
  const [isDescriptionDialogOpen, setIsDescriptionDialogOpen] = useState(false);
  const [generatedStrategy, setGeneratedStrategy] = useState(null);
  const [activeTab, setActiveTab] = useState('strategy');
  // Initialize editingStrategyId from localStorage if available
  const [editingStrategyId, setEditingStrategyId] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem("editingStrategyId") || null;
    }
    return null;
  });
  const [newIndicator, setNewIndicator] = useState({
    type: "",
    parameters: {},
    source: "close" // default to close
  });

  // Add this with other useState declarations at the top
  const [resultsRef, setResultsRef] = useState(null);

  // Add this state variable
  const [isStrategyValid, setIsStrategyValid] = useState(false);

  // Add new state for animations
  const [isAnimating, setIsAnimating] = useState(false);
  const [ref, inView] = useInView({
    threshold: 0.1,
    triggerOnce: true
  });

  // Add new state for entry rule modal
  const [isEntryRuleModalOpen, setIsEntryRuleModalOpen] = useState(false);
  const [newEntryRule, setNewEntryRule] = useState({
    id: '',
    tradeType: 'long',
    indicator1: '',
    operator: 'Crossing above',
    compareType: 'value',
    indicator2: '',
    value: '',
    barRef: 'close', // Add bar reference field with default value
    band: undefined, // Add band property for Bollinger Bands (first indicator)
    band2: undefined, // Add band2 property for Bollinger Bands (second indicator)
    macdComponent: undefined, // Add macdComponent property for MACD (first indicator)
    macdComponent2: undefined // Add macdComponent2 property for MACD (second indicator)
  });

  // Add new state for exit rule modal
  const [isExitRuleModalOpen, setIsExitRuleModalOpen] = useState(false);
  const [newExitRule, setNewExitRule] = useState({
    id: '',
    tradeType: 'long',
    indicator1: '',
    operator: 'Crossing above',
    compareType: 'value',
    indicator2: '',
    value: '',
    barRef: 'close', // Add bar reference field with default value
    band: undefined, // Add band property for Bollinger Bands (first indicator)
    band2: undefined, // Add band2 property for Bollinger Bands (second indicator)
    macdComponent: undefined, // Add macdComponent property for MACD (first indicator)
    macdComponent2: undefined // Add macdComponent2 property for MACD (second indicator)
  });

  const [showSaveNotification, setShowSaveNotification] = useState(false);
  const [isUpdateNotification, setIsUpdateNotification] = useState(false);
  const [isSavingStrategy, setIsSavingStrategy] = useState(false);

  // Add this with other state declarations
  const [isBacktestDialogOpen, setIsBacktestDialogOpen] = useState(false);

  // Rename the state variable to avoid conflict with the array
  const [selectedTradingSessions, setSelectedTradingSessions] = useState([]);

  // State for indicator search and filtering
  const [indicatorSearch, setIndicatorSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  // Add handler for trading session changes
  const handleTradingSessionChange = (e) => {
    handleStrategyModification();
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);

    // If "All" is selected, clear other selections
    if (selectedOptions.includes("All")) {
      setSelectedTradingSessions(["All"]);
    } else {
      // Remove "All" if it was previously selected
      setSelectedTradingSessions(selectedOptions.filter(option => option !== "All"));
    }
  };

  // Handler for session card clicks
  const handleSessionCardClick = (timezone) => {
    handleStrategyModification();

    // If "All" is currently selected, clear it first
    if (selectedTradingSessions.includes('All')) {
      setSelectedTradingSessions([timezone]);
      return;
    }

    // Toggle the session
    if (selectedTradingSessions.includes(timezone)) {
      // Remove the session
      const newSessions = selectedTradingSessions.filter(tz => tz !== timezone);
      setSelectedTradingSessions(newSessions);
    } else {
      // Add the session
      setSelectedTradingSessions([...selectedTradingSessions, timezone]);
    }
  };

  // Handler for "Select All" option
  const handleSelectAllSessions = () => {
    handleStrategyModification();

    if (selectedTradingSessions.includes('All')) {
      // If "All" is selected, deselect it
      setSelectedTradingSessions([]);
    } else {
      // Select "All" and clear other selections
      setSelectedTradingSessions(['All']);
    }
  };

  // Handler for timeframe card clicks
  const handleTimeframeCardClick = (selectedTimeframe) => {
    handleStrategyModification();
    setTimeframe(selectedTimeframe);

    // Minimal logging for performance
    if (process.env.NODE_ENV === 'development') {
      console.log('Timeframe changed to:', selectedTimeframe);
    }
  };

  // Handler for indicator card clicks
  const handleIndicatorCardClick = (indicatorName) => {
    handleStrategyModification();
    const found = supportedIndicators.find((ind) => ind.name === indicatorName);
    if (found) {
      setNewIndicator({
        type: indicatorName,
        parameters: { ...found.defaultParameters },
        source: "close"
      });
      // Clear search when indicator is selected for better UX
      setIndicatorSearch('');
    }
  };

  // Filtered indicators based on search and category
  const filteredIndicators = useMemo(() => {
    return supportedIndicators.filter(indicator => {
      const indicatorInfo = getIndicatorInfo(indicator.name);

      // Filter by category
      const categoryMatch = selectedCategory === 'All' || indicatorInfo.category === selectedCategory;

      // Filter by search term
      const searchMatch = indicatorSearch === '' ||
        indicator.name.toLowerCase().includes(indicatorSearch.toLowerCase()) ||
        indicatorInfo.description.toLowerCase().includes(indicatorSearch.toLowerCase()) ||
        indicatorInfo.usage.toLowerCase().includes(indicatorSearch.toLowerCase());

      return categoryMatch && searchMatch;
    });
  }, [indicatorSearch, selectedCategory]);

  // Update the effect that handles strategyId from query params
  useEffect(() => {
    const { strategyId } = router.query;
    if (strategyId && firebaseUser?.uid && savedStrategies.length > 0) {
      const strat = savedStrategies.find(s => s.id.toString() === strategyId);
      if (strat) {
        const s = JSON.parse(strat.strategy_json);
        // Set basic information
        setStrategyName(s.name);
        setStrategyDescription(s.description || "");
        setForexPair(s.instruments);
        setTimeframe(s.timeframe);
        setTimezone(s.TimeZone);

        // Set risk management
        setRiskPercentage(s.riskManagement.riskPercentage || "");
        setRiskRewardRatio(s.riskManagement.riskRewardRatio || "");
        setStopLossMethod(s.riskManagement.stopLossMethod || "fixed");
        setFixedPips(s.riskManagement.fixedPips || "");

        // Set indicator-based stop loss settings if present
        if (s.riskManagement.stopLossMethod === 'indicator' && s.riskManagement.indicatorBasedSL) {
          const indicator = s.riskManagement.indicatorBasedSL.indicator || "";
          setIndicatorBasedSL(indicator);
          if (s.riskManagement.indicatorBasedSL.parameters && Object.keys(s.riskManagement.indicatorBasedSL.parameters).length > 0) {
            setIndicatorParams(s.riskManagement.indicatorBasedSL.parameters);
          } else {
            setIndicatorParams(getDefaultIndicatorParams(indicator));
          }

          // Set the appropriate indicator state to "added to chart" based on the indicator type
          // This ensures that the remove logic works correctly when changing stop loss methods
          if (indicator === 'atr') {
            setIsAtrAddedToChart(true);
            setIsAddingAtrToChart(false);
          } else if (indicator === 'bollinger') {
            setIsBollingerAddedToChart(true);
            setIsAddingBollingerToChart(false);
          } else if (indicator === 'support_resistance') {
            setIsSRAddedToChart(true);
            setIsAddingSRToChart(false);
          }
        } else {
          setIndicatorBasedSL("");
          setIndicatorParams({});
          // Reset all indicator states when no indicator-based stop loss
          setIsAtrAddedToChart(false);
          setIsAddingAtrToChart(false);
          setIsBollingerAddedToChart(false);
          setIsAddingBollingerToChart(false);
          setIsSRAddedToChart(false);
          setIsAddingSRToChart(false);
        }

        // Set lot size if present
        setLotSize(s.riskManagement.lotSize || "");

        // Set indicators and rules
        const indicators = s.indicators || [];

        // If this strategy has indicator-based stop loss, mark the corresponding indicator
        // with fromRiskManagement flag so it can be properly removed when changing stop loss methods
        if (s.riskManagement.stopLossMethod === 'indicator' && s.riskManagement.indicatorBasedSL) {
          const stopLossIndicatorType = s.riskManagement.indicatorBasedSL.indicator;
          const updatedIndicators = indicators.map(ind => {
            // Mark indicators that match the stop loss indicator type as fromRiskManagement
            if ((stopLossIndicatorType === 'atr' && ind.type === 'ATR') ||
                (stopLossIndicatorType === 'bollinger' && ind.type === 'BollingerBands') ||
                (stopLossIndicatorType === 'support_resistance' && ind.type === 'SupportResistance')) {
              return { ...ind, fromRiskManagement: true };
            }
            return ind;
          });
          setSelectedIndicators(updatedIndicators);
        } else {
          setSelectedIndicators(indicators);
        }

        setEntryRules(s.entryRules || []);
        setExitRules(s.exitRules || []);

        // Convert trading sessions to timezone values for the select input
        const sessions = Array.isArray(s.tradingSession) ? s.tradingSession : [s.tradingSession];
        const sessionTimezones = sessions.map(sessionName => {
          if (sessionName === "All") return "All";
          const session = tradingSessions.find(ts => ts.name === sessionName);
          return session ? session.timezone : sessionName;
        });
        setSelectedTradingSessions(sessionTimezones);

        setGeneratedStrategy(s);
        setEditingStrategyId(strat.id);
        setIsStrategyFinalized(true);
        setHasBeenModified(false);
      }
    }
  }, [router.query, firebaseUser, savedStrategies]);

  // When a stored strategy is selected, update the form state.
  // Note: This useEffect is triggered after handleStoredStrategySelect sets selectedStoredStrategyId
  // It's kept for compatibility but most of the logic is already handled in handleStoredStrategySelect
  useEffect(() => {
    if (selectedStoredStrategyId && savedStrategies.length > 0) {
      const strat = savedStrategies.find(
        (s) => s.id.toString() === selectedStoredStrategyId
      );
      if (strat) {
        // Most of the state setting is already done in handleStoredStrategySelect
        // This useEffect is mainly kept for any additional processing if needed
        setEditingStrategyId(strat.id);
      }
    }
  }, [selectedStoredStrategyId, savedStrategies]);

  // Add a function to check if strategy is saved
  const checkIfStrategyIsSaved = useCallback(() => {
    if (!strategyName || !savedStrategies) return false;
    return savedStrategies.some(
      (s) => s.name.toLowerCase() === strategyName.trim().toLowerCase()
    );
  }, [strategyName, savedStrategies]);

  // Update the effect to check if strategy is saved when name or savedStrategies changes
  useEffect(() => {
    if (!strategyName || !savedStrategies) {
      setIsStrategySaved(false);
      return;
    }
    const isSaved = savedStrategies.some(
      (s) => s.name.toLowerCase() === strategyName.trim().toLowerCase()
    );
    setIsStrategySaved(isSaved);
  }, [strategyName, savedStrategies]); // Removed checkIfStrategyIsSaved from dependencies

  // Add a debug effect to monitor key state changes (throttled for performance)
  useEffect(() => {
    // Only log in development and throttle the logging
    if (process.env.NODE_ENV === 'development') {
      const timeoutId = setTimeout(() => {
        console.log("State change detected:");
        console.log(`- editingStrategyId: ${editingStrategyId}`);
        console.log(`- hasBeenModified: ${hasBeenModified}`);
        console.log(`- isStrategySaved: ${isStrategySaved}`);
      }, 100); // Throttle logging to prevent spam

      return () => clearTimeout(timeoutId);
    }

    // Store editingStrategyId in localStorage to ensure it persists
    if (editingStrategyId) {
      localStorage.setItem("editingStrategyId", editingStrategyId);
    }
  }, [editingStrategyId, hasBeenModified, isStrategySaved]);

  // Add getSourceOptions helper function after the existing helper functions
  const getSourceOptions = () => {
    const baseOptions = [
      { value: "close", label: "Close" },
      { value: "open", label: "Open" },
      { value: "high", label: "High" },
      { value: "low", label: "Low" },
      { value: "volume", label: "Volume" }
    ];

    // Add existing indicators as source options
    const indicatorOptions = selectedIndicators.map(ind => ({
      value: ind.id,
      label: `${ind.type} (${Object.entries(ind.parameters)
        .map(([k, v]) => `${k}: ${v}`)
        .join(", ")})`
    }));

    return [...baseOptions, ...indicatorOptions];
  };

  // Update the handleNewIndicatorTypeChange function
  const handleNewIndicatorTypeChange = (e) => {
    handleStrategyModification();
    const type = e.target.value;
    if (!type) {
      setNewIndicator({ type: "", parameters: {}, source: "close" });
      return;
    }
    const found = supportedIndicators.find((ind) => ind.name === type);
    setNewIndicator({
      type,
      parameters: { ...found.defaultParameters },
      source: "close" // Reset source to close when changing indicator type
    });
  };

  const handleNewIndicatorParamChange = (paramKey, value) => {
    handleStrategyModification();
    setNewIndicator((prev) => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [paramKey]: Number(value)
      },
    }));
  };

  // Update the addNewIndicator function
  const addNewIndicator = () => {
    handleStrategyModification();
    if (!newIndicator.type) return;
    const instance = {
      id: generateId(),
      type: newIndicator.type,
      parameters: { ...newIndicator.parameters },
      source: newIndicator.source
    };
    setSelectedIndicators((prev) => [...prev, instance]);
    setNewIndicator({ type: "", parameters: {}, source: "close" });
  };

  // --- Selected Indicators Handlers ---
  const removeSelectedIndicator = (id) => {
    handleStrategyModification();
    setSelectedIndicators((prev) => prev.filter((ind) => ind.id !== id));
  };

  // --- Conditions Handlers ---
  const handleAddEntryRule = () => {
    handleStrategyModification();
    if (!newEntryRule.indicator1) return;

    // Check if this is a Bollinger Bands indicator and set a default band if not specified
    const isBollingerBands1 = selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator1 && ind.type === 'BollingerBands'
    );

    // Check if second indicator is Bollinger Bands
    const isBollingerBands2 = newEntryRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator2 && ind.type === 'BollingerBands'
    );

    // Check if this is a MACD indicator and set a default component if not specified
    const isMacd1 = selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator1 && ind.type === 'MACD'
    );

    // Check if second indicator is MACD
    const isMacd2 = newEntryRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newEntryRule.indicator2 && ind.type === 'MACD'
    );

    const ruleToAdd = {
      ...newEntryRule,
      id: generateId(),
      // Set default band to 'middle' for Bollinger Bands if not specified
      band: isBollingerBands1 && !newEntryRule.band ? 'middle' : newEntryRule.band,
      // Set default band2 to 'middle' for second Bollinger Bands indicator if not specified
      band2: isBollingerBands2 && !newEntryRule.band2 ? 'middle' : newEntryRule.band2,
      // Set default macdComponent to 'macd' for MACD if not specified
      macdComponent: isMacd1 && !newEntryRule.macdComponent ? 'macd' : newEntryRule.macdComponent,
      // Set default macdComponent2 to 'macd' for second MACD indicator if not specified
      macdComponent2: isMacd2 && !newEntryRule.macdComponent2 ? 'macd' : newEntryRule.macdComponent2
    };

    console.log("Adding entry rule:", ruleToAdd);
    setEntryRules(prev => [...prev, ruleToAdd]);

    setNewEntryRule({
      id: '',
      tradeType: 'long',
      indicator1: '',
      operator: 'Crossing above',
      compareType: 'value',
      indicator2: '',
      value: '',
      barRef: 'close', // Add bar reference field with default value
      band: undefined, // Reset band
      band2: undefined, // Reset band2
      macdComponent: undefined, // Reset macdComponent
      macdComponent2: undefined // Reset macdComponent2
    });
    setIsEntryRuleModalOpen(false);
  };

  const addEntryRule = () => {
    setIsEntryRuleModalOpen(true);
  };

  const removeEntryRule = (index) => {
    handleStrategyModification();
    setEntryRules((prev) => prev.filter((_, i) => i !== index));
  };

  // Compute grouping counts.
  const entryLongCount = entryRules.filter((r) => r.tradeType === "long").length;
  const entryShortCount = entryRules.filter(
    (r) => r.tradeType === "short"
  ).length;
  const exitLongCount = exitRules.filter((r) => r.tradeType === "long").length;
  const exitShortCount = exitRules.filter((r) => r.tradeType === "short").length;

  // OPTIMIZED: Memoized indicator label function to prevent excessive re-computations
  // This was a major performance bottleneck causing crashes
  const getIndicatorLabel = useMemo(() => {
    // Create a cache for indicator labels to avoid repeated computations
    const labelCache = new Map();

    return (val, rule, isSecondIndicator = false) => {
      // Create cache key
      const cacheKey = `${val}-${rule?.id || 'no-rule'}-${isSecondIndicator}`;

      // Return cached result if available
      if (labelCache.has(cacheKey)) {
        return labelCache.get(cacheKey);
      }

      let result;

      if (val === "price") {
        result = "Price";
      } else {
        // Find indicator in selectedIndicators
        const foundInSelected = selectedIndicators.find((ind) => ind.id === val);
        if (foundInSelected) {
          // Handle Bollinger Bands
          if (foundInSelected.type === 'BollingerBands') {
            const bandProperty = isSecondIndicator ? rule?.band2 : rule?.band;
            if (bandProperty) {
              const bandLabel = bandProperty === 'upper' ? 'Upper Band' :
                               bandProperty === 'lower' ? 'Lower Band' : 'Middle Band';
              result = `${foundInSelected.type} ${bandLabel} (${foundInSelected.parameters.period})`;
            } else {
              result = `${foundInSelected.type} (${foundInSelected.parameters.period})`;
            }
          }
          // Handle MACD
          else if (foundInSelected.type === 'MACD') {
            const macdComponentProperty = isSecondIndicator ? rule?.macdComponent2 : rule?.macdComponent;
            if (macdComponentProperty) {
              const componentLabel = macdComponentProperty === 'signal' ? 'Signal Line' : 'MACD Line';
              result = `${foundInSelected.type} ${componentLabel} (${foundInSelected.parameters.fast},${foundInSelected.parameters.slow},${foundInSelected.parameters.signal})`;
            } else {
              result = `${foundInSelected.type} (${foundInSelected.parameters.fast},${foundInSelected.parameters.slow},${foundInSelected.parameters.signal})`;
            }
          }
          // Handle other indicators
          else {
            const paramStr = foundInSelected.parameters.period ?
              foundInSelected.parameters.period :
              Object.values(foundInSelected.parameters).join(',');
            result = `${foundInSelected.type} (${paramStr})`;
          }
        } else {
          result = "N/A";
        }
      }

      // Cache the result
      labelCache.set(cacheKey, result);

      // Limit cache size to prevent memory leaks
      if (labelCache.size > 100) {
        const firstKey = labelCache.keys().next().value;
        labelCache.delete(firstKey);
      }

      return result;
    };
  }, [selectedIndicators, entryRules, exitRules]);

  // Add these helper functions after the existing helper functions
  const getEntryLongCount = (rules) => rules.filter(rule => rule.tradeType === "long").length;
  const getEntryShortCount = (rules) => rules.filter(rule => rule.tradeType === "short").length;
  const getExitLongCount = (rules) => rules.filter(rule => rule.tradeType === "long").length;
  const getExitShortCount = (rules) => rules.filter(rule => rule.tradeType === "short").length;

  // Helper function to display rule in Trading Rules section
  const displayRule = (rule) => {
    return (
      <>
        {getIndicatorLabel(rule.indicator1, rule)}
        {' '}
        <span className="text-[#EFBD3A]">{rule.operator}</span>
        {' '}
        {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
        {rule.indicator1 === 'price' && (
          <>
            {' '}
            <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
          </>
        )}
      </>
    );
  };

  // Function to render a rule in the Trading Rules section
  const renderRule = (rule) => (
    <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#1a1a1a] p-2 rounded">
      {getIndicatorLabel(rule.indicator1, rule)}
      {' '}
      <span className="text-[#EFBD3A]">{rule.operator}</span>
      {' '}
      {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
      {rule.indicator1 === 'price' && (
        <>
          {' '}
          <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
        </>
      )}
    </div>
  );

  // --- Form Submission ---
  const handleSubmit = async (e) => {
    if (e && e.preventDefault) e.preventDefault();

    // Validate required fields
    const isValid = !!(
      strategyName.trim() &&
      forexPair.trim() &&
      selectedIndicators.length > 0 &&
      entryRules.length > 0 &&
      exitRules.length > 0
    );

    if (!isValid) {
      alert(
        "Please complete all required fields: Strategy Name, Forex Pair, at least one indicator, one entry rule, and one exit rule."
      );
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
      return;
    }

    const strategyPayload = {
      name: strategyName,
      instruments: forexPair,
      timeframe: timeframe,
      indicators: selectedIndicators.map((ind) => ({
        id: ind.id,
        indicator_class: ind.type,
        type: ind.type,
        parameters: { ...ind.parameters },
        source: ind.source
      })),
      entryRules: entryRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close' // Ensure barRef is included
      })),
      exitRules: exitRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close' // Ensure barRef is included
      })),
      riskManagement: {
        stopLoss,
        riskPercentage,
        riskRewardRatio,
        stopLossUnit,
        takeProfitUnit: "percentage",
        stopLossMethod,
        fixedPips: stopLossMethod === 'fixed' ? String(fixedPips || "50") : undefined,
        indicatorBasedSL: stopLossMethod === 'indicator' ? {
          indicator: indicatorBasedSL,
          parameters: indicatorParams
        } : undefined,
        lotSize: stopLossMethod === 'risk' ? lotSize : undefined
      },
      tradingSession: selectedTradingSessions.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    try {
      console.log("Strategy generated successfully:", strategyPayload);
      setMessage("Strategy generated successfully!");
      setGeneratedStrategy(strategyPayload);
      setIsStrategyValid(true);
      setIsStrategyFinalized(true);
      setHasBeenModified(false); // Reset modification flag when strategy is finalized
    } catch (error) {
      console.error("Error submitting strategy:", error);
      setMessage("Error submitting strategy");
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
    }
  };

  // --- Save Strategy Handler using Firebase Functions & Firestore ---
  const handleSaveStrategy = async () => {
    console.log("%c handleSaveStrategy called", "background: #333; color: #ff0; font-weight: bold; padding: 2px 5px;");

    // Get the effective editing ID from state or localStorage
    const effectiveEditingId = editingStrategyId || localStorage.getItem("editingStrategyId");
    console.log(`Current editingStrategyId: ${editingStrategyId}`);
    console.log(`localStorage editingStrategyId: ${localStorage.getItem("editingStrategyId")}`);
    console.log(`Effective editing ID: ${effectiveEditingId}`);

    // Validate required fields
    const requiredFields = {
      'Strategy Name': strategyName,
      'Forex Pair': forexPair,
      'Timeframe': timeframe,
      'Trading Sessions': selectedTradingSessions.length > 0,
      'Indicators': selectedIndicators.length > 0,
      'Entry Rules': entryRules.length > 0,
      'Exit Rules': exitRules.length > 0
    };

    const missingFields = Object.entries(requiredFields)
      .filter(([_, value]) => !value)
      .map(([field]) => field);

    if (missingFields.length > 0) {
      alert(`Please complete the following required fields: ${missingFields.join(', ')}`);
      return;
    }

    setIsSavingStrategy(true);
    setSaveSuccessMessage("");

    try {
      const strategyData = {
        name: strategyName,
        description: strategyDescription,
        forexPair,
        timeframe,
        timezone,
        tradingSession: selectedTradingSessions,
        indicators: selectedIndicators,
        entryRules,
        exitRules,
        riskManagement: {
          stopLoss,
          stopLossUnit,
          riskPercentage,
          riskRewardRatio,
          takeProfitUnit: "percentage",
          stopLossMethod,
          fixedPips: stopLossMethod === 'fixed' ? String(fixedPips || "50") : undefined,
          indicatorBasedSL: stopLossMethod === 'indicator' ? {
            indicator: indicatorBasedSL,
            parameters: indicatorParams
          } : undefined,
          lotSize: stopLossMethod === 'risk' ? lotSize : undefined
        }
      };

      // Determine if we're updating or saving a new strategy
      if (effectiveEditingId) {
        console.log(`Updating existing strategy with ID: ${effectiveEditingId}`);
        await updateStrategy(effectiveEditingId, strategyData);
      } else {
        console.log("Saving new strategy");
        const result = await saveNewStrategy(strategyData);

        // If a new strategy was saved, set the editingStrategyId
        if (result && result.strategyId) {
          console.log(`Setting editingStrategyId to: ${result.strategyId}`);
          setEditingStrategyId(result.strategyId);
          localStorage.setItem("editingStrategyId", result.strategyId);
        }
      }

      // Set success state
      setIsStrategySaved(true);
      setHasBeenModified(false);

      // Update localStorage
      localStorage.setItem("hasBeenModified", JSON.stringify(false));
      localStorage.setItem("isStrategySaved", JSON.stringify(true));

      // Set success message
      const actionText = effectiveEditingId ? "updated" : "saved";
      setSaveSuccessMessage(`Strategy successfully ${actionText}! You can find it in the Strategy Library.`);

      // Clear success message after 5 seconds
      setTimeout(() => {
        setSaveSuccessMessage("");
      }, 5000);

    } catch (error) {
      console.error("Error saving/updating strategy:", error);
      const actionText = effectiveEditingId ? "updating" : "saving";
      alert(`Error ${actionText} strategy: ${error.message}`);
    } finally {
      setIsSavingStrategy(false);
    }
  };

  // --- Backtest Handlers ---
  const handleOpenModal = () => setIsModalOpen(true);
  const handleConfirmBacktest = () => {
    handleBacktest();
    setIsModalOpen(false);
  };
  const handleBacktest = async () => {
    try {
      setIsLoading(true);
      setBacktestResults(null);
      setError(null);

      // Validate strategy before backtesting
      if (!strategy.name || !strategy.instruments || !strategy.timeframe) {
        setError("Please complete the strategy configuration before backtesting.");
        setIsLoading(false);
        return;
      }

      if (!strategy.indicators || strategy.indicators.length === 0) {
        setError("Please add at least one indicator to your strategy.");
        setIsLoading(false);
        return;
      }

      if (!strategy.entryRules || strategy.entryRules.length === 0) {
        setError("Please add at least one entry rule to your strategy.");
        setIsLoading(false);
        return;
      }

      if (!strategy.exitRules || strategy.exitRules.length === 0) {
        setError("Please add at least one exit rule to your strategy.");
        setIsLoading(false);
        return;
      }

      // Transform entry and exit rules to ensure band and band2 properties are preserved
      const transformedEntryRules = strategy.entryRules.map(rule => ({
        ...rule,
        trade_type: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compare_type: rule.compareType,
        indicator2: rule.indicator2 || null,
        value: rule.value || null,
        logical_operator: rule.logicalOperator || "AND",
        bar_ref: rule.barRef || "close",
        band: rule.band || null,  // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2 || null  // Preserve band2 property for Bollinger Bands (second indicator)
      }));

      const transformedExitRules = strategy.exitRules.map(rule => ({
        ...rule,
        trade_type: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compare_type: rule.compareType,
        indicator2: rule.indicator2 || null,
        value: rule.value || null,
        logical_operator: rule.logicalOperator || "AND",
        bar_ref: rule.barRef || "close",
        band: rule.band || null,  // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2 || null  // Preserve band2 property for Bollinger Bands (second indicator)
      }));

      // Add trading costs to strategy (default values if not set)
      const strategyWithCosts = {
        ...strategy,
        entry_rules: transformedEntryRules,
        exit_rules: transformedExitRules,
        tradingCosts: {
          spreadPips: 1.0,  // Default 1 pip spread
          commissionPercentage: 0.0,  // Default no percentage commission
          commissionFixed: 0.0  // Default no fixed commission
        }
      };

      // Call the new backtest endpoint
      const response = await fetch(`${process.env.REACT_APP_API_URL}/run_oryn_backtest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          strategy: strategyWithCosts
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to run backtest');
      }

      const results = await response.json();

      // Format dates in trades
      const formattedResults = {
        ...results,
        trades: results.trades.map(trade => ({
          ...trade,
          entry_time: new Date(trade.entry_time),
          exit_time: new Date(trade.exit_time)
        }))
      };

      setBacktestResults(formattedResults);
      setActiveTab('results');  // Switch to results tab

      // Show success message
      toast.success('Backtest completed successfully!', {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

    } catch (error) {
      console.error('Backtest error:', error);
      setError(error.message || 'Failed to run backtest');

      // Show error message
      toast.error(error.message || 'Failed to run backtest', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStrategyGenerated = (response) => {
    console.log("Received strategy from AI:", response);

    // Handle both nested and flat response formats
    const strategy = response.strategy || response;

    // Store the description
    setStrategyDescription(response.description || "");

    // Basic Information
    setStrategyName(strategy.name || strategy.strategyName || "");
    setForexPair(strategy.instruments || "");

    // Time Settings
    setTimeframe(strategy.timeframe || "");
    setTimezone(strategy.TimeZone || "UTC");

    // Handle trading sessions
    const tradingSessions = strategy.tradingSession || ["All"];
    const selectedTimezones = tradingSessions.map(sessionName => {
      // If "All" is selected, keep it as is
      if (sessionName === "All") {
        return "All";
      }

      // Convert session names to corresponding timezone values
      switch(sessionName.toLowerCase()) {
        case "london":
          return "Europe/London";
        case "new york":
        case "new_york":
          return "America/New_York";
        case "tokyo":
          return "Asia/Tokyo";
        case "sydney":
          return "Australia/Sydney";
        default:
          return sessionName; // Keep original value if unknown
      }
    });
    console.log("Setting trading sessions:", tradingSessions, "->", selectedTimezones);
    setSelectedTradingSessions(selectedTimezones);

    // Indicators
    setSelectedIndicators((strategy.indicators || []).map(indicator => ({
      id: indicator.id || generateId(),
      type: indicator.indicator_class || indicator.type,
      parameters: indicator.parameters || {},
      source: indicator.source || "price"
    })));

    // Entry Rules
    const entryRulesArray = strategy.entry_rules || strategy.entryRules || [];
    const processedEntryRules = entryRulesArray.map(rule => {
      const newRule = {
        id: rule.id || generateId(),
        tradeType: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compareType: rule.compareType,
        indicator2: rule.indicator2 || "",
        value: rule.value || "",
        logicalOperator: rule.logicalOperator || "AND",
        barRef: rule.barRef || "close",
        band: rule.band, // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2, // Preserve band2 property for Bollinger Bands (second indicator)
        macdComponent: rule.macdComponent, // Preserve macdComponent property for MACD (first indicator)
        macdComponent2: rule.macdComponent2 // Preserve macdComponent2 property for MACD (second indicator)
      };

      // Check if this is a MACD crossover rule (same indicator for both indicator1 and indicator2)
      if (rule.compareType === 'indicator' && rule.indicator1 === rule.indicator2) {
        // Find the indicator
        const indicator = (strategy.indicators || []).find(ind => ind.id === rule.indicator1);
        if (indicator && (indicator.indicator_class === 'MACD' || indicator.type === 'MACD')) {
          // This is a MACD crossover rule
          // Set default components if not specified
          if (!newRule.macdComponent) newRule.macdComponent = 'macd';
          if (!newRule.macdComponent2) newRule.macdComponent2 = 'signal';
        }
      }

      return newRule;
    });
    setEntryRules(processedEntryRules);

    // Exit Rules
    const exitRulesArray = strategy.exit_rules || strategy.exitRules || [];
    const processedExitRules = exitRulesArray.map(rule => {
      const newRule = {
        id: rule.id || generateId(),
        tradeType: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compareType: rule.compareType,
        indicator2: rule.indicator2 || "",
        value: rule.value || "",
        logicalOperator: rule.logicalOperator || "AND",
        barRef: rule.barRef || "close",
        band: rule.band, // Preserve band property for Bollinger Bands (first indicator)
        band2: rule.band2, // Preserve band2 property for Bollinger Bands (second indicator)
        macdComponent: rule.macdComponent, // Preserve macdComponent property for MACD (first indicator)
        macdComponent2: rule.macdComponent2 // Preserve macdComponent2 property for MACD (second indicator)
      };

      // Check if this is a MACD crossover rule (same indicator for both indicator1 and indicator2)
      if (rule.compareType === 'indicator' && rule.indicator1 === rule.indicator2) {
        // Find the indicator
        const indicator = (strategy.indicators || []).find(ind => ind.id === rule.indicator1);
        if (indicator && (indicator.indicator_class === 'MACD' || indicator.type === 'MACD')) {
          // This is a MACD crossover rule
          // Set default components if not specified
          if (!newRule.macdComponent) newRule.macdComponent = 'macd';
          if (!newRule.macdComponent2) newRule.macdComponent2 = 'signal';
        }
      }

      return newRule;
    });
    setExitRules(processedExitRules);

    // Risk Management
    if (strategy.riskManagement) {
      const { stopLoss, riskPercentage, riskRewardRatio, stopLossUnit, takeProfitUnit } = strategy.riskManagement;
      setStopLoss(stopLoss);
      setRiskPercentage(riskPercentage);
      setRiskRewardRatio(riskRewardRatio);
      setStopLossUnit(stopLossUnit || "percentage");
      setTakeProfitUnit(takeProfitUnit || "percentage");
    }

    // Group Operators
    setEntryLongGroupOperator(strategy.entryLongGroupOperator || "AND");
    setExitLongGroupOperator(strategy.exitLongGroupOperator || "OR");

    // Set current step to 1 for review
    setCurrentStep(1);

    // Mark strategy as modified
    setHasBeenModified(true);
  };

  // Function to clear the strategy form
  const clearStrategyForm = () => {
    // Clear editing state
    setEditingStrategyId(null);
    localStorage.removeItem("editingStrategyId"); // Also remove from localStorage
    setSelectedStoredStrategyId(null);

    // Basic Information
    setStrategyName("");
    setStrategyDescription("");
    setForexPair("");
    setIsForexPairValid(true);
    setIsForexPairExists(true);
    setForexPairError("");

    // Time Settings
    setTimeframe("1h");
    setTimezone("UTC");
    setSelectedTradingSessions(["All"]);

    // Indicators
    setSelectedIndicators([]);
    setNewIndicator({
      type: "",
      parameters: {},
      source: "price"
    });

    // Entry Rules
    setEntryRules([]);
    setNewEntryRule({
      tradeType: "long",
      indicator1: "",
      operator: "Crossing above",
      compareType: "value",
      indicator2: "",
      value: "",
      logicalOperator: "AND",
      barRef: "close",
      band: undefined, // Reset band property
      band2: undefined // Reset band2 property
    });
    setIsEntryRuleModalOpen(false);

    // Exit Rules
    setExitRules([]);
    setNewExitRule({
      tradeType: "long",
      indicator1: "",
      operator: "Crossing above",
      compareType: "value",
      indicator2: "",
      value: "",
      logicalOperator: "AND",
      barRef: "close",
      band: undefined, // Reset band property
      band2: undefined // Reset band2 property
    });
    setIsExitRuleModalOpen(false);

    // Risk Management
    setRiskPercentage("");
    setRiskRewardRatio("");
    setStopLossMethod("fixed");
    setFixedPips("");
    setIndicatorBasedSL("");
    setLotSize("");
    setStopLoss("");
    setStopLossUnit("pips");

    // Reset all indicator states
    // ATR
    setIsAtrAddedToChart(false);
    setIsAddingAtrToChart(false);
    setPendingAtrIndicator(null);

    // Bollinger Bands
    setIsBollingerAddedToChart(false);
    setIsAddingBollingerToChart(false);
    setPendingBollingerIndicator(null);

    // Support & Resistance
    setIsSRAddedToChart(false);
    setIsAddingSRToChart(false);
    setPendingSRIndicator(null);

    // Remove all indicator types
    setSelectedIndicators(prev =>
      prev.filter(ind => !(
        ind.type === 'ATR' ||
        ind.type === 'BollingerBands' ||
        ind.type === 'SupportResistance'
      ))
    );

    // Group Operators
    setEntryBuyGroupOperator("AND");
    setEntrySellGroupOperator("AND");
    setExitBuyGroupOperator("OR");
    setExitSellGroupOperator("OR");

    // Strategy State
    setGeneratedStrategy(null);
    setIsStrategyFinalized(false);
    setIsStrategyValid(false);
    setHasBeenModified(false);
    setIsStrategySaved(false);

    // Clear any backtest related states
    setBacktestResults(null);
    setBacktestStatus(null);
    setBacktestError(null);
    setLoadingBacktest(false);
    setIsBacktestDialogOpen(false);

    // Clear any modals/dialogs
    setIsModalOpen(false);
    setIsStrategyDescriptionOpen(false);
    setShowDuplicatePopup(false);
    setIsDescriptionDialogOpen(false);

    // Reset to first step
    setCurrentStep(1);

    // Clear URL parameter if it exists
    if (router.query.strategyId) {
      router.replace('/strategy-generation', undefined, { shallow: true });
    }

    // Clear all localStorage items related to strategy generation
    const strategyKeys = [
      'timeframe', 'timezone', 'selectedIndicators', 'entryRules', 'exitRules',
      'entryBuyGroupOperator', 'entrySellGroupOperator', 'exitBuyGroupOperator',
      'exitSellGroupOperator', 'isStrategyFinalized', 'savedStrategies',
      'selectedStoredStrategyId', 'backtestResults', 'hasBeenModified',
      'isStrategySaved', 'editingStrategyId', 'stopLossUnit', 'takeProfitUnit'
    ];

    strategyKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    // Reset community save status
    setIsSavedToCommunity(false);

    // Scroll to top of the page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Add a function to handle any strategy modifications (optimized for performance)
  const handleStrategyModification = useCallback(() => {
    // Only log in development mode and throttle it
    if (process.env.NODE_ENV === 'development') {
      console.log("Strategy modified");
    }

    // Check if we're editing an existing strategy
    const effectiveEditingId = editingStrategyId || localStorage.getItem("editingStrategyId");

    // Update states efficiently
    setHasBeenModified(true);
    setIsStrategySaved(false);
    setIsSavedToCommunity(false);

    // Handle editing strategy ID
    if (effectiveEditingId && editingStrategyId !== effectiveEditingId) {
      setEditingStrategyId(effectiveEditingId);
    }

    // Batch localStorage updates
    requestAnimationFrame(() => {
      localStorage.setItem("hasBeenModified", "true");
      localStorage.setItem("isStrategySaved", "false");
      if (effectiveEditingId) {
        localStorage.setItem("editingStrategyId", effectiveEditingId);
      }
    });
  }, [editingStrategyId]);

  // Update basic info handlers
  const handleStrategyNameChange = (e) => {
    handleStrategyModification();
    setStrategyName(e.target.value);
  };

  const handleTimeframeChange = (e) => {
    handleStrategyModification();
    const newTimeframe = e.target.value;
    setTimeframe(newTimeframe);

    // Minimal logging for performance
    if (process.env.NODE_ENV === 'development') {
      console.log('Timeframe changed to:', newTimeframe);
    }
  };

  const handleTimezoneChange = (e) => {
    handleStrategyModification();
    setTimezone(e.target.value);
  };

  const handleStopLossChange = (e) => {
    handleStrategyModification();
    setStopLoss(e.target.value);
  };

  // Check for risky parameter combinations that might cause insufficient margin (memoized)
  const checkRiskyParameterCombinations = useCallback((fixedPipsValue, riskPercentageValue) => {
    const fixedPips = parseFloat(fixedPipsValue);
    const riskPct = parseFloat(riskPercentageValue);

    if (!isNaN(riskPct) && !isNaN(fixedPips) && riskPct >= 10 && fixedPips >= 50) {
      setRiskWarning('Warning: High risk percentage combined with large fixed pips may cause insufficient margin errors when trading. Consider reducing risk percentage or fixed pips value.');
    } else {
      setRiskWarning('');
    }
  }, []);

  const handleRiskPercentageChange = useCallback((e) => {
    // Throttled logging for performance
    if (process.env.NODE_ENV === 'development' && Math.random() < 0.1) {
      console.log("Risk percentage changed");
    }

    handleStrategyModification();

    const value = e.target.value;
    // Only allow numbers between 0 and 100
    if (value === '' || (value >= 0 && value <= 100)) {
      setRiskPercentage(value);

      // Throttle risk checking to prevent excessive calculations
      if (stopLossMethod === 'fixed') {
        setTimeout(() => {
          checkRiskyParameterCombinations(fixedPips, value);
        }, 300);
      }
    }
  }, [handleStrategyModification, stopLossMethod, fixedPips]);

  const handleRiskRewardRatioChange = (e) => {
    handleStrategyModification();
    const value = e.target.value;
    // Only allow positive numbers
    if (value === '' || value >= 0) {
      setRiskRewardRatio(value);
    }
  };

  const handleStopLossMethodChange = useCallback((e) => {
    handleStrategyModification();
    const value = e.target.value;
    setStopLossMethod(value);

    // Clear risk warning if not using fixed pips
    if (value !== 'fixed') {
      setRiskWarning('');
    } else {
      // Throttle risk checking
      setTimeout(() => {
        checkRiskyParameterCombinations(fixedPips, riskPercentage);
      }, 100);
    }

    // Batch state updates for better performance
    requestAnimationFrame(() => {
      // Reset all indicator states when changing stop loss method
      setIsAtrAddedToChart(false);
      setIsAddingAtrToChart(false);
      setPendingAtrIndicator(null);
      setIsSRAddedToChart(false);
      setIsAddingSRToChart(false);
      setPendingSRIndicator(null);

      // Check if Bollinger Bands is already added from step 3
      const existingBollingerBands = selectedIndicators.some(
        ind => ind.type === 'BollingerBands' && !ind.fromRiskManagement
      );

      if (existingBollingerBands) {
        setIsBollingerAddedToChart(true);
      } else {
        setIsBollingerAddedToChart(false);
        setIsAddingBollingerToChart(false);
        setPendingBollingerIndicator(null);
      }

      // Remove risk management indicators when changing stop loss method
      // But keep indicators added from step 3
      setSelectedIndicators(prev =>
        prev.filter(ind => !(
          (ind.type === 'ATR' && ind.fromRiskManagement) ||
          (ind.type === 'BollingerBands' && ind.fromRiskManagement) ||
          (ind.type === 'SupportResistance' && ind.fromRiskManagement)
        ))
      );
    });
  }, [handleStrategyModification, fixedPips, riskPercentage, selectedIndicators, checkRiskyParameterCombinations]);

  const handleFixedPipsChange = useCallback((e) => {
    handleStrategyModification();
    const value = e.target.value;
    setFixedPips(value);

    // Throttle risk checking to prevent excessive calculations
    setTimeout(() => {
      checkRiskyParameterCombinations(value, riskPercentage);
    }, 300);
  }, [handleStrategyModification, riskPercentage, checkRiskyParameterCombinations]);

  const handleIndicatorParamChange = useCallback((param, value) => {
    handleStrategyModification();
    setIndicatorParams(prev => ({
      ...prev,
      [param]: value
    }));

    // Batch state updates for better performance
    requestAnimationFrame(() => {
      // Reset indicator states when changing parameters
      if (indicatorBasedSL === 'atr') {
        setIsAtrAddedToChart(false);
        setIsAddingAtrToChart(false);
        setPendingAtrIndicator(null);

        // Remove all ATR indicators from risk management
        setSelectedIndicators(prev =>
          prev.filter(ind => !(ind.type === 'ATR' && ind.fromRiskManagement))
        );
      } else if (indicatorBasedSL === 'bollinger') {
        // Check if Bollinger Bands is already added from step 3
        const existingBollingerBands = selectedIndicators.some(
          ind => ind.type === 'BollingerBands' && !ind.fromRiskManagement
        );

        if (existingBollingerBands) {
          setIsBollingerAddedToChart(true);
          // Only remove Bollinger Bands indicators from risk management
          setSelectedIndicators(prev =>
            prev.filter(ind => !(ind.type === 'BollingerBands' && ind.fromRiskManagement))
          );
        } else {
          setIsBollingerAddedToChart(false);
          setIsAddingBollingerToChart(false);
          setPendingBollingerIndicator(null);
          // Remove all Bollinger Bands indicators from risk management
          setSelectedIndicators(prev =>
            prev.filter(ind => !(ind.type === 'BollingerBands' && ind.fromRiskManagement))
          );
        }
      } else if (indicatorBasedSL === 'support_resistance') {
        setIsSRAddedToChart(false);
        setIsAddingSRToChart(false);
        setPendingSRIndicator(null);
        // Remove all Support & Resistance indicators from risk management
        setSelectedIndicators(prev =>
          prev.filter(ind => !(ind.type === 'SupportResistance' && ind.fromRiskManagement))
        );
      }
    });
  }, [handleStrategyModification, indicatorBasedSL]);

  const getDefaultIndicatorParams = (indicator) => {
    if (indicator === 'support_resistance') {
      // Default values based on timeframe
      const currentTimeframe = timeframe.toLowerCase();
      if (currentTimeframe.includes('m')) {
        // For minute timeframes, use fewer periods since each candle represents less time
        const minutes = parseInt(currentTimeframe);
        if (minutes <= 5) {
          return { left: 5, right: 5 }; // 1m, 5m
        } else if (minutes <= 15) {
          return { left: 4, right: 4 }; // 15m
        } else {
          return { left: 3, right: 3 }; // 30m
        }
      } else if (currentTimeframe.includes('h')) {
        // For hour timeframes, use more periods
        const hours = parseInt(currentTimeframe);
        if (hours <= 1) {
          return { left: 6, right: 6 }; // 1h
        } else if (hours <= 4) {
          return { left: 8, right: 8 }; // 4h
        } else {
          return { left: 10, right: 10 }; // 12h
        }
      } else if (currentTimeframe.includes('d')) {
        // For daily timeframes, use even more periods
        return { left: 15, right: 15 }; // 1d
      } else if (currentTimeframe.includes('w')) {
        // For weekly timeframes, use the most periods
        return { left: 20, right: 20 }; // 1w
      }
      // Default fallback
      return { left: 10, right: 10 };
    } else if (indicator === 'bollinger') {
      // Default Bollinger Bands parameters
      return { period: 20, stdDev: 2 };
    } else if (indicator === 'atr') {
      // Default ATR parameters
      return { period: 14, multiplier: 2 };
    }
    return {};
  };

  const handleIndicatorBasedSLChange = (e) => {
    handleStrategyModification();
    const value = e.target.value;
    setIndicatorBasedSL(value);

    // Reset all indicator states when changing indicator type
    // ATR
    setIsAtrAddedToChart(false);
    setIsAddingAtrToChart(false);
    setPendingAtrIndicator(null);

    // Bollinger Bands - special handling
    if (value === 'bollinger') {
      // Check if Bollinger Bands is already added from step 3
      const existingBollingerBands = selectedIndicators.some(
        ind => ind.type === 'BollingerBands' && !ind.fromRiskManagement
      );

      if (existingBollingerBands) {
        // If Bollinger Bands is already on the chart from step 3, just mark it as added
        console.log('Bollinger Bands already exists on chart from step 3');
        setIsBollingerAddedToChart(true);
      } else {
        // Otherwise reset the state
        setIsBollingerAddedToChart(false);
        setIsAddingBollingerToChart(false);
        setPendingBollingerIndicator(null);
      }
    } else {
      // For other indicators, reset Bollinger Bands state
      setIsBollingerAddedToChart(false);
      setIsAddingBollingerToChart(false);
      setPendingBollingerIndicator(null);
    }

    // Support & Resistance
    setIsSRAddedToChart(false);
    setIsAddingSRToChart(false);
    setPendingSRIndicator(null);

    // Remove risk management indicators when changing indicator type
    // But keep indicators added from step 3
    setSelectedIndicators(prev =>
      prev.filter(ind => !(
        (ind.type === 'ATR' && ind.fromRiskManagement) ||
        (ind.type === 'BollingerBands' && ind.fromRiskManagement) ||
        (ind.type === 'SupportResistance' && ind.fromRiskManagement)
      ))
    );

    if (value) {
      // Only set default params if indicatorParams is empty or not for this indicator
      setIndicatorParams(prev => {
        if (!prev || Object.keys(prev).length === 0 || indicatorBasedSL !== value) {
          return getDefaultIndicatorParams(value);
        }
        return prev;
      });
    } else {
      setIndicatorParams({});
    }
  };

  const handleLotSizeChange = (e) => {
    handleStrategyModification();
    setLotSize(e.target.value);
  };

  // Update stored strategy selection handler
  const handleStoredStrategySelect = (e) => {
    if (hasBeenModified && !isStrategySaved) {
      const confirm = window.confirm("You have unsaved changes. Loading another strategy will discard these changes. Do you want to continue?");
      if (!confirm) {
        e.preventDefault();
        return;
      }
    }

    const selectedId = e.target.value;
    setSelectedStoredStrategyId(selectedId);

    // Clear backtest related states
    setBacktestResults(null);
    setBacktestStatus(null);
    setLoadingBacktest(false);

    if (selectedId && savedStrategies.length > 0) {
      const strat = savedStrategies.find(s => s.id.toString() === selectedId);
      if (strat) {
        const s = JSON.parse(strat.strategy_json);
        // Set basic information
        setStrategyName(s.name);
        setStrategyDescription(s.description || "");
        setForexPair(s.instruments);
        setTimeframe(s.timeframe);
        setTimezone(s.TimeZone);

        // Set risk management
        setRiskPercentage(s.riskManagement.riskPercentage || "");
        setRiskRewardRatio(s.riskManagement.riskRewardRatio || "");
        setStopLossMethod(s.riskManagement.stopLossMethod || "fixed");
        setFixedPips(s.riskManagement.fixedPips || "");

        // Set indicator-based stop loss settings if present
        if (s.riskManagement.stopLossMethod === 'indicator' && s.riskManagement.indicatorBasedSL) {
          const indicator = s.riskManagement.indicatorBasedSL.indicator || "";
          setIndicatorBasedSL(indicator);
          if (s.riskManagement.indicatorBasedSL.parameters && Object.keys(s.riskManagement.indicatorBasedSL.parameters).length > 0) {
            setIndicatorParams(s.riskManagement.indicatorBasedSL.parameters);
          } else {
            setIndicatorParams(getDefaultIndicatorParams(indicator));
          }

          // Set the appropriate indicator state to "added to chart" based on the indicator type
          // This ensures that the remove logic works correctly when changing stop loss methods
          if (indicator === 'atr') {
            setIsAtrAddedToChart(true);
            setIsAddingAtrToChart(false);
          } else if (indicator === 'bollinger') {
            setIsBollingerAddedToChart(true);
            setIsAddingBollingerToChart(false);
          } else if (indicator === 'support_resistance') {
            setIsSRAddedToChart(true);
            setIsAddingSRToChart(false);
          }
        } else {
          setIndicatorBasedSL("");
          setIndicatorParams({});
          // Reset all indicator states when no indicator-based stop loss
          setIsAtrAddedToChart(false);
          setIsAddingAtrToChart(false);
          setIsBollingerAddedToChart(false);
          setIsAddingBollingerToChart(false);
          setIsSRAddedToChart(false);
          setIsAddingSRToChart(false);
        }

        // Set lot size if present
        setLotSize(s.riskManagement.lotSize || "");

        // Set indicators and rules
        const indicators = s.indicators || [];

        // If this strategy has indicator-based stop loss, mark the corresponding indicator
        // with fromRiskManagement flag so it can be properly removed when changing stop loss methods
        if (s.riskManagement.stopLossMethod === 'indicator' && s.riskManagement.indicatorBasedSL) {
          const stopLossIndicatorType = s.riskManagement.indicatorBasedSL.indicator;
          const updatedIndicators = indicators.map(ind => {
            // Mark indicators that match the stop loss indicator type as fromRiskManagement
            if ((stopLossIndicatorType === 'atr' && ind.type === 'ATR') ||
                (stopLossIndicatorType === 'bollinger' && ind.type === 'BollingerBands') ||
                (stopLossIndicatorType === 'support_resistance' && ind.type === 'SupportResistance')) {
              return { ...ind, fromRiskManagement: true };
            }
            return ind;
          });
          setSelectedIndicators(updatedIndicators);
        } else {
          setSelectedIndicators(indicators);
        }

        setEntryRules(s.entryRules || []);
        setExitRules(s.exitRules || []);

        // Convert trading sessions to timezone values for the select input
        const sessions = Array.isArray(s.tradingSession) ? s.tradingSession : [s.tradingSession];
        const sessionTimezones = sessions.map(sessionName => {
          if (sessionName === "All") return "All";
          const session = tradingSessions.find(ts => ts.name === sessionName);
          return session ? session.timezone : sessionName;
        });
        setSelectedTradingSessions(sessionTimezones);

        setGeneratedStrategy(s);
        setEditingStrategyId(strat.id);
        setIsStrategyFinalized(true);
        setHasBeenModified(false);
      }
    } else {
      clearStrategyForm();
    }
  };

  const handleAddExitRule = () => {
    handleStrategyModification();
    if (!newExitRule.indicator1) return;

    // Check if this is a Bollinger Bands indicator and set a default band if not specified
    const isBollingerBands1 = selectedIndicators.find(
      ind => ind.id === newExitRule.indicator1 && ind.type === 'BollingerBands'
    );

    // Check if second indicator is Bollinger Bands
    const isBollingerBands2 = newExitRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newExitRule.indicator2 && ind.type === 'BollingerBands'
    );

    // Check if this is a MACD indicator and set a default component if not specified
    const isMacd1 = selectedIndicators.find(
      ind => ind.id === newExitRule.indicator1 && ind.type === 'MACD'
    );

    // Check if second indicator is MACD
    const isMacd2 = newExitRule.compareType === 'indicator' && selectedIndicators.find(
      ind => ind.id === newExitRule.indicator2 && ind.type === 'MACD'
    );

    const ruleToAdd = {
      ...newExitRule,
      id: generateId(),
      // Set default band to 'middle' for Bollinger Bands if not specified
      band: isBollingerBands1 && !newExitRule.band ? 'middle' : newExitRule.band,
      // Set default band2 to 'middle' for second Bollinger Bands indicator if not specified
      band2: isBollingerBands2 && !newExitRule.band2 ? 'middle' : newExitRule.band2,
      // Set default macdComponent to 'macd' for MACD if not specified
      macdComponent: isMacd1 && !newExitRule.macdComponent ? 'macd' : newExitRule.macdComponent,
      // Set default macdComponent2 to 'macd' for second MACD indicator if not specified
      macdComponent2: isMacd2 && !newExitRule.macdComponent2 ? 'macd' : newExitRule.macdComponent2
    };

    console.log("Adding exit rule:", ruleToAdd);
    setExitRules(prev => [...prev, ruleToAdd]);

    setNewExitRule({
      id: '',
      tradeType: 'long',
      indicator1: '',
      operator: 'Crossing above',
      compareType: 'value',
      indicator2: '',
      value: '',
      barRef: 'close', // Add bar reference field with default value
      band: undefined, // Reset band
      band2: undefined, // Reset band2
      macdComponent: undefined, // Reset macdComponent
      macdComponent2: undefined // Reset macdComponent2
    });
    setIsExitRuleModalOpen(false);
  };

  const addExitRule = () => {
    setIsExitRuleModalOpen(true);
  };

  const removeExitRule = (index) => {
    handleStrategyModification();
    setExitRules((prev) => prev.filter((_, i) => i !== index));
  };

  // Add this function to handle step navigation
  const handleStepClick = (stepId) => {
    // Only allow going to previous steps
    if (stepId >= currentStep) {
      return;
    }

    // Always allow going back to any previous step
    setCurrentStep(stepId);
  };

  const [isLoading, setIsLoading] = useState(true);

  // Add this useEffect to handle initial loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0A0B0B] flex items-center justify-center">
        <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Add this function to handle strategy finalization
  const finalizeStrategy = async () => {
    // Helper function to check if indicator parameters are valid
    const hasValidIndicatorParameters = () => {
      if (stopLossMethod !== 'indicator' || !indicatorBasedSL) return true;

      // Get default parameters for the selected indicator
      const defaultParams = (() => {
        switch (indicatorBasedSL) {
          case 'atr':
            return { period: 14, multiplier: 2 };
          case 'bollinger':
            return { period: 20, stdDev: 2 };
          case 'support_resistance':
            return getDefaultIndicatorParams('support_resistance');
          default:
            return {};
        }
      })();

      // If no parameters are set, use default parameters
      if (Object.keys(indicatorParams).length === 0) {
        return true; // Default parameters will be used
      }

      // If parameters are set, make sure they're valid
      return Object.keys(indicatorParams).length > 0;
    };

    // Validate required fields
    const isValid = !!(
      strategyName.trim() &&
      forexPair.trim() &&
      selectedIndicators.length > 0 &&
      entryRules.length > 0 &&
      exitRules.length > 0 &&
      riskPercentage &&
      riskRewardRatio &&
      stopLossMethod &&
      (
        (stopLossMethod === 'fixed' && fixedPips) ||
        (stopLossMethod === 'indicator' && indicatorBasedSL && hasValidIndicatorParameters()) ||
        (stopLossMethod === 'risk' && lotSize)
      )
    );

    if (!isValid) {
      alert(
        "Please complete all required fields: Strategy Name, Forex Pair, at least one indicator, one entry rule, one exit rule, risk percentage, risk-reward ratio, and stop loss method with its parameters."
      );
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
      return false;
    }

    // Get the actual parameters to use (either user-set or default)
    const getActualIndicatorParameters = () => {
      if (stopLossMethod !== 'indicator' || !indicatorBasedSL) return {};

      // If no parameters are set, return default parameters
      if (Object.keys(indicatorParams).length === 0) {
        switch (indicatorBasedSL) {
          case 'atr':
            return { period: 14, multiplier: 2 };
          case 'bollinger':
            return { period: 20, stdDev: 2 };
          case 'support_resistance':
            return getDefaultIndicatorParams('support_resistance');
          default:
            return {};
        }
      }

      // Return user-set parameters
      return indicatorParams;
    };

    const strategyPayload = {
      name: strategyName,
      instruments: forexPair,
      timeframe: timeframe,
      indicators: selectedIndicators.map((ind) => ({
        id: ind.id,
        indicator_class: ind.type,
        parameters: ind.parameters,
        source: ind.source
      })),
      entryRules: entryRules,
      exitRules: exitRules,
      riskManagement: {
        riskPercentage,
        riskRewardRatio,
        stopLossMethod,
        fixedPips: stopLossMethod === 'fixed' ? String(fixedPips || "50") : "",
        indicatorBasedSL: stopLossMethod === 'indicator' ? {
          indicator: indicatorBasedSL,
          parameters: getActualIndicatorParameters()
        } : {
          indicator: "",
          parameters: {}
        },
        lotSize: stopLossMethod === 'risk' ? lotSize : ""
      },
      tradingSession: selectedTradingSessions.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    try {
      console.log("Strategy generated successfully:", strategyPayload);
      setGeneratedStrategy(strategyPayload);
      setIsStrategyValid(true);
      setIsStrategyFinalized(true);
      return true;
    } catch (error) {
      console.error("Error finalizing strategy:", error);
      setIsStrategyValid(false);
      setIsStrategyFinalized(false);
      return false;
    }
  };

  // Update the setCurrentStep function to handle finalization
  const handleStepChange = async (newStep) => {
    // If moving to step 7 (Review), attempt to finalize the strategy
    if (newStep === 7) {
      const success = await finalizeStrategy();
      if (!success) {
        // If finalization fails, don't proceed to the Review step
        return;
      }
    }
    setCurrentStep(newStep);
  };

  // Add the OrynAI dialog handlers
  const handleOpenStrategyDescription = () => setIsStrategyDescriptionOpen(true);
  const handleCloseStrategyDescription = () => setIsStrategyDescriptionOpen(false);

  // Function to save strategy to community strategies
  const handleSaveToCommunity = async () => {
    if (!firebaseUser) {
      alert("Please log in to save to community strategies.");
      return;
    }

    if (!hasPrivileges) {
      alert("You don't have permission to save community strategies.");
      return;
    }

    setIsSavingToCommunity(true);
    try {
      // Build the strategy data
      const strategyData = {
        name: strategyName,
        description: strategyDescription,
        instruments: forexPair,
        timeframe: timeframe,
        tradingSession: selectedTradingSessions.length === 0 ? ["All"] :
          selectedTradingSessions.map(tz => {
            if (tz === "All") return "All";
            const session = tradingSessions.find(s => s.timezone === tz);
            return session ? session.name : tz;
          }),
        indicators: selectedIndicators,
        entryRules: entryRules,
        exitRules: exitRules,
        riskManagement: {
          riskPercentage: riskPercentage,
          riskRewardRatio: riskRewardRatio,
          stopLossMethod: stopLossMethod,
          fixedPips: fixedPips,
          indicatorBasedSL: {
            indicator: indicatorBasedSL,
            parameters: indicatorParams
          },
          lotSize: lotSize
        }
      };

      const response = await axios.post(SAVE_TO_COMMUNITY_URL, {
        user_email: firebaseUser.email,
        strategy: strategyData,
        category: "Custom", // You can make this configurable
        difficulty: "Intermediate", // You can make this configurable
        author: firebaseUser.displayName || firebaseUser.email || "Oryn Team"
      });

      if (response.data.success) {
        setIsSavedToCommunity(true);
        alert("Strategy successfully saved to community strategies!");
      } else {
        alert("Failed to save to community strategies: " + response.data.message);
      }
    } catch (error) {
      console.error("Error saving to community strategies:", error);
      alert("Failed to save to community strategies: " + (error.response?.data?.message || error.message));
    } finally {
      setIsSavingToCommunity(false);
    }
  };

  // Add the helper functions for saving and updating strategies
  const saveNewStrategy = async (strategyData) => {
    console.log("%c saveNewStrategy called", "background: #333; color: #0f0; font-weight: bold; padding: 2px 5px;");

    if (!firebaseUser) {
      throw new Error("Please log in to save your strategy.");
    }

    // Check for duplicate names
    const duplicate = savedStrategies.find(
      (s) => s.name.toLowerCase() === strategyName.trim().toLowerCase()
    );
    if (duplicate) {
      throw new Error("A strategy with this name already exists. Please choose a different name.");
    }

    const strategyToSave = {
      name: strategyData.name,
      description: strategyData.description,
      instruments: strategyData.forexPair,
      timeframe: strategyData.timeframe,
      tradingSession: strategyData.tradingSession.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      indicators: strategyData.indicators.map(indicator => ({
        id: indicator.id || generateId(),
        indicator_class: indicator.type,
        type: indicator.type,
        parameters: { ...indicator.parameters },
        source: indicator.source || "price"
      })),
      entryRules: strategyData.entryRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      exitRules: strategyData.exitRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      riskManagement: {
        riskPercentage: strategyData.riskManagement.riskPercentage,
        riskRewardRatio: strategyData.riskManagement.riskRewardRatio,
        stopLossMethod: strategyData.riskManagement.stopLossMethod,
        fixedPips: strategyData.riskManagement.stopLossMethod === 'fixed' ?
          String(strategyData.riskManagement.fixedPips || "50") : "",
        indicatorBasedSL: strategyData.riskManagement.stopLossMethod === 'indicator' ?
          {
            indicator: strategyData.riskManagement.indicatorBasedSL.indicator,
            parameters: strategyData.riskManagement.indicatorBasedSL.parameters
          } :
          {
            indicator: "",
            parameters: {}
          },
        lotSize: strategyData.riskManagement.stopLossMethod === 'risk' ?
          strategyData.riskManagement.lotSize : ""
      },
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    console.log("Saving strategy:", strategyToSave);

    const response = await axios.post(SAVE_STRATEGY_URL, {
      firebase_uid: firebaseUser.uid,
      strategy: strategyToSave
    }, {
      headers: { "Content-Type": "application/json" }
    });

    console.log("Save strategy response:", response.data);

    if (response.data.message !== "Strategy saved successfully") {
      throw new Error(response.data.message || "Failed to save strategy");
    }

    // Refresh saved strategies list
    const res = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${firebaseUser.uid}`);
    setSavedStrategies(res.data);

    // Return the strategy ID so it can be used to set editingStrategyId
    return {
      strategyId: response.data.data?.strategyId,
      name: response.data.data?.name
    };
  };

  const updateStrategy = async (strategyId, strategyData) => {
    console.log("%c updateStrategy called", "background: #333; color: #0f0; font-weight: bold; padding: 2px 5px;");
    console.log(`Updating strategy with ID: ${strategyId}`);

    if (!firebaseUser) {
      throw new Error("Please log in to update your strategy.");
    }

    const strategyToUpdate = {
      name: strategyData.name,
      description: strategyData.description,
      instruments: strategyData.forexPair,
      timeframe: strategyData.timeframe,
      tradingSession: strategyData.tradingSession.map(session =>
        tradingSessions.find(s => s.timezone === session)?.name || session
      ),
      indicators: strategyData.indicators.map(indicator => ({
        id: indicator.id || generateId(),
        indicator_class: indicator.type,
        type: indicator.type,
        parameters: { ...indicator.parameters },
        source: indicator.source || "price"
      })),
      entryRules: strategyData.entryRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      exitRules: strategyData.exitRules.map(rule => ({
        ...rule,
        id: rule.id || generateId(),
        barRef: rule.barRef || 'close'
      })),
      riskManagement: {
        riskPercentage: strategyData.riskManagement.riskPercentage,
        riskRewardRatio: strategyData.riskManagement.riskRewardRatio,
        stopLossMethod: strategyData.riskManagement.stopLossMethod,
        fixedPips: strategyData.riskManagement.stopLossMethod === 'fixed' ?
          String(strategyData.riskManagement.fixedPips || "50") : "",
        indicatorBasedSL: strategyData.riskManagement.stopLossMethod === 'indicator' ?
          {
            indicator: strategyData.riskManagement.indicatorBasedSL.indicator,
            parameters: strategyData.riskManagement.indicatorBasedSL.parameters
          } :
          {
            indicator: "",
            parameters: {}
          },
        lotSize: strategyData.riskManagement.stopLossMethod === 'risk' ?
          strategyData.riskManagement.lotSize : ""
      },
      ...(entryLongCount > 1 && { entryLongGroupOperator: entryBuyGroupOperator }),
      ...(entryShortCount > 1 && { entryShortGroupOperator: entrySellGroupOperator }),
      ...(exitLongCount > 1 && { exitLongGroupOperator: exitBuyGroupOperator }),
      ...(exitShortCount > 1 && { exitShortGroupOperator: exitSellGroupOperator })
    };

    console.log("Updating strategy:", strategyToUpdate);

    const response = await axios.put(UPDATE_STRATEGY_URL, {
      firebase_uid: firebaseUser.uid,
      strategyId: strategyId,
      strategy: strategyToUpdate
    }, {
      headers: { "Content-Type": "application/json" }
    });

    console.log("Update strategy response:", response.data);

    if (response.data.message !== "Strategy updated successfully") {
      throw new Error(response.data.message || "Failed to update strategy");
    }

    // Refresh saved strategies list
    const res = await axios.get(`${GET_STRATEGIES_URL}?firebase_uid=${firebaseUser.uid}`);
    setSavedStrategies(res.data);

    // Return the updated strategy ID for consistency with saveNewStrategy
    return {
      strategyId: strategyId,
      name: strategyData.name
    };
  };

  // Add this function to handle backtest dialog
  const handleCloseBacktestDialog = () => {
    setIsBacktestDialogOpen(false);
  };

  // Add this function to handle the reset button click
  const handleResetClick = () => {
    if (hasBeenModified) {
      const confirmReset = window.confirm(
        "Are you sure you want to reset the form? This will clear all your progress and cannot be undone."
      );
      if (!confirmReset) {
        return;
      }
    }
    clearStrategyForm();
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen bg-[#0A0B0B] text-white p-4 md:p-6 lg:p-8 overflow-x-auto min-w-[320px]">
        {/* Header Section with OrynAI Assistant Button */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <h1 className="text-2xl md:text-3xl font-bold">Strategy Generation</h1>
          <div className="flex flex-wrap items-center gap-2 md:gap-4 w-full sm:w-auto">
            <button
              onClick={handleResetClick}
              className={`${buttonStyles.secondary} group text-sm md:text-base`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5 mr-1 md:mr-2 group-hover:rotate-180 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Reset Form
            </button>
            <button
              onClick={handleOpenStrategyDescription}
              className={`${buttonStyles.primary} text-sm md:text-base`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5 mr-1 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              OrynAI Assistant
            </button>
          </div>
        </div>

        {/* Strategy Description Dialog */}
        <StrategyDescriptionDialog
          isOpen={isStrategyDescriptionOpen}
          onClose={handleCloseStrategyDescription}
          onStrategyGenerated={handleStrategyGenerated}
        />

        {/* Introduction Section */}
        <div className="mb-12">
          <p className="text-[#FEFEFF]/80 text-lg mb-6">
            Create your custom trading strategy in just a few steps. Our intuitive strategy builder will guide you through the process of defining your trading rules, from basic setup to advanced configurations.
          </p>
          <div className="bg-[#EFBD3A]/10 border border-[#EFBD3A]/20 rounded-lg p-4">
            <p className="text-[#EFBD3A] text-sm">
              <span className="font-semibold">Pro Tip:</span> Take your time to carefully consider each step. A well-thought-out strategy is key to successful trading. You can find your saved strategies in the Strategy Library.
            </p>
              </div>
              </div>

        {/* Add the Strategy Description section after the Introduction and before the Steps */}
        {strategyDescription && strategyDescription.trim() !== "" && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mb-8 bg-gray-800/50 border border-gray-700/50 rounded-xl p-6"
          >
            <div className="flex items-start gap-4">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0012 18.75c-1.03 0-1.96-.474-2.56-1.213l-.548.547z" />
        </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-white mb-2">AI-Generated Strategy Overview</h3>
                <div className="prose prose-invert max-w-none">
                  <p className="text-gray-300 whitespace-pre-line">{strategyDescription}</p>
                </div>
              </div>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-700/50">
              <p className="text-sm text-gray-400">
                This strategy was generated by OrynAI based on your requirements. You can modify any aspect of it in the steps below.
              </p>
            </div>
          </motion.div>
        )}

        <StepNavigation
          steps={steps}
          currentStep={currentStep}
          onStepClick={handleStepClick}
        />

        {/* Chart Section - Moved outside steps */}
        {forexPair && isForexPairValid && isForexPairExists && (
          <div className="mb-8 bg-[#1a1a1a] rounded-xl p-4 md:p-6 shadow-lg overflow-hidden min-w-[320px]">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
              <h3 className="text-lg font-semibold text-[#FEFEFF]">Price Chart</h3>
              <div className="flex items-center gap-2 md:gap-4">
                <div className="text-xs md:text-sm text-[#FEFEFF]/60">
                  {forexPair} • {timeframe} • {userTimezone}
                </div>
              </div>
            </div>
            <div className="w-full overflow-x-auto min-w-[300px]" style={{ minWidth: '300px', maxWidth: '100%' }}>
              <StrategyChart
                forexPair={forexPair}
                timeframe={timeframe}
                userTimezone={userTimezone}
                selectedTradingSessions={selectedTradingSessions
                  .map(getSessionNameFromTimezone)
                  .filter(session => session && session !== "All")}
                data={forexData}
                isLoading={isLoadingForexData}
                error={forexDataError}
                indicators={selectedIndicators}
                trades={backtestResults?.trades || []} // Pass trades to chart
                onIndicatorAdd={() => {
                  // OPTIMIZED: Simplified callback to prevent performance issues
                  // Removed complex state management that was causing crashes
                }}
              />
            </div>
          </div>
        )}

        {/* Step 1: Basic Information */}
        {currentStep === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-8"
          >
            {/* Welcome Header */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center gap-3 mb-4">
                <svg className="w-8 h-8 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <h1 className="text-4xl font-bold text-[#FEFEFF]">Create Your Trading Strategy</h1>
              </div>
              <p className="text-lg text-[#FEFEFF]/70 max-w-2xl mx-auto">
                Build a professional forex trading strategy with our step-by-step wizard.
                Define your strategy parameters, set risk management rules, and backtest your approach.
              </p>
            </div>

            {/* Strategy Information Card */}
            <div className="bg-[#1a1a1a] rounded-xl p-8 border border-[#3a3a3a]">
              <div className="flex items-center gap-3 mb-6">
                <svg className="w-6 h-6 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h2 className="text-2xl font-bold text-[#FEFEFF]">Strategy Information</h2>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column - Form Fields */}
                <div className="space-y-6">
                  {/* Strategy Name */}
                  <div>
                    <label htmlFor="strategyName" className="block text-sm font-semibold text-[#FEFEFF] mb-3">
                      Strategy Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="strategyName"
                        value={strategyName}
                        onChange={handleStrategyNameChange}
                        className="w-full px-4 py-3 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200 pl-12"
                        placeholder="e.g., Momentum Breakout Strategy"
                      />
                      <svg className="w-5 h-5 text-[#FEFEFF]/40 absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                    </div>
                    <p className="mt-2 text-sm text-[#FEFEFF]/60">
                      Choose a descriptive name that reflects your strategy's approach
                    </p>
                  </div>

                  {/* Forex Pair */}
                  <div>
                    <label htmlFor="forexPair" className="block text-sm font-semibold text-[#FEFEFF] mb-3">
                      Currency Pair
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        id="forexPair"
                        value={forexPair}
                        onChange={handleForexPairChange}
                        className={`w-full px-4 py-3 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border ${
                          isForexPairValid && isForexPairExists
                            ? 'border-[#3a3a3a] focus:ring-[#EFBD3A]'
                            : 'border-red-500 focus:ring-red-500'
                        } focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-200 pl-12`}
                        placeholder="e.g., EUR/USD"
                      />
                      <svg className="w-5 h-5 text-[#FEFEFF]/40 absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    {forexPairError ? (
                      <p className="mt-2 text-sm text-red-400 flex items-center gap-2">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        {forexPairError}
                      </p>
                    ) : (
                      <p className="mt-2 text-sm text-[#FEFEFF]/60">
                        Enter the currency pair you want to trade (e.g., EUR/USD, GBP/JPY)
                      </p>
                    )}
                  </div>
                </div>

                {/* Right Column - Information Cards */}
                <div className="space-y-6">
                  {/* Popular Pairs */}
                  <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                    <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                      Popular Currency Pairs
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      {['EUR/USD', 'GBP/USD', 'USD/JPY', 'AUD/USD', 'USD/CAD', 'USD/CHF'].map((pair) => (
                        <button
                          key={pair}
                          onClick={() => setForexPair(pair)}
                          className="text-left p-3 bg-[#1a1a1a] rounded-lg border border-[#3a3a3a] hover:border-[#EFBD3A]/50 transition-all duration-200 group"
                        >
                          <div className="text-[#FEFEFF] font-medium group-hover:text-[#EFBD3A]">{pair}</div>
                          <div className="text-xs text-[#FEFEFF]/60">Major Pair</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Strategy Tips */}
                  <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                    <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4 flex items-center gap-2">
                      <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      Strategy Tips
                    </h3>
                    <div className="space-y-3 text-sm text-[#FEFEFF]/70">
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#EFBD3A] rounded-full mt-2 flex-shrink-0"></div>
                        <p>Choose pairs you understand and have good liquidity</p>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#EFBD3A] rounded-full mt-2 flex-shrink-0"></div>
                        <p>Major pairs typically have tighter spreads and more data</p>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#EFBD3A] rounded-full mt-2 flex-shrink-0"></div>
                        <p>Consider the trading sessions that align with your pair</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* What's Next Preview */}
            <div className="bg-[#1a1a1a] rounded-xl p-8 border border-[#3a3a3a]">
              <div className="flex items-center gap-3 mb-6">
                <svg className="w-6 h-6 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3-3 3m-6-3h12" />
                </svg>
                <h2 className="text-2xl font-bold text-[#FEFEFF]">What's Next?</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                      <span className="text-[#EFBD3A] font-bold text-sm">2</span>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF]">Trading Sessions</h3>
                  </div>
                  <p className="text-[#FEFEFF]/60 text-sm">
                    Choose which market sessions to trade during - London, New York, Tokyo, or Sydney.
                  </p>
                </div>

                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                      <span className="text-[#EFBD3A] font-bold text-sm">3</span>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF]">Technical Indicators</h3>
                  </div>
                  <p className="text-[#FEFEFF]/60 text-sm">
                    Add technical indicators like Moving Averages, RSI, MACD, and Bollinger Bands.
                  </p>
                </div>

                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                      <span className="text-[#EFBD3A] font-bold text-sm">4-7</span>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF]">Strategy Rules</h3>
                  </div>
                  <p className="text-[#FEFEFF]/60 text-sm">
                    Define entry/exit conditions, risk management, and backtest your strategy.
                  </p>
                </div>
              </div>

              <div className="mt-8 p-6 bg-[#2a2a2a] rounded-lg border border-[#3a3a3a]">
                <div className="flex items-center gap-3 mb-4">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-lg font-semibold text-[#FEFEFF]">Strategy Building Process</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-[#FEFEFF]/70">
                  <div>
                    <h4 className="text-[#FEFEFF] font-medium mb-2">📊 Technical Analysis</h4>
                    <p>Build your strategy using proven technical indicators and price action patterns.</p>
                  </div>
                  <div>
                    <h4 className="text-[#FEFEFF] font-medium mb-2">⚡ Automated Trading</h4>
                    <p>Deploy your strategy as an automated trading agent with real-time execution.</p>
                  </div>
                  <div>
                    <h4 className="text-[#FEFEFF] font-medium mb-2">🛡️ Risk Management</h4>
                    <p>Set stop losses, take profits, and position sizing rules to protect your capital.</p>
                  </div>
                  <div>
                    <h4 className="text-[#FEFEFF] font-medium mb-2">📈 Backtesting</h4>
                    <p>Test your strategy against historical data to validate its performance.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center mt-8">
              <div className="text-sm text-[#FEFEFF]/60">
                Step 1 of 7 - Basic Information
              </div>
              <button
                onClick={() => handleStepChange(2)}
                disabled={!strategyName || !forexPair || !isForexPairValid || !isForexPairExists}
                className={`px-8 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-3 ${
                  (!strategyName || !forexPair || !isForexPairValid || !isForexPairExists)
                    ? 'bg-[#3a3a3a] text-[#FEFEFF]/40 cursor-not-allowed'
                    : 'bg-[#EFBD3A] text-[#0A0B0B] hover:bg-[#EFBD3A]/90 shadow-lg hover:shadow-xl transform hover:scale-105'
                }`}
              >
                Continue to Trading Sessions
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 2: Time Settings */}
        {currentStep === 2 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h2 className="text-2xl font-bold text-[#FEFEFF]">Trading Sessions</h2>
              </div>

              {/* Trading Sessions Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {tradingSessions.filter(session => session.name !== 'All').map((session) => {
                  const isSelected = selectedTradingSessions.includes(session.timezone);
                  const volatility = getSessionVolatility(session.name);

                  return (
                    <motion.div
                      key={session.name}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleSessionCardClick(session.timezone)}
                      className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
                        isSelected
                          ? 'border-[#EFBD3A] bg-[#EFBD3A]/10'
                          : 'border-[#3a3a3a] bg-[#2a2a2a] hover:border-[#4a4a4a]'
                      }`}
                    >
                      {/* Selection indicator */}
                      {isSelected && (
                        <div className="absolute top-2 right-2">
                          <svg className="w-5 h-5 text-[#EFBD3A]" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}

                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-[#FEFEFF] mb-3">{session.name}</h3>
                        <div className={`text-sm font-medium ${getVolatilityColor(volatility)}`}>
                          {volatility}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Select All Option */}
              <div className="mb-4">
                <motion.div
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                  onClick={() => handleSelectAllSessions()}
                  className={`cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                    selectedTradingSessions.includes('All')
                      ? 'border-[#EFBD3A] bg-[#EFBD3A]/10'
                      : 'border-[#3a3a3a] bg-[#2a2a2a] hover:border-[#4a4a4a]'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-[#FEFEFF] font-medium">All Trading Sessions</span>
                    {selectedTradingSessions.includes('All') && (
                      <svg className="w-5 h-5 text-[#EFBD3A]" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </motion.div>
              </div>

              {/* Selected sessions display */}
              {selectedTradingSessions.length > 0 && !selectedTradingSessions.includes('All') && (
                <div className="bg-[#2a2a2a] rounded-lg p-3 border border-[#3a3a3a]">
                  <p className="text-sm text-[#FEFEFF]/60 mb-2">Selected Sessions:</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedTradingSessions.map(timezone => {
                      const session = tradingSessions.find(s => s.timezone === timezone);
                      return session ? (
                        <span key={timezone} className="px-2 py-1 bg-[#EFBD3A]/20 text-[#EFBD3A] rounded text-sm">
                          {session.name}
                        </span>
                      ) : null;
                    })}
                  </div>
                </div>
              )}

              {/* Timeframe */}
              <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3 mb-6">
                  <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <h3 className="text-xl font-bold text-[#FEFEFF]">Timeframe</h3>
                </div>

                {/* Timeframe Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
                  {timeframeOptions.map((tf) => {
                    const isSelected = timeframe === tf;
                    const timeframeInfo = getTimeframeInfo(tf);

                    return (
                      <motion.div
                        key={tf}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleTimeframeCardClick(tf)}
                        className={`relative cursor-pointer rounded-lg border-2 p-3 transition-all duration-200 ${
                          isSelected
                            ? 'border-[#EFBD3A] bg-[#EFBD3A]/10'
                            : 'border-[#3a3a3a] bg-[#2a2a2a] hover:border-[#4a4a4a]'
                        }`}
                      >
                        {/* Selection indicator */}
                        {isSelected && (
                          <div className="absolute top-1 right-1">
                            <svg className="w-4 h-4 text-[#EFBD3A]" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}

                        <div className="text-center">
                          <div className="text-lg font-bold text-[#FEFEFF] mb-1">{tf}</div>
                          <div className="text-xs text-[#FEFEFF]/60">{timeframeInfo.description}</div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>

                <p className="mt-4 text-sm text-[#FEFEFF]/60">
                  Choose the timeframe for your trading strategy analysis. Shorter timeframes provide more signals but may be noisier.
                </p>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(1)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(3)}
                className={buttonStyles.primary}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 3: Indicators */}
        {currentStep === 3 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Header */}
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3">
                <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <h2 className="text-2xl font-bold text-[#FEFEFF]">Technical Indicators</h2>
              </div>
            </div>

            {/* Split View Container */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Side - Indicator Selection */}
              <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-[#FEFEFF]">Choose an Indicator</h3>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-[#FEFEFF]/60">{supportedIndicators.length} available</span>
                  </div>
                </div>

                {/* Search and Filter */}
                <div className="mb-4 space-y-3">
                  <div className="relative">
                    <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <input
                      type="text"
                      placeholder="Search indicators..."
                      value={indicatorSearch}
                      onChange={(e) => setIndicatorSearch(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  {/* Category Filter */}
                  <div className="flex flex-wrap gap-2">
                    {['All', 'Trend', 'Momentum', 'Volatility'].map((category) => (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category)}
                        className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 ${
                          selectedCategory === category
                            ? 'bg-[#EFBD3A] text-[#0A0B0B]'
                            : 'bg-[#2a2a2a] text-[#FEFEFF]/80 hover:bg-[#3a3a3a]'
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Indicators List */}
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {filteredIndicators.map((indicator) => {
                    const isSelected = newIndicator.type === indicator.name;
                    const indicatorInfo = getIndicatorInfo(indicator.name);

                    return (
                      <motion.div
                        key={indicator.name}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                        onClick={() => handleIndicatorCardClick(indicator.name)}
                        className={`flex items-center gap-4 p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                          isSelected
                            ? 'border-[#EFBD3A] bg-[#EFBD3A]/10'
                            : 'border-[#3a3a3a] bg-[#2a2a2a] hover:border-[#4a4a4a] hover:bg-[#2a2a2a]/80'
                        }`}
                      >
                        <div className="text-2xl">{indicatorInfo.icon}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-[#FEFEFF]">{indicator.name}</h4>
                            <span className={`text-xs px-2 py-0.5 rounded-full ${getCategoryColor(indicatorInfo.category)} bg-current/10`}>
                              {indicatorInfo.category}
                            </span>
                          </div>
                          <p className="text-sm text-[#FEFEFF]/70">{indicatorInfo.description}</p>
                        </div>
                        {isSelected && (
                          <div className="text-[#EFBD3A]">
                            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>

                {filteredIndicators.length === 0 && (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">🔍</div>
                    <p className="text-[#FEFEFF]/60">No indicators found matching your search</p>
                  </div>
                )}
              </div>

                {/* Configuration Section - Only show when indicator is selected */}
                {newIndicator.type && (
                  <div className="space-y-6">
                    {/* Source Selection */}
                    <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                      <h4 className="text-lg font-semibold text-[#FEFEFF] mb-4">Data Source</h4>

                      {/* Price Data Sources */}
                      <div className="mb-4">
                        <h5 className="text-sm font-medium text-[#FEFEFF]/80 mb-2">📈 Price Data</h5>
                        <div className="grid grid-cols-2 gap-2">
                          {getSourceOptions().filter(opt => ['close', 'open', 'high', 'low', 'volume'].includes(opt.value)).map((option) => {
                            const isSelected = newIndicator.source === option.value;

                            return (
                              <motion.button
                                key={option.value}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => setNewIndicator(prev => ({ ...prev, source: option.value }))}
                                className={`p-2 rounded-lg border text-sm font-medium transition-all duration-200 ${
                                  isSelected
                                    ? 'border-[#EFBD3A] bg-[#EFBD3A]/10 text-[#EFBD3A]'
                                    : 'border-[#4a4a4a] bg-[#3a3a3a] text-[#FEFEFF] hover:border-[#5a5a5a] hover:bg-[#4a4a4a]'
                                }`}
                              >
                                {option.label}
                              </motion.button>
                            );
                          })}
                        </div>
                      </div>

                      {/* Indicator Sources */}
                      {selectedIndicators.length > 0 && (
                        <div>
                          <h5 className="text-sm font-medium text-[#FEFEFF]/80 mb-2">📊 Existing Indicators</h5>
                          <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                            {getSourceOptions().filter(opt => !['close', 'open', 'high', 'low', 'volume'].includes(opt.value)).map((option) => {
                              const isSelected = newIndicator.source === option.value;

                              return (
                                <motion.button
                                  key={option.value}
                                  whileHover={{ scale: 1.01 }}
                                  whileTap={{ scale: 0.99 }}
                                  onClick={() => setNewIndicator(prev => ({ ...prev, source: option.value }))}
                                  className={`p-2 rounded-lg border text-sm text-left transition-all duration-200 ${
                                    isSelected
                                      ? 'border-[#EFBD3A] bg-[#EFBD3A]/10 text-[#EFBD3A]'
                                      : 'border-[#4a4a4a] bg-[#3a3a3a] text-[#FEFEFF] hover:border-[#5a5a5a] hover:bg-[#4a4a4a]'
                                  }`}
                                >
                                  {option.label}
                                </motion.button>
                              );
                            })}
                          </div>
                        </div>
                      )}

                      {selectedIndicators.length === 0 && (
                        <div className="text-center py-4 border-2 border-dashed border-[#4a4a4a] rounded-lg">
                          <div className="text-2xl mb-2">💡</div>
                          <p className="text-sm text-[#FEFEFF]/60">
                            Add indicators to use them as data sources
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Parameters Configuration */}
                    <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                      <h4 className="text-lg font-semibold text-[#FEFEFF] mb-4">Parameters</h4>
                      <div className="grid grid-cols-1 gap-4">
                        {Object.entries(newIndicator.parameters).map(([key, value]) => (
                          <div key={key} className="space-y-2">
                            <label className="block text-sm font-medium text-[#FEFEFF]/80 capitalize">
                              {key}
                            </label>
                            <input
                              type="number"
                              value={value}
                              onChange={(e) => handleNewIndicatorParamChange(key, e.target.value)}
                              className="w-full px-3 py-2 bg-[#1a1a1a] text-[#FEFEFF] rounded-lg border border-[#4a4a4a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                              placeholder={`Enter ${key}`}
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Add Button */}
                    <div className="flex justify-center">
                      <button
                        onClick={addNewIndicator}
                        className={buttonStyles.add}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add {newIndicator.type} to Strategy
                      </button>
                    </div>
                  </div>
                )}

                {/* Help text when no indicator selected */}
                {!newIndicator.type && (
                  <div className="flex items-center justify-center min-h-[300px]">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📊</div>
                      <p className="text-[#FEFEFF]/60 text-lg">Select an indicator above to configure its parameters</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Right Side - Selected Indicators */}
              <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3 mb-6">
                  <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-xl font-bold text-[#FEFEFF]">Selected Indicators</h3>
                  {selectedIndicators.length > 0 && (
                    <span className="bg-[#EFBD3A] text-[#0A0B0B] px-2 py-1 rounded-full text-sm font-medium">
                      {selectedIndicators.length}
                    </span>
                  )}
                </div>

                {selectedIndicators.length > 0 ? (
                  <div className="grid grid-cols-1 gap-4">
                    {selectedIndicators.map((indicator) => {
                      const indicatorInfo = getIndicatorInfo(indicator.type);

                      return (
                        <motion.div
                          key={indicator.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.9 }}
                          className="relative group bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] p-4 hover:border-[#EFBD3A]/30 transition-all duration-200"
                        >
                          {/* Remove button */}
                          <button
                            onClick={() => removeSelectedIndicator(indicator.id)}
                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-400"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>

                          <div className="flex items-start gap-3">
                            <div className="text-2xl">{indicatorInfo.icon}</div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="text-lg font-semibold text-[#FEFEFF]">{indicator.type}</h4>
                                <span className={`text-xs px-2 py-1 rounded-full ${getCategoryColor(indicatorInfo.category)} bg-current/10`}>
                                  {indicatorInfo.category}
                                </span>
                              </div>

                              <div className="space-y-1 text-sm">
                                <div className="text-[#FEFEFF]/80">
                                  <span className="font-medium">Parameters:</span>
                                  <span className="ml-2">
                                    {Object.entries(indicator.parameters)
                                      .map(([k, v]) => `${k}: ${v}`)
                                      .join(", ")}
                                  </span>
                                </div>

                                <div className="text-[#EFBD3A]/80">
                                  <span className="font-medium">Source:</span>
                                  <span className="ml-2">
                                    {indicator.source === "volume" ? "Volume" :
                                     indicator.source === "open" ? "Open" :
                                     indicator.source === "close" ? "Close" :
                                     indicator.source === "high" ? "High" :
                                     indicator.source === "low" ? "Low" :
                                     selectedIndicators.find(ind => ind.id === indicator.source)?.type || "Price"}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="flex items-center justify-center min-h-[400px]">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📊</div>
                      <h4 className="text-lg font-semibold text-[#FEFEFF] mb-2">No Indicators Added</h4>
                      <p className="text-[#FEFEFF]/60">
                        Select and configure indicators from the left panel to add them to your strategy
                      </p>
                    </div>
                  </div>
                )}
              </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(2)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(4)}
                disabled={selectedIndicators.length === 0}
                className={`${buttonStyles.primary} ${
                  selectedIndicators.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 4: Entry Rules */}
        {currentStep === 4 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v-1a2 2 0 012-2z" />
                </svg>
                <h2 className="text-2xl font-bold text-[#FEFEFF]">Entry Rules</h2>
                {entryRules.length > 0 && (
                  <span className="bg-[#EFBD3A] text-[#0A0B0B] px-2 py-1 rounded-full text-sm font-medium">
                    {entryRules.length} rule{entryRules.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>

              {/* Entry Rules List */}
              {entryRules.length > 0 ? (
                <div className="space-y-6">
                  {/* Long Rules */}
                  {entryRules.filter(r => r.tradeType === 'long').length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-green-400">Long Entry Rules</h3>
                        {entryRules.filter(r => r.tradeType === 'long').length > 1 && (
                          <span className="text-sm text-[#FEFEFF]/60">
                            (Combined with {entryBuyGroupOperator})
                          </span>
                        )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {entryRules.filter(r => r.tradeType === 'long').map((rule, index) => (
                          <motion.div
                            key={rule.id}
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.9 }}
                            className="relative group bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] p-4 hover:border-green-500/30 transition-all duration-200"
                          >
                            <button
                              onClick={() => removeEntryRule(entryRules.findIndex(r => r.id === rule.id))}
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-400"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>

                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-sm font-medium">
                                  Long
                                </span>
                                <span className="text-xs text-[#FEFEFF]/60">Entry #{entryRules.filter(r => r.tradeType === 'long').findIndex(r => r.id === rule.id) + 1}</span>
                              </div>

                              <div className="text-sm space-y-1">
                                <div className="flex items-center gap-2 flex-wrap">
                                  <span className="font-medium text-[#FEFEFF]">
                                    {getIndicatorLabel(rule.indicator1, rule)}
                                  </span>
                                  <span className="text-[#EFBD3A] font-medium">{rule.operator}</span>
                                  <span className="font-medium text-[#FEFEFF]">
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </span>
                                </div>
                                {rule.indicator1 === 'price' && (
                                  <div className="text-[#FEFEFF]/60 text-xs">
                                    📊 Reference: {rule.barRef}
                                  </div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Short Rules */}
                  {entryRules.filter(r => r.tradeType === 'short').length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-red-400">Short Entry Rules</h3>
                        {entryRules.filter(r => r.tradeType === 'short').length > 1 && (
                          <span className="text-sm text-[#FEFEFF]/60">
                            (Combined with {entrySellGroupOperator})
                          </span>
                        )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {entryRules.filter(r => r.tradeType === 'short').map((rule, index) => (
                          <motion.div
                            key={rule.id}
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.9 }}
                            className="relative group bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] p-4 hover:border-red-500/30 transition-all duration-200"
                          >
                            <button
                              onClick={() => removeEntryRule(entryRules.findIndex(r => r.id === rule.id))}
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-400"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>

                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded text-sm font-medium">
                                  Short
                                </span>
                                <span className="text-xs text-[#FEFEFF]/60">Entry #{entryRules.filter(r => r.tradeType === 'short').findIndex(r => r.id === rule.id) + 1}</span>
                              </div>

                              <div className="text-sm space-y-1">
                                <div className="flex items-center gap-2 flex-wrap">
                                  <span className="font-medium text-[#FEFEFF]">
                                    {getIndicatorLabel(rule.indicator1, rule)}
                                  </span>
                                  <span className="text-[#EFBD3A] font-medium">{rule.operator}</span>
                                  <span className="font-medium text-[#FEFEFF]">
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </span>
                                </div>
                                {rule.indicator1 === 'price' && (
                                  <div className="text-[#FEFEFF]/60 text-xs">
                                    📊 Reference: {rule.barRef}
                                  </div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🎯</div>
                  <h3 className="text-xl font-semibold text-[#FEFEFF] mb-2">No Entry Rules Yet</h3>
                  <p className="text-[#FEFEFF]/60 mb-6">Add your first entry rule to define when to enter trades</p>
                </div>
              )}

              {/* Group Operators */}
              {(entryRules.filter(r => r.tradeType === 'long').length > 1 || entryRules.filter(r => r.tradeType === 'short').length > 1) && (
                <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                  <h4 className="text-lg font-semibold text-[#FEFEFF] mb-4">Rule Combination Logic</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {entryRules.filter(r => r.tradeType === 'long').length > 1 && (
                      <div className="space-y-3">
                        <h5 className="text-sm font-medium text-green-400">Long Entry Rules</h5>
                        <div className="flex gap-3">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entryBuyGroupOperator === "AND"}
                              onChange={() => setEntryBuyGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">AND (All must be true)</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entryBuyGroupOperator === "OR"}
                              onChange={() => setEntryBuyGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">OR (Any can be true)</span>
                          </label>
                        </div>
                      </div>
                    )}

                    {entryRules.filter(r => r.tradeType === 'short').length > 1 && (
                      <div className="space-y-3">
                        <h5 className="text-sm font-medium text-red-400">Short Entry Rules</h5>
                        <div className="flex gap-3">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entrySellGroupOperator === "AND"}
                              onChange={() => setEntrySellGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">AND (All must be true)</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={entrySellGroupOperator === "OR"}
                              onChange={() => setEntrySellGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">OR (Any can be true)</span>
                          </label>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Add Rule Button */}
              <div className="flex justify-center">
                <button
                  onClick={addEntryRule}
                  className={buttonStyles.add}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Add Entry Rule
                </button>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(3)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(5)}
                disabled={entryRules.length === 0}
                className={`${buttonStyles.primary} ${
                  entryRules.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 5: Exit Rules */}
        {currentStep === 5 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <h2 className="text-2xl font-bold text-[#FEFEFF]">Exit Rules</h2>
                {exitRules.length > 0 && (
                  <span className="bg-[#EFBD3A] text-[#0A0B0B] px-2 py-1 rounded-full text-sm font-medium">
                    {exitRules.length} rule{exitRules.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>

              {/* Exit Rules List */}
              {exitRules.length > 0 ? (
                <div className="space-y-6">
                  {/* Long Rules */}
                  {exitRules.filter(r => r.tradeType === 'long').length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-green-400">Long Exit Rules</h3>
                        {exitRules.filter(r => r.tradeType === 'long').length > 1 && (
                          <span className="text-sm text-[#FEFEFF]/60">
                            (Combined with {exitBuyGroupOperator})
                          </span>
                        )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {exitRules.filter(r => r.tradeType === 'long').map((rule, index) => (
                          <motion.div
                            key={rule.id}
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.9 }}
                            className="relative group bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] p-4 hover:border-green-500/30 transition-all duration-200"
                          >
                            <button
                              onClick={() => removeExitRule(exitRules.findIndex(r => r.id === rule.id))}
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-400"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>

                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-sm font-medium">
                                  Long
                                </span>
                                <span className="text-xs text-[#FEFEFF]/60">Exit #{exitRules.filter(r => r.tradeType === 'long').findIndex(r => r.id === rule.id) + 1}</span>
                              </div>

                              <div className="text-sm space-y-1">
                                <div className="flex items-center gap-2 flex-wrap">
                                  <span className="font-medium text-[#FEFEFF]">
                                    {getIndicatorLabel(rule.indicator1, rule, false)}
                                  </span>
                                  <span className="text-[#EFBD3A] font-medium">{rule.operator}</span>
                                  <span className="font-medium text-[#FEFEFF]">
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </span>
                                </div>
                                {rule.indicator1 === 'price' && (
                                  <div className="text-[#FEFEFF]/60 text-xs">
                                    📊 Reference: {rule.barRef}
                                  </div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Short Rules */}
                  {exitRules.filter(r => r.tradeType === 'short').length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-red-400">Short Exit Rules</h3>
                        {exitRules.filter(r => r.tradeType === 'short').length > 1 && (
                          <span className="text-sm text-[#FEFEFF]/60">
                            (Combined with {exitSellGroupOperator})
                          </span>
                        )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {exitRules.filter(r => r.tradeType === 'short').map((rule, index) => (
                          <motion.div
                            key={rule.id}
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.9 }}
                            className="relative group bg-[#2a2a2a] rounded-lg border border-[#3a3a3a] p-4 hover:border-red-500/30 transition-all duration-200"
                          >
                            <button
                              onClick={() => removeExitRule(exitRules.findIndex(r => r.id === rule.id))}
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-full bg-red-500/20 hover:bg-red-500/30 text-red-400"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>

                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded text-sm font-medium">
                                  Short
                                </span>
                                <span className="text-xs text-[#FEFEFF]/60">Exit #{exitRules.filter(r => r.tradeType === 'short').findIndex(r => r.id === rule.id) + 1}</span>
                              </div>

                              <div className="text-sm space-y-1">
                                <div className="flex items-center gap-2 flex-wrap">
                                  <span className="font-medium text-[#FEFEFF]">
                                    {getIndicatorLabel(rule.indicator1, rule, false)}
                                  </span>
                                  <span className="text-[#EFBD3A] font-medium">{rule.operator}</span>
                                  <span className="font-medium text-[#FEFEFF]">
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </span>
                                </div>
                                {rule.indicator1 === 'price' && (
                                  <div className="text-[#FEFEFF]/60 text-xs">
                                    📊 Reference: {rule.barRef}
                                  </div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🚪</div>
                  <h3 className="text-xl font-semibold text-[#FEFEFF] mb-2">No Exit Rules Yet</h3>
                  <p className="text-[#FEFEFF]/60 mb-6">Add your first exit rule to define when to close trades</p>
                </div>
              )}

              {/* Group Operators */}
              {(exitRules.filter(r => r.tradeType === 'long').length > 1 || exitRules.filter(r => r.tradeType === 'short').length > 1) && (
                <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                  <h4 className="text-lg font-semibold text-[#FEFEFF] mb-4">Rule Combination Logic</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {exitRules.filter(r => r.tradeType === 'long').length > 1 && (
                      <div className="space-y-3">
                        <h5 className="text-sm font-medium text-green-400">Long Exit Rules</h5>
                        <div className="flex gap-3">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitBuyGroupOperator === "AND"}
                              onChange={() => setExitBuyGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">AND (All must be true)</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitBuyGroupOperator === "OR"}
                              onChange={() => setExitBuyGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">OR (Any can be true)</span>
                          </label>
                        </div>
                      </div>
                    )}

                    {exitRules.filter(r => r.tradeType === 'short').length > 1 && (
                      <div className="space-y-3">
                        <h5 className="text-sm font-medium text-red-400">Short Exit Rules</h5>
                        <div className="flex gap-3">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitSellGroupOperator === "AND"}
                              onChange={() => setExitSellGroupOperator("AND")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">AND (All must be true)</span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="radio"
                              checked={exitSellGroupOperator === "OR"}
                              onChange={() => setExitSellGroupOperator("OR")}
                              className="form-radio text-[#EFBD3A] focus:ring-[#EFBD3A]"
                            />
                            <span className="text-[#FEFEFF] text-sm">OR (Any can be true)</span>
                          </label>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Add Rule Button */}
              <div className="flex justify-center">
                <button
                  onClick={addExitRule}
                  className={buttonStyles.add}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Add Exit Rule
                </button>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(4)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(6)}
                disabled={exitRules.length === 0}
                className={`${buttonStyles.primary} ${
                  exitRules.length === 0 ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
                      </div>
          </motion.div>
        )}

        {/* Step 6: Risk Management */}
        {currentStep === 6 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <h2 className="text-2xl font-bold text-[#FEFEFF]">Risk Management</h2>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Risk Percentage Card */}
                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] hover:border-[#EFBD3A]/30 transition-all duration-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-[#FEFEFF]">Risk Percentage</h3>
                      <p className="text-sm text-[#FEFEFF]/60">How much to risk per trade</p>
                    </div>
                    <div className="relative group">
                      <div className="cursor-help w-5 h-5 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                        <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="absolute z-10 w-72 p-3 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#4a4a4a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 right-0 top-6">
                        <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">How it works:</span></p>
                        <p>This percentage determines how much of your account you're willing to risk on each trade. For example, with a 2% risk on a $10,000 account, you would risk $200 per trade.</p>
                        <p className="mt-2">Your position size will be calculated based on this percentage and your stop loss distance.</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <label htmlFor="riskPercentage" className="block text-sm font-medium text-[#FEFEFF]/80">
                      Risk Percentage (1-100%)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="riskPercentage"
                        value={riskPercentage}
                        onChange={handleRiskPercentageChange}
                        min="0"
                        max="100"
                        step="0.1"
                        className="w-full px-4 py-3 bg-[#1a1a1a] text-[#FEFEFF] rounded-lg border border-[#4a4a4a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                        placeholder="e.g., 2.0"
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#FEFEFF]/40 text-sm">
                        %
                      </div>
                    </div>
                    {riskPercentage && (
                      <div className={`text-sm p-2 rounded ${
                        Number(riskPercentage) > 2
                          ? 'bg-red-500/20 text-red-400'
                          : Number(riskPercentage) >= 1
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {Number(riskPercentage) > 2
                          ? '⚠️ High risk - Consider reducing to 1-2%'
                          : Number(riskPercentage) >= 1
                            ? '✅ Good risk level for most traders'
                            : '💡 Very conservative - Consider 1-2% for better growth'
                        }
                      </div>
                    )}
                  </div>
                </div>

                {/* Risk-Reward Ratio Card */}
                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] hover:border-[#EFBD3A]/30 transition-all duration-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-[#FEFEFF]">Risk-Reward Ratio</h3>
                      <p className="text-sm text-[#FEFEFF]/60">Profit potential vs risk</p>
                    </div>
                    <div className="relative group">
                      <div className="cursor-help w-5 h-5 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                        <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="absolute z-10 w-72 p-3 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#4a4a4a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 right-0 top-6">
                        <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">How it works:</span></p>
                        <p>This ratio determines how your take profit level is calculated relative to your stop loss. For example, with a 2:1 ratio:</p>
                        <ul className="list-disc list-inside mt-1 mb-1">
                          <li>If your stop loss is 50 pips, your take profit will be 100 pips</li>
                          <li>If you risk $100 on a trade, your potential profit will be $200</li>
                        </ul>
                        <p className="mt-1">A higher ratio improves your chances of long-term profitability even with a lower win rate.</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <label htmlFor="riskRewardRatio" className="block text-sm font-medium text-[#FEFEFF]/80">
                      Risk-Reward Ratio
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="riskRewardRatio"
                        value={riskRewardRatio}
                        onChange={handleRiskRewardRatioChange}
                        min="0"
                        step="0.1"
                        className="w-full px-4 py-3 bg-[#1a1a1a] text-[#FEFEFF] rounded-lg border border-[#4a4a4a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                        placeholder="e.g., 2.0"
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#FEFEFF]/40 text-sm">
                        :1
                      </div>
                    </div>
                    {riskRewardRatio && (
                      <div className={`text-sm p-2 rounded ${
                        Number(riskRewardRatio) >= 2
                          ? 'bg-green-500/20 text-green-400'
                          : Number(riskRewardRatio) >= 1.5
                            ? 'bg-blue-500/20 text-blue-400'
                            : 'bg-yellow-500/20 text-yellow-400'
                      }`}>
                        {Number(riskRewardRatio) >= 2
                          ? '🎯 Excellent ratio for long-term profitability'
                          : Number(riskRewardRatio) >= 1.5
                            ? '✅ Good ratio - allows profit with <60% win rate'
                            : '💡 Consider increasing to 1.5+ for better results'
                        }
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Stop Loss Determination */}
              <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3 mb-6">
                  <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  <h3 className="text-xl font-bold text-[#FEFEFF]">Stop Loss Determination</h3>
                  <div className="relative group">
                    <div className="cursor-help w-5 h-5 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                      <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="absolute z-10 w-80 p-3 bg-[#2a2a2a] rounded-lg shadow-lg border border-[#4a4a4a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 right-0 top-6">
                      <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">How it works:</span></p>
                      <p className="mb-2">Choose how your stop loss will be calculated:</p>
                      <ul className="list-disc list-inside space-y-1 mb-2">
                        <li><span className="font-semibold">Fixed Pips:</span> Sets a fixed distance for stop loss. Your lot size will be calculated based on your risk percentage.</li>
                        <li><span className="font-semibold">Indicator Based:</span> Uses technical indicators to dynamically set stop loss levels based on market conditions.</li>
                        <li><span className="font-semibold">Risk Based:</span> Uses a fixed lot size and calculates stop loss distance based on your risk percentage.</li>
                      </ul>
                      <p>Your take profit will be calculated using your risk-reward ratio multiplied by your stop loss distance.</p>
                    </div>
                  </div>
                </div>

                {/* Stop Loss Method Selection */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  {[
                    {
                      value: 'fixed',
                      title: 'Fixed Pips',
                      description: 'Set a fixed pip distance',
                      icon: '📏',
                      pros: 'Consistent & predictable',
                      cons: 'Doesn\'t adapt to volatility'
                    },
                    {
                      value: 'indicator',
                      title: 'Indicator Based',
                      description: 'Use technical indicators',
                      icon: '📊',
                      pros: 'Adapts to market conditions',
                      cons: 'More complex setup'
                    },
                    {
                      value: 'risk',
                      title: 'Risk Based',
                      description: 'Fixed lot size approach',
                      icon: '⚖️',
                      pros: 'Simple position sizing',
                      cons: 'Variable risk per trade'
                    }
                  ].map((method) => (
                    <motion.div
                      key={method.value}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleStopLossMethodChange({ target: { value: method.value } })}
                      className={`cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
                        stopLossMethod === method.value
                          ? 'border-[#EFBD3A] bg-[#EFBD3A]/10'
                          : 'border-[#3a3a3a] bg-[#2a2a2a] hover:border-[#4a4a4a]'
                      }`}
                    >
                      <div className="text-center">
                        <div className="text-3xl mb-2">{method.icon}</div>
                        <h4 className="text-lg font-semibold text-[#FEFEFF] mb-1">{method.title}</h4>
                        <p className="text-sm text-[#FEFEFF]/70 mb-3">{method.description}</p>
                        <div className="space-y-1 text-xs">
                          <div className="text-green-400">✓ {method.pros}</div>
                          <div className="text-yellow-400">⚠ {method.cons}</div>
                        </div>
                      </div>
                      {stopLossMethod === method.value && (
                        <div className="absolute top-2 right-2">
                          <svg className="w-5 h-5 text-[#EFBD3A]" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Method-specific Configuration */}
                {stopLossMethod && (
                  <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                    <h4 className="text-lg font-semibold text-[#FEFEFF] mb-4">
                      {stopLossMethod === 'fixed' && '📏 Fixed Pips Configuration'}
                      {stopLossMethod === 'indicator' && '📊 Indicator Configuration'}
                      {stopLossMethod === 'risk' && '⚖️ Risk-Based Configuration'}
                    </h4>

                    {/* Fixed Pips */}
                    {stopLossMethod === 'fixed' && (
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center gap-2 mb-3">
                            <label htmlFor="fixedPips" className="text-sm font-medium text-[#FEFEFF]">
                              Stop Loss Distance (Pips)
                            </label>
                            <div className="relative group">
                              <div className="cursor-help w-4 h-4 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                                <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <div className="absolute z-10 w-72 p-3 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#4a4a4a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                                <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">Fixed Pips Calculation:</span></p>
                                <ul className="list-disc list-inside space-y-1">
                                  <li><span className="font-semibold">Stop Loss:</span> Fixed distance of {fixedPips || "X"} pips from entry price</li>
                                  <li><span className="font-semibold">Take Profit:</span> {fixedPips || "X"} pips × {riskRewardRatio || "Y"} (risk-reward ratio)</li>
                                  <li><span className="font-semibold">Lot Size:</span> (Account Balance × Risk%) ÷ (Stop Loss in pips × Pip Value)</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                          <input
                            type="number"
                            id="fixedPips"
                            value={fixedPips}
                            onChange={handleFixedPipsChange}
                            min="1"
                            className="w-full px-4 py-3 bg-[#1a1a1a] text-[#FEFEFF] rounded-lg border border-[#4a4a4a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                            placeholder="e.g., 50"
                          />
                          {fixedPips && riskRewardRatio && (
                            <div className="mt-2 p-3 bg-blue-500/20 border border-blue-500/30 rounded-lg">
                              <div className="text-sm text-blue-300">
                                📊 <strong>Calculation Preview:</strong><br/>
                                Stop Loss: {fixedPips} pips | Take Profit: {(fixedPips * riskRewardRatio).toFixed(1)} pips
                              </div>
                            </div>
                          )}
                          {riskWarning && (
                            <div className="mt-2 p-3 bg-yellow-900/50 border border-yellow-600/50 rounded-lg">
                              <div className="flex items-start">
                                <div className="flex-shrink-0">
                                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                <div className="ml-3">
                                  <p className="text-sm text-yellow-300">{riskWarning}</p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Indicator Based */}
                    {stopLossMethod === 'indicator' && (
                      <div className="space-y-6">
                        {/* Indicator Selection */}
                        <div>
                          <div className="flex items-center gap-2 mb-4">
                            <label className="text-sm font-medium text-[#FEFEFF]">
                              Select Stop Loss Indicator
                            </label>
                            <div className="relative group">
                              <div className="cursor-help w-4 h-4 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                                <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <div className="absolute z-10 w-80 p-3 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#4a4a4a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                                <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">Indicator-Based Calculations:</span></p>
                                <ul className="list-disc list-inside space-y-1">
                                  <li><span className="font-semibold">ATR:</span> Stop loss = Entry price ± (ATR value × multiplier)</li>
                                  <li><span className="font-semibold">Bollinger Bands:</span> Stop loss = Lower/Upper band value</li>
                                  <li><span className="font-semibold">Support & Resistance:</span> Stop loss = Nearest support/resistance level</li>
                                  <li><span className="font-semibold">Take Profit:</span> Distance to stop loss × risk-reward ratio</li>
                                  <li><span className="font-semibold">Lot Size:</span> (Account Balance × Risk%) ÷ (Stop Loss distance in pips × Pip Value)</li>
                                </ul>
                              </div>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {[
                              {
                                value: 'atr',
                                title: 'ATR',
                                description: 'Average True Range',
                                icon: '📈',
                                benefit: 'Adapts to volatility'
                              },
                              {
                                value: 'bollinger',
                                title: 'Bollinger Bands',
                                description: 'Dynamic support/resistance',
                                icon: '📊',
                                benefit: 'Market-based levels'
                              },
                              {
                                value: 'support_resistance',
                                title: 'Support & Resistance',
                                description: 'Key price levels',
                                icon: '🎯',
                                benefit: 'Strong market levels'
                              }
                            ].map((indicator) => (
                              <motion.div
                                key={indicator.value}
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                onClick={() => handleIndicatorBasedSLChange({ target: { value: indicator.value } })}
                                className={`cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
                                  indicatorBasedSL === indicator.value
                                    ? 'border-[#EFBD3A] bg-[#EFBD3A]/10'
                                    : 'border-[#4a4a4a] bg-[#3a3a3a] hover:border-[#5a5a5a]'
                                }`}
                              >
                                <div className="text-center">
                                  <div className="text-2xl mb-2">{indicator.icon}</div>
                                  <h5 className="font-semibold text-[#FEFEFF] mb-1">{indicator.title}</h5>
                                  <p className="text-sm text-[#FEFEFF]/70 mb-2">{indicator.description}</p>
                                  <div className="text-xs text-green-400">✓ {indicator.benefit}</div>
                                </div>
                                {indicatorBasedSL === indicator.value && (
                                  <div className="absolute top-2 right-2">
                                    <svg className="w-4 h-4 text-[#EFBD3A]" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                  </div>
                                )}
                              </motion.div>
                            ))}
                          </div>
                        </div>

                      {/* Indicator Parameters */}
                      {indicatorBasedSL === 'atr' && (
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="atrPeriod" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              ATR Period
                            </label>
                            <input
                              type="number"
                              id="atrPeriod"
                              value={indicatorParams.period || 14}
                              onChange={(e) => handleIndicatorParamChange('period', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="atrMultiplier" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              ATR Multiplier
                            </label>
                            <input
                              type="number"
                              id="atrMultiplier"
                              value={indicatorParams.multiplier || 2}
                              onChange={(e) => handleIndicatorParamChange('multiplier', e.target.value)}
                              min="0.1"
                              step="0.1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <button
                            onClick={() => {
                              // Minimal logging for performance
                              if (process.env.NODE_ENV === 'development') {
                                console.log('Adding ATR indicator...');
                              }

                              handleStrategyModification();

                              // Batch all state updates for better performance
                              requestAnimationFrame(() => {
                                // Set the adding state to true to show loading state
                                setIsAddingAtrToChart(true);

                                // Create the ATR indicator object with a unique ID
                                const atrId = generateId();
                                const atrIndicator = {
                                  id: atrId,
                                  type: 'ATR',
                                  parameters: {
                                    period: indicatorParams.period || 14,
                                    multiplier: indicatorParams.multiplier || 2
                                  },
                                  source: 'price',
                                  displayPreference: null
                                };

                                // Store the pending indicator for the callback to use
                                setPendingAtrIndicator(atrIndicator);

                                // Batch indicator updates
                                setSelectedIndicators(prev => {
                                  // Remove existing ATR indicators from risk management and add new one
                                  const filtered = prev.filter(ind => !(ind.type === 'ATR' && ind.fromRiskManagement));
                                  return [...filtered, atrIndicator];
                                });

                                // Reset states after a brief delay
                                setTimeout(() => {
                                  setIsAtrAddedToChart(false);
                                  setIsAddingAtrToChart(false);
                                }, 100);
                              });
                            }}
                            disabled={isAtrAddedToChart || isAddingAtrToChart}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                              isAtrAddedToChart
                                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white opacity-80 cursor-not-allowed'
                                : isAddingAtrToChart
                                  ? 'bg-[#EFBD3A]/70 text-[#0A0B0B] cursor-wait'
                                  : 'bg-[#EFBD3A] text-[#0A0B0B] hover:bg-[#EFBD3A]/90'
                            }`}
                          >
                            {isAtrAddedToChart
                              ? 'Added to Chart'
                              : isAddingAtrToChart
                                ? 'Adding...'
                                : 'Add to Chart'
                            }
                          </button>
                        </div>
                      )}

                      {indicatorBasedSL === 'bollinger' && (
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="bbPeriod" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Period
                            </label>
                            <input
                              type="number"
                              id="bbPeriod"
                              value={indicatorParams.period || 20}
                              onChange={(e) => handleIndicatorParamChange('period', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="bbStdDev" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Standard Deviation
                            </label>
                            <input
                              type="number"
                              id="bbStdDev"
                              value={indicatorParams.stdDev || 2}
                              onChange={(e) => handleIndicatorParamChange('stdDev', e.target.value)}
                              min="0.1"
                              step="0.1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <button
                            onClick={() => {
                              // Minimal logging for performance
                              if (process.env.NODE_ENV === 'development') {
                                console.log('Adding Bollinger Bands indicator...');
                              }

                              handleStrategyModification();

                              // Check if Bollinger Bands is already added from step 3
                              const existingBollingerBands = selectedIndicators.some(
                                ind => ind.type === 'BollingerBands'
                              );

                              if (existingBollingerBands) {
                                setIsBollingerAddedToChart(true);
                                return;
                              }

                              // Batch all state updates for better performance
                              requestAnimationFrame(() => {
                                // Set the adding state to true to show loading state
                                setIsAddingBollingerToChart(true);

                                // Create the Bollinger Bands indicator object with a unique ID
                                const bbId = generateId();
                                const bbIndicator = {
                                  id: bbId,
                                  type: 'BollingerBands',
                                  parameters: {
                                    period: indicatorParams.period || 20,
                                    devfactor: indicatorParams.stdDev || 2,
                                    offset: 0
                                  },
                                  source: 'close',
                                  displayPreference: null,
                                  fromRiskManagement: true
                                };

                                // Store the pending indicator for the callback to use
                                setPendingBollingerIndicator(bbIndicator);

                                // Batch indicator updates
                                setSelectedIndicators(prev => {
                                  // Remove existing Bollinger Bands from risk management and add new one
                                  const filtered = prev.filter(ind => !(ind.type === 'BollingerBands' && ind.fromRiskManagement));
                                  return [...filtered, bbIndicator];
                                });

                                // Reset states after a brief delay
                                setTimeout(() => {
                                  setIsBollingerAddedToChart(false);
                                  setIsAddingBollingerToChart(false);
                                }, 100);
                              });
                            }}
                            disabled={isBollingerAddedToChart || isAddingBollingerToChart}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                              isBollingerAddedToChart
                                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white opacity-80 cursor-not-allowed'
                                : isAddingBollingerToChart
                                  ? 'bg-[#EFBD3A]/70 text-[#0A0B0B] cursor-wait'
                                  : 'bg-[#EFBD3A] text-[#0A0B0B] hover:bg-[#EFBD3A]/90'
                            }`}
                          >
                            {isBollingerAddedToChart
                              ? 'Added to Chart'
                              : isAddingBollingerToChart
                                ? 'Adding...'
                                : 'Add to Chart'
                            }
                          </button>
                        </div>
                      )}

                      {indicatorBasedSL === 'support_resistance' && (
                        <div className="space-y-4">
                          <div>
                            <label htmlFor="srLeft" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Left Lookback Periods
                            </label>
                            <input
                              type="number"
                              id="srLeft"
                              value={indicatorParams.left || getDefaultIndicatorParams('support_resistance').left}
                              onChange={(e) => handleIndicatorParamChange('left', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <div>
                            <label htmlFor="srRight" className="block text-sm font-medium text-[#FEFEFF] mb-2">
                              Right Lookback Periods
                            </label>
                            <input
                              type="number"
                              id="srRight"
                              value={indicatorParams.right || getDefaultIndicatorParams('support_resistance').right}
                              onChange={(e) => handleIndicatorParamChange('right', e.target.value)}
                              min="1"
                              className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                            />
                          </div>
                          <button
                            onClick={() => {
                              console.log('Adding Support & Resistance indicator...');

                              // Prevent multiple clicks
                              if (isAddingSRToChart) return;

                              handleStrategyModification();
                              setIsAddingSRToChart(true);

                              try {
                                // Create the Support & Resistance indicator object with a unique ID
                                const srId = generateId();
                                const srIndicator = {
                                  id: srId,
                                  type: 'SupportResistance',
                                  parameters: {
                                    left: parseInt(indicatorParams.left) || getDefaultIndicatorParams('support_resistance').left,
                                    right: parseInt(indicatorParams.right) || getDefaultIndicatorParams('support_resistance').right
                                  },
                                  source: 'close',
                                  displayPreference: 'new'
                                };

                                console.log('Created S&R indicator:', srIndicator);

                                // Store the pending indicator for the callback to use
                                setPendingSRIndicator(srIndicator);

                                // Update indicators synchronously
                                setSelectedIndicators(prev => {
                                  const filtered = prev.filter(ind => !(ind.type === 'SupportResistance' && ind.fromRiskManagement));
                                  const newIndicators = [...filtered, srIndicator];
                                  console.log('Updated selectedIndicators:', newIndicators);
                                  return newIndicators;
                                });

                                // Update button state
                                setTimeout(() => {
                                  setIsSRAddedToChart(true);
                                  setIsAddingSRToChart(false);
                                  console.log('S&R indicator added to chart');
                                }, 100);

                              } catch (error) {
                                console.error('Error adding S&R indicator:', error);
                                setIsAddingSRToChart(false);
                              }
                            }}
                            disabled={isSRAddedToChart || isAddingSRToChart}
                            className={`w-full px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                              isSRAddedToChart
                                ? 'bg-gradient-to-r from-green-500 to-green-600 text-white opacity-80 cursor-not-allowed'
                                : isAddingSRToChart
                                  ? 'bg-[#EFBD3A]/70 text-[#0A0B0B] cursor-wait'
                                  : 'bg-[#EFBD3A] text-[#0A0B0B] hover:bg-[#EFBD3A]/90'
                            }`}
                          >
                            {isSRAddedToChart
                              ? 'Added to Chart'
                              : isAddingSRToChart
                                ? 'Adding...'
                                : 'Add to Chart'
                            }
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                    {/* Risk Based */}
                    {stopLossMethod === 'risk' && (
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center gap-2 mb-3">
                            <label htmlFor="lotSize" className="text-sm font-medium text-[#FEFEFF]">
                              Fixed Lot Size
                            </label>
                            <div className="relative group">
                              <div className="cursor-help w-4 h-4 bg-[#EFBD3A]/20 rounded-full flex items-center justify-center text-[#EFBD3A]">
                                <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                              <div className="absolute z-10 w-72 p-3 bg-[#1a1a1a] rounded-lg shadow-lg border border-[#4a4a4a] text-[#FEFEFF]/80 text-sm opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 left-6 top-0">
                                <p className="mb-2"><span className="font-semibold text-[#EFBD3A]">Risk-Based Calculation:</span></p>
                                <ul className="list-disc list-inside space-y-1">
                                  <li><span className="font-semibold">Lot Size:</span> Fixed at {lotSize || "X"} lots</li>
                                  <li><span className="font-semibold">Stop Loss:</span> (Account Balance × Risk%) ÷ (Lot Size × Pip Value)</li>
                                  <li><span className="font-semibold">Take Profit:</span> Stop Loss distance × {riskRewardRatio || "Y"} (risk-reward ratio)</li>
                                </ul>
                                <p className="mt-2 text-xs">This method uses a fixed lot size and calculates the stop loss distance based on your risk percentage.</p>
                              </div>
                            </div>
                          </div>
                          <input
                            type="number"
                            id="lotSize"
                            value={lotSize}
                            onChange={handleLotSizeChange}
                            min="0.01"
                            step="0.01"
                            className="w-full px-4 py-3 bg-[#1a1a1a] text-[#FEFEFF] rounded-lg border border-[#4a4a4a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent transition-all duration-200"
                            placeholder="e.g., 0.1"
                          />
                          {lotSize && riskPercentage && (
                            <div className="mt-2 p-3 bg-purple-500/20 border border-purple-500/30 rounded-lg">
                              <div className="text-sm text-purple-300">
                                ⚖️ <strong>Fixed Position:</strong> {lotSize} lots per trade<br/>
                                Stop loss distance will vary based on your {riskPercentage}% risk setting
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Risk Management Tips */}
              <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3 mb-6">
                  <svg className="w-6 h-6 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  <h4 className="text-xl font-bold text-[#EFBD3A]">💡 Risk Management Insights</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Risk Percentage Insight */}
                  {riskPercentage && (
                    <div className={`p-4 rounded-lg border ${
                      Number(riskPercentage) > 2
                        ? 'bg-red-500/10 border-red-500/30'
                        : Number(riskPercentage) >= 1
                          ? 'bg-green-500/10 border-green-500/30'
                          : 'bg-yellow-500/10 border-yellow-500/30'
                    }`}>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-lg">💰</span>
                        <h5 className="font-semibold text-[#FEFEFF]">Risk Analysis</h5>
                      </div>
                      <p className="text-sm text-[#FEFEFF]/80">
                        Your {riskPercentage}% risk per trade {Number(riskPercentage) > 2
                          ? 'is quite aggressive. Consider reducing to 1-2% for better long-term sustainability.'
                          : Number(riskPercentage) >= 1
                            ? 'is well-balanced for most trading strategies.'
                            : 'is very conservative. You might consider 1-2% for better growth potential.'
                        }
                      </p>
                    </div>
                  )}

                  {/* Risk-Reward Insight */}
                  {riskRewardRatio && (
                    <div className={`p-4 rounded-lg border ${
                      Number(riskRewardRatio) >= 2
                        ? 'bg-green-500/10 border-green-500/30'
                        : Number(riskRewardRatio) >= 1.5
                          ? 'bg-blue-500/10 border-blue-500/30'
                          : 'bg-yellow-500/10 border-yellow-500/30'
                    }`}>
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-lg">📈</span>
                        <h5 className="font-semibold text-[#FEFEFF]">Profit Potential</h5>
                      </div>
                      <p className="text-sm text-[#FEFEFF]/80">
                        Your {riskRewardRatio}:1 ratio means you can be profitable with a {(100 / (Number(riskRewardRatio) + 1)).toFixed(0)}% win rate.
                        {Number(riskRewardRatio) >= 1.5
                          ? ' This gives you excellent profit potential!'
                          : ' Consider increasing to 1.5+ for better long-term results.'
                        }
                      </p>
                    </div>
                  )}

                  {/* Method-specific Insight */}
                  {stopLossMethod && (
                    <div className="p-4 rounded-lg border bg-blue-500/10 border-blue-500/30 md:col-span-2">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-lg">
                          {stopLossMethod === 'fixed' && '📏'}
                          {stopLossMethod === 'indicator' && '📊'}
                          {stopLossMethod === 'risk' && '⚖️'}
                        </span>
                        <h5 className="font-semibold text-[#FEFEFF]">
                          {stopLossMethod === 'fixed' && 'Fixed Pips Strategy'}
                          {stopLossMethod === 'indicator' && 'Indicator-Based Strategy'}
                          {stopLossMethod === 'risk' && 'Risk-Based Strategy'}
                        </h5>
                      </div>
                      <p className="text-sm text-[#FEFEFF]/80">
                        {stopLossMethod === 'fixed' && fixedPips &&
                          `Using ${fixedPips} pip stops provides consistency but may not adapt to market volatility. Consider wider stops in volatile markets.`
                        }
                        {stopLossMethod === 'indicator' && indicatorBasedSL === 'atr' &&
                          `ATR-based stops adapt to market volatility, making them excellent for dynamic market conditions.`
                        }
                        {stopLossMethod === 'indicator' && indicatorBasedSL === 'bollinger' &&
                          `Bollinger Bands provide dynamic support/resistance levels, working well in trending markets.`
                        }
                        {stopLossMethod === 'indicator' && indicatorBasedSL === 'support_resistance' &&
                          `Support & Resistance levels offer strong psychological price points for stop placement.`
                        }
                        {stopLossMethod === 'risk' && lotSize &&
                          `Fixed ${lotSize} lot sizing keeps position size constant but creates variable risk per trade.`
                        }
                      </p>
                    </div>
                  )}
                </div>

                <div className="mt-6 p-4 bg-[#EFBD3A]/10 border border-[#EFBD3A]/20 rounded-lg">
                  <p className="text-sm text-[#FEFEFF]/80 text-center">
                    🎯 <strong>Pro Tip:</strong> Always use stop losses and take profits to automate your risk management and remove emotional decision-making from your trading.
                  </p>
                </div>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(5)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
              <button
                onClick={() => handleStepChange(7)}
                disabled={!riskPercentage || !riskRewardRatio || !stopLossMethod}
                className={`${buttonStyles.primary} ${
                  (!riskPercentage || !riskRewardRatio || !stopLossMethod) ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                Next Step
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 7: Review */}
        {currentStep === 7 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-[#1a1a1a] rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-3">
                  <svg className="w-8 h-8 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h2 className="text-3xl font-bold text-[#FEFEFF]">Strategy Review</h2>
                    <p className="text-[#FEFEFF]/60 mt-1">Review your complete trading strategy</p>
                  </div>
                </div>
                <div className="flex gap-3">
                  {/* Save to Community Button - Only show for privileged users */}
                  {hasPrivileges && (
                    <button
                      onClick={handleSaveToCommunity}
                      disabled={isSavingToCommunity || isSavedToCommunity || !strategyName || !forexPair}
                      className={`px-6 py-2.5 rounded-xl transition-all duration-200 flex items-center gap-2 ${
                        isSavedToCommunity
                          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white cursor-not-allowed opacity-75'
                          : (isSavingToCommunity || !strategyName || !forexPair)
                            ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white opacity-50 cursor-not-allowed'
                            : 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:from-orange-600 hover:to-orange-700'
                      }`}
                    >
                      {isSavingToCommunity ? (
                        <>
                          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Saving to Community...
                        </>
                      ) : isSavedToCommunity ? (
                        <>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Saved to Community
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                          Save to Community
                        </>
                      )}
                    </button>
                  )}

                  {/* Regular Save Button */}
                  <button
                    onClick={handleSaveStrategy}
                    disabled={isSavingStrategy || (isStrategySaved && !hasBeenModified)}
                    className={`${buttonStyles.primary} ${
                      (isSavingStrategy || (isStrategySaved && !hasBeenModified)) ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                  >
                    {(() => {
                    // Get editingStrategyId from localStorage if not available in state
                    const effectiveEditingId = editingStrategyId || localStorage.getItem("editingStrategyId");

                    if (isSavingStrategy) {
                      return (
                        <>
                          <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Saving Strategy...
                        </>
                      );
                    } else if (isStrategySaved && !hasBeenModified) {
                      return (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Strategy Saved
                        </>
                      );
                    } else if (effectiveEditingId) {
                      return (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          Update Strategy
                        </>
                      );
                    } else {
                      return (
                        <>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                          </svg>
                          Save Strategy
                        </>
                      );
                    }
                  })()}
                  </button>
                </div>
              </div>

              {/* Show success message when strategy is saved */}
              {isStrategySaved && !hasBeenModified && (
                <div className="mb-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <p className="text-green-400">
                    <span className="font-semibold">Strategy successfully saved!</span> You can find this strategy in the Strategy Library anytime.
                  </p>
                  </div>
                )}

              {/* Strategy Information Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {/* Basic Info Card */}
                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF]">Strategy Info</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-[#FEFEFF]/60">Name:</span>
                      <span className="text-[#FEFEFF] font-medium">{strategyName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#FEFEFF]/60">Pair:</span>
                      <span className="text-[#FEFEFF] font-medium">{forexPair}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#FEFEFF]/60">Timeframe:</span>
                      <span className="text-[#FEFEFF] font-medium">{timeframe}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#FEFEFF]/60">Sessions:</span>
                      <span className="text-[#FEFEFF] font-medium text-right">
                        {selectedTradingSessions.length === 0 ? 'All' :
                          selectedTradingSessions.map(session =>
                            tradingSessions.find(s => s.timezone === session)?.name
                          ).join(', ')}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Risk Management Card */}
                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF]">Risk Management</h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-[#FEFEFF]/60">Risk:</span>
                      <span className="text-[#FEFEFF] font-medium">{riskPercentage}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#FEFEFF]/60">R:R Ratio:</span>
                      <span className="text-[#FEFEFF] font-medium">{riskRewardRatio}:1</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#FEFEFF]/60">Stop Loss:</span>
                      <span className="text-[#FEFEFF] font-medium text-right">
                        {(() => {
                          switch (stopLossMethod) {
                            case 'fixed':
                              return `${fixedPips} pips`;
                            case 'indicator':
                              return `${indicatorBasedSL}`;
                            case 'risk':
                              return `${lotSize} lots`;
                            default:
                              return 'Not Set';
                          }
                        })()}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Indicators Card */}
                <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a]">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-[#FEFEFF]">Indicators</h3>
                  </div>
                  <div className="space-y-2">
                    {selectedIndicators.slice(0, 3).map((indicator, index) => (
                      <div key={indicator.id} className="flex justify-between text-sm">
                        <span className="text-[#FEFEFF]/60">{indicator.type}:</span>
                        <span className="text-[#FEFEFF] font-medium">
                          {Object.values(indicator.parameters).slice(0, 2).join(', ')}
                        </span>
                      </div>
                    ))}
                    {selectedIndicators.length > 3 && (
                      <div className="text-xs text-[#FEFEFF]/40 text-center pt-2">
                        +{selectedIndicators.length - 3} more indicators
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Strategy Logic Flow Diagram */}
              <div className="bg-[#1a1a1a] rounded-xl p-8 border border-[#3a3a3a] mb-8">
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-[#FEFEFF] mb-2">Strategy Logic Flow</h3>
                  <p className="text-[#FEFEFF]/60">Visual representation of your trading strategy conditions and execution</p>
                </div>

                {/* Entry Conditions Section */}
                <div className="mb-12">
                  <h4 className="text-xl font-bold text-[#FEFEFF] mb-6 flex items-center gap-2">
                    <span className="w-2 h-2 bg-[#EFBD3A] rounded-full"></span>
                    Entry Conditions
                  </h4>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Long Entry Signal */}
                    {entryRules.filter(r => r.tradeType === 'long').length > 0 && (
                      <div className="space-y-6">
                        <div className="flex items-center gap-3">
                          <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                          </svg>
                          <h5 className="text-lg font-bold text-green-400">Long Entry Signal</h5>
                        </div>

                        <div className="space-y-4">
                          {entryRules.filter(r => r.tradeType === 'long').map((rule, index) => (
                            <div key={rule.id} className="relative">
                              <div className="flex items-center gap-4">
                                <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center border border-green-500/40">
                                  <span className="text-green-400 font-medium text-sm">{index + 1}</span>
                                </div>
                                <div className="flex-1 bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                                  <div className="text-[#FEFEFF] font-medium">
                                    {getIndicatorLabel(rule.indicator1, rule)}
                                    <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </div>
                                  {rule.indicator1 === 'price' && (
                                    <div className="text-[#FEFEFF]/60 text-sm mt-1">
                                      Reference: {rule.barRef}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {index < entryRules.filter(r => r.tradeType === 'long').length - 1 && (
                                <div className="flex justify-center my-3">
                                  <div className="bg-[#3a3a3a] text-[#FEFEFF] px-3 py-1 rounded text-sm font-medium">
                                    {entryBuyGroupOperator}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>

                        {/* Arrow pointing down */}
                        <div className="flex justify-center my-4">
                          <svg className="w-5 h-5 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>

                        {/* Execute Long Box */}
                        <div className="bg-[#2a2a2a] border border-green-500/30 rounded-lg p-4 text-center">
                          <div className="text-green-400 font-semibold text-base mb-1">Execute Long Position</div>
                          <div className="text-[#FEFEFF]/60 text-sm">
                            Risk: {riskPercentage}% | Reward Ratio: {riskRewardRatio}:1
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Short Entry Signal */}
                    {entryRules.filter(r => r.tradeType === 'short').length > 0 && (
                      <div className="space-y-6">
                        <div className="flex items-center gap-3">
                          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                          </svg>
                          <h5 className="text-lg font-bold text-red-400">Short Entry Signal</h5>
                        </div>

                        <div className="space-y-4">
                          {entryRules.filter(r => r.tradeType === 'short').map((rule, index) => (
                            <div key={rule.id} className="relative">
                              <div className="flex items-center gap-4">
                                <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center border border-red-500/40">
                                  <span className="text-red-400 font-medium text-sm">{index + 1}</span>
                                </div>
                                <div className="flex-1 bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                                  <div className="text-[#FEFEFF] font-medium">
                                    {getIndicatorLabel(rule.indicator1, rule)}
                                    <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </div>
                                  {rule.indicator1 === 'price' && (
                                    <div className="text-[#FEFEFF]/60 text-sm mt-1">
                                      Reference: {rule.barRef}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {index < entryRules.filter(r => r.tradeType === 'short').length - 1 && (
                                <div className="flex justify-center my-3">
                                  <div className="bg-[#3a3a3a] text-[#FEFEFF] px-3 py-1 rounded text-sm font-medium">
                                    {entrySellGroupOperator}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>

                        {/* Arrow pointing down */}
                        <div className="flex justify-center my-4">
                          <svg className="w-5 h-5 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>

                        {/* Execute Short Box */}
                        <div className="bg-[#2a2a2a] border border-red-500/30 rounded-lg p-4 text-center">
                          <div className="text-red-400 font-semibold text-base mb-1">Execute Short Position</div>
                          <div className="text-[#FEFEFF]/60 text-sm">
                            Risk: {riskPercentage}% | Reward Ratio: {riskRewardRatio}:1
                          </div>
                        </div>
                      </div>
                    )}


                  </div>
                </div>

                {/* Exit Conditions Section */}
                <div className="mb-12">
                  <h4 className="text-xl font-bold text-[#FEFEFF] mb-6 flex items-center gap-2">
                    <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                    Exit Conditions
                  </h4>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Long Exit Signal */}
                    {exitRules.filter(r => r.tradeType === 'long').length > 0 && (
                      <div className="space-y-6">
                        <div className="flex items-center gap-3">
                          <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                          </svg>
                          <h5 className="text-lg font-bold text-green-400">Long Exit Signal</h5>
                        </div>

                        <div className="space-y-4">
                          {exitRules.filter(r => r.tradeType === 'long').map((rule, index) => (
                            <div key={rule.id} className="relative">
                              <div className="flex items-center gap-4">
                                <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center border border-green-500/40">
                                  <span className="text-green-400 font-medium text-sm">{index + 1}</span>
                                </div>
                                <div className="flex-1 bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                                  <div className="text-[#FEFEFF] font-medium">
                                    {getIndicatorLabel(rule.indicator1, rule, false)}
                                    <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </div>
                                  {rule.indicator1 === 'price' && (
                                    <div className="text-[#FEFEFF]/60 text-sm mt-1">
                                      Reference: {rule.barRef}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {index < exitRules.filter(r => r.tradeType === 'long').length - 1 && (
                                <div className="flex justify-center my-3">
                                  <div className="bg-[#3a3a3a] text-[#FEFEFF] px-3 py-1 rounded text-sm font-medium">
                                    {exitBuyGroupOperator}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>

                        {/* Arrow pointing down */}
                        <div className="flex justify-center my-4">
                          <svg className="w-5 h-5 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>

                        {/* Close Long Box */}
                        <div className="bg-[#2a2a2a] border border-green-500/30 rounded-lg p-4 text-center">
                          <div className="text-green-400 font-semibold text-base mb-1">Close Long Position</div>
                          <div className="text-[#FEFEFF]/60 text-sm">
                            Exit conditions met
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Short Exit Signal */}
                    {exitRules.filter(r => r.tradeType === 'short').length > 0 && (
                      <div className="space-y-6">
                        <div className="flex items-center gap-3">
                          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                          </svg>
                          <h5 className="text-lg font-bold text-red-400">Short Exit Signal</h5>
                        </div>

                        <div className="space-y-4">
                          {exitRules.filter(r => r.tradeType === 'short').map((rule, index) => (
                            <div key={rule.id} className="relative">
                              <div className="flex items-center gap-4">
                                <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center border border-red-500/40">
                                  <span className="text-red-400 font-medium text-sm">{index + 1}</span>
                                </div>
                                <div className="flex-1 bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                                  <div className="text-[#FEFEFF] font-medium">
                                    {getIndicatorLabel(rule.indicator1, rule, false)}
                                    <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                                    {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                                  </div>
                                  {rule.indicator1 === 'price' && (
                                    <div className="text-[#FEFEFF]/60 text-sm mt-1">
                                      Reference: {rule.barRef}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {index < exitRules.filter(r => r.tradeType === 'short').length - 1 && (
                                <div className="flex justify-center my-3">
                                  <div className="bg-[#3a3a3a] text-[#FEFEFF] px-3 py-1 rounded text-sm font-medium">
                                    {exitSellGroupOperator}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>

                        {/* Arrow pointing down */}
                        <div className="flex justify-center my-4">
                          <svg className="w-5 h-5 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>

                        {/* Close Short Box */}
                        <div className="bg-[#2a2a2a] border border-red-500/30 rounded-lg p-4 text-center">
                          <div className="text-red-400 font-semibold text-base mb-1">Close Short Position</div>
                          <div className="text-[#FEFEFF]/60 text-sm">
                            Exit conditions met
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Risk Management Summary */}
                  <div className="mt-8">
                    <div className="flex items-center gap-2 mb-4">
                      <span className="w-2 h-2 bg-[#EFBD3A] rounded-full"></span>
                      <h5 className="text-lg font-bold text-[#FEFEFF]">Risk Management</h5>
                    </div>

                    <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                      <div className="flex flex-wrap gap-6 text-sm">
                        <div className="flex items-center gap-2">
                          <span className="text-[#FEFEFF]/60">Stop Loss:</span>
                          <span className="text-red-400 font-medium">
                            {stopLossMethod === 'fixed' ? `${fixedPips} pips` :
                             stopLossMethod === 'indicator' ? `${indicatorBasedSL}` :
                             `${lotSize} lots`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-[#FEFEFF]/60">Take Profit:</span>
                          <span className="text-green-400 font-medium">
                            {stopLossMethod === 'fixed' ? `${(fixedPips * riskRewardRatio).toFixed(0)} pips` :
                             `${riskRewardRatio}x SL`}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-[#FEFEFF]/60">Risk per Trade:</span>
                          <span className="text-[#EFBD3A] font-medium">{riskPercentage}%</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-[#FEFEFF]/60">Risk-Reward Ratio:</span>
                          <span className="text-[#EFBD3A] font-medium">{riskRewardRatio}:1</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Backtest Section */}
              <div className="bg-[#1a1a1a] rounded-xl p-8 border border-[#3a3a3a]">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <svg className="w-6 h-6 text-[#FEFEFF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <h3 className="text-2xl font-bold text-[#FEFEFF]">Strategy Performance</h3>
                  </div>
                  <button
                    onClick={() => setIsBacktestDialogOpen(true)}
                    className="px-6 py-2.5 bg-[#EFBD3A] text-[#0A0B0B] rounded-lg hover:bg-[#EFBD3A]/90 transition-all duration-200 flex items-center gap-2 font-medium"
                    disabled={!isStrategyFinalized}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Run Backtest
                  </button>
                </div>

                {backtestResults ? (
                  <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#3a3a3a]">
                    <BacktestResults results={backtestResults} />
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <h4 className="text-lg font-semibold text-[#FEFEFF] mb-2">Strategy Performance Analysis</h4>
                    <p className="text-[#FEFEFF]/60 mb-6 max-w-md mx-auto">
                      Run a comprehensive backtest to evaluate your strategy's historical performance.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                      <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                        <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                          </svg>
                        </div>
                        <div className="text-sm font-medium text-[#FEFEFF]">Performance Metrics</div>
                        <div className="text-xs text-[#FEFEFF]/60">Win rate, profit factor, drawdown</div>
                      </div>
                      <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                        <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                          </svg>
                        </div>
                        <div className="text-sm font-medium text-[#FEFEFF]">Trade Analysis</div>
                        <div className="text-xs text-[#FEFEFF]/60">Entry/exit points, trade duration</div>
                      </div>
                      <div className="bg-[#2a2a2a] rounded-lg p-4 border border-[#3a3a3a]">
                        <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                          <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                          </svg>
                        </div>
                        <div className="text-sm font-medium text-[#FEFEFF]">Risk Assessment</div>
                        <div className="text-xs text-[#FEFEFF]/60">Risk-adjusted returns, volatility</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={() => handleStepChange(6)}
                className={buttonStyles.secondary}
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous Step
              </button>
            </div>
          </motion.div>
        )}

        {/* Entry Rule Modal */}
        {isEntryRuleModalOpen && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsEntryRuleModalOpen(false)} />
            <div className="relative bg-[#1a1a1a] rounded-xl p-6 w-full max-w-2xl shadow-lg">
              <h3 className="text-xl font-bold text-[#FEFEFF] mb-6">Add Entry Rule</h3>

                              <div className="space-y-4">
                {/* Trade Type Selection */}
                        <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Trade Type
                  </label>
                  <div className="flex gap-4">
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, tradeType: 'long' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.tradeType === 'long'
                          ? 'bg-green-500/20 border-green-500/50 text-green-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Long
                    </button>
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, tradeType: 'short' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.tradeType === 'short'
                          ? 'bg-red-500/20 border-red-500/50 text-red-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Short
                    </button>
                                  </div>
                                </div>

                {/* First Indicator Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    First Indicator
                  </label>
                  <select
                    value={newEntryRule.indicator1}
                    onChange={(e) => {
                      const value = e.target.value;
                      setNewEntryRule(prev => ({
                        ...prev,
                        indicator1: value,
                        // Reset barRef to empty string if not price
                        barRef: value === 'price' ? prev.barRef : '',
                        // Reset band if changing from a Bollinger Bands indicator
                        band: undefined,
                        // Reset macdComponent if changing from a MACD indicator
                        macdComponent: undefined
                      }));
                    }}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                  >
                    <option value="">Select an indicator...</option>
                    <option value="price">Price</option>
                    {selectedIndicators.map((ind) => (
                      <option key={ind.id} value={ind.id}>
                        {ind.type} ({Object.entries(ind.parameters)
                          .map(([k, v]) => `${k}: ${v}`)
                          .join(", ")})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Bar Reference Selection - Only show when price is selected */}
                {newEntryRule.indicator1 === 'price' && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bar Reference
                    </label>
                    <select
                      value={newEntryRule.barRef}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, barRef: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="open">Open</option>
                      <option value="high">High</option>
                      <option value="low">Low</option>
                      <option value="close">Close</option>
                    </select>
                  </div>
                )}

                {/* Bollinger Band Selection - Only show when a Bollinger Bands indicator is selected */}
                {newEntryRule.indicator1 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator1 && ind.type === 'BollingerBands') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bollinger Band
                    </label>
                    <select
                      value={newEntryRule.band || 'middle'}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, band: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="upper">Upper Band</option>
                      <option value="middle">Middle Band (SMA)</option>
                      <option value="lower">Lower Band</option>
                    </select>
                  </div>
                )}

                {/* MACD Component Selection - Only show when a MACD indicator is selected */}
                {newEntryRule.indicator1 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator1 && ind.type === 'MACD') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      MACD Component
                    </label>
                    <select
                      value={newEntryRule.macdComponent || 'macd'}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, macdComponent: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="macd">MACD Line</option>
                      <option value="signal">Signal Line</option>
                    </select>
                  </div>
                )}

                {/* Operator Selection */}
                        <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Operator
                  </label>
                        <select
                    value={newEntryRule.operator}
                    onChange={(e) => setNewEntryRule(prev => ({ ...prev, operator: e.target.value }))}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        >
                          {conditionOperators.map((op) => (
                      <option key={op} value={op}>
                        {op}
                      </option>
                          ))}
                        </select>
                                </div>

                {/* Compare Type Selection */}
                        <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Compare Against
                  </label>
                  <div className="flex gap-4">
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, compareType: 'value' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.compareType === 'value'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Value
                    </button>
                    <button
                      onClick={() => setNewEntryRule(prev => ({ ...prev, compareType: 'indicator' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newEntryRule.compareType === 'indicator'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Indicator
                    </button>
                      </div>
              </div>

                {/* Value or Second Indicator */}
                {newEntryRule.compareType === 'value' ? (
                        <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Value
                    </label>
                                    <input
                                      type="number"
                      value={newEntryRule.value}
                      onChange={(e) => setNewEntryRule(prev => ({ ...prev, value: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                      placeholder="Enter value..."
                                    />
                                  </div>
                ) : (
                                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Second Indicator
                    </label>
                            <select
                      value={newEntryRule.indicator2}
                      onChange={(e) => {
                        const value = e.target.value;
                        setNewEntryRule(prev => ({
                          ...prev,
                          indicator2: value,
                          // Reset band2 if changing from a Bollinger Bands indicator
                          band2: undefined,
                          // Reset macdComponent2 if changing from a MACD indicator
                          macdComponent2: undefined
                        }));
                      }}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="">Select an indicator...</option>
                      <option value="price">Price</option>
                              {selectedIndicators.map((ind) => (
                                <option key={ind.id} value={ind.id}>
                                            {ind.type} ({Object.entries(ind.parameters)
                                              .map(([k, v]) => `${k}: ${v}`)
                                              .join(", ")})
                                </option>
                              ))}
                            </select>

                            {/* Bollinger Band Selection for Second Indicator - Only show when a Bollinger Bands indicator is selected */}
                            {newEntryRule.indicator2 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator2 && ind.type === 'BollingerBands') && (
                              <div className="mt-4">
                                <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                                  Bollinger Band (Second Indicator)
                                </label>
                                <select
                                  value={newEntryRule.band2 || 'middle'}
                                  onChange={(e) => setNewEntryRule(prev => ({ ...prev, band2: e.target.value }))}
                                  className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                                >
                                  <option value="upper">Upper Band</option>
                                  <option value="middle">Middle Band (SMA)</option>
                                  <option value="lower">Lower Band</option>
                                </select>
                              </div>
                            )}

                            {/* MACD Component Selection for Second Indicator - Only show when a MACD indicator is selected */}
                            {newEntryRule.indicator2 && selectedIndicators.find(ind => ind.id === newEntryRule.indicator2 && ind.type === 'MACD') && (
                              <div className="mt-4">
                                <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                                  MACD Component (Second Indicator)
                                </label>
                                <select
                                  value={newEntryRule.macdComponent2 || 'macd'}
                                  onChange={(e) => setNewEntryRule(prev => ({ ...prev, macdComponent2: e.target.value }))}
                                  className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                                >
                                  <option value="macd">MACD Line</option>
                                  <option value="signal">Signal Line</option>
                                </select>
                              </div>
                            )}
              </div>
                                )}
              </div>

              {/* Modal Actions */}
              <div className="flex justify-end gap-4 mt-6">
                                    <button
                  onClick={() => setIsEntryRuleModalOpen(false)}
                  className={buttonStyles.secondary}
                >
                  Cancel
                                    </button>
                                    <button
                  onClick={handleAddEntryRule}
                  disabled={!newEntryRule.indicator1 || (newEntryRule.compareType === 'value' ? !newEntryRule.value : !newEntryRule.indicator2)}
                  className={`${buttonStyles.primary} ${
                    (!newEntryRule.indicator1 || (newEntryRule.compareType === 'value' ? !newEntryRule.value : !newEntryRule.indicator2))
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }`}
                >
                  Add Rule
                                    </button>
              </div>
              </div>
              </div>
                            )}

        {/* Exit Rule Modal */}
        {isExitRuleModalOpen && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsExitRuleModalOpen(false)} />
            <div className="relative bg-[#1a1a1a] rounded-xl p-6 w-full max-w-2xl shadow-lg">
              <h3 className="text-xl font-bold text-[#FEFEFF] mb-6">Add Exit Rule</h3>

              <div className="space-y-4">
                {/* Trade Type Selection */}
                              <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Trade Type
                  </label>
                  <div className="flex gap-4">
                                    <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, tradeType: 'long' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.tradeType === 'long'
                          ? 'bg-green-500/20 border-green-500/50 text-green-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Long
                                    </button>
                                    <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, tradeType: 'short' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.tradeType === 'short'
                          ? 'bg-red-500/20 border-red-500/50 text-red-400'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Short
                                    </button>
            </div>
            </div>

                {/* First Indicator Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    First Indicator
                  </label>
                  <select
                    value={newExitRule.indicator1}
                    onChange={(e) => {
                      const value = e.target.value;
                      setNewExitRule(prev => ({
                        ...prev,
                        indicator1: value,
                        // Reset barRef to empty string if not price
                        barRef: value === 'price' ? prev.barRef : '',
                        // Reset band if changing from a Bollinger Bands indicator
                        band: undefined,
                        // Reset macdComponent if changing from a MACD indicator
                        macdComponent: undefined
                      }));
                    }}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                  >
                    <option value="">Select an indicator...</option>
                    <option value="price">Price</option>
                    {selectedIndicators.map((ind) => (
                      <option key={ind.id} value={ind.id}>
                        {ind.type} ({Object.entries(ind.parameters)
                          .map(([k, v]) => `${k}: ${v}`)
                          .join(", ")})
                      </option>
                    ))}
                  </select>
                    </div>

                {/* Bar Reference Selection - Only show when price is selected */}
                {newExitRule.indicator1 === 'price' && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bar Reference
                    </label>
                    <select
                      value={newExitRule.barRef}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, barRef: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="open">Open</option>
                      <option value="high">High</option>
                      <option value="low">Low</option>
                      <option value="close">Close</option>
                    </select>
                  </div>
                )}

                {/* Bollinger Band Selection - Only show when a Bollinger Bands indicator is selected */}
                {newExitRule.indicator1 && selectedIndicators.find(ind => ind.id === newExitRule.indicator1 && ind.type === 'BollingerBands') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Bollinger Band
                    </label>
                    <select
                      value={newExitRule.band || 'middle'}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, band: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="upper">Upper Band</option>
                      <option value="middle">Middle Band (SMA)</option>
                      <option value="lower">Lower Band</option>
                    </select>
                  </div>
                )}

                {/* MACD Component Selection - Only show when a MACD indicator is selected */}
                {newExitRule.indicator1 && selectedIndicators.find(ind => ind.id === newExitRule.indicator1 && ind.type === 'MACD') && (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      MACD Component
                    </label>
                    <select
                      value={newExitRule.macdComponent || 'macd'}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, macdComponent: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="macd">MACD Line</option>
                      <option value="signal">Signal Line</option>
                    </select>
                  </div>
                )}

                {/* Operator Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Operator
                  </label>
                  <select
                    value={newExitRule.operator}
                    onChange={(e) => setNewExitRule(prev => ({ ...prev, operator: e.target.value }))}
                    className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                  >
                    {conditionOperators.map((op) => (
                      <option key={op} value={op}>
                        {op}
                      </option>
                    ))}
                  </select>
                            </div>

                {/* Compare Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                    Compare Against
                  </label>
                  <div className="flex gap-4">
                  <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, compareType: 'value' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.compareType === 'value'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Value
                  </button>
                  <button
                      onClick={() => setNewExitRule(prev => ({ ...prev, compareType: 'indicator' }))}
                      className={`flex-1 px-4 py-2 rounded-lg border ${
                        newExitRule.compareType === 'indicator'
                          ? 'bg-[#EFBD3A]/20 border-[#EFBD3A]/50 text-[#EFBD3A]'
                          : 'bg-[#2a2a2a] border-[#3a3a3a] text-[#FEFEFF]/60'
                      }`}
                    >
                      Indicator
                  </button>
                </div>
            </div>

                {/* Value or Second Indicator */}
                {newExitRule.compareType === 'value' ? (
                  <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Value
                    </label>
                            <input
                              type="number"
                      value={newExitRule.value}
                      onChange={(e) => setNewExitRule(prev => ({ ...prev, value: e.target.value }))}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                      placeholder="Enter value..."
                    />
        </div>
                ) : (
                                <div>
                    <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                      Second Indicator
                    </label>
                    <select
                      value={newExitRule.indicator2}
                      onChange={(e) => {
                        const value = e.target.value;
                        setNewExitRule(prev => ({
                          ...prev,
                          indicator2: value,
                          // Reset band2 if changing from a Bollinger Bands indicator
                          band2: undefined,
                          // Reset macdComponent2 if changing from a MACD indicator
                          macdComponent2: undefined
                        }));
                      }}
                      className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                    >
                      <option value="">Select an indicator...</option>
                      <option value="price">Price</option>
                      {selectedIndicators.map((ind) => (
                        <option key={ind.id} value={ind.id}>
                          {ind.type} ({Object.entries(ind.parameters)
                            .map(([k, v]) => `${k}: ${v}`)
                            .join(", ")})
                        </option>
                      ))}
                    </select>

                    {/* Bollinger Band Selection for Second Indicator - Only show when a Bollinger Bands indicator is selected */}
                    {newExitRule.indicator2 && selectedIndicators.find(ind => ind.id === newExitRule.indicator2 && ind.type === 'BollingerBands') && (
                      <div className="mt-4">
                        <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                          Bollinger Band (Second Indicator)
                        </label>
                        <select
                          value={newExitRule.band2 || 'middle'}
                          onChange={(e) => setNewExitRule(prev => ({ ...prev, band2: e.target.value }))}
                          className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        >
                          <option value="upper">Upper Band</option>
                          <option value="middle">Middle Band (SMA)</option>
                          <option value="lower">Lower Band</option>
                        </select>
                      </div>
                    )}

                    {/* MACD Component Selection for Second Indicator - Only show when a MACD indicator is selected */}
                    {newExitRule.indicator2 && selectedIndicators.find(ind => ind.id === newExitRule.indicator2 && ind.type === 'MACD') && (
                      <div className="mt-4">
                        <label className="block text-sm font-medium text-[#FEFEFF] mb-2">
                          MACD Component (Second Indicator)
                        </label>
                        <select
                          value={newExitRule.macdComponent2 || 'macd'}
                          onChange={(e) => setNewExitRule(prev => ({ ...prev, macdComponent2: e.target.value }))}
                          className="w-full px-4 py-2 bg-[#2a2a2a] text-[#FEFEFF] rounded-lg border border-[#3a3a3a] focus:outline-none focus:ring-2 focus:ring-[#EFBD3A] focus:border-transparent"
                        >
                          <option value="macd">MACD Line</option>
                          <option value="signal">Signal Line</option>
                        </select>
                      </div>
                    )}
          </div>
        )}
                </div>

              {/* Modal Actions */}
              <div className="flex justify-end gap-4 mt-6">
                <button
                  onClick={() => setIsExitRuleModalOpen(false)}
                  className={buttonStyles.secondary}
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddExitRule}
                  disabled={!newExitRule.indicator1 || (newExitRule.compareType === 'value' ? !newExitRule.value : !newExitRule.indicator2)}
                  className={`${buttonStyles.primary} ${
                    (!newExitRule.indicator1 || (newExitRule.compareType === 'value' ? !newExitRule.value : !newExitRule.indicator2))
                      ? 'opacity-50 cursor-not-allowed'
                      : ''
                  }`}
                >
                  Add Rule
                </button>
                              </div>
                            </div>
          </div>
        )}

        {/* Other steps will be added here */}
        {/* Add BacktestDialog */}
        {isBacktestDialogOpen && (
          <BacktestDialogWrapper
            isOpen={isBacktestDialogOpen}
            onClose={handleCloseBacktestDialog}
            strategy={generatedStrategy}
            onBacktestComplete={(results) => {
              console.log('🎯 handleBacktestComplete called with results:', {
                hasResults: !!results,
                tradesCount: results?.trades?.length || 0,
                generatedStrategy: !!generatedStrategy,
                sampleRawTrade: results?.trades?.[0]
              });

              // Format trades data for chart compatibility (same as StrategyLibrary.js)
              const formattedResults = {
                ...results,
                strategy: generatedStrategy, // Include strategy data for chart
                trades: results.trades?.map(trade => ({
                  ...trade,
                  // Map backend field names to chart expected field names
                  type: trade.trade_type || trade.type, // Backend uses 'trade_type', chart expects 'type'
                  entry_time: trade.entry_time ? (
                    // Handle both timestamp integers and date strings
                    typeof trade.entry_time === 'number'
                      ? new Date(trade.entry_time * 1000) // Convert Unix timestamp to Date
                      : new Date(trade.entry_time + 'Z') // Add 'Z' to parse as UTC
                  ) : null,
                  exit_time: trade.exit_time ? (
                    // Handle both timestamp integers and date strings
                    typeof trade.exit_time === 'number'
                      ? new Date(trade.exit_time * 1000) // Convert Unix timestamp to Date
                      : new Date(trade.exit_time + 'Z') // Add 'Z' to parse as UTC
                  ) : null
                })) || []
              };

              console.log('✅ Formatted backtest results for chart:', {
                tradesCount: formattedResults.trades.length,
                sampleTrade: formattedResults.trades[0]
              });

              setBacktestResults(formattedResults);
              handleCloseBacktestDialog();
              // Show success toast
              toast.success('Backtest completed successfully!', {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
              });
            }}
          />
        )}
      </div>
    </DashboardLayout>
  );
}