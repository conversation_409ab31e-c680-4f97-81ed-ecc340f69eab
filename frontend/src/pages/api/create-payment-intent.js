import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { amount, currency = 'usd', plan, period, customerEmail, userData, firebaseUid } = req.body;

    // Validate required fields
    if (!amount || !plan || !customerEmail) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Convert amount to cents (Stripe expects amounts in cents)
    const amountInCents = Math.round(amount * 100);

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: currency,
      metadata: {
        plan: plan,
        period: period || 'monthly',
        customerEmail: customerEmail,
        firebaseUid: firebaseUid || '',
        userData: userData ? JSON.stringify(userData) : '',
      },
      receipt_email: customerEmail,
      description: `OrynTrade ${plan} Plan - ${period || 'monthly'}`,
    });

    res.status(200).json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(500).json({ 
      error: 'Failed to create payment intent',
      message: error.message 
    });
  }
}
