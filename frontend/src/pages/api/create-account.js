import { USE_FIREBASE_EMULATOR } from '../../config';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { user, plan, period, paymentData, amount } = req.body;

    // Validate required fields
    if (!user || !user.uid || !user.email || !plan || !amount) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Here you would integrate with your payment processor (Stripe, PayPal, etc.)
    // For now, we'll simulate payment processing
    console.log('Processing payment:', {
      amount,
      plan,
      period,
      userEmail: user.email
    });

    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Call your backend signup function
    const SIGNUP_URL = USE_FIREBASE_EMULATOR
      ? "http://127.0.0.1:5001/oryntrade/us-central1/signup"
      : "https://signup-ihjc6tjxia-uc.a.run.app";

    const signupResponse = await fetch(SIGNUP_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: user.email,
        firebase_uid: user.uid,
        terms_accepted: true,
        terms_version: "1.0",
        terms_accepted_at: new Date().toISOString(),
        subscription_plan: plan,
        subscription_period: period || 'monthly',
        payment_amount: amount,
        first_name: user.firstName,
        last_name: user.lastName,
        phone: user.phone
      }),
    });

    if (!signupResponse.ok) {
      throw new Error('Failed to create user account');
    }

    const signupData = await signupResponse.json();

    // Log successful account creation
    console.log('Account created successfully:', {
      userId: user.uid,
      email: user.email,
      plan,
      amount
    });

    return res.status(200).json({
      success: true,
      message: 'Account created successfully',
      user: signupData
    });

  } catch (error) {
    console.error('Account creation error:', error);
    return res.status(500).json({
      error: 'Failed to create account',
      message: error.message
    });
  }
}
