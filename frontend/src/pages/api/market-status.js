import { db } from "../../../firebaseConfig";
import { doc, getDoc } from "firebase/firestore";

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { firebase_uid, strategy_id } = req.query;

    if (!firebase_uid || !strategy_id) {
      return res
        .status(400)
        .json({ message: "Firebase UID and Strategy ID are required" });
    }

    // Get the specific strategy document
    const strategyRef = doc(
      db,
      "users",
      firebase_uid,
      "submittedStrategies",
      strategy_id
    );
    const strategyDoc = await getDoc(strategyRef);

    if (!strategyDoc.exists()) {
      return res.status(404).json({ message: "Strategy not found" });
    }

    const strategyData = strategyDoc.data();
    console.log("Strategy data:", strategyData);

    // Get market status from the strategy document
    const marketStatus = strategyData.marketStatus || null;

    return res.status(200).json({ marketStatus });
  } catch (error) {
    console.error("Error fetching market status:", error);
    return res.status(500).json({ message: "Internal server error" });
  }
}
