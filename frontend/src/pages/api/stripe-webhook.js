import Stripe from 'stripe';
import { USE_FIREBASE_EMULATOR } from '../../config';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

export const config = {
  api: {
    bodyParser: false,
  },
};

// Helper function to get raw body
const getRawBody = async (req) => {
  const chunks = [];
  for await (const chunk of req) {
    chunks.push(typeof chunk === 'string' ? Buffer.from(chunk) : chunk);
  }
  return Buffer.concat(chunks);
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const buf = await getRawBody(req);
  const sig = req.headers['stripe-signature'];

  let event;

  try {
    event = stripe.webhooks.constructEvent(buf, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('Payment succeeded:', paymentIntent.id);
      
      // Create user account after successful payment
      try {
        await createUserAccount(paymentIntent);
      } catch (error) {
        console.error('Failed to create user account:', error);
      }
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('Payment failed:', failedPayment.id);
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.json({ received: true });
}

async function createUserAccount(paymentIntent) {
  const { plan, period, customerEmail, firebaseUid, userData } = paymentIntent.metadata;

  if (!firebaseUid || !userData) {
    console.log('Missing user data in payment intent metadata');
    return;
  }

  try {
    const parsedUserData = JSON.parse(userData);
    
    // Call your backend signup function
    const SIGNUP_URL = USE_FIREBASE_EMULATOR
      ? "http://127.0.0.1:5001/oryntrade/us-central1/signup"
      : "https://signup-ihjc6tjxia-uc.a.run.app";

    const signupResponse = await fetch(SIGNUP_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: customerEmail,
        firebase_uid: firebaseUid,
        terms_accepted: true,
        terms_version: "1.0",
        terms_accepted_at: new Date().toISOString(),
        subscription_plan: plan,
        subscription_period: period || 'monthly',
        payment_amount: paymentIntent.amount / 100, // Convert back from cents
        payment_intent_id: paymentIntent.id,
        first_name: parsedUserData.firstName,
        last_name: parsedUserData.lastName,
        phone: parsedUserData.phone,
        stripe_customer_id: paymentIntent.customer,
      }),
    });

    if (!signupResponse.ok) {
      throw new Error('Failed to create user account');
    }

    console.log('User account created successfully for payment:', paymentIntent.id);
  } catch (error) {
    console.error('Error creating user account:', error);
    throw error;
  }
}
