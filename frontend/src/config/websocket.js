/**
 * WebSocket Configuration
 * 
 * Handles switching between local and production WebSocket services
 * based on environment variables.
 */

// Get the production WebSocket service URL
const getProductionWebSocketUrl = async () => {
  // You can either hardcode this or fetch it dynamically
  const hardcodedUrl = process.env.NEXT_PUBLIC_WEBSOCKET_DATA_SERVICE_URL_PRODUCTION;
  
  if (hardcodedUrl) {
    return hardcodedUrl;
  }
  
  // Fallback: Try to fetch from a known endpoint (optional)
  try {
    // This would be an API endpoint that returns the current WebSocket URL
    // const response = await fetch('/api/websocket-url');
    // const data = await response.json();
    // return data.websocketUrl;
    
    // For now, return a default production URL
    return 'wss://websocket-data-distribution-service-ihjc6tjxia-uc.a.run.app';
  } catch (error) {
    console.warn('⚠️ Could not fetch production WebSocket URL, using default');
    return 'wss://websocket-data-distribution-service-ihjc6tjxia-uc.a.run.app';
  }
};

// Configuration object
export const WebSocketConfig = {
  // Environment flags
  useProduction: process.env.NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET === 'true',
  
  // URLs
  localUrl: process.env.NEXT_PUBLIC_WEBSOCKET_DATA_SERVICE_URL_LOCAL || 'ws://localhost:8765',
  productionUrl: process.env.NEXT_PUBLIC_WEBSOCKET_DATA_SERVICE_URL_PRODUCTION || 'wss://websocket-data-distribution-service-ihjc6tjxia-uc.a.run.app',
  
  // Get the appropriate URL based on configuration
  getWebSocketUrl: function() {
    const url = this.useProduction ? this.productionUrl : this.localUrl;
    
    // Remove trailing slash if present
    return url.endsWith('/') ? url.slice(0, -1) : url;
  },
  
  // Get environment info for debugging
  getEnvironmentInfo: function() {
    return {
      useProduction: this.useProduction,
      localUrl: this.localUrl,
      productionUrl: this.productionUrl,
      selectedUrl: this.getWebSocketUrl(),
      environment: this.useProduction ? 'production' : 'local'
    };
  },
  
  // Authentication configuration
  requiresAuth: function() {
    // Production WebSocket service requires Firebase authentication
    return this.useProduction;
  }
};

// Helper function to get Firebase ID token for production connections
export const getFirebaseIdToken = async () => {
  if (!WebSocketConfig.requiresAuth()) {
    return null;
  }
  
  try {
    // Import Firebase auth dynamically to avoid issues in non-browser environments
    const { getAuth } = await import('firebase/auth');
    const auth = getAuth();
    
    if (!auth.currentUser) {
      throw new Error('User not authenticated');
    }
    
    const idToken = await auth.currentUser.getIdToken();
    return idToken;
  } catch (error) {
    console.error('❌ Failed to get Firebase ID token:', error);
    throw error;
  }
};

// Helper function to build WebSocket URL with authentication
export const buildWebSocketUrl = async () => {
  const baseUrl = WebSocketConfig.getWebSocketUrl();

  if (!WebSocketConfig.requiresAuth()) {
    return baseUrl;
  }

  try {
    const idToken = await getFirebaseIdToken();
    return `${baseUrl}?token=${idToken}`;
  } catch (error) {
    console.error('❌ Failed to build authenticated WebSocket URL:', error);

    // If we're using production WebSocket but local Firebase (development scenario)
    // Return URL with development bypass parameter
    if (WebSocketConfig.useProduction) {
      console.warn('🔧 Using development bypass for production WebSocket service');
      return `${baseUrl}?dev=local-testing`;
    }

    // Return base URL without auth - connection will fail but won't crash
    return baseUrl;
  }
};

// Helper function to get WebSocket connection URL with proper authentication
export const getWebSocketConnectionUrl = async () => {
  const baseUrl = WebSocketConfig.getWebSocketUrl();

  // For local WebSocket service, no authentication needed
  if (!WebSocketConfig.useProduction) {
    return baseUrl;
  }

  try {
    // Try to get Firebase ID token for production
    const idToken = await getFirebaseIdToken();
    return `${baseUrl}?token=${idToken}`;
  } catch (error) {
    console.warn('🔧 Firebase auth failed, using development mode for production WebSocket');

    // Use development bypass query parameter for production WebSocket service
    return `${baseUrl}?dev=local-testing`;
  }
};

// Debug helper
export const logWebSocketConfig = () => {
  const info = WebSocketConfig.getEnvironmentInfo();
  console.log('🔧 WebSocket Configuration:', info);
  
  if (info.useProduction) {
    console.log('🌐 Using PRODUCTION WebSocket service');
    console.log('🔐 Authentication required: YES');
  } else {
    console.log('🏠 Using LOCAL WebSocket service');
    console.log('🔐 Authentication required: NO');
  }
};

export default WebSocketConfig;
