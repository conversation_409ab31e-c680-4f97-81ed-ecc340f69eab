/**
 * Service Configuration
 * 
 * Handles switching between local and production services
 * based on environment variables.
 */

import { USE_FIREBASE_EMULATOR } from '../config';

// Environment flags
const USE_PRODUCTION_WEBSOCKET = process.env.NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET === 'true';
const USE_PRODUCTION_SERVICES = process.env.NEXT_PUBLIC_USE_PRODUCTION_SERVICES === 'true';

// Service URLs
export const ServiceConfig = {
  // WebSocket Data Distribution Service
  websocket: {
    useProduction: USE_PRODUCTION_WEBSOCKET,
    localUrl: process.env.NEXT_PUBLIC_WEBSOCKET_DATA_SERVICE_URL_LOCAL || 'ws://localhost:8765',
    productionUrl: process.env.NEXT_PUBLIC_WEBSOCKET_DATA_SERVICE_URL_PRODUCTION || 'wss://websocket-data-distribution-service-ihjc6tjxia-uc.a.run.app',
    
    getUrl: function() {
      const url = this.useProduction ? this.productionUrl : this.localUrl;
      return url.endsWith('/') ? url.slice(0, -1) : url;
    }
  },

  // Strategy Controller Service
  strategyController: {
    useProduction: USE_PRODUCTION_SERVICES,
    localUrl: process.env.NEXT_PUBLIC_STRATEGY_CONTROLLER_URL_LOCAL || 'http://localhost:8080',
    productionUrl: process.env.NEXT_PUBLIC_STRATEGY_CONTROLLER_URL_PRODUCTION || 'https://control-strategy-ihjc6tjxia-uc.a.run.app',
    
    getUrl: function() {
      const url = this.useProduction ? this.productionUrl : this.localUrl;
      return url.endsWith('/') ? url.slice(0, -1) : url;
    }
  },

  // Trade-Bot Service (for direct API calls if needed)
  tradeBot: {
    useProduction: USE_PRODUCTION_SERVICES,
    localUrl: process.env.NEXT_PUBLIC_TRADE_BOT_SERVICE_URL_LOCAL || 'http://localhost:8001',
    productionUrl: process.env.NEXT_PUBLIC_TRADE_BOT_SERVICE_URL_PRODUCTION || 'http://trade-bot-service:80',
    
    getUrl: function() {
      const url = this.useProduction ? this.productionUrl : this.localUrl;
      return url.endsWith('/') ? url.slice(0, -1) : url;
    }
  },

  // Firebase Functions (existing logic)
  functions: {
    useEmulator: USE_FIREBASE_EMULATOR,
    
    getUrl: function(functionName) {
      const baseUrl = this.useEmulator 
        ? "http://127.0.0.1:5001/oryntrade/us-central1"
        : "https://us-central1-oryntrade.cloudfunctions.net";
      
      return `${baseUrl}/${functionName}`;
    }
  }
};

// Convenience functions for backward compatibility
export const getWebSocketUrl = () => ServiceConfig.websocket.getUrl();
export const getStrategyControllerUrl = () => ServiceConfig.strategyController.getUrl();
export const getTradeBotUrl = () => ServiceConfig.tradeBot.getUrl();

// Environment info for debugging
export const getEnvironmentInfo = () => ({
  websocketEnvironment: ServiceConfig.websocket.useProduction ? 'production' : 'local',
  servicesEnvironment: ServiceConfig.strategyController.useProduction ? 'production' : 'local',
  firebaseEnvironment: ServiceConfig.functions.useEmulator ? 'emulator' : 'production',
  websocketUrl: ServiceConfig.websocket.getUrl(),
  strategyControllerUrl: ServiceConfig.strategyController.getUrl(),
  tradeBotUrl: ServiceConfig.tradeBot.getUrl()
});

// Log current configuration in development
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Service Configuration:', getEnvironmentInfo());
}
