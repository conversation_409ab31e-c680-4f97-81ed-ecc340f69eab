/**
 * Frontend Polygon API Service
 * Simplified approach that matches the backend trade-agent implementation
 * Fetches 1000 candles directly from Polygon API and replaces buffer every time
 */

import { POLYGON_API_KEY } from '../config';

class PolygonApiService {
  constructor() {
    this.apiKey = POLYGON_API_KEY;
    this.baseUrl = 'https://api.polygon.io/v2/aggs/ticker';
    this.candleBuffer = new Map(); // symbol_timeframe -> candles array
    this.lastCandleTime = new Map(); // symbol_timeframe -> last candle timestamp
    this.bufferSize = 1000;

    // Debug: Log API key status
    console.log('🔑 Polygon API Key status:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');
  }

  /**
   * Get timeframe in minutes for calculations
   */
  _getTimeframeMinutes(timeframe) {
    const timeframeMap = {
      '1m': 1,
      '3m': 3,
      '5m': 5,
      '15m': 15,
      '30m': 30,
      '1h': 60,
      '4h': 240,
      '1d': 1440
    };
    return timeframeMap[timeframe] || 60;
  }

  /**
   * Convert symbol format for Polygon API
   */
  _formatSymbol(symbol) {
    // Convert EUR/USD -> EURUSD -> C:EURUSD
    const cleanSymbol = symbol.replace('/', '').replace('_', '');
    return `C:${cleanSymbol}`;
  }

  /**
   * Calculate date range for API request
   */
  _calculateDateRange() {
    const now = new Date();
    const endDate = new Date(now);
    endDate.setDate(endDate.getDate() + 1); // Tomorrow to ensure today's data
    
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 8); // 8-day range for coverage
    
    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  }

  /**
   * Map timeframe to Polygon API format
   */
  _mapTimeframeToPolygon(timeframe) {
    const timeframeMinutes = this._getTimeframeMinutes(timeframe);
    
    if (timeframeMinutes < 60) {
      return {
        multiplier: timeframeMinutes,
        timespan: 'minute'
      };
    } else if (timeframeMinutes < 1440) {
      return {
        multiplier: timeframeMinutes / 60,
        timespan: 'hour'
      };
    } else {
      return {
        multiplier: timeframeMinutes / 1440,
        timespan: 'day'
      };
    }
  }

  /**
   * Fetch candles from Polygon API (same logic as backend)
   */
  async fetchCandles(symbol, timeframe, count = 1000) {
    try {
      console.log(`🔄 Frontend: Fetching ${count} candles from Polygon API: ${symbol} ${timeframe}`);
      
      // Format symbol and calculate date range
      const polygonSymbol = this._formatSymbol(symbol);
      const { startDate, endDate } = this._calculateDateRange();
      const { multiplier, timespan } = this._mapTimeframeToPolygon(timeframe);
      
      // Construct API URL
      const url = `${this.baseUrl}/${polygonSymbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}`;
      
      // API parameters
      const params = new URLSearchParams({
        apiKey: this.apiKey,
        limit: 50000, // Large limit to get all available data
        _t: Date.now() // Cache busting parameter
      });

      console.log(`🔗 Frontend URL: ${url}?${params}`);
      console.log(`🔑 Using API key: ${this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'MISSING'}`);

      // Make API request
      const response = await fetch(`${url}?${params}`);

      if (response.status === 429) {
        throw new Error('Rate limited by Polygon API');
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Polygon API Error ${response.status}:`, errorText);
        throw new Error(`Polygon API request failed: ${response.status}`);
      }
      
      const data = await response.json();
      const results = data.results || [];
      
      if (!results.length) {
        console.warn(`⚠️ No candle data returned from Polygon for ${symbol} ${timeframe}`);
        return {
          status: 'success',
          candles: [],
          info: { message: 'No data available', count: 0 }
        };
      }
      
      // Convert Polygon format to our format
      const candles = results.map(candle => ({
        time: Math.floor(candle.t / 1000), // Convert to seconds
        open: parseFloat(candle.o),
        high: parseFloat(candle.h),
        low: parseFloat(candle.l),
        close: parseFloat(candle.c),
        volume: parseInt(candle.v || 0)
      }));
      
      // Sort by time and take the last 'count' candles
      candles.sort((a, b) => a.time - b.time);
      const limitedCandles = candles.slice(-count);
      
      console.log(`✅ Frontend: Successfully fetched ${limitedCandles.length} candles from Polygon API`);
      console.log(`📊 Frontend: Latest candle: ${new Date(limitedCandles[limitedCandles.length - 1].time * 1000).toISOString()}`);

      // Debug: Show the last 3 candles to see if we're getting fresh data
      console.log(`🔍 Frontend: Last 3 candles:`, limitedCandles.slice(-3).map(candle => ({
        time: new Date(candle.time * 1000).toISOString(),
        close: candle.close
      })));
      
      return {
        status: 'success',
        candles: limitedCandles,
        info: {
          message: 'Data fetched from Polygon API',
          count: limitedCandles.length,
          source: 'polygon_api',
          latest: new Date(limitedCandles[limitedCandles.length - 1].time * 1000).toISOString()
        }
      };
      
    } catch (error) {
      console.error(`❌ Frontend: Error fetching from Polygon API:`, error);
      return {
        status: 'error',
        message: error.message,
        candles: []
      };
    }
  }

  /**
   * Fetch candles for a specific date range (for trading period charts)
   */
  async fetchCandlesForDateRange(symbol, timeframe, startDate, endDate) {
    try {
      console.log(`🔄 Frontend: Fetching candles for date range: ${symbol} ${timeframe}`);
      console.log(`📅 Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Format symbol and timeframe
      const polygonSymbol = this._formatSymbol(symbol);
      const { multiplier, timespan } = this._mapTimeframeToPolygon(timeframe);

      // Format dates for API
      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      // Construct API URL
      const url = `${this.baseUrl}/${polygonSymbol}/range/${multiplier}/${timespan}/${startDateStr}/${endDateStr}`;

      // API parameters
      const params = new URLSearchParams({
        apiKey: this.apiKey,
        limit: 50000 // Large limit to get all available data in range
      });

      console.log(`🔗 Frontend URL: ${url}?${params}`);

      // Make API request
      const response = await fetch(`${url}?${params}`);

      if (response.status === 429) {
        throw new Error('Rate limited by Polygon API');
      }

      if (!response.ok) {
        throw new Error(`Polygon API request failed: ${response.status}`);
      }

      const data = await response.json();
      const results = data.results || [];

      if (!results.length) {
        console.warn(`⚠️ No candle data returned from Polygon for date range: ${startDateStr} to ${endDateStr}`);
        console.warn(`⚠️ API URL was: ${url}?${params}`);
        return {
          status: 'success',
          candles: [],
          info: { message: `No data available for date range ${startDateStr} to ${endDateStr}`, count: 0 }
        };
      }

      // Convert to our format
      const candles = results.map(candle => ({
        time: Math.floor(candle.t / 1000), // Convert from milliseconds
        open: parseFloat(candle.o),
        high: parseFloat(candle.h),
        low: parseFloat(candle.l),
        close: parseFloat(candle.c),
        volume: parseInt(candle.v || 0)
      }));

      // Sort by time
      candles.sort((a, b) => a.time - b.time);

      console.log(`✅ Frontend: Successfully fetched ${candles.length} candles for date range`);
      console.log(`📊 Frontend: Range: ${new Date(candles[0].time * 1000).toISOString()} to ${new Date(candles[candles.length - 1].time * 1000).toISOString()}`);

      return {
        status: 'success',
        candles: candles,
        info: {
          message: 'Data fetched from Polygon API for date range',
          count: candles.length,
          source: 'polygon_api_range',
          startDate: startDateStr,
          endDate: endDateStr
        }
      };

    } catch (error) {
      console.error(`❌ Frontend: Error fetching candles for date range:`, error);
      return {
        status: 'error',
        message: error.message,
        candles: []
      };
    }
  }

  /**
   * Get candles with buffer management (same as backend approach)
   */
  async getCandles(symbol, timeframe, count = 1000) {
    const subscriptionKey = `${symbol}_${timeframe}`;

    try {
      console.log(`🔄 Frontend: Getting candles for ${symbol} ${timeframe}`);

      // Always fetch fresh 1000 candles from Polygon (same as backend)
      const result = await this.fetchCandles(symbol, timeframe, count);

      if (result.status !== 'success' || !result.candles.length) {
        console.warn(`⚠️ Frontend: Failed to fetch candles: ${result.message || 'No data'}`);
        return result;
      }
      
      // Replace entire buffer with fresh candles (same as backend)
      this.candleBuffer.set(subscriptionKey, result.candles);
      
      // Update last candle time
      const latestCandle = result.candles[result.candles.length - 1];
      this.lastCandleTime.set(subscriptionKey, new Date(latestCandle.time * 1000));
      
      console.log(`🔄 Frontend: Replaced entire buffer with ${result.candles.length} fresh candles for ${symbol} ${timeframe}`);
      console.log(`📊 Frontend: Latest candle: ${new Date(latestCandle.time * 1000).toISOString()}`);
      
      return result;
      
    } catch (error) {
      console.error(`❌ Frontend: Error in getCandles:`, error);
      return {
        status: 'error',
        message: error.message,
        candles: []
      };
    }
  }

  /**
   * Get cached candles from buffer
   */
  getCachedCandles(symbol, timeframe) {
    const subscriptionKey = `${symbol}_${timeframe}`;
    return this.candleBuffer.get(subscriptionKey) || [];
  }

  /**
   * Check if we have fresh data
   */
  hasRecentData(symbol, timeframe, maxAgeMinutes = 10) {
    const subscriptionKey = `${symbol}_${timeframe}`;
    const lastTime = this.lastCandleTime.get(subscriptionKey);
    
    if (!lastTime) return false;
    
    const ageMinutes = (Date.now() - lastTime.getTime()) / (1000 * 60);
    return ageMinutes <= maxAgeMinutes;
  }

  /**
   * Start polling for updates
   */
  startPolling(symbol, timeframe, callback, intervalMinutes = null) {
    const subscriptionKey = `${symbol}_${timeframe}`;
    
    // Calculate polling interval based on timeframe
    if (!intervalMinutes) {
      const timeframeMinutes = this._getTimeframeMinutes(timeframe);
      intervalMinutes = timeframeMinutes; // Poll every timeframe period
    }
    
    console.log(`🔄 Frontend: Starting polling for ${symbol} ${timeframe} every ${intervalMinutes} minutes`);
    
    const pollFunction = async () => {
      try {
        const result = await this.getCandles(symbol, timeframe);
        if (result.status === 'success' && callback) {
          callback(result.candles);
        }
      } catch (error) {
        console.error(`❌ Frontend: Polling error for ${subscriptionKey}:`, error);
      }
    };
    
    // Start polling
    const intervalId = setInterval(pollFunction, intervalMinutes * 60 * 1000);
    
    // Store interval ID for cleanup
    if (!this.pollingIntervals) {
      this.pollingIntervals = new Map();
    }
    this.pollingIntervals.set(subscriptionKey, intervalId);
    
    return intervalId;
  }

  /**
   * Stop polling for a specific symbol/timeframe
   */
  stopPolling(symbol, timeframe) {
    const subscriptionKey = `${symbol}_${timeframe}`;
    
    if (this.pollingIntervals && this.pollingIntervals.has(subscriptionKey)) {
      clearInterval(this.pollingIntervals.get(subscriptionKey));
      this.pollingIntervals.delete(subscriptionKey);
      console.log(`🛑 Frontend: Stopped polling for ${subscriptionKey}`);
    }
  }

  /**
   * Stop all polling
   */
  stopAllPolling() {
    if (this.pollingIntervals) {
      this.pollingIntervals.forEach((intervalId, key) => {
        clearInterval(intervalId);
        console.log(`🛑 Frontend: Stopped polling for ${key}`);
      });
      this.pollingIntervals.clear();
    }
  }

  /**
   * Clear all buffers and cache
   */
  clearCache() {
    this.candleBuffer.clear();
    this.lastCandleTime.clear();
    console.log(`🧹 Frontend: Cleared all candle buffers and cache`);
  }
}

// Create singleton instance
const polygonApiService = new PolygonApiService();

export default polygonApiService;
