/**
 * WebSocket Data Service for Real-time Candle Data
 *
 * Connects to the WebSocket Data Distribution Service to receive real-time
 * candle data instead of polling the Polygon API directly.
 *
 * Supports both local and production WebSocket services with automatic
 * authentication for production connections.
 */

import { WebSocketConfig, getWebSocketConnectionUrl, logWebSocketConfig } from '../config/websocket.js';

class WebSocketDataService {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000; // 5 seconds
    this.subscriptions = new Map(); // symbol_timeframe -> callback
    this.messageHandlers = new Map();
    this.connectionPromise = null;

    // Setup message handlers
    this.setupMessageHandlers();

    // Log configuration on initialization
    logWebSocketConfig();

    console.log('🌐 WebSocket Data Service initialized');
  }

  setupMessageHandlers() {
    this.messageHandlers.set('connection_established', this.handleConnectionEstablished.bind(this));
    this.messageHandlers.set('subscription_confirmed', this.handleSubscriptionConfirmed.bind(this));
    this.messageHandlers.set('initial_data', this.handleInitialData.bind(this));
    this.messageHandlers.set('catch_up_data', this.handleCatchUpData.bind(this));
    this.messageHandlers.set('candle_data', this.handleCandleData.bind(this));
    this.messageHandlers.set('error', this.handleError.bind(this));
    this.messageHandlers.set('pong', this.handlePong.bind(this));
  }

  async connect() {
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    this.connectionPromise = new Promise(async (resolve, reject) => {
      try {
        // Get WebSocket connection URL with proper authentication
        const wsUrl = await getWebSocketConnectionUrl();

        console.log(`🔗 Connecting to WebSocket Data Service: ${wsUrl.replace(/\?token=.*/, '?token=***').replace(/\?dev=.*/, '?dev=***')}`);
        console.log(`🔧 Environment: ${WebSocketConfig.useProduction ? 'PRODUCTION' : 'LOCAL'}`);
        console.log(`🔐 Authentication: ${WebSocketConfig.requiresAuth() ? 'REQUIRED' : 'NOT REQUIRED'}`);

        if (wsUrl.includes('?dev=')) {
          console.log(`🔧 Using development mode bypass for production service`);
        }

        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
          console.log('✅ Connected to WebSocket Data Service');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };
        
        this.ws.onclose = (event) => {
          console.log(`🔌 WebSocket connection closed: ${event.code} ${event.reason}`);
          this.isConnected = false;
          this.connectionPromise = null;
          
          // Attempt to reconnect if not a clean close
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };
        
        this.ws.onerror = (error) => {
          console.error('❌ WebSocket error:', error);
          this.isConnected = false;
          this.connectionPromise = null;
          reject(error);
        };
        
      } catch (error) {
        console.error('❌ Failed to create WebSocket connection:', error);
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  scheduleReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('❌ Reconnect failed:', error);
      });
    }, delay);
  }

  handleMessage(data) {
    try {
      const message = JSON.parse(data);
      const handler = this.messageHandlers.get(message.type);
      
      if (handler) {
        handler(message);
      } else {
        console.warn(`⚠️ Unknown message type: ${message.type}`, message);
      }
    } catch (error) {
      console.error('❌ Error parsing WebSocket message:', error, data);
    }
  }

  handleConnectionEstablished(message) {
    console.log('🎉 Connection established:', message.connection_id);
  }

  handleSubscriptionConfirmed(message) {
    console.log(`✅ Subscription confirmed: ${message.symbol} ${message.timeframe}`);
  }

  handleInitialData(message) {
    const subscriptionKey = `${message.symbol}_${message.timeframe}`;
    const callback = this.subscriptions.get(subscriptionKey);

    if (callback) {
      console.log(`📊 Received initial data for ${subscriptionKey}: ${message.count} candles`);
      // Call the callback with initial data flag
      callback(message.candles, true); // true indicates this is initial data
    } else {
      console.warn(`⚠️ No callback registered for ${subscriptionKey}`);
    }
  }

  handleCatchUpData(message) {
    const subscriptionKey = `${message.symbol}_${message.timeframe}`;
    const callback = this.subscriptions.get(subscriptionKey);

    if (callback) {
      console.log(`🔄 Received catch-up data for ${subscriptionKey}: ${message.count} candles`);
      callback(message.candles, true); // true indicates this is catch-up data (array of candles)
    } else {
      console.warn(`⚠️ No callback registered for catch-up data: ${subscriptionKey}`);
    }
  }

  handleCandleData(message) {
    const subscriptionKey = `${message.symbol}_${message.timeframe}`;
    const callback = this.subscriptions.get(subscriptionKey);

    if (callback) {
      console.log(`📊 Received candle data for ${subscriptionKey}:`, message.candle);
      callback(message.candle, false); // false indicates this is real-time update
    } else {
      console.warn(`⚠️ No callback registered for ${subscriptionKey}`);
    }
  }

  handleError(message) {
    console.error('❌ WebSocket error message:', message.message);
  }

  handlePong(message) {
    console.log('🏓 Pong received');
  }

  async subscribe(symbol, timeframe, callback, sinceTimestamp = null) {
    const subscriptionKey = `${symbol}_${timeframe}`;

    // Store the callback
    this.subscriptions.set(subscriptionKey, callback);

    // Ensure we're connected
    if (!this.isConnected) {
      await this.connect();
    }

    // Send subscription message with optional sinceTimestamp
    const message = {
      type: 'subscribe',
      symbol: symbol,
      timeframe: timeframe
    };

    // Add sinceTimestamp if provided
    if (sinceTimestamp) {
      message.sinceTimestamp = sinceTimestamp;
      console.log(`🔄 Subscribing to ${subscriptionKey} with catch-up since ${new Date(sinceTimestamp * 1000).toISOString()}`);
    }

    this.sendMessage(message);
    console.log(`📈 Subscribed to ${subscriptionKey}`);
  }

  async unsubscribe(symbol, timeframe) {
    const subscriptionKey = `${symbol}_${timeframe}`;
    
    // Remove the callback
    this.subscriptions.delete(subscriptionKey);
    
    // Send unsubscription message if connected
    if (this.isConnected) {
      const message = {
        type: 'unsubscribe',
        symbol: symbol,
        timeframe: timeframe
      };
      
      this.sendMessage(message);
    }
    
    console.log(`📉 Unsubscribed from ${subscriptionKey}`);
  }

  sendMessage(message) {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('⚠️ Cannot send message: WebSocket not connected');
    }
  }

  ping() {
    this.sendMessage({ type: 'ping' });
  }

  getStatus() {
    this.sendMessage({ type: 'get_status' });
  }

  disconnect() {
    if (this.ws) {
      console.log('🔌 Disconnecting from WebSocket Data Service');
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
      this.isConnected = false;
      this.connectionPromise = null;
    }
  }

  // Utility method to check connection status
  isConnectionReady() {
    return this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN;
  }

  // Get subscription statistics
  getSubscriptionStats() {
    return {
      totalSubscriptions: this.subscriptions.size,
      subscriptions: Array.from(this.subscriptions.keys()),
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// Create singleton instance
const websocketDataService = new WebSocketDataService();

export default websocketDataService;
