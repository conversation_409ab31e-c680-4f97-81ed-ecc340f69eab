import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { onAuthStateChanged } from 'firebase/auth';
import { auth } from '../../firebaseConfig';

/**
 * A custom hook to handle authentication and protect routes
 * This provides consistent auth behavior across the application
 */
export function useAuthGuard() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  useEffect(() => {
    let mounted = true;
    let isNavigating = false;
    
    // Check if we have a stored user ID and recent navigation
    const storedUserId = localStorage.getItem("user_id");
    const lastNavigation = localStorage.getItem("last_navigation");
    const isRecentNavigation = lastNavigation && 
      (new Date().getTime() - new Date(lastNavigation).getTime() < 5000); // Within 5 seconds
    
    // Handle route change start
    const handleRouteChangeStart = () => {
      console.log("Route change starting in useAuthGuard");
      isNavigating = true;
    };
    
    // Handle route change complete
    const handleRouteChangeComplete = () => {
      console.log("Route change complete in useAuthGuard");
      isNavigating = false;
    };
    
    // Add router event listeners
    router.events.on('routeChangeStart', handleRouteChangeStart);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);
    
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      // Only proceed if component is still mounted and not navigating
      if (!mounted) return;
      
      if (currentUser) {
        console.log("User authenticated in useAuthGuard:", currentUser.uid);
        setUser(currentUser);
        setIsAuthenticated(true);
        
        // Ensure user_id is set in localStorage
        localStorage.setItem("user_id", currentUser.uid);
        
        // Always update loading state
        setLoading(false);
      } else if (isNavigating && storedUserId) {
        // We're navigating and have a stored user ID
        // Don't change authentication state during navigation
        console.log("Navigation in progress, maintaining auth state with stored ID:", storedUserId);
      } else if (!storedUserId) {
        // No stored user ID, definitely not authenticated
        console.log("No stored user ID in useAuthGuard, not authenticated");
        setUser(null);
        setIsAuthenticated(false);
        setLoading(false);
        
        // Only redirect if we're on a protected route
        if (!isPublicRoute(router.pathname)) {
          router.push("/login");
        }
      } else if (isRecentNavigation) {
        // Recent navigation with stored user ID but no current user
        // This is likely a temporary state during navigation, so don't redirect
        console.log("Recent navigation detected, maintaining auth state despite missing user");
      } else {
        // We have a stored ID but no current user and it's not during navigation
        // Wait a bit longer before deciding
        setTimeout(() => {
          if (!auth.currentUser && mounted && !isNavigating) {
            console.log("User not authenticated after delay in useAuthGuard");
            setUser(null);
            setIsAuthenticated(false);
            setLoading(false);
            
            // Only redirect if we're on a protected route
            if (!isPublicRoute(router.pathname)) {
              router.push("/login");
            }
          }
        }, 1000);
      }
    });

    return () => {
      mounted = false;
      unsubscribe();
      router.events.off('routeChangeStart', handleRouteChangeStart);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
    };
  }, [router]);

  return { user, loading, isAuthenticated };
}

// Helper function to determine if a route is public
function isPublicRoute(pathname) {
  const publicRoutes = ['/login', '/signup', '/reset-password', '/'];
  return publicRoutes.includes(pathname);
}

export default useAuthGuard;
