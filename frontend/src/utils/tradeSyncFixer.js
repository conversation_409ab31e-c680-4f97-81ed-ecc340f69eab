/**
 * Trade Synchronization Fixer
 * 
 * This utility helps diagnose and fix trade data synchronization issues
 * between OANDA (actual broker) and Firebase (frontend data source).
 */

import { db } from '../../firebaseConfig';
import { collection, getDocs, doc, updateDoc, query, where } from 'firebase/firestore';

export class TradeSyncFixer {
  constructor(userId, strategyId) {
    this.userId = userId;
    this.strategyId = strategyId;
  }

  /**
   * Diagnose trade synchronization issues
   */
  async diagnose() {
    try {
      console.log('🔍 Diagnosing trade synchronization issues...');
      
      // Get all trades from Firebase
      const trades = await this.getFirebaseTrades();
      
      // Classify trades
      const openTrades = trades.filter(trade => this.isTradeConsideredOpen(trade));
      const closedTrades = trades.filter(trade => !this.isTradeConsideredOpen(trade));
      
      const diagnosis = {
        timestamp: new Date().toISOString(),
        totalTrades: trades.length,
        openTrades: openTrades.length,
        closedTrades: closedTrades.length,
        openTradeDetails: openTrades.map(trade => ({
          tradeID: trade.tradeID,
          instrument: trade.instrument,
          status: trade.status,
          openTime: trade.openTime,
          closeTime: trade.closeTime,
          realizedPL: trade.realizedPL,
          unrealizedPL: trade.unrealizedPL
        })),
        suspiciousPatterns: []
      };

      // Check for suspicious patterns
      
      // Pattern 1: Trades with no closeTime but status is not OPEN
      const suspiciousStatus = openTrades.filter(trade => 
        trade.status && 
        !['OPEN', 'open'].includes(trade.status) && 
        !trade.closeTime
      );
      
      if (suspiciousStatus.length > 0) {
        diagnosis.suspiciousPatterns.push({
          type: 'inconsistent_status',
          count: suspiciousStatus.length,
          description: 'Trades with non-OPEN status but no closeTime',
          trades: suspiciousStatus.map(t => t.tradeID)
        });
      }

      // Pattern 2: Very old open trades (older than 7 days)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const oldOpenTrades = openTrades.filter(trade => {
        const openTime = trade.openTime?.toDate ? trade.openTime.toDate() : new Date(trade.openTime);
        return openTime < sevenDaysAgo;
      });

      if (oldOpenTrades.length > 0) {
        diagnosis.suspiciousPatterns.push({
          type: 'old_open_trades',
          count: oldOpenTrades.length,
          description: 'Open trades older than 7 days (likely stale)',
          trades: oldOpenTrades.map(t => t.tradeID)
        });
      }

      // Pattern 3: Multiple open trades for same instrument
      const instrumentGroups = {};
      openTrades.forEach(trade => {
        const instrument = trade.instrument;
        if (!instrumentGroups[instrument]) {
          instrumentGroups[instrument] = [];
        }
        instrumentGroups[instrument].push(trade.tradeID);
      });

      const multipleTradesPerInstrument = Object.entries(instrumentGroups)
        .filter(([instrument, tradeIds]) => tradeIds.length > 1);

      if (multipleTradesPerInstrument.length > 0) {
        diagnosis.suspiciousPatterns.push({
          type: 'multiple_trades_per_instrument',
          count: multipleTradesPerInstrument.length,
          description: 'Multiple open trades for same instrument (violates one-trade-per-instrument rule)',
          details: multipleTradesPerInstrument.map(([instrument, tradeIds]) => ({
            instrument,
            tradeIds,
            count: tradeIds.length
          }))
        });
      }

      console.log('📊 Diagnosis complete:', diagnosis);
      return diagnosis;

    } catch (error) {
      console.error('❌ Error during diagnosis:', error);
      throw error;
    }
  }

  /**
   * Fix identified issues by marking stale trades as closed
   */
  async fixStaleOpenTrades(tradeIds = null) {
    try {
      console.log('🔧 Starting to fix stale open trades...');
      
      let tradesToFix = [];
      
      if (tradeIds) {
        // Fix specific trades
        tradesToFix = tradeIds;
      } else {
        // Auto-detect trades to fix
        const diagnosis = await this.diagnose();
        
        // Collect all suspicious trade IDs
        diagnosis.suspiciousPatterns.forEach(pattern => {
          if (pattern.trades) {
            tradesToFix.push(...pattern.trades);
          }
          if (pattern.details) {
            pattern.details.forEach(detail => {
              if (detail.tradeIds) {
                tradesToFix.push(...detail.tradeIds);
              }
            });
          }
        });
      }

      // Remove duplicates
      tradesToFix = [...new Set(tradesToFix)];

      if (tradesToFix.length === 0) {
        console.log('✅ No stale trades found to fix');
        return { fixed: 0, errors: [] };
      }

      console.log(`🔧 Fixing ${tradesToFix.length} stale trades:`, tradesToFix);

      const results = {
        fixed: 0,
        errors: []
      };

      // Fix each trade
      for (const tradeId of tradesToFix) {
        try {
          await this.markTradeAsClosed(tradeId);
          results.fixed++;
          console.log(`✅ Fixed trade ${tradeId}`);
        } catch (error) {
          console.error(`❌ Error fixing trade ${tradeId}:`, error);
          results.errors.push({
            tradeId,
            error: error.message
          });
        }
      }

      console.log(`🎉 Fix complete: ${results.fixed} trades fixed, ${results.errors.length} errors`);
      return results;

    } catch (error) {
      console.error('❌ Error during fix:', error);
      throw error;
    }
  }

  /**
   * Get all trades from Firebase
   */
  async getFirebaseTrades() {
    try {
      const tradeHistoryRef = collection(
        db,
        'users',
        this.userId,
        'submittedStrategies',
        this.strategyId,
        'tradeHistory'
      );

      const snapshot = await getDocs(tradeHistoryRef);
      const trades = [];

      snapshot.forEach(doc => {
        const data = doc.data();
        trades.push({
          id: doc.id,
          ...data,
          // Convert Firestore timestamps to Date objects
          openTime: data.openTime?.toDate ? data.openTime.toDate() : new Date(data.openTime),
          closeTime: data.closeTime?.toDate ? data.closeTime.toDate() : (data.closeTime ? new Date(data.closeTime) : null)
        });
      });

      return trades;
    } catch (error) {
      console.error('Error fetching Firebase trades:', error);
      throw error;
    }
  }

  /**
   * Check if a trade is considered "open" by the frontend logic
   */
  isTradeConsideredOpen(trade) {
    return (
      trade.status === 'OPEN' || 
      trade.status === 'open' ||
      (trade.status !== 'CLOSED' && trade.status !== 'closed' && !trade.closeTime)
    );
  }

  /**
   * Mark a specific trade as closed
   */
  async markTradeAsClosed(tradeId, closeDetails = {}) {
    try {
      const tradeRef = doc(
        db,
        'users',
        this.userId,
        'submittedStrategies',
        this.strategyId,
        'tradeHistory',
        tradeId
      );

      const updateData = {
        status: 'CLOSED',
        closeTime: closeDetails.closeTime || new Date().toISOString(),
        realizedPL: closeDetails.realizedPL || 0.0,
        note: closeDetails.note || 'Trade marked as closed by sync fixer'
      };

      await updateDoc(tradeRef, updateData);
      console.log(`✅ Marked trade ${tradeId} as closed`);

    } catch (error) {
      console.error(`❌ Error marking trade ${tradeId} as closed:`, error);
      throw error;
    }
  }

  /**
   * Generate a detailed report
   */
  async generateReport() {
    try {
      const diagnosis = await this.diagnose();
      
      const report = {
        ...diagnosis,
        recommendations: []
      };

      // Generate recommendations based on findings
      if (diagnosis.suspiciousPatterns.length === 0) {
        report.recommendations.push({
          type: 'success',
          message: 'No synchronization issues detected. Trade data appears to be in sync.'
        });
      } else {
        diagnosis.suspiciousPatterns.forEach(pattern => {
          switch (pattern.type) {
            case 'inconsistent_status':
              report.recommendations.push({
                type: 'fix',
                message: `Fix ${pattern.count} trades with inconsistent status by marking them as closed.`,
                action: 'markAsClosed',
                tradeIds: pattern.trades
              });
              break;
            
            case 'old_open_trades':
              report.recommendations.push({
                type: 'fix',
                message: `Fix ${pattern.count} old open trades (likely closed in OANDA but not updated in Firebase).`,
                action: 'markAsClosed',
                tradeIds: pattern.trades
              });
              break;
            
            case 'multiple_trades_per_instrument':
              report.recommendations.push({
                type: 'warning',
                message: `Found multiple open trades per instrument, which violates the one-trade-per-instrument rule. Consider closing older trades.`,
                details: pattern.details
              });
              break;
          }
        });
      }

      return report;
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }
}

/**
 * Quick fix function for immediate use
 */
export async function quickFixTradeSyncIssues(userId, strategyId) {
  try {
    console.log('🚀 Starting quick fix for trade sync issues...');
    
    const fixer = new TradeSyncFixer(userId, strategyId);
    
    // Generate report
    const report = await fixer.generateReport();
    console.log('📊 Trade Sync Report:', report);
    
    // Auto-fix if there are issues
    if (report.suspiciousPatterns.length > 0) {
      console.log('🔧 Auto-fixing detected issues...');
      const fixResults = await fixer.fixStaleOpenTrades();
      console.log('✅ Fix results:', fixResults);
      
      return {
        report,
        fixResults,
        success: true,
        message: `Fixed ${fixResults.fixed} trades with ${fixResults.errors.length} errors`
      };
    } else {
      return {
        report,
        success: true,
        message: 'No issues detected - trade data is in sync'
      };
    }
    
  } catch (error) {
    console.error('❌ Quick fix failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
