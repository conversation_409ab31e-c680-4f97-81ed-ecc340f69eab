/**
 * Indicator calculation utilities for real-time chart updates
 * These functions calculate technical indicators from candle data
 */

/**
 * Calculate Simple Moving Average (SMA)
 * @param {Array} data - Array of price values
 * @param {number} period - SMA period
 * @returns {Array} Array of SMA values with time
 */
export const calculateSMA = (candleData, period = 14, source = 'close') => {
  if (!candleData || candleData.length < period) {
    return [];
  }

  // Helper function to get source value from candle data
  const getSourceValue = (candle, sourceField) => {
    // Handle special case where source is "price" - default to 'close'
    if (sourceField === 'price') {
      return candle.close;
    }

    // Handle case where source field doesn't exist - default to 'close'
    const actualSource = candle[sourceField] !== undefined ? sourceField : 'close';

    // Get the value based on source type
    switch (actualSource.toLowerCase()) {
      case 'open':
        return candle.open;
      case 'high':
        return candle.high;
      case 'low':
        return candle.low;
      case 'volume':
        return candle.volume || 0;
      case 'close':
      default:
        return candle.close;
    }
  };

  const result = [];

  for (let i = period - 1; i < candleData.length; i++) {
    let sum = 0;
    for (let j = i - period + 1; j <= i; j++) {
      const sourceValue = getSourceValue(candleData[j], source);
      const parsedValue = parseFloat(sourceValue);

      if (isNaN(parsedValue)) {
        console.warn(`🔍 SMA: NaN value found at index ${j}, source: ${source}, value: ${sourceValue}`);
        return []; // Return empty array if we encounter NaN
      }

      sum += parsedValue;
    }
    const smaValue = sum / period;

    result.push({
      time: candleData[i].time,
      value: smaValue
    });
  }

  return result;
};

/**
 * Calculate Exponential Moving Average (EMA)
 * @param {Array} candleData - Array of candle data
 * @param {number} period - EMA period
 * @param {string} source - Price source (open, high, low, close)
 * @returns {Array} Array of EMA values with time
 */
export const calculateEMA = (candleData, period = 14, source = 'close') => {
  if (!candleData || candleData.length < period) {
    return [];
  }

  // Helper function to get source value from candle data
  const getSourceValue = (candle, sourceField) => {
    // Handle special case where source is "price" - default to 'close'
    if (sourceField === 'price') {
      return candle.close;
    }

    // Handle case where source field doesn't exist - default to 'close'
    const actualSource = candle[sourceField] !== undefined ? sourceField : 'close';

    // Get the value based on source type
    switch (actualSource.toLowerCase()) {
      case 'open':
        return candle.open;
      case 'high':
        return candle.high;
      case 'low':
        return candle.low;
      case 'volume':
        return candle.volume || 0;
      case 'close':
      default:
        return candle.close;
    }
  };

  const result = [];
  const multiplier = 2 / (period + 1);

  // Calculate initial SMA for the first EMA value
  let sum = 0;
  for (let i = 0; i < period; i++) {
    const sourceValue = getSourceValue(candleData[i], source);
    const parsedValue = parseFloat(sourceValue);

    if (isNaN(parsedValue)) {
      console.warn(`🔍 EMA: NaN value found at index ${i}, source: ${source}, value: ${sourceValue}`);
      return []; // Return empty array if we encounter NaN
    }

    sum += parsedValue;
  }
  let ema = sum / period;

  result.push({
    time: candleData[period - 1].time,
    value: ema
  });

  // Calculate EMA for remaining values
  for (let i = period; i < candleData.length; i++) {
    const currentPrice = parseFloat(getSourceValue(candleData[i], source));

    if (isNaN(currentPrice)) {
      console.warn(`🔍 EMA: NaN value found at index ${i}, source: ${source}, value: ${getSourceValue(candleData[i], source)}`);
      return result; // Return what we have so far
    }

    ema = (currentPrice * multiplier) + (ema * (1 - multiplier));

    result.push({
      time: candleData[i].time,
      value: ema
    });
  }

  return result;
};

/**
 * Calculate Relative Strength Index (RSI)
 * @param {Array} candleData - Array of candle data
 * @param {number} period - RSI period
 * @param {string} source - Price source (open, high, low, close)
 * @returns {Array} Array of RSI values with time
 */
export const calculateRSI = (candleData, period = 14, source = 'close') => {
  if (!candleData || candleData.length < period + 1) {
    return [];
  }

  const result = [];
  const gains = [];
  const losses = [];

  // Calculate price changes
  for (let i = 1; i < candleData.length; i++) {
    // Handle special case where source is "price" - default to 'close'
    let actualSource = source;
    if (source === 'price') {
      actualSource = 'close';
    } else if (candleData[i][source] === undefined) {
      actualSource = 'close';
    }

    const currentPrice = parseFloat(candleData[i][actualSource]);
    const previousPrice = parseFloat(candleData[i - 1][actualSource]);

    const change = currentPrice - previousPrice;
    gains.push(change > 0 ? change : 0);
    losses.push(change < 0 ? Math.abs(change) : 0);
  }

  // Calculate initial average gain and loss
  let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
  let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;

  // Calculate RSI for the first valid point
  let rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
  let rsi = 100 - (100 / (1 + rs));

  result.push({
    time: candleData[period].time,
    value: rsi
  });

  // Calculate RSI for remaining points using smoothed averages
  for (let i = period + 1; i < candleData.length; i++) {
    const currentGain = gains[i - 1];
    const currentLoss = losses[i - 1];

    // Smoothed averages
    avgGain = (avgGain * (period - 1) + currentGain) / period;
    avgLoss = (avgLoss * (period - 1) + currentLoss) / period;

    rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
    rsi = 100 - (100 / (1 + rs));

    result.push({
      time: candleData[i].time,
      value: rsi
    });
  }

  return result;
};

/**
 * Calculate MACD (Moving Average Convergence Divergence)
 * @param {Array} candleData - Array of candle data
 * @param {number} fastPeriod - Fast EMA period (default: 12)
 * @param {number} slowPeriod - Slow EMA period (default: 26)
 * @param {number} signalPeriod - Signal line EMA period (default: 9)
 * @param {string} source - Price source (open, high, low, close)
 * @returns {Object} Object containing macd, signal, and histogram arrays
 */
export const calculateMACD = (candleData, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9, source = 'close') => {
  if (!candleData || candleData.length < slowPeriod) {
    return { macd: [], signal: [], histogram: [] };
  }

  // Calculate fast and slow EMAs
  const fastEMA = calculateEMA(candleData, fastPeriod, source);
  const slowEMA = calculateEMA(candleData, slowPeriod, source);

  console.log('🔍 MACD EMA Debug:', {
    fastEMALength: fastEMA.length,
    slowEMALength: slowEMA.length,
    fastEMASample: fastEMA.slice(0, 3),
    slowEMASample: slowEMA.slice(0, 3)
  });

  // Calculate MACD line (fast EMA - slow EMA)
  const macdLine = [];

  console.log('🔍 MACD Line Calculation Debug:', {
    fastEMALength: fastEMA.length,
    slowEMALength: slowEMA.length,
    fastEMAFirst3Times: fastEMA.slice(0, 3).map(d => d.time),
    slowEMAFirst3Times: slowEMA.slice(0, 3).map(d => d.time),
    slowEMAFirstTime: slowEMA[0]?.time,
    fastEMAFirstTime: fastEMA[0]?.time
  });

  // Find the starting point where both EMAs have data
  // Since slow EMA starts later, we need to find where slow EMA starts in the fast EMA array
  const slowStartTime = slowEMA[0]?.time;
  const fastStartIndex = fastEMA.findIndex(point => point.time === slowStartTime);

  console.log('🔍 MACD Alignment Debug:', {
    slowStartTime: slowStartTime,
    fastStartIndex: fastStartIndex,
    alignmentCheck: fastStartIndex >= 0 ? 'Found alignment' : 'No alignment found'
  });

  if (fastStartIndex >= 0) {
    // Calculate MACD from the aligned starting point
    for (let i = 0; i < slowEMA.length; i++) {
      const fastIndex = fastStartIndex + i;
      if (fastIndex < fastEMA.length) {
        macdLine.push({
          time: slowEMA[i].time,
          value: fastEMA[fastIndex].value - slowEMA[i].value
        });
      }
    }
  }

  console.log('🔍 MACD Line Result:', {
    macdLineLength: macdLine.length,
    macdLineSample: macdLine.slice(0, 3)
  });

  // Calculate signal line (EMA of MACD line)
  const signalLine = calculateEMAFromValues(macdLine, signalPeriod);

  console.log('🔍 Histogram Calculation Debug:', {
    macdLineLength: macdLine.length,
    signalLineLength: signalLine.length,
    macdFirst3Times: macdLine.slice(0, 3).map(d => d.time),
    signalFirst3Times: signalLine.slice(0, 3).map(d => d.time)
  });

  // Calculate histogram (MACD - Signal)
  const histogram = [];

  // Find alignment between MACD line and signal line
  const signalStartTime = signalLine[0]?.time;
  const macdStartIndex = macdLine.findIndex(point => point.time === signalStartTime);

  console.log('🔍 Histogram Alignment Debug:', {
    signalStartTime: signalStartTime,
    macdStartIndex: macdStartIndex,
    alignmentCheck: macdStartIndex >= 0 ? 'Found alignment' : 'No alignment found'
  });

  if (macdStartIndex >= 0) {
    // Calculate histogram from the aligned starting point
    for (let i = 0; i < signalLine.length; i++) {
      const macdIndex = macdStartIndex + i;
      if (macdIndex < macdLine.length) {
        histogram.push({
          time: signalLine[i].time,
          value: macdLine[macdIndex].value - signalLine[i].value
        });
      }
    }
  }

  console.log('🔍 Histogram Result:', {
    histogramLength: histogram.length,
    histogramSample: histogram.slice(0, 3)
  });

  return {
    macd: macdLine,
    signal: signalLine,
    histogram: histogram
  };
};

/**
 * Calculate Bollinger Bands
 * @param {Array} candleData - Array of candle data
 * @param {number} period - Period for moving average (default: 20)
 * @param {number} stdDev - Standard deviation multiplier (default: 2)
 * @param {string} source - Price source (open, high, low, close)
 * @returns {Array} Array of Bollinger Bands data with upper, middle, lower bands
 */
export const calculateBollingerBands = (candleData, period = 20, stdDev = 2, source = 'close') => {
  if (!candleData || candleData.length < period) {
    return [];
  }

  // Helper function to get source value from candle data
  const getSourceValue = (candle, sourceField) => {
    // Handle special case where source is "price" - default to 'close'
    if (sourceField === 'price') {
      return candle.close;
    }

    // Handle case where source field doesn't exist - default to 'close'
    const actualSource = candle[sourceField] !== undefined ? sourceField : 'close';

    // Get the value based on source type
    switch (actualSource.toLowerCase()) {
      case 'open':
        return candle.open;
      case 'high':
        return candle.high;
      case 'low':
        return candle.low;
      case 'volume':
        return candle.volume || 0;
      case 'close':
      default:
        return candle.close;
    }
  };

  const result = [];

  for (let i = period - 1; i < candleData.length; i++) {
    // Calculate SMA (middle band)
    let sum = 0;
    const values = [];

    for (let j = i - period + 1; j <= i; j++) {
      const sourceValue = getSourceValue(candleData[j], source);
      const parsedValue = parseFloat(sourceValue);

      if (isNaN(parsedValue)) {
        console.warn(`🔍 Bollinger Bands: NaN value found at index ${j}, source: ${source}, value: ${sourceValue}`);
        return []; // Return empty array if we encounter NaN
      }

      values.push(parsedValue);
      sum += parsedValue;
    }

    const sma = sum / period;

    // Calculate standard deviation
    let sumSquaredDiff = 0;
    for (let j = 0; j < values.length; j++) {
      const diff = values[j] - sma;
      sumSquaredDiff += diff * diff;
    }
    const standardDeviation = Math.sqrt(sumSquaredDiff / period);

    // Calculate upper and lower bands
    const upperBand = sma + (standardDeviation * stdDev);
    const lowerBand = sma - (standardDeviation * stdDev);

    result.push({
      time: candleData[i].time,
      upper: upperBand,
      middle: sma,
      lower: lowerBand
    });
  }

  return result;
};

/**
 * Calculate Average True Range (ATR)
 * @param {Array} candleData - Array of candle data
 * @param {number} period - ATR period (default: 14)
 * @returns {Array} Array of ATR values with time
 */
export const calculateATR = (candleData, period = 14) => {
  if (!candleData || candleData.length < period + 1) {
    return [];
  }

  const result = [];
  const trueRanges = [];

  // Calculate True Range for each candle
  for (let i = 1; i < candleData.length; i++) {
    const current = candleData[i];
    const previous = candleData[i - 1];

    const high = parseFloat(current.high);
    const low = parseFloat(current.low);
    const prevClose = parseFloat(previous.close);

    // True Range = max(High - Low, |High - Previous Close|, |Low - Previous Close|)
    const tr = Math.max(
      high - low,
      Math.abs(high - prevClose),
      Math.abs(low - prevClose)
    );

    trueRanges.push(tr);
  }

  // Calculate ATR using Wilder's smoothing method
  for (let i = period - 1; i < trueRanges.length; i++) {
    let atr;

    if (i === period - 1) {
      // First ATR value is simple average
      atr = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period;
    } else {
      // Subsequent ATR values use Wilder's smoothing
      const prevATR = result[result.length - 1].value;
      atr = (prevATR * (period - 1) + trueRanges[i]) / period;
    }

    result.push({
      time: candleData[i + 1].time, // +1 because trueRanges starts from index 1
      value: atr
    });
  }

  return result;
};

/**
 * Calculate Support and Resistance levels
 * @param {Array} candleData - Array of candle data
 * @param {number} leftBars - Number of bars to look back for pivot points (default: 10)
 * @param {number} rightBars - Number of bars to look ahead for pivot points (default: 10)
 * @returns {Array} Array of support and resistance levels
 */
export const calculateSupportResistance = (candleData, leftBars = 10, rightBars = 10) => {
  const startTime = performance.now();
  console.log(`🔍 S&R: OPTIMIZED pivot calculation with ${candleData?.length} candles, left=${leftBars}, right=${rightBars}`);

  try {

    // Safety checks
    if (!candleData || !Array.isArray(candleData) || candleData.length < 50) {
      console.log('🚫 S&R: Insufficient data (need at least 50 candles)');
      return [];
    }

    // PROCESS FULL DATASET for S&R lines throughout the year
    const dataToProcess = candleData; // Use full dataset

    console.log(`🔍 S&R: Processing FULL dataset with ${dataToProcess.length} candles`);

    // Use the ACTUAL user-provided parameters (respect user's choice)
    const safeLeftBars = Math.max(leftBars, 1);   // Minimum 1, no maximum limit
    const safeRightBars = Math.max(rightBars, 1);  // Minimum 1, no maximum limit

    console.log(`🔍 S&R: Using USER parameters - left=${safeLeftBars}, right=${safeRightBars} (user input: left=${leftBars}, right=${rightBars})`);

    const result = [];
    const highs = dataToProcess.map(d => parseFloat(d.high));
    const lows = dataToProcess.map(d => parseFloat(d.low));

    // Calculate the range we can scan
    const startIndex = safeLeftBars;
    const endIndex = dataToProcess.length - safeRightBars;
    const actualEndIndex = endIndex;
    const scanRange = actualEndIndex - startIndex;

    console.log(`🔍 S&R: Scanning FULL dataset from index ${startIndex} to ${actualEndIndex} (${scanRange} candles)`);
    console.log(`🔍 S&R: Data time range: ${dataToProcess[startIndex]?.time} to ${dataToProcess[actualEndIndex - 1]?.time}`);
    console.log(`🔍 S&R: Data date range: ${new Date(dataToProcess[startIndex]?.time * 1000).toISOString()} to ${new Date(dataToProcess[actualEndIndex - 1]?.time * 1000).toISOString()}`);

    // Add debugging to track pivot detection throughout the year
    let pivotsByMonth = {};
    let lastLoggedMonth = null;

    // Find pivot highs (resistance levels) - OPTIMIZED SCAN
    console.log(`🚀 S&R: Starting resistance scan from ${startIndex} to ${actualEndIndex}`);
    let resistanceCount = 0;

    for (let i = startIndex; i < actualEndIndex; i++) {
      // NO LIMITS - Find ALL resistance levels for trade-agent accuracy
      // (Removed artificial limits for complete S&R coverage)

      // Progress logging every 10000 candles with date info
      if (i % 10000 === 0) {
        const currentDate = new Date(dataToProcess[i].time * 1000).toISOString().substring(0, 7); // YYYY-MM
        console.log(`🔍 S&R: Resistance progress ${i}/${actualEndIndex} (${((i/actualEndIndex)*100).toFixed(1)}%) - ${currentDate} - Found: ${resistanceCount}`);
      }

      const currentHigh = highs[i];

    // SIMPLIFIED: Traditional pivot high detection (faster)
    let isPivotHigh = true;

    // Check left side (lookback) - must be higher than all
    for (let j = i - safeLeftBars; j < i; j++) {
      if (j >= 0 && highs[j] >= currentHigh) {
        isPivotHigh = false;
        break;
      }
    }

    // Check right side (lookahead) - must be higher than all
    if (isPivotHigh) {
      for (let j = i + 1; j <= i + safeRightBars; j++) {
        if (j < highs.length && highs[j] >= currentHigh) {
          isPivotHigh = false;
          break;
        }
      }
    }

      // If it's a valid pivot high, add it as resistance
      if (isPivotHigh) {
        const pivotDate = new Date(dataToProcess[i].time * 1000);
        const monthKey = pivotDate.toISOString().substring(0, 7); // YYYY-MM

        result.push({
          time: dataToProcess[i].time,
          value: currentHigh,
          type: 'resistance',
          strength: 1,
          index: i
        });
        resistanceCount++;

        // Track pivots by month
        if (!pivotsByMonth[monthKey]) pivotsByMonth[monthKey] = { resistance: 0, support: 0 };
        pivotsByMonth[monthKey].resistance++;

        if (resistanceCount <= 5 || monthKey !== lastLoggedMonth) {
          console.log(`🔴 Found resistance ${resistanceCount}: ${currentHigh.toFixed(5)} at ${pivotDate.toISOString().substring(0, 10)} (${monthKey})`);
          lastLoggedMonth = monthKey;
        }
      }
    }

  // Find pivot lows (support levels) - FULL SCAN
  let supportCount = 0;
  for (let i = startIndex; i < actualEndIndex; i++) {
    // NO LIMITS - Find ALL support levels for trade-agent accuracy
    // (Removed artificial limits for complete S&R coverage)

    // Progress logging every 10000 candles with date info
    if (i % 10000 === 0) {
      const currentDate = new Date(dataToProcess[i].time * 1000).toISOString().substring(0, 7); // YYYY-MM
      console.log(`🔍 S&R: Support progress ${i}/${actualEndIndex} (${((i/actualEndIndex)*100).toFixed(1)}%) - ${currentDate} - Found: ${supportCount}`);
    }

    const currentLow = lows[i];

    // Traditional strict pivot low detection - lower than ALL surrounding bars
    let isPivotLow = true;

    // Check left side (lookback) - must be lower than all
    for (let j = i - safeLeftBars; j < i; j++) {
      if (j >= 0 && lows[j] <= currentLow) {
        isPivotLow = false;
        break;
      }
    }

    // Check right side (lookahead) - must be lower than all
    if (isPivotLow) {
      for (let j = i + 1; j <= i + safeRightBars; j++) {
        if (j < lows.length && lows[j] <= currentLow) {
          isPivotLow = false;
          break;
        }
      }
    }

    // If it's a valid pivot low, add it as support
    if (isPivotLow) {
      const pivotDate = new Date(dataToProcess[i].time * 1000);
      const monthKey = pivotDate.toISOString().substring(0, 7); // YYYY-MM

      result.push({
        time: dataToProcess[i].time,
        value: currentLow,
        type: 'support',
        strength: 1,
        index: i
      });
      supportCount++;

      // Track pivots by month
      if (!pivotsByMonth[monthKey]) pivotsByMonth[monthKey] = { resistance: 0, support: 0 };
      pivotsByMonth[monthKey].support++;

      if (supportCount <= 5 || monthKey !== lastLoggedMonth) {
        console.log(`🔵 Found support ${supportCount}: ${currentLow.toFixed(5)} at ${pivotDate.toISOString().substring(0, 10)} (${monthKey})`);
        lastLoggedMonth = monthKey;
      }
    }
  }

  const finalSupportCount = result.filter(r => r.type === 'support').length;
  const finalResistanceCount = result.filter(r => r.type === 'resistance').length;

  const endTime = performance.now();
  const calculationTime = endTime - startTime;

  console.log(`✅ S&R: Found ${result.length} pivot points (${finalSupportCount} support, ${finalResistanceCount} resistance) in ${calculationTime.toFixed(2)}ms`);
  console.log(`🎯 S&R: NO LIMITS - Found ALL pivots throughout the dataset for complete trade-agent coverage`);

  if (result.length > 0) {
    console.log(`🔍 S&R: Sample results:`, result.slice(0, 3).map(r => `${r.type} at ${r.value.toFixed(5)} (index ${r.index})`));

    // Show time distribution of results
    const timeRange = {
      first: Math.min(...result.map(r => r.time)),
      last: Math.max(...result.map(r => r.time))
    };
    console.log(`🔍 S&R: Results time range: ${timeRange.first} to ${timeRange.last}`);
    console.log(`🔍 S&R: Results date range: ${new Date(timeRange.first * 1000).toISOString()} to ${new Date(timeRange.last * 1000).toISOString()}`);

    // Show monthly distribution of pivots
    console.log(`📊 S&R: Monthly pivot distribution:`, pivotsByMonth);
    const monthsWithPivots = Object.keys(pivotsByMonth).length;
    console.log(`📊 S&R: Found pivots in ${monthsWithPivots} different months`);
  }

    return result;

  } catch (error) {
    console.error('🚫 S&R: Calculation failed:', error);
    return [];
  }
};

/**
 * Helper function to calculate EMA from an array of {time, value} objects
 * @param {Array} data - Array of {time, value} objects
 * @param {number} period - EMA period
 * @returns {Array} Array of EMA values with time
 */
const calculateEMAFromValues = (data, period) => {
  if (!data || data.length < period) {
    return [];
  }

  const result = [];
  const multiplier = 2 / (period + 1);

  // Calculate initial SMA for the first EMA value
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += data[i].value;
  }
  let ema = sum / period;

  result.push({
    time: data[period - 1].time,
    value: ema
  });

  // Calculate EMA for remaining values
  for (let i = period; i < data.length; i++) {
    const currentValue = data[i].value;
    ema = (currentValue * multiplier) + (ema * (1 - multiplier));

    result.push({
      time: data[i].time,
      value: ema
    });
  }

  return result;
};

/**
 * Calculate indicators based on strategy configuration
 * @param {Array} candleData - Array of candle data
 * @param {Object} strategy - Strategy configuration with indicators
 * @returns {Object} Object containing all calculated indicators
 */
export const calculateIndicatorsFromStrategy = (candleData, strategy) => {
  console.log('🔍 calculateIndicatorsFromStrategy called', {
    candleDataLength: candleData?.length || 0,
    hasStrategy: !!strategy,
    hasStrategyJson: !!strategy?.strategy_json,
    hasIndicators: !!strategy?.strategy_json?.indicators,
    indicatorCount: strategy?.strategy_json?.indicators?.length || 0,
    indicators: strategy?.strategy_json?.indicators?.map(ind => ({ type: ind.type || ind.indicator_class, id: ind.id })) || []
  });

  if (!candleData || !strategy?.strategy_json?.indicators) {
    console.log('🔍 calculateIndicatorsFromStrategy: Early return - missing data');
    return {};
  }

  const indicators = {};
  let strategyIndicators = [...(strategy.strategy_json.indicators || [])];

  // Add Support & Resistance indicator if it's used for risk management
  if (strategy?.riskManagement?.stopLossMethod === 'indicator' &&
      strategy?.riskManagement?.indicatorBasedSL?.indicator === 'support_resistance') {

    const riskParams = strategy.riskManagement.indicatorBasedSL.parameters || {};
    const srIndicator = {
      id: 'risk_management_sr',
      type: 'SUPPORT_RESISTANCE',
      indicator_class: 'SUPPORT_RESISTANCE',
      parameters: {
        left: riskParams.left || 10,
        right: riskParams.right || 10
      },
      source: 'close'
    };

    // Check if S&R indicator already exists in strategy indicators
    const existingSR = strategyIndicators.find(ind =>
      (ind.type === 'SUPPORT_RESISTANCE' || ind.indicator_class === 'SUPPORT_RESISTANCE')
    );

    if (!existingSR) {
      strategyIndicators.push(srIndicator);
      console.log(`🔍 Added S&R indicator for risk management: left=${srIndicator.parameters.left}, right=${srIndicator.parameters.right}`);
    }
  }

  strategyIndicators.forEach(indicator => {
    const { id, type, indicator_class, parameters = {}, source = 'close' } = indicator;
    const indicatorType = type || indicator_class;

    // Ensure source is a valid string, fallback to 'close' if undefined/null/empty
    const validSource = (source && typeof source === 'string' && source.trim()) ? source.trim() : 'close';

    try {
      let calculatedData = [];

      switch (indicatorType.toUpperCase()) {
        case 'SMA':
          calculatedData = calculateSMA(candleData, parameters.period || 14, validSource);
          break;
        case 'EMA':
          calculatedData = calculateEMA(candleData, parameters.period || 14, validSource);
          break;
        case 'RSI':
          calculatedData = calculateRSI(candleData, parameters.period || 14, validSource);
          break;
        case 'MACD':
          console.log('🔍 MACD Calculation Debug:', {
            candleDataLength: candleData.length,
            fastPeriod: parameters.fastPeriod || 12,
            slowPeriod: parameters.slowPeriod || 26,
            signalPeriod: parameters.signalPeriod || 9,
            source: validSource,
            sampleCandles: candleData.slice(0, 3).map(c => ({ time: new Date(c.time * 1000).toISOString(), close: c.close }))
          });

          const macdData = calculateMACD(
            candleData,
            parameters.fastPeriod || 12,
            parameters.slowPeriod || 26,
            parameters.signalPeriod || 9,
            validSource
          );

          console.log('🔍 MACD Calculation Result:', {
            macdLength: macdData.macd?.length || 0,
            signalLength: macdData.signal?.length || 0,
            histogramLength: macdData.histogram?.length || 0,
            macdSample: macdData.macd?.slice(0, 3),
            signalSample: macdData.signal?.slice(0, 3),
            histogramSample: macdData.histogram?.slice(0, 3)
          });

          // Store MACD components separately
          indicators[`${id}_macd`] = macdData.macd;
          indicators[`${id}_signal`] = macdData.signal;
          indicators[`${id}_histogram`] = macdData.histogram;
          calculatedData = macdData.macd; // Main MACD line
          break;
        case 'BOLLINGER_BANDS':
        case 'BOLLINGERBANDS':
        case 'BB':
          calculatedData = calculateBollingerBands(
            candleData,
            parameters.period || 20,
            parameters.stdDev || parameters.devfactor || 2,
            validSource
          );
          // Store Bollinger Bands components separately for easier access
          if (calculatedData.length > 0) {
            indicators[`${id}_upper`] = calculatedData.map(d => ({ time: d.time, value: d.upper }));
            indicators[`${id}_middle`] = calculatedData.map(d => ({ time: d.time, value: d.middle }));
            indicators[`${id}_lower`] = calculatedData.map(d => ({ time: d.time, value: d.lower }));
          }
          break;
        case 'ATR':
          calculatedData = calculateATR(candleData, parameters.period || 14);
          break;
        case 'SUPPORT_RESISTANCE':
        case 'SUPPORTRESISTANCE':
        case 'SR':
          // Get parameters from strategy indicators OR risk management settings
          let leftBars = parameters.leftBars || parameters.left_bars || parameters.left || 10;
          let rightBars = parameters.rightBars || parameters.right_bars || parameters.right || 10;

          // Check if this is for risk management (indicator-based stop loss)
          if (strategy?.riskManagement?.stopLossMethod === 'indicator' &&
              strategy?.riskManagement?.indicatorBasedSL?.indicator === 'support_resistance' &&
              strategy?.riskManagement?.indicatorBasedSL?.parameters) {
            const riskParams = strategy.riskManagement.indicatorBasedSL.parameters;
            leftBars = riskParams.left || leftBars;
            rightBars = riskParams.right || rightBars;
            console.log(`🔍 Using risk management S&R parameters: left=${leftBars}, right=${rightBars}`);
          }

          calculatedData = calculateSupportResistance(candleData, leftBars, rightBars);

          // Store Support and Resistance components separately
          if (calculatedData && calculatedData.length > 0) {
            const supportLevels = calculatedData.filter(d => d.type === 'support');
            const resistanceLevels = calculatedData.filter(d => d.type === 'resistance');
            indicators[`${id}_support`] = supportLevels;
            indicators[`${id}_resistance`] = resistanceLevels;
            console.log(`✅ S&R: ${supportLevels.length} support, ${resistanceLevels.length} resistance levels (left=${leftBars}, right=${rightBars})`);
          }
          break;
        default:
          console.warn(`Unsupported indicator type: ${indicatorType}`);
          return;
      }

      // Store indicator data by ID and type
      indicators[id] = calculatedData;

      // Also store by type for backward compatibility
      if (!indicators[indicatorType.toLowerCase()]) {
        indicators[indicatorType.toLowerCase()] = calculatedData;
      }

    } catch (error) {
      console.error(`Error calculating ${indicatorType} indicator:`, error);
    }
  });

  console.log('🔍 calculateIndicatorsFromStrategy: Final result', {
    indicatorKeys: Object.keys(indicators),
    indicatorCount: Object.keys(indicators).length,
    sampleData: Object.keys(indicators).length > 0 ? {
      key: Object.keys(indicators)[0],
      dataLength: indicators[Object.keys(indicators)[0]]?.length || 0,
      sampleValues: indicators[Object.keys(indicators)[0]]?.slice(0, 3) || []
    } : null
  });

  return indicators;
};
