import React, { useState } from 'react';
import { quickFixTradeSyncIssues } from '../utils/tradeSyncFixer';

const TradeSyncFixerButton = ({ userId, strategyId, onFixComplete }) => {
  const [isFixing, setIsFixing] = useState(false);
  const [results, setResults] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const handleFix = async () => {
    if (!userId || !strategyId) {
      alert('Missing user ID or strategy ID');
      return;
    }

    setIsFixing(true);
    setResults(null);

    try {
      console.log('🔧 Running trade sync fix...');
      const fixResults = await quickFixTradeSyncIssues(userId, strategyId);
      
      setResults(fixResults);
      
      if (fixResults.success) {
        console.log('✅ Trade sync fix completed successfully');
        
        // Call the callback to refresh the page data
        if (onFixComplete) {
          onFixComplete(fixResults);
        }
      } else {
        console.error('❌ Trade sync fix failed:', fixResults.error);
      }
      
    } catch (error) {
      console.error('❌ Error running trade sync fix:', error);
      setResults({
        success: false,
        error: error.message
      });
    } finally {
      setIsFixing(false);
    }
  };

  const renderResults = () => {
    if (!results) return null;

    return (
      <div className="mt-4 p-4 rounded-lg border border-gray-600 bg-gray-800">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-lg font-semibold text-white">
            {results.success ? '✅ Fix Results' : '❌ Fix Failed'}
          </h4>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-sm text-blue-400 hover:text-blue-300"
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </button>
        </div>

        {results.success ? (
          <div className="text-green-400">
            <p className="font-medium">{results.message}</p>
            
            {results.fixResults && (
              <div className="mt-2 text-sm">
                <p>• Fixed trades: {results.fixResults.fixed}</p>
                <p>• Errors: {results.fixResults.errors?.length || 0}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-red-400">
            <p className="font-medium">Error: {results.error}</p>
          </div>
        )}

        {showDetails && results.report && (
          <div className="mt-4 p-3 bg-gray-900 rounded border border-gray-700">
            <h5 className="text-white font-medium mb-2">Detailed Report:</h5>
            
            <div className="text-sm text-gray-300 space-y-2">
              <p>• Total trades: {results.report.totalTrades}</p>
              <p>• Open trades: {results.report.openTrades}</p>
              <p>• Closed trades: {results.report.closedTrades}</p>
              <p>• Issues found: {results.report.suspiciousPatterns?.length || 0}</p>
              
              {results.report.suspiciousPatterns?.length > 0 && (
                <div className="mt-3">
                  <p className="text-yellow-400 font-medium">Issues detected:</p>
                  {results.report.suspiciousPatterns.map((pattern, index) => (
                    <div key={index} className="ml-2 mt-1">
                      <p className="text-yellow-300">• {pattern.description}</p>
                      <p className="text-gray-400 text-xs ml-2">Count: {pattern.count}</p>
                    </div>
                  ))}
                </div>
              )}

              {results.report.recommendations?.length > 0 && (
                <div className="mt-3">
                  <p className="text-blue-400 font-medium">Recommendations:</p>
                  {results.report.recommendations.map((rec, index) => (
                    <div key={index} className="ml-2 mt-1">
                      <p className="text-blue-300">• {rec.message}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
      <div className="flex items-center justify-between mb-3">
        <div>
          <h3 className="text-lg font-semibold text-white">Trade Sync Fixer</h3>
          <p className="text-sm text-gray-400">
            Fix synchronization issues between OANDA and Firebase trade data
          </p>
        </div>
        
        <button
          onClick={handleFix}
          disabled={isFixing}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            isFixing
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isFixing ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Fixing...
            </div>
          ) : (
            'Fix Trade Sync'
          )}
        </button>
      </div>

      <div className="text-sm text-gray-400">
        <p>This tool will:</p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Check for trades that are closed in OANDA but still show as open in the frontend</li>
          <li>Identify multiple open trades for the same instrument</li>
          <li>Fix stale trade data by marking orphaned trades as closed</li>
          <li>Ensure data consistency between your broker and the dashboard</li>
        </ul>
      </div>

      {renderResults()}
    </div>
  );
};

export default TradeSyncFixerButton;
