import React from 'react';

const StrategyVisualization = ({ strategy }) => {
  console.log("Strategy passed to StrategyVisualization:", strategy);

  if (!strategy) return null;

  // Parse the strategy if it's a string
  let parsedStrategy = strategy;
  if (typeof strategy === 'string') {
    try {
      parsedStrategy = JSON.parse(strategy);
      console.log("Parsed strategy JSON:", parsedStrategy);
    } catch (e) {
      console.error('Error parsing strategy JSON:', e);
      return <div className="text-red-500">Error parsing strategy JSON</div>;
    }
  } else {
    console.log("Strategy is already an object:", parsedStrategy);
  }

  const {
    name,
    description,
    instruments,
    timeframe,
    tradingSession = [],
    indicators = [],
    entryRules = [],
    exitRules = [],
    riskManagement: initialRiskManagement = {},
    risk_management = {},
    parameters = {},
    entryLongGroupOperator = 'AND',
    entryShortGroupOperator = 'AND',
    exitLongGroupOperator = 'OR',
    exitShortGroupOperator = 'OR'
  } = parsedStrategy;

  // Combine risk management data from all possible sources
  const riskManagement = {
    ...initialRiskManagement,
    ...risk_management,
    ...(parameters.stopLoss || parameters.takeProfit ? parameters : {})
  };

  // Add debugging for risk management
  console.log("Combined riskManagement:", riskManagement);
  console.log("Sources - initialRiskManagement:", initialRiskManagement);
  console.log("Sources - risk_management:", risk_management);
  console.log("Sources - parameters:", parameters);

  // Group rules by trade type
  const entryLongRules = entryRules.filter(rule => rule.tradeType === 'long' || rule.tradeType === 'buy');
  const entryShortRules = entryRules.filter(rule => rule.tradeType === 'short' || rule.tradeType === 'sell');
  const exitLongRules = exitRules.filter(rule => rule.tradeType === 'long' || rule.tradeType === 'buy');
  const exitShortRules = exitRules.filter(rule => rule.tradeType === 'short' || rule.tradeType === 'sell');

  // Helper function to get indicator label with parameters
  const getIndicatorLabel = (indicatorId) => {
    if (indicatorId === 'price') return 'Price';
    const indicator = indicators.find(ind => ind.id === indicatorId);
    if (!indicator) return 'Unknown';

    const baseType = indicator.indicator_class || indicator.type;
    const params = indicator.parameters || {};

    // Format parameters for display
    if (Object.keys(params).length === 0) {
      return baseType;
    }

    // Special formatting for common indicators
    if (baseType === 'RSI' && params.period) {
      return `RSI(${params.period})`;
    } else if (baseType === 'EMA' && params.period) {
      return `EMA(${params.period})`;
    } else if (baseType === 'SMA' && params.period) {
      return `SMA(${params.period})`;
    } else if (baseType === 'MACD') {
      const fast = params.fast || 12;
      const slow = params.slow || 26;
      const signal = params.signal || 9;
      return `MACD(${fast},${slow},${signal})`;
    } else if (baseType === 'BollingerBands') {
      const period = params.period || 20;
      const devfactor = params.devfactor || 2;
      return `BB(${period},${devfactor})`;
    } else {
      // Generic parameter formatting
      const paramStr = Object.entries(params)
        .map(([k, v]) => `${k}:${v}`)
        .join(',');
      return `${baseType}(${paramStr})`;
    }
  };

  // Calculate risk/reward ratio
  const calculateRiskReward = () => {
    // If the new risk management structure has a risk reward ratio, use it
    if (riskManagement?.riskRewardRatio) {
      return `1:${parseFloat(riskManagement.riskRewardRatio).toFixed(2)}`;
    }

    // Fall back to the old method if the new structure doesn't have a risk reward ratio
    if (!riskManagement?.stopLoss || !riskManagement?.takeProfit) return 'N/A';

    // If units are different, we can't calculate the ratio accurately
    if (riskManagement.stopLossUnit !== riskManagement.takeProfitUnit) {
      return 'N/A (different units)';
    }

    const stopLoss = parseFloat(riskManagement.stopLoss);
    const takeProfit = parseFloat(riskManagement.takeProfit);

    if (isNaN(stopLoss) || isNaN(takeProfit) || stopLoss === 0) return 'N/A';

    return `1:${(takeProfit/stopLoss).toFixed(2)}`;
  };

  return (
    <div className="space-y-6 overflow-x-auto w-full" style={{ maxWidth: '100%', overflowX: 'auto' }}>
      {/* Strategy Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] shadow-md">
          <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Basic Information</h3>
          <div className="space-y-3">
            <div>
              <span className="text-[#FEFEFF]/60">Strategy Name:</span>
              <span className="text-[#FEFEFF] ml-2">{name}</span>
            </div>
            <div>
              <span className="text-[#FEFEFF]/60">Forex Pair:</span>
              <span className="text-[#FEFEFF] ml-2">{instruments}</span>
            </div>
            <div>
              <span className="text-[#FEFEFF]/60">Timeframe:</span>
              <span className="text-[#FEFEFF] ml-2">{timeframe}</span>
            </div>
            <div>
              <span className="text-[#FEFEFF]/60">Trading Sessions:</span>
              <span className="text-[#FEFEFF] ml-2">
                {tradingSession.length === 0 ? 'All Sessions' : tradingSession.join(', ')}
              </span>
            </div>
            {description && (
              <div>
                <span className="text-[#FEFEFF]/60">Description:</span>
                <span className="text-[#FEFEFF] ml-2">{description}</span>
              </div>
            )}
          </div>
        </div>

        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] shadow-md">
          <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Risk Management</h3>
          {console.log("Risk Management data:", riskManagement)}
          <div className="space-y-3">
            {/* Risk Percentage */}
            <div>
              <span className="text-[#FEFEFF]/60">Risk Percentage:</span>
              <span className="text-[#FEFEFF] ml-2">
                {riskManagement?.riskPercentage ?
                  (riskManagement.riskPercentage.includes('%') ?
                    riskManagement.riskPercentage :
                    `${riskManagement.riskPercentage}%`) :
                  'N/A'}
              </span>
            </div>

            {/* Risk-Reward Ratio */}
            <div>
              <span className="text-[#FEFEFF]/60">Risk-Reward Ratio:</span>
              <span className="text-[#FEFEFF] ml-2">
                {riskManagement?.riskRewardRatio ?
                  `${riskManagement.riskRewardRatio}:1` :
                  'N/A'}
              </span>
            </div>

            {/* Stop Loss Method */}
            <div>
              <span className="text-[#FEFEFF]/60">Stop Loss Method:</span>
              <span className="text-[#FEFEFF] ml-2">
                {riskManagement?.stopLossMethod ?
                  (riskManagement.stopLossMethod === 'fixed' ? 'Fixed Pips' :
                   riskManagement.stopLossMethod === 'indicator' ? 'Indicator-Based' :
                   riskManagement.stopLossMethod === 'risk' ? 'Risk-Based' :
                   riskManagement.stopLossMethod) :
                  'N/A'}
              </span>
            </div>

            {/* Method-specific parameters */}
            {riskManagement?.stopLossMethod === 'fixed' && riskManagement?.fixedPips && (
              <div>
                <span className="text-[#FEFEFF]/60">Fixed Pips:</span>
                <span className="text-[#FEFEFF] ml-2">{riskManagement.fixedPips}</span>
              </div>
            )}

            {riskManagement?.stopLossMethod === 'indicator' && riskManagement?.indicatorBasedSL && (
              <div>
                <span className="text-[#FEFEFF]/60">Indicator:</span>
                <span className="text-[#FEFEFF] ml-2">
                  {riskManagement.indicatorBasedSL.indicator || 'N/A'}
                </span>
              </div>
            )}

            {riskManagement?.stopLossMethod === 'risk' && riskManagement?.lotSize && (
              <div>
                <span className="text-[#FEFEFF]/60">Lot Size:</span>
                <span className="text-[#FEFEFF] ml-2">{riskManagement.lotSize}</span>
              </div>
            )}

            {/* Legacy stop loss and take profit percentages are no longer displayed
                 as they are redundant with Risk Percentage and Risk-Reward Ratio */}
          </div>
        </div>
      </div>

      {/* Technical Indicators */}
      <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] shadow-md mb-6">
        <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Technical Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {indicators.map((indicator) => (
            <div
              key={indicator.id}
              className="bg-[#3a3a3a] rounded-lg p-4 border border-[#4a4a4a] shadow-sm"
            >
              <div className="font-medium text-[#FEFEFF]">
                {indicator.indicator_class || indicator.type}
              </div>
              <div className="text-sm text-[#FEFEFF]/60">
                {Object.entries(indicator.parameters || {})
                  .map(([k, v]) => `${k}: ${v}`)
                  .join(", ")}
              </div>
              <div className="text-sm text-[#EFBD3A]/80 mt-1">
                Source: {indicator.source === "volume" ? "Volume" :
                        indicator.source === "open" ? "Open" :
                        indicator.source === "close" ? "Close" :
                        indicator.source === "high" ? "High" :
                        indicator.source === "low" ? "Low" :
                        indicators.find(ind => ind.id === indicator.source)?.type || "Price"}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Trading Rules */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Entry Rules */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] shadow-md">
          <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Entry Rules</h3>
          <div className="space-y-4">
            {/* Long Entry Rules */}
            {entryLongRules.length > 0 && (
              <div>
                <h4 className="text-green-400 font-medium mb-2">Long Entry Conditions</h4>
                <div className="space-y-2">
                  {entryLongRules.map((rule) => (
                    <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#3a3a3a] p-3 rounded border border-[#4a4a4a] shadow-sm">
                      {getIndicatorLabel(rule.indicator1)}
                      {' '}
                      <span className="text-[#EFBD3A]">{rule.operator}</span>
                      {' '}
                      {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                      {rule.indicator1 === 'price' && rule.barRef && (
                        <>
                          {' '}
                          <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                        </>
                      )}
                    </div>
                  ))}
                  {entryLongRules.length > 1 && (
                    <div className="text-xs text-[#FEFEFF]/60 mt-1">
                      Combined with: {entryLongGroupOperator}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Short Entry Rules */}
            {entryShortRules.length > 0 && (
              <div>
                <h4 className="text-red-400 font-medium mb-2">Short Entry Conditions</h4>
                <div className="space-y-2">
                  {entryShortRules.map((rule) => (
                    <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#3a3a3a] p-3 rounded border border-[#4a4a4a] shadow-sm">
                      {getIndicatorLabel(rule.indicator1)}
                      {' '}
                      <span className="text-[#EFBD3A]">{rule.operator}</span>
                      {' '}
                      {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                      {rule.indicator1 === 'price' && rule.barRef && (
                        <>
                          {' '}
                          <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                        </>
                      )}
                    </div>
                  ))}
                  {entryShortRules.length > 1 && (
                    <div className="text-xs text-[#FEFEFF]/60 mt-1">
                      Combined with: {entryShortGroupOperator}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Exit Rules */}
        <div className="bg-[#2a2a2a] rounded-lg p-6 border border-[#3a3a3a] shadow-md">
          <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Exit Rules</h3>
          <div className="space-y-4">
            {/* Long Exit Rules */}
            {exitLongRules.length > 0 && (
              <div>
                <h4 className="text-green-400 font-medium mb-2">Long Exit Conditions</h4>
                <div className="space-y-2">
                  {exitLongRules.map((rule) => (
                    <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#3a3a3a] p-3 rounded border border-[#4a4a4a] shadow-sm">
                      {getIndicatorLabel(rule.indicator1)}
                      {' '}
                      <span className="text-[#EFBD3A]">{rule.operator}</span>
                      {' '}
                      {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                      {rule.indicator1 === 'price' && rule.barRef && (
                        <>
                          {' '}
                          <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                        </>
                      )}
                    </div>
                  ))}
                  {exitLongRules.length > 1 && (
                    <div className="text-xs text-[#FEFEFF]/60 mt-1">
                      Combined with: {exitLongGroupOperator}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Short Exit Rules */}
            {exitShortRules.length > 0 && (
              <div>
                <h4 className="text-red-400 font-medium mb-2">Short Exit Conditions</h4>
                <div className="space-y-2">
                  {exitShortRules.map((rule) => (
                    <div key={rule.id} className="text-sm text-[#FEFEFF]/80 bg-[#3a3a3a] p-3 rounded border border-[#4a4a4a] shadow-sm">
                      {getIndicatorLabel(rule.indicator1)}
                      {' '}
                      <span className="text-[#EFBD3A]">{rule.operator}</span>
                      {' '}
                      {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2)}
                      {rule.indicator1 === 'price' && rule.barRef && (
                        <>
                          {' '}
                          <span className="text-[#FEFEFF]/60">on {rule.barRef}</span>
                        </>
                      )}
                    </div>
                  ))}
                  {exitShortRules.length > 1 && (
                    <div className="text-xs text-[#FEFEFF]/60 mt-1">
                      Combined with: {exitShortGroupOperator}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyVisualization;
