import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  BarChart3,
  CheckCircle,
  DollarSign,
  Hexagon,
  Users,
  TrendingUp,
  TrendingDown,
  Minus,
  CreditCard,
  Target,
  BarChart2,
  Wallet
} from 'lucide-react';

const PerformanceDashboard = ({ tradeHistory = [], accountBalance = 0, openTrades = [] }) => {
  // Process trade history data for charts
  const chartData = useMemo(() => {
    if (!tradeHistory || tradeHistory.length === 0) {
      return {
        dailyPerformance: [],
        tradeResults: []
      };
    }

    // Include both closed and open trades for comprehensive performance view
    const allTrades = [...tradeHistory]
      .filter(trade => trade && trade.status)
      .sort((a, b) => {
        // Sort by closeTime for closed trades, openTime for open trades
        const timeA = a.status?.toUpperCase() === 'CLOSED' ?
          new Date(a.closeTime || a.openTime) : new Date(a.openTime);
        const timeB = b.status?.toUpperCase() === 'CLOSED' ?
          new Date(b.closeTime || b.openTime) : new Date(b.openTime);
        return timeA - timeB;
      });

    // Separate closed and open trades
    const closedTrades = allTrades.filter(trade =>
      trade.status?.toUpperCase() === 'CLOSED' &&
      trade.realizedPL !== undefined &&
      trade.closeTime
    );

    const openTrades = allTrades.filter(trade =>
      trade.status?.toUpperCase() === 'OPEN' &&
      trade.unrealizedPL !== undefined
    );

    // Calculate daily performance (only from closed trades for historical accuracy)
    const dailyData = {};
    closedTrades.forEach(trade => {
      const date = new Date(trade.closeTime).toISOString().split('T')[0];
      if (!dailyData[date]) {
        dailyData[date] = {
          date: date,
          totalPL: 0,
          trades: 0,
          wins: 0,
          losses: 0
        };
      }

      dailyData[date].totalPL += (trade.realizedPL || 0);
      dailyData[date].trades += 1;
      if ((trade.realizedPL || 0) > 0) {
        dailyData[date].wins += 1;
      } else if ((trade.realizedPL || 0) < 0) {
        dailyData[date].losses += 1;
      }
    });

    const dailyPerformance = Object.values(dailyData).sort((a, b) => a.date.localeCompare(b.date));

    // Calculate individual trade results including both closed and open trades
    const tradeResults = [];

    // Add closed trades first
    closedTrades.forEach((trade, index) => {
      tradeResults.push({
        tradeNumber: index + 1,
        realizedPL: trade.realizedPL || 0,
        date: new Date(trade.closeTime).toISOString().split('T')[0],
        instrument: trade.instrument || 'Unknown',
        type: trade.type || 'Unknown',
        status: 'CLOSED'
      });
    });

    // Add open trades with unrealized P&L
    openTrades.forEach((trade, index) => {
      tradeResults.push({
        tradeNumber: closedTrades.length + index + 1,
        realizedPL: trade.unrealizedPL || 0, // Use unrealized P&L for open trades
        date: new Date(trade.openTime).toISOString().split('T')[0],
        instrument: trade.instrument || 'Unknown',
        type: trade.type || 'Unknown',
        status: 'OPEN'
      });
    });

    return {
      dailyPerformance,
      tradeResults,
      closedTrades,
      openTrades
    };
  }, [tradeHistory, accountBalance]);

  // Calculate comprehensive metrics (similar to PerformanceMetrics component)
  const metrics = useMemo(() => {
    if (!tradeHistory || tradeHistory.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        totalRealizedPL: 0,
        averageWin: 0,
        averageLoss: 0,
        largestWin: 0,
        largestLoss: 0,
        profitFactor: 0,
        averageTrade: 0,
        maxDrawdown: 0,
        sharpeRatio: 0
      };
    }

    const closedTrades = tradeHistory.filter(trade =>
      trade &&
      trade.status &&
      trade.status.toUpperCase() === 'CLOSED' &&
      trade.realizedPL !== undefined
    );

    const openTrades = tradeHistory.filter(trade =>
      trade &&
      trade.status &&
      trade.status.toUpperCase() === 'OPEN' &&
      trade.unrealizedPL !== undefined
    );

    const totalTrades = closedTrades.length + openTrades.length;
    const winningTrades = closedTrades.filter(trade => (trade.realizedPL || 0) > 0);
    const losingTrades = closedTrades.filter(trade => (trade.realizedPL || 0) < 0);

    // Calculate total P&L including unrealized P&L from open trades
    const totalRealizedPL = closedTrades.reduce((sum, trade) => sum + (trade.realizedPL || 0), 0);
    const totalUnrealizedPL = openTrades.reduce((sum, trade) => sum + (trade.unrealizedPL || 0), 0);
    const totalPL = totalRealizedPL + totalUnrealizedPL;

    const totalWins = winningTrades.reduce((sum, trade) => sum + (trade.realizedPL || 0), 0);
    const totalLosses = Math.abs(losingTrades.reduce((sum, trade) => sum + (trade.realizedPL || 0), 0));

    // Win rate based on closed trades only (open trades can't be determined as wins/losses yet)
    const winRate = closedTrades.length > 0 ? (winningTrades.length / closedTrades.length) : 0;
    const averageTrade = closedTrades.length > 0 ? totalRealizedPL / closedTrades.length : 0;
    const profitFactor = totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? 'PERFECT' : 0;

    // Calculate average win/loss
    const averageWin = winningTrades.length > 0 ? totalWins / winningTrades.length : 0;
    const averageLoss = losingTrades.length > 0 ? totalLosses / losingTrades.length : 0;

    // Calculate largest win/loss
    const largestWin = winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.realizedPL || 0)) : 0;
    const largestLoss = losingTrades.length > 0 ? Math.abs(Math.min(...losingTrades.map(t => t.realizedPL || 0))) : 0;

    return {
      totalTrades,
      closedTrades: closedTrades.length,
      openTrades: openTrades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      totalRealizedPL,
      totalUnrealizedPL,
      totalPL,
      averageWin,
      averageLoss,
      largestWin,
      largestLoss,
      profitFactor,
      averageTrade,
      maxDrawdown: 0, // Would need equity curve to calculate
      sharpeRatio: 0
    };
  }, [tradeHistory]);



  if (!tradeHistory || tradeHistory.length === 0) {
    return (
      <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] p-8 text-center">
        <div className="text-gray-400 mb-4">
          <BarChart3 className="w-16 h-16 mx-auto mb-4 opacity-50" />
        </div>
        <h3 className="text-lg font-medium text-[#FEFEFF] mb-2">No Performance Data</h3>
        <p className="text-gray-400">Once trades are executed, performance data will be displayed here.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {/* Total Trades */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-[#FEFEFF]">{metrics.totalTrades}</div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-[#FEFEFF]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Total Trades</div>
          <div className="text-xs text-[#FEFEFF]/40 mt-1">
            {metrics.closedTrades} closed • {metrics.openTrades} open
          </div>
        </motion.div>

        {/* Win Rate */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-[#FEFEFF]">{(metrics.winRate * 100).toFixed(1)}%</div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <Target className="w-5 h-5 text-[#FEFEFF]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Win Rate</div>
        </motion.div>

        {/* Realized P&L */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className={`text-2xl font-bold ${metrics.totalRealizedPL >= 0 ? 'text-[#10B981]' : 'text-[#EF4444]'}`}>
              {metrics.totalRealizedPL >= 0 ? '+' : ''}${metrics.totalRealizedPL.toFixed(2)}
            </div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <DollarSign className="w-5 h-5 text-[#FEFEFF]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Realized P&L</div>
        </motion.div>

        {/* Unrealized P&L */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className={`text-2xl font-bold ${metrics.totalUnrealizedPL >= 0 ? 'text-[#10B981]' : 'text-[#EF4444]'}`}>
              {metrics.totalUnrealizedPL >= 0 ? '+' : ''}${metrics.totalUnrealizedPL.toFixed(2)}
            </div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <Hexagon className="w-5 h-5 text-[#FEFEFF]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Unrealized P&L</div>
        </motion.div>

        {/* Average Trade */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className={`text-2xl font-bold ${metrics.averageTrade >= 0 ? 'text-[#10B981]' : 'text-[#EF4444]'}`}>
              {metrics.averageTrade >= 0 ? '+' : ''}${metrics.averageTrade.toFixed(2)}
            </div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <BarChart2 className="w-5 h-5 text-[#FEFEFF]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Avg Trade</div>
        </motion.div>

        {/* Profit Factor */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-[#FEFEFF]">
              {metrics.profitFactor === 'PERFECT' ? '∞' :
               typeof metrics.profitFactor === 'number' ? metrics.profitFactor.toFixed(2) : 'N/A'}
            </div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-[#FEFEFF]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Profit Factor</div>
        </motion.div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Performance Line Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] p-6"
        >
          <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Daily Performance</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData.dailyPerformance}>
                <CartesianGrid strokeDasharray="3 3" stroke="#1a1a1a" />
                <XAxis
                  dataKey="date"
                  stroke="#FEFEFF"
                  fontSize={12}
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis
                  stroke="#FEFEFF"
                  fontSize={12}
                  tickFormatter={(value) => `$${value.toFixed(0)}`}
                />
                <Tooltip
                  formatter={(value, name) => [`$${value.toFixed(2)}`, 'Daily P&L']}
                  labelFormatter={(label) => `Date: ${new Date(label).toLocaleDateString()}`}
                  contentStyle={{
                    backgroundColor: '#0F1011',
                    border: '1px solid #1a1a1a',
                    borderRadius: '8px',
                    color: '#FEFEFF'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="totalPL"
                  stroke="#10B981"
                  strokeWidth={2}
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#10B981', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Individual Trade Results */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] p-6"
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-[#FEFEFF]">Trade Results</h3>
            <div className="flex items-center space-x-4 text-xs">
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-[#10B981] rounded"></div>
                <span className="text-[#FEFEFF]/60">Closed Win</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-[#EF4444] rounded"></div>
                <span className="text-[#FEFEFF]/60">Closed Loss</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-[#3B82F6] rounded"></div>
                <span className="text-[#FEFEFF]/60">Open +</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-[#F59E0B] rounded"></div>
                <span className="text-[#FEFEFF]/60">Open -</span>
              </div>
            </div>
          </div>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData.tradeResults}>
                <CartesianGrid strokeDasharray="3 3" stroke="#1a1a1a" />
                <XAxis
                  dataKey="tradeNumber"
                  stroke="#FEFEFF"
                  fontSize={12}
                  label={{ value: 'Trade #', position: 'insideBottom', offset: -5, style: { textAnchor: 'middle', fill: '#FEFEFF' } }}
                />
                <YAxis
                  stroke="#FEFEFF"
                  fontSize={12}
                  tickFormatter={(value) => `$${value.toFixed(0)}`}
                />
                <Tooltip
                  formatter={(value, name, props) => {
                    const status = props.payload?.status;
                    const plType = status === 'OPEN' ? 'Unrealized P&L' : 'Realized P&L';
                    return [`$${value.toFixed(2)}`, plType];
                  }}
                  labelFormatter={(label, payload) => {
                    const status = payload?.[0]?.payload?.status;
                    return `Trade #${label} (${status})`;
                  }}
                  contentStyle={{
                    backgroundColor: '#0F1011',
                    border: '1px solid #1a1a1a',
                    borderRadius: '8px',
                    color: '#FEFEFF'
                  }}
                />
                <Bar
                  dataKey="realizedPL"
                  fill="#10B981"
                  radius={[2, 2, 0, 0]}
                >
                  {chartData.tradeResults.map((entry, index) => {
                    // Different colors for open vs closed trades
                    if (entry.status === 'OPEN') {
                      // Open trades: blue for positive, orange for negative (unrealized)
                      return <Cell key={`cell-${index}`} fill={entry.realizedPL >= 0 ? "#3B82F6" : "#F59E0B"} />;
                    } else {
                      // Closed trades: green for positive, red for negative (realized)
                      return <Cell key={`cell-${index}`} fill={entry.realizedPL >= 0 ? "#10B981" : "#EF4444"} />;
                    }
                  })}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

      </div>

      {/* Additional Trade Details - Smaller Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-3">
        {/* Largest Win */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#10B981]">
              ${metrics.largestWin.toFixed(2)}
            </div>
            <TrendingUp className="w-4 h-4 text-[#10B981]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Largest Win</div>
        </motion.div>

        {/* Largest Loss */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#EF4444]">
              ${metrics.largestLoss.toFixed(2)}
            </div>
            <TrendingDown className="w-4 h-4 text-[#EF4444]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Largest Loss</div>
        </motion.div>

        {/* Average Win */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.8 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#10B981]">
              ${metrics.averageWin.toFixed(2)}
            </div>
            <CheckCircle className="w-4 h-4 text-[#10B981]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Avg Win</div>
        </motion.div>

        {/* Average Loss */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.9 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#EF4444]">
              ${Math.abs(metrics.averageLoss).toFixed(2)}
            </div>
            <Minus className="w-4 h-4 text-[#EF4444]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Avg Loss</div>
        </motion.div>

        {/* Total Commission */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.0 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#FEFEFF]">
              ${(tradeHistory.reduce((sum, trade) => sum + (parseFloat(trade.commission) || 0), 0)).toFixed(2)}
            </div>
            <CreditCard className="w-4 h-4 text-[#FEFEFF]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Total Commission</div>
        </motion.div>

        {/* Total Spread Cost */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.1 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#FEFEFF]">
              ${(tradeHistory.reduce((sum, trade) => sum + (parseFloat(trade.halfSpreadCost) || 0), 0)).toFixed(2)}
            </div>
            <DollarSign className="w-4 h-4 text-[#FEFEFF]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Total Spread</div>
        </motion.div>

        {/* Win/Loss Ratio */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.2 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#FEFEFF]">
              {metrics.averageLoss > 0 ? (metrics.averageWin / Math.abs(metrics.averageLoss)).toFixed(2) : 'N/A'}
            </div>
            <BarChart3 className="w-4 h-4 text-[#FEFEFF]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Win/Loss Ratio</div>
        </motion.div>

        {/* Total Margin Used */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 1.3 }}
          className="bg-[#0F1011] rounded-lg p-3 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-1">
            <div className="text-lg font-bold text-[#FEFEFF]">
              ${(tradeHistory.reduce((sum, trade) => sum + (parseFloat(trade.initialMarginRequired) || 0), 0)).toFixed(2)}
            </div>
            <Wallet className="w-4 h-4 text-[#FEFEFF]" />
          </div>
          <div className="text-xs text-[#FEFEFF]/60">Total Margin</div>
        </motion.div>

      </div>
    </div>
  );
};

export default PerformanceDashboard;
