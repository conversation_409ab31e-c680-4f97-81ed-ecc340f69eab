import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Play,
  Pause,
  Square,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  X
} from 'lucide-react';

const StatusNotification = ({ status, previousStatus, onClose }) => {
  const [visible, setVisible] = useState(true);
  const [autoCloseTimer, setAutoCloseTimer] = useState(null);

  // Define status messages and styles
  const statusConfig = {
    initializing: {
      title: 'Agent Initializing',
      message: 'The trading agent is starting up and preparing to trade.',
      icon: <Plus className="w-6 h-6 text-amber-500" />,
      bgColor: 'bg-amber-900/20',
      borderColor: 'border-amber-500/30',
      textColor: 'text-amber-400',
      autoClose: false
    },
    initialized: {
      title: 'Agent Initialized',
      message: 'The trading agent has been initialized and is ready to start trading.',
      icon: <CheckCircle className="w-6 h-6 text-blue-500" />,
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-500/30',
      textColor: 'text-blue-400',
      autoClose: true
    },
    running: {
      title: 'Agent Running',
      message: 'The trading agent is actively monitoring the market and executing trades based on your strategy.',
      icon: <Play className="w-6 h-6 text-green-500" />,
      bgColor: 'bg-green-900/20',
      borderColor: 'border-green-500/30',
      textColor: 'text-green-400',
      autoClose: true
    },
    pausing: {
      title: 'Agent Pausing',
      message: 'The trading agent is in the process of pausing. Open positions will be closed.',
      icon: <Pause className="w-6 h-6 text-yellow-500 animate-pulse" />,
      bgColor: 'bg-yellow-900/20',
      borderColor: 'border-yellow-500/30',
      textColor: 'text-yellow-400',
      autoClose: false
    },
    paused: {
      title: 'Agent Paused',
      message: 'The trading agent has been paused. No new trades will be executed until resumed.',
      icon: <Pause className="w-6 h-6 text-yellow-500" />,
      bgColor: 'bg-yellow-900/20',
      borderColor: 'border-yellow-500/30',
      textColor: 'text-yellow-400',
      autoClose: true
    },
    resuming: {
      title: 'Agent Resuming',
      message: 'The trading agent is in the process of resuming operations.',
      icon: <Play className="w-6 h-6 text-green-500 animate-pulse" />,
      bgColor: 'bg-green-900/20',
      borderColor: 'border-green-500/30',
      textColor: 'text-green-400',
      autoClose: false
    },
    stopping: {
      title: 'Agent Stopping',
      message: 'The trading agent is in the process of stopping. All open positions will be closed.',
      icon: <Square className="w-6 h-6 text-red-500 animate-pulse" />,
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400',
      autoClose: false
    },
    stopped: {
      title: 'Agent Stopped',
      message: 'The trading agent has been stopped. All positions have been closed.',
      icon: <Square className="w-6 h-6 text-gray-500" />,
      bgColor: 'bg-gray-900/20',
      borderColor: 'border-gray-500/30',
      textColor: 'text-gray-400',
      autoClose: true
    },
    error: {
      title: 'Agent Error',
      message: 'The trading agent has encountered an error. Please check the logs for details.',
      icon: <AlertTriangle className="w-6 h-6 text-red-500" />,
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400',
      autoClose: false
    },
    market_closed: {
      title: 'Market Closed',
      message: 'The market is currently closed. The agent will resume trading when the market opens.',
      icon: (
        <svg className="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      bgColor: 'bg-purple-900/20',
      borderColor: 'border-purple-500/30',
      textColor: 'text-purple-400',
      autoClose: true
    },
    data_stale: {
      title: 'Data Provider Issue',
      message: 'Trading is paused because the data provider is not providing fresh market data. The agent will resume automatically when live data becomes available.',
      icon: (
        <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
      bgColor: 'bg-orange-900/20',
      borderColor: 'border-orange-500/30',
      textColor: 'text-orange-400',
      autoClose: false
    }
  };

  // Get config for current status
  const config = statusConfig[status] || {
    title: 'Status Update',
    message: `Agent status changed to ${status}`,
    icon: <Info className="w-6 h-6 text-blue-500" />,
    bgColor: 'bg-blue-900/20',
    borderColor: 'border-blue-500/30',
    textColor: 'text-blue-400',
    autoClose: true
  };

  // Generate transition message based on status change
  const getTransitionMessage = () => {
    if (!previousStatus || previousStatus === status) return config.message;
    
    const transitions = {
      'running_to_paused': 'The agent has been paused. All open positions have been closed.',
      'paused_to_running': 'The agent has been resumed and is now actively trading.',
      'running_to_stopped': 'The agent has been stopped. All open positions have been closed.',
      'paused_to_stopped': 'The agent has been stopped from its paused state.',
      'running_to_error': 'The agent encountered an error while running. Check the logs for details.',
      'running_to_market_closed': 'The market has closed. The agent will resume when the market opens.',
      'market_closed_to_running': 'The market has opened. The agent has resumed trading.',
    };
    
    const transitionKey = `${previousStatus}_to_${status}`;
    return transitions[transitionKey] || config.message;
  };

  useEffect(() => {
    // Auto-close notification after 5 seconds for certain statuses
    if (config.autoClose) {
      const timer = setTimeout(() => {
        setVisible(false);
      }, 5000);
      
      setAutoCloseTimer(timer);
      
      return () => {
        if (timer) clearTimeout(timer);
      };
    }
  }, [status, config.autoClose]);

  const handleClose = () => {
    setVisible(false);
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
    }
    if (onClose) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className={`${config.bgColor} border ${config.borderColor} rounded-lg p-4 mb-4 relative`}
        >
          <button
            onClick={handleClose}
            className="absolute top-2 right-2 text-gray-400 hover:text-white"
            aria-label="Close notification"
          >
            <X className="w-5 h-5" />
          </button>
          <div className="flex items-start">
            <div className="flex-shrink-0">{config.icon}</div>
            <div className="ml-3 flex-1">
              <h3 className={`text-lg font-medium ${config.textColor}`}>{config.title}</h3>
              <div className="mt-1 text-sm text-gray-300">{getTransitionMessage()}</div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default StatusNotification;
