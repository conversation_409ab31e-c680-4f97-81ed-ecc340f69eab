/**
 * WebSocket Environment Toggle Component
 * 
 * Shows current WebSocket environment and allows toggling between
 * local and production WebSocket services.
 */

import React, { useState, useEffect } from 'react';
import { WebSocketConfig } from '../config/websocket.js';
import websocketDataService from '../services/websocketDataService.js';

const WebSocketEnvironmentToggle = ({ className = '' }) => {
  const [currentEnv, setCurrentEnv] = useState(WebSocketConfig.useProduction ? 'production' : 'local');
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  // Update connection status
  useEffect(() => {
    const checkConnectionStatus = () => {
      const connected = websocketDataService.isConnectionReady();
      setIsConnected(connected);
      setConnectionStatus(connected ? 'connected' : 'disconnected');
    };

    // Check initially
    checkConnectionStatus();

    // Check periodically
    const interval = setInterval(checkConnectionStatus, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleEnvironmentChange = async (newEnv) => {
    try {
      console.log(`🔄 Switching WebSocket environment from ${currentEnv} to ${newEnv}`);
      
      // Disconnect current connection
      websocketDataService.disconnect();
      
      // Update environment variable (this would require a page reload in a real app)
      // For now, we'll just show a message
      setCurrentEnv(newEnv);
      setConnectionStatus('switching');
      
      // Show instructions to user
      alert(`To switch to ${newEnv} environment:\n\n1. Update your .env.local file:\n   NEXT_PUBLIC_USE_PRODUCTION_WEBSOCKET=${newEnv === 'production' ? 'true' : 'false'}\n\n2. Restart your development server\n\n3. Refresh this page`);
      
    } catch (error) {
      console.error('❌ Error switching environment:', error);
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'disconnected': return 'text-red-600';
      case 'switching': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return '🟢';
      case 'disconnected': return '🔴';
      case 'switching': return '🟡';
      default: return '⚪';
    }
  };

  const getEnvironmentInfo = () => {
    const info = WebSocketConfig.getEnvironmentInfo();
    return info;
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">WebSocket Environment</h3>
        <div className="flex items-center space-x-2">
          <span className={`text-sm ${getStatusColor()}`}>
            {getStatusIcon()} {connectionStatus}
          </span>
        </div>
      </div>
      
      <div className="space-y-3">
        {/* Current Environment */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Current:</span>
          <span className={`text-sm font-medium px-2 py-1 rounded ${
            currentEnv === 'production' 
              ? 'bg-blue-100 text-blue-800' 
              : 'bg-green-100 text-green-800'
          }`}>
            {currentEnv === 'production' ? '🌐 Production' : '🏠 Local'}
          </span>
        </div>

        {/* Environment Toggle Buttons */}
        <div className="flex space-x-2">
          <button
            onClick={() => handleEnvironmentChange('local')}
            disabled={currentEnv === 'local'}
            className={`flex-1 px-3 py-2 text-xs rounded transition-colors ${
              currentEnv === 'local'
                ? 'bg-green-100 text-green-800 cursor-not-allowed'
                : 'bg-gray-100 text-gray-700 hover:bg-green-50 hover:text-green-700'
            }`}
          >
            🏠 Local
          </button>
          <button
            onClick={() => handleEnvironmentChange('production')}
            disabled={currentEnv === 'production'}
            className={`flex-1 px-3 py-2 text-xs rounded transition-colors ${
              currentEnv === 'production'
                ? 'bg-blue-100 text-blue-800 cursor-not-allowed'
                : 'bg-gray-100 text-gray-700 hover:bg-blue-50 hover:text-blue-700'
            }`}
          >
            🌐 Production
          </button>
        </div>

        {/* Connection Details */}
        <details className="text-xs">
          <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
            Connection Details
          </summary>
          <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono">
            <div>URL: {getEnvironmentInfo().selectedUrl}</div>
            <div>Auth: {WebSocketConfig.requiresAuth() ? 'Required' : 'Not Required'}</div>
            <div>Status: {connectionStatus}</div>
          </div>
        </details>

        {/* Instructions */}
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          <strong>💡 How to switch:</strong>
          <ol className="mt-1 ml-4 list-decimal">
            <li>Update <code>.env.local</code></li>
            <li>Restart dev server</li>
            <li>Refresh page</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default WebSocketEnvironmentToggle;
