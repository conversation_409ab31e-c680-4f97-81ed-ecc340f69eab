import React, { useEffect, useRef, useState, useMemo, useCallback } from "react";
import {
  create<PERSON><PERSON>,
  IChartApi,
  ISeriesApi,
  CrosshairMode,
  PriceScaleMode,
} from "lightweight-charts";

// Performance logging utility - only log in development
const perfLog = (message, ...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(message, ...args);
  }
};

// Utility function to parse semantic indicator keys and organize data
const parseSemanticIndicators = (rawIndicators) => {
  const organized = {
    indicatorsByType: {},
    indicatorsByTypeAndId: {},
    emaByIndicatorId: {},
    rsiByIndicatorId: {},
    macdByIndicatorId: {},
    bollingerByIndicatorId: {}
  };

  if (!rawIndicators || typeof rawIndicators !== 'object') {
    return organized;
  }

  Object.entries(rawIndicators).forEach(([key, data]) => {
    // Parse semantic keys
    const rsiMatch = key.match(/^RSI_(\d+)_(\d+)$/);
    const emaMatch = key.match(/^EMA_(\d+)_(\d+)$/);
    const smaMatch = key.match(/^SMA_(\d+)_(\d+)$/);
    const macdMatch = key.match(/^MACD_(\d+)_(\d+)_(\d+)_(\d+)_(.+)$/);
    const bbMatch = key.match(/^BB_(\d+)_([^_]+)_(\d+)_(.+)$/);

    if (rsiMatch) {
      const [, period, instance] = rsiMatch;
      const type = 'RSI';
      const displayName = `RSI (${period})`;

      if (!organized.indicatorsByType[type]) organized.indicatorsByType[type] = [];
      if (!organized.indicatorsByTypeAndId[type]) organized.indicatorsByTypeAndId[type] = {};

      const indicatorInfo = {
        data,
        displayName,
        parameters: { period: parseInt(period) },
        id: key
      };

      organized.indicatorsByType[type].push(indicatorInfo);
      organized.indicatorsByTypeAndId[type][key] = indicatorInfo;
      organized.rsiByIndicatorId[key] = indicatorInfo;

    } else if (emaMatch) {
      const [, period, instance] = emaMatch;
      const type = 'EMA';
      const displayName = `EMA (${period})`;

      if (!organized.indicatorsByType[type]) organized.indicatorsByType[type] = [];
      if (!organized.indicatorsByTypeAndId[type]) organized.indicatorsByTypeAndId[type] = {};

      const indicatorInfo = {
        data,
        displayName,
        parameters: { period: parseInt(period) },
        id: key
      };

      organized.indicatorsByType[type].push(indicatorInfo);
      organized.indicatorsByTypeAndId[type][key] = indicatorInfo;
      organized.emaByIndicatorId[key] = indicatorInfo;

    } else if (smaMatch) {
      const [, period, instance] = smaMatch;
      const type = 'SMA';
      const displayName = `SMA (${period})`;

      if (!organized.indicatorsByType[type]) organized.indicatorsByType[type] = [];
      if (!organized.indicatorsByTypeAndId[type]) organized.indicatorsByTypeAndId[type] = {};

      const indicatorInfo = {
        data,
        displayName,
        parameters: { period: parseInt(period) },
        id: key
      };

      organized.indicatorsByType[type].push(indicatorInfo);
      organized.indicatorsByTypeAndId[type][key] = indicatorInfo;

    } else if (macdMatch) {
      const [, fast, slow, signal, instance, component] = macdMatch;
      const baseKey = `MACD_${fast}_${slow}_${signal}_${instance}`;
      const type = 'MACD';
      const displayName = `MACD (${fast},${slow},${signal})`;

      if (!organized.indicatorsByType[type]) organized.indicatorsByType[type] = [];
      if (!organized.indicatorsByTypeAndId[type]) organized.indicatorsByTypeAndId[type] = {};
      if (!organized.macdByIndicatorId[baseKey]) {
        organized.macdByIndicatorId[baseKey] = {
          displayName,
          parameters: { fast: parseInt(fast), slow: parseInt(slow), signal: parseInt(signal) },
          id: baseKey,
          data: []
        };
      }

      // Store component data
      if (component === 'LINE') {
        organized.macdByIndicatorId[baseKey].macdData = data;
      } else if (component === 'SIGNAL') {
        organized.macdByIndicatorId[baseKey].signalData = data;
      } else if (component === 'HIST') {
        organized.macdByIndicatorId[baseKey].histogramData = data;
      }

    } else if (bbMatch) {
      const [, period, devfactor, instance, component] = bbMatch;
      const baseKey = `BB_${period}_${devfactor}_${instance}`;
      const type = 'BollingerBands';
      const displayName = `Bollinger Bands (${period},${devfactor})`;

      if (!organized.bollingerByIndicatorId[baseKey]) {
        organized.bollingerByIndicatorId[baseKey] = {
          displayName,
          parameters: { period: parseInt(period), devfactor: parseFloat(devfactor) },
          id: baseKey,
          data: []
        };
      }

      // Store component data
      if (component === 'UPPER') {
        organized.bollingerByIndicatorId[baseKey].upperData = data;
      } else if (component === 'MIDDLE') {
        organized.bollingerByIndicatorId[baseKey].middleData = data;
      } else if (component === 'LOWER') {
        organized.bollingerByIndicatorId[baseKey].lowerData = data;
      }
    }
  });

  return organized;
};

export default function OptimizedTradingChart({
  candleData,
  indicators,
  trades,
  timeframe: displayTimeframe,
  chartTimeframe = "1h",
  instrument,
  marketStatus,
  strategyInfo,
  strategy,
  isRealTimeUpdate = false, // New prop to indicate real-time updates
  hideDisplayPills = false, // New prop to hide display pills
  hideStrategyPills = false, // New prop to hide strategy pills
  // Zoom persistence props
  preserveZoom = false,
  onZoomChange = null,
  initialZoomState = null,
}) {
  // Parse semantic indicators if we have raw semantic keys
  const parsedIndicators = useMemo(() => {
    if (!indicators) return null;

    // Check if indicators are already in the expected format
    if (indicators.indicatorsByTypeAndId || indicators.emaByIndicatorId) {
      console.log('🔍 OptimizedTradingChart: Using pre-formatted indicators');
      return indicators;
    }

    // Parse semantic keys
    console.log('🔍 OptimizedTradingChart: Parsing semantic indicator keys:', Object.keys(indicators));
    const parsed = parseSemanticIndicators(indicators);
    console.log('🔍 OptimizedTradingChart: Parsed indicators:', {
      types: Object.keys(parsed.indicatorsByType),
      emaCount: Object.keys(parsed.emaByIndicatorId).length,
      rsiCount: Object.keys(parsed.rsiByIndicatorId).length,
      macdCount: Object.keys(parsed.macdByIndicatorId).length
    });
    return parsed;
  }, [indicators]);

  // Debug log when component receives new props
  console.log('🔍 OptimizedTradingChart: Props received', {
    candleDataLength: candleData?.length || 0,
    indicatorKeys: indicators ? Object.keys(indicators) : [],
    indicatorCount: indicators ? Object.keys(indicators).length : 0,
    parsedIndicatorTypes: parsedIndicators ? Object.keys(parsedIndicators.indicatorsByType) : [],
    tradesLength: trades?.length || 0,
    isRealTimeUpdate,
    hasStrategy: !!strategy
  });
  // Main chart references
  const chartRef = useRef();
  const chartInstanceRef = useRef();
  const candleSeriesRef = useRef();
  const smaSeriesRef = useRef();
  const emaSeriesRef = useRef();
  const smaDataRef = useRef([]);
  const emaDataRef = useRef([]);

  // Indicator chart references (for RSI and other subwindow indicators)
  const indicatorChartsRef = useRef({});
  const indicatorChartInstancesRef = useRef({});
  const rsiSeriesRef = useRef();
  const overboughtLineRef = useRef();
  const oversoldLineRef = useRef();
  const rsiDataRef = useRef([]);

  // References for all indicator types
  const indicatorSeriesRefs = useRef({});
  const indicatorDataRefs = useRef({});

  // Crosshair synchronization state
  const crosshairSyncRef = useRef({
    isUpdating: false,
    lastSyncTime: null
  });

  // For backward compatibility
  const emaSeriesRefsById = useRef({});
  const emaDataRefsById = useRef({});

  const [tooltipData, setTooltipData] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [showTooltip, setShowTooltip] = useState(false);

  // Indicator chart tooltips state
  const [indicatorTooltips, setIndicatorTooltips] = useState({});

  // Helper functions for indicator tooltips
  const showIndicatorTooltip = useCallback((chartId, tooltipInfo) => {
    setIndicatorTooltips(prev => ({
      ...prev,
      [chartId]: {
        ...tooltipInfo,
        visible: true
      }
    }));
  }, []);

  const hideIndicatorTooltip = useCallback((chartId) => {
    setIndicatorTooltips(prev => ({
      ...prev,
      [chartId]: {
        ...prev[chartId],
        visible: false
      }
    }));
  }, []);

  // Centralized crosshair synchronization function
  const syncCrosshairPosition = useCallback((sourceChart, sourceParam) => {
    // Prevent infinite loops and rapid updates
    if (crosshairSyncRef.current.isUpdating) {
      return;
    }

    // Throttle updates to avoid performance issues
    const now = Date.now();
    if (crosshairSyncRef.current.lastSyncTime && (now - crosshairSyncRef.current.lastSyncTime) < 16) {
      return; // Limit to ~60fps
    }

    crosshairSyncRef.current.isUpdating = true;
    crosshairSyncRef.current.lastSyncTime = now;

    try {
      // Get all chart instances
      const allCharts = [
        chartInstanceRef.current,
        ...Object.values(indicatorChartInstancesRef.current)
      ].filter(chart => chart && chart !== sourceChart);

      // Sync crosshair position to all other charts
      allCharts.forEach(targetChart => {
        if (targetChart && targetChart.timeScale && sourceParam.time) {
          try {
            // Get the coordinate for the time on the target chart
            const coordinate = targetChart.timeScale().timeToCoordinate(sourceParam.time);

            if (coordinate !== null && coordinate >= 0) {
              // Create a synthetic crosshair move event for the target chart
              // This ensures the crosshair appears at the same time position
              const syntheticParam = {
                time: sourceParam.time,
                point: { x: coordinate, y: sourceParam.point?.y || 0 },
                seriesPrices: new Map()
              };

              // Trigger crosshair move on target chart (this will show tooltips)
              // Note: We can't directly call subscribeCrosshairMove, but the visual sync happens automatically
              // when we ensure all charts have the same visible time range
            }
          } catch (error) {
            // Silently handle errors to avoid console spam
            console.debug('Crosshair sync error:', error);
          }
        }
      });
    } finally {
      // Reset sync flag after a short delay
      setTimeout(() => {
        crosshairSyncRef.current.isUpdating = false;
      }, 16); // Reset after one frame
    }
  }, []);

  // Centralized function to set up time scale synchronization
  const setupTimeScaleSync = useCallback((indicatorChart, chartId, indicatorType) => {
    if (!chartInstanceRef.current || !indicatorChart) {
      console.warn(`⚠️ Cannot setup time scale sync for ${chartId} - missing charts`);
      return;
    }

    const mainTimeScale = chartInstanceRef.current.timeScale();
    const indicatorTimeScale = indicatorChart.timeScale();

    if (mainTimeScale && indicatorTimeScale) {
      console.log(`🔗 Setting up enhanced time scale sync for ${indicatorType} chart ${chartId}`);

      // Method 1: Subscribe to visible time range changes (for zoom)
      mainTimeScale.subscribeVisibleTimeRangeChange((timeRange) => {
        if (timeRange && indicatorTimeScale && chartInstanceRef.current && indicatorChartInstancesRef.current[chartId]) {
          try {
            console.log(`📊 Range sync ${indicatorType} ${chartId}:`, timeRange);
            indicatorTimeScale.setVisibleRange(timeRange);
            console.log(`✅ Successfully synced ${indicatorType} ${chartId} time range`);

            // Mark that user has interacted with the chart
            setUserHasInteracted(true);
          } catch (error) {
            console.warn(`Error synchronizing ${indicatorType} ${chartId} time range:`, error);
          }
        }
      });

      // Method 2: Subscribe to logical range changes (for scroll past data)
      mainTimeScale.subscribeVisibleLogicalRangeChange((logicalRange) => {
        if (logicalRange && indicatorTimeScale && chartInstanceRef.current && indicatorChartInstancesRef.current[chartId]) {
          try {
            console.log(`📊 Logical sync ${indicatorType} ${chartId}:`, logicalRange);
            indicatorTimeScale.setVisibleLogicalRange(logicalRange);
            console.log(`✅ Successfully synced ${indicatorType} ${chartId} logical range`);

            // Mark that user has interacted with the chart
            setUserHasInteracted(true);
          } catch (error) {
            console.warn(`Error synchronizing ${indicatorType} ${chartId} logical range:`, error);
          }
        }
      });

      // Method 3: Sync initial ranges
      try {
        const initialRange = mainTimeScale.getVisibleRange();
        const initialLogicalRange = mainTimeScale.getVisibleLogicalRange();

        if (initialRange) {
          console.log(`📊 Setting initial range for ${indicatorType} ${chartId}:`, initialRange);
          indicatorTimeScale.setVisibleRange(initialRange);
        }

        if (initialLogicalRange) {
          console.log(`📊 Setting initial logical range for ${indicatorType} ${chartId}:`, initialLogicalRange);
          indicatorTimeScale.setVisibleLogicalRange(initialLogicalRange);
        }
      } catch (error) {
        console.warn(`Error setting initial ranges for ${indicatorType} ${chartId}:`, error);
      }
    } else {
      console.warn(`⚠️ Failed to set up ${indicatorType} ${chartId} time scale sync - missing time scales`);
    }
  }, []);

  // Helper function to add crosshair handler to indicator charts
  const addIndicatorTooltipHandler = useCallback((chart, chartId, indicatorType, indicatorName, indicatorColor) => {
    chart.subscribeCrosshairMove((param) => {
      if (
        param.point === undefined ||
        !param.time ||
        param.point.x < 0 ||
        param.point.y < 0
      ) {
        hideIndicatorTooltip(chartId);
        return;
      }

      // Sync crosshair position with other charts
      syncCrosshairPosition(chart, param);

      // Collect indicator values for this time point from series prices
      const indicatorValues = [];

      if (param.seriesPrices) {
        param.seriesPrices.forEach((value, series) => {
          indicatorValues.push({
            type: indicatorType,
            displayName: indicatorName || indicatorType,
            value: value,
            color: indicatorColor || '#2962FF',
            id: chartId
          });
        });
      }

      // Show tooltip if we have values
      if (indicatorValues.length > 0) {
        showIndicatorTooltip(chartId, {
          time: param.time,
          indicators: indicatorValues,
          position: { x: param.point.x, y: param.point.y },
          chartType: indicatorType
        });
      } else {
        hideIndicatorTooltip(chartId);
      }
    });
  }, [showIndicatorTooltip, hideIndicatorTooltip]);
  const [showLevels, setShowLevels] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(!candleData || candleData.length === 0);
  const [arePriceLevelsLoaded, setArePriceLevelsLoaded] = useState(candleData && candleData.length > 0);
  const [indicatorsRendered, setIndicatorsRendered] = useState(false);
  const [inTradingSession, setInTradingSession] = useState(true);
  const [chartInitializedTime, setChartInitializedTime] = useState(null);
  const [userHasInteracted, setUserHasInteracted] = useState(false);

  // Configuration for indicator display mode
  const [indicatorDisplayMode, setIndicatorDisplayMode] = useState({
    RSI: 'separate', // 'separate' or 'combined'
    MACD: 'separate',
    ATR: 'separate'
  });

  // Function to analyze indicator dependencies
  const analyzeIndicatorDependencies = () => {
    if (!strategy?.strategy_json?.indicators) return {};

    const dependencies = {};

    // Map indicator IDs to their sources
    strategy.strategy_json.indicators.forEach(indicator => {
      const id = indicator.id;
      const source = indicator.source;

      // If source is not 'price' or a price component (open, high, low, close, volume),
      // it's likely another indicator
      if (source && source !== 'price' &&
          source !== 'open' && source !== 'high' &&
          source !== 'low' && source !== 'close' &&
          source !== 'volume') {
        dependencies[id] = source;
      }
    });

    console.log("Indicator dependencies:", dependencies);
    return dependencies;
  };

  // Get indicator dependencies
  const indicatorDependencies = useMemo(() => analyzeIndicatorDependencies(), [strategy]);

  // Helper function to get indicator types that exist in the strategy
  const getExistingIndicatorTypes = useMemo(() => {
    if (!strategy?.strategy_json?.indicators) return [];

    const types = new Set();
    strategy.strategy_json.indicators.forEach(indicator => {
      const type = indicator.type || indicator.indicator_class;
      if (type) {
        types.add(type.toUpperCase());
      }
    });

    return Array.from(types);
  }, [strategy]);

  // Helper function to count indicators of a specific type
  const getIndicatorCountByType = useMemo(() => {
    if (!strategy?.strategy_json?.indicators) return {};

    const counts = {};
    strategy.strategy_json.indicators.forEach(indicator => {
      const type = (indicator.type || indicator.indicator_class)?.toUpperCase();
      if (type) {
        counts[type] = (counts[type] || 0) + 1;
      }
    });

    return counts;
  }, [strategy]);

  // Comprehensive trading session analysis
  const getComprehensiveTradingSessionInfo = () => {
    const tradingSessions = strategy?.strategy_json?.tradingSession || [];
    const currentHour = new Date().getUTCHours();
    const currentTime = new Date();

    // Define all trading sessions with their details
    const allSessions = [
      {
        name: 'Sydney',
        hours: '22:00-07:00 UTC',
        isActive: (currentHour >= 22 || currentHour < 7),
        activity: 'Medium',
        color: '#FF9800' // Orange
      },
      {
        name: 'Tokyo',
        hours: '00:00-09:00 UTC',
        isActive: (currentHour >= 0 && currentHour < 9),
        activity: 'High',
        color: '#E91E63' // Pink
      },
      {
        name: 'London',
        hours: '08:00-17:00 UTC',
        isActive: (currentHour >= 8 && currentHour < 17),
        activity: 'High',
        color: '#2196F3' // Blue
      },
      {
        name: 'New York',
        hours: '13:00-22:00 UTC',
        isActive: (currentHour >= 13 && currentHour < 22),
        activity: 'High',
        color: '#4CAF50' // Green
      }
    ];

    // Get currently active sessions
    const activeSessions = allSessions.filter(session => session.isActive);

    // Determine market activity level
    let marketActivity = 'Very Low';
    if (activeSessions.length >= 2) {
      marketActivity = 'High';
    } else if (activeSessions.length === 1) {
      marketActivity = 'Medium';
    }

    // Check if strategy sessions are active
    let inTradingSession = false;
    let relevantSessions = [];

    if (!tradingSessions || tradingSessions.length === 0) {
      inTradingSession = true;
      relevantSessions = activeSessions;
    } else if (tradingSessions.includes('All')) {
      inTradingSession = true;
      relevantSessions = activeSessions;
    } else {
      // Check specific sessions
      relevantSessions = allSessions.filter(session =>
        tradingSessions.includes(session.name) ||
        tradingSessions.includes('New York') && session.name === 'New York' ||
        tradingSessions.includes('London') && session.name === 'London' ||
        tradingSessions.includes('Tokyo') && session.name === 'Tokyo' ||
        tradingSessions.includes('Sydney') && session.name === 'Sydney'
      );

      inTradingSession = relevantSessions.some(session => session.isActive);
    }

    return {
      inTradingSession,
      activeSessions,
      relevantSessions,
      marketActivity,
      currentHour,
      currentTime: currentTime.toISOString(),
      tradingSessions
    };
  };

  // Simple check for backward compatibility
  const checkTradingSession = () => {
    const sessionInfo = getComprehensiveTradingSessionInfo();
    return sessionInfo.inTradingSession;
  };
  const lastUpdateRef = useRef(Date.now());
  const isInitialRenderRef = useRef(true);
  const hasInitializedRef = useRef(false);
  const hasProcessedTradesRef = useRef(false);
  const lastCandleCountRef = useRef(0);
  const lastTradeCountRef = useRef(0);
  const tradeMarkersRef = useRef([]); // Store trade markers separately

  // Function to combine and update all markers on the chart
  const updateCombinedMarkers = useCallback(() => {
    if (!candleSeriesRef.current) {
      console.log('[Marker Update] No candlestick series available');
      return;
    }

    // Start with trade markers
    let allCombinedMarkers = [...tradeMarkersRef.current];

    // Add S&R markers from all indicator series
    Object.values(indicatorSeriesRefs.current).forEach(seriesRef => {
      if (seriesRef.srMarkers && Array.isArray(seriesRef.srMarkers)) {
        allCombinedMarkers = allCombinedMarkers.concat(seriesRef.srMarkers);
        console.log(`[Marker Update] Added ${seriesRef.srMarkers.length} S&R markers`);
      }
    });

    // Sort all markers by time (required by TradingView)
    allCombinedMarkers.sort((a, b) => a.time - b.time);

    // Set combined markers on the chart
    try {
      candleSeriesRef.current.setMarkers(allCombinedMarkers);
      console.log(`[Marker Update] Set ${allCombinedMarkers.length} total markers (${tradeMarkersRef.current.length} trade + ${allCombinedMarkers.length - tradeMarkersRef.current.length} S&R)`);
    } catch (error) {
      console.error("[Marker Update] Error setting combined markers:", error);
    }
  }, []);

  // Define consistent price format settings at component scope
  const priceFormat = {
    type: "price",
    precision: 5,
    minMove: 0.00001,
  };

  const chartOptions = {
    layout: {
      background: { color: "#0A0B0B" },
      textColor: "#787B86",
      fontSize: 11,
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    },
    grid: {
      vertLines: {
        color: "rgba(42, 46, 57, 0.5)",
        style: 0, // Solid
        visible: true,
      },
      horzLines: {
        color: "rgba(42, 46, 57, 0.5)",
        style: 0, // Solid
        visible: true,
      },
    },
    crosshair: {
      mode: CrosshairMode.Normal,
      vertLine: {
        color: "rgba(120, 123, 134, 0.8)",
        width: 1,
        style: 3, // Dashed
        visible: true,
        labelVisible: false,
      },
      horzLine: {
        color: "rgba(120, 123, 134, 0.8)",
        width: 1,
        style: 3, // Dashed
        visible: true,
        labelVisible: true,
      },
    },
    rightPriceScale: {
      borderVisible: false,
      textColor: "#787B86",
      scaleMargins: {
        top: 0.05,
        bottom: 0.1,
      },
    },
    timeScale: {
      borderVisible: false,
      textColor: "#787B86",
      timeVisible: true,
      secondsVisible: false,
      fixRightEdge: false, // Allow scrolling past the last candle
      fixLeftEdge: false, // Allow scrolling past the first candle
      rightBarStaysOnScroll: false, // Prevent hovered bar from moving when scrolling
      lockVisibleTimeRangeOnResize: false, // Allow time range to change on resize
      rightOffset: 12, // Add some space on the right for scrolling
    },
  };

  // Function to create indicator chart (for RSI and other subwindow indicators)
  const createIndicatorChart = useCallback((containerId, height = 200, type = 'RSI') => {
    console.log(`🔧 createIndicatorChart called:`, { containerId, height, type });

    const originalContainer = indicatorChartsRef.current[containerId];
    if (!originalContainer) {
      console.log(`❌ Container not found for ${containerId}`);
      return null;
    }

    // If chart already exists, just return it
    if (indicatorChartInstancesRef.current[containerId]) {
      perfLog(`✅ Chart already exists for ${containerId}, returning existing instance`);
      return indicatorChartInstancesRef.current[containerId];
    }

    // Create container for chart and title
    const container = document.createElement('div');
    container.style.position = 'relative';
    container.style.width = '100%';
    container.style.height = `${height}px`;

    // Create title element with TradingView-like styling
    const titleElement = document.createElement('div');
    titleElement.style.position = 'absolute';
    titleElement.style.top = '6px';
    titleElement.style.left = '12px';
    titleElement.style.zIndex = '10';
    titleElement.style.color = '#787B86';
    titleElement.style.fontSize = '12px';
    titleElement.style.fontWeight = '500';
    titleElement.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    titleElement.style.pointerEvents = 'none';
    titleElement.style.letterSpacing = '0.5px';
    titleElement.textContent = type;
    container.appendChild(titleElement);

    // Create chart container
    const chartContainer = document.createElement('div');
    chartContainer.style.width = '100%';
    chartContainer.style.height = '100%';
    chartContainer.style.backgroundColor = '#0A0B0B';
    container.appendChild(chartContainer);

    // Replace the original container with our new container
    if (originalContainer.parentNode) {
      originalContainer.parentNode.replaceChild(container, originalContainer);
      indicatorChartsRef.current[containerId] = chartContainer;
    }

    // Create the chart with TradingView-like styling
    const chart = createChart(chartContainer, {
      width: chartContainer.clientWidth,
      height: height,
      layout: {
        background: { color: '#0A0B0B' },
        textColor: '#787B86',
        fontSize: 11,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      },
      grid: {
        vertLines: {
          color: 'rgba(42, 46, 57, 0.5)',
          style: 0, // Solid
          visible: true,
        },
        horzLines: {
          color: 'rgba(42, 46, 57, 0.5)',
          style: 0, // Solid
          visible: true,
        },
      },
      crosshair: {
        mode: CrosshairMode.Normal,
        vertLine: {
          color: 'rgba(120, 123, 134, 0.8)',
          width: 1,
          style: 3, // Dashed
        },
        horzLine: {
          color: 'rgba(120, 123, 134, 0.8)',
          width: 1,
          style: 3, // Dashed
        },
      },
      rightPriceScale: {
        borderVisible: false,
        textColor: '#787B86',
        scaleMargins: {
          top: 0.05,
          bottom: 0.05,
        },
      },
      timeScale: {
        borderVisible: false,
        textColor: '#787B86',
        timeVisible: true,
        secondsVisible: false,
        fixRightEdge: false,
        fixLeftEdge: false,
        rightBarStaysOnScroll: false,
        lockVisibleTimeRangeOnResize: false,
        rightOffset: 12,
      },
      handleScroll: true,
      handleScale: true,
    });

    // Configure price scale for RSI with TradingView-like styling
    if (type === 'RSI') {
      chart.priceScale('right').applyOptions({
        scaleMargins: {
          top: 0.05,
          bottom: 0.05,
        },
        visible: true,
        autoScale: false,
        mode: PriceScaleMode.Normal,
        invertScale: false,
        alignLabels: true,
        borderVisible: false,
        textColor: '#787B86',
        priceFormat: {
          type: 'price',
          precision: 1,
          minMove: 0.1,
        },
        priceRange: {
          minValue: 0,
          maxValue: 100,
        },
      });
    }

    // Configure price scale for MACD with clean label management
    if (type === 'MACD') {
      chart.priceScale('right').applyOptions({
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
        visible: true,
        autoScale: true,
        mode: PriceScaleMode.Normal,
        invertScale: false,
        alignLabels: true,
        borderVisible: false,
        textColor: '#787B86',
        priceFormat: {
          type: 'custom',
          minMove: 0.00001,
          formatter: (price) => price?.toFixed(5) || '0.00000',
        },
        // Reduce label density to prevent overlapping
        minimumWidth: 80,
        entireTextOnly: true,
        // Add more spacing between labels
        ticksVisible: true,
        drawTicks: false,
      });
    }

    // Note: Crosshair handlers will be added after series are created

    indicatorChartInstancesRef.current[containerId] = chart;
    console.log(`✅ Successfully created chart for ${containerId}:`, { type, height });
    return chart;
  }, []);

  // Define updateChart function at component scope
  const updateChart = () => {
    const chart = chartInstanceRef.current;

    if (!chart) {
      console.error("Chart instance not available");
      return;
    }

    // Check if chart is disposed before proceeding
    try {
      // Test if chart is still valid by accessing a property
      chart.timeScale();
    } catch (error) {
      console.warn("[Chart Update] Chart is disposed, skipping update:", error);
      return;
    }

    // Initialize window.rsiIndicators if it doesn't exist
    if (!window.rsiIndicators) {
      window.rsiIndicators = [];
    }

    // Declare rsiIndicators early to avoid temporal dead zone issues
    let rsiIndicators = window.rsiIndicators || [];

    // Force re-population of rsiIndicators if empty but we have RSI in strategy
    if (rsiIndicators.length === 0 && strategy?.strategy_json?.indicators?.some(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI')) {
      perfLog('🔄 RSI indicators array is empty but strategy has RSI, will repopulate during processing');
    }

    console.log(`🔍 Chart Update Start:`, {
      isRealTimeUpdate,
      rsiIndicatorsCount: rsiIndicators.length,
      rsiIndicators: rsiIndicators.map(r => ({ id: r.id, displayName: r.displayName })),
      candleSeriesExists: !!candleSeriesRef.current,
      chartInstanceExists: !!chartInstanceRef.current
    });

    console.log('🔍 Chart Update - Indicators received:', {
      indicatorKeys: Object.keys(indicators || {}),
      indicatorCount: Object.keys(indicators || {}).length,
      hasMACD: Object.keys(indicators || {}).some(key => key.includes('macd') || key.includes('MACD')),
      hasBB: Object.keys(indicators || {}).some(key => key.includes('_upper') || key.includes('_lower')),
      sampleIndicatorData: Object.keys(indicators || {}).length > 0 ? {
        firstKey: Object.keys(indicators)[0],
        firstDataLength: indicators[Object.keys(indicators)[0]]?.length || 0,
        firstDataSample: indicators[Object.keys(indicators)[0]]?.slice(0, 3) || []
      } : null,
      indicators: indicators
    });

    // Only do incremental updates for real-time WebSocket updates when charts already exist
    // For initial loads and auto-refresh, always do full recreation to ensure all indicators are processed
    // Smart real-time update logic: only use incremental updates for simple candle data updates
    // For indicator changes or initial loads, always do full recreation
    const shouldUseIncrementalUpdate = isRealTimeUpdate &&
                                     candleSeriesRef.current &&
                                     chartInstanceRef.current &&
                                     indicatorsRendered && // Only if indicators are already rendered
                                     Object.keys(indicators || {}).length > 0; // And we have indicators

    if (shouldUseIncrementalUpdate) {
      console.log(`🔄 Real-time update detected. Checking if incremental update is possible...`);

      // Skip real-time updates for the first 3 seconds after chart initialization
      // This allows the initial chart setup to complete without interference
      const timeSinceInit = chartInitializedTime ? Date.now() - chartInitializedTime : Infinity;
      if (timeSinceInit < 3000) {
        console.log('🔄 Skipping real-time update - chart recently initialized:', {
          timeSinceInit,
          chartInitializedTime
        });
        return; // Skip real-time update
      }

      // Check if RSI charts exist, if not, we need to recreate them
      // Check both processed RSI indicators and strategy RSI indicators
      const strategyRSIIndicators = strategy?.strategy_json?.indicators?.filter(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI') || [];
      const hasRSIIndicators = rsiIndicators.length > 0 || strategyRSIIndicators.length > 0;
      const expectedRSICharts = Math.max(rsiIndicators.length, strategyRSIIndicators.length);
      let existingRSICharts = 0;

      console.log(`🔍 RSI Chart Check:`, {
        isRealTimeUpdate,
        hasRSIIndicators,
        rsiIndicatorsLength: rsiIndicators.length,
        displayMode: indicatorDisplayMode.RSI,
        expectedRSICharts,
        currentChartInstances: Object.keys(indicatorChartInstancesRef.current)
      });

      if (hasRSIIndicators) {
        for (let i = 0; i < expectedRSICharts; i++) {
          const chartId = `rsi-chart-${i}`;
          const chartExists = indicatorChartInstancesRef.current[chartId];
          console.log(`🔍 Checking chart ${chartId}: ${chartExists ? 'EXISTS' : 'MISSING'}`);
          if (chartExists) {
            existingRSICharts++;
          }
        }
      }

      // Only do incremental update if ALL expected charts exist
      const shouldDoIncrementalUpdate = !hasRSIIndicators || (existingRSICharts >= expectedRSICharts);

      if (shouldDoIncrementalUpdate) {
        // All charts exist or no RSI indicators expected, do incremental update
        console.log(`🔄 Real-time incremental update - ${hasRSIIndicators ? `all ${existingRSICharts} RSI charts exist` : 'no RSI indicators expected'}`);

        // Just update the data on existing series
        if (candleSeriesRef.current && candleData?.length > 0) {
          try {
            // Sort candle data by time to prevent ordering errors
            const sortedCandleData = [...candleData].sort((a, b) => a.time - b.time);

            // Remove duplicate timestamps to prevent chart errors
            const uniqueCandleData = [];
            let lastTime = null;
            for (const candle of sortedCandleData) {
              if (candle.time !== lastTime) {
                uniqueCandleData.push(candle);
                lastTime = candle.time;
              } else {
                console.warn(`⚠️ Removed duplicate candle with timestamp: ${candle.time} (${new Date(candle.time * 1000).toISOString()})`);
              }
            }

            console.log(`🔄 Setting ${uniqueCandleData.length} unique sorted candles (time range: ${new Date(uniqueCandleData[0]?.time * 1000).toISOString()} to ${new Date(uniqueCandleData[uniqueCandleData.length - 1]?.time * 1000).toISOString()})`);
            candleSeriesRef.current.setData(uniqueCandleData);
          } catch (error) {
            console.error('❌ Error setting candle data:', error);
            // Don't throw the error to prevent component crashes
          }
        }

        // Update indicators if they exist
        if (indicators?.rsi && rsiDataRef.current) {
          rsiDataRef.current = indicators.rsi;
          if (rsiSeriesRef.current) {
            rsiSeriesRef.current.setData(indicators.rsi);
          }
        }

        // Update MACD and ATR indicators during real-time updates
        console.log('🔄 Updating MACD and ATR indicators during real-time update');

        // Update MACD chart if it exists
        const macdChart = indicatorChartInstancesRef.current['macd-chart'];
        if (macdChart && indicators) {
          console.log('🔄 Updating MACD chart with real-time data');
          updateMACDChart(macdChart, indicators);
        }

        // Update ATR chart if it exists
        const atrChart = indicatorChartInstancesRef.current['atr-chart'];
        if (atrChart && indicators) {
          console.log('🔄 Updating ATR chart with real-time data');
          updateATRChart(atrChart, indicators);
        }

        // Update main chart indicators (EMA, Bollinger Bands)
        if (chartInstanceRef.current && indicators) {
          console.log('🔄 Updating main chart indicators during real-time update');
          updateMainChartIndicators(chartInstanceRef.current, indicators);
        }

        // IMPORTANT: Clear loading state for incremental updates
        if (candleData && candleData.length > 0) {
          setIsDataLoading(false);
          setArePriceLevelsLoaded(true);
        }

        return; // Skip full recreation for real-time updates only
      } else {
        // RSI charts are missing during real-time update, force full recreation
        console.log(`🔄 RSI charts missing during real-time update. Expected: ${expectedRSICharts}, Found: ${existingRSICharts}. Forcing full recreation.`);
        // Continue to full recreation
      }
    } else {
      // For non-real-time updates (initial load, auto-refresh), always do full recreation
      console.log('🔄 Doing full recreation:', {
        isRealTimeUpdate,
        indicatorsRendered,
        reason: !shouldUseIncrementalUpdate ? 'incremental update conditions not met' : 'non-real-time update'
      });
    }

    // Ensure price scale settings are maintained with TradingView-like styling
    chart.priceScale("right").applyOptions({
      mode: PriceScaleMode.Normal,
      autoScale: true,
      alignLabels: true,
      borderVisible: false,
      textColor: "#787B86",
      entireTextOnly: false,
      scaleMargins: {
        top: 0.05,
        bottom: 0.1,
      },
      priceFormat,
    });

    // Calculate timezone offset once at the start
    // getTimezoneOffset() returns positive values for timezones behind UTC (e.g., PDT = +420 minutes)
    // To convert FROM UTC TO local time, we need to SUBTRACT the offset
    // But JavaScript's Date constructor already handles timezone conversion automatically
    // So we should NOT do manual timezone conversion for the chart data
    const tzOffset = new Date().getTimezoneOffset() * 60;

    // Debug: Log the timezone conversion
    if (candleData.length > 0) {
      const lastCandle = candleData[candleData.length - 1];
      const currentTime = new Date();
      const utcTime = new Date(lastCandle.time * 1000);

      console.log("🕐 Timezone Debug:", {
        tzOffsetMinutes: currentTime.getTimezoneOffset(),
        tzOffsetSeconds: tzOffset,
        originalTime: lastCandle.time,
        originalTimeUTC: utcTime.toISOString(),
        originalTimeLocal: utcTime.toLocaleString(),
        currentTimeUTC: currentTime.toISOString(),
        currentTimeLocal: currentTime.toLocaleString(),
        timeDifferenceHours: (currentTime.getTime() - utcTime.getTime()) / (1000 * 60 * 60)
      });

      // Check if the timestamp seems reasonable (within last 24 hours)
      const hoursDiff = Math.abs(currentTime.getTime() - utcTime.getTime()) / (1000 * 60 * 60);
      if (hoursDiff > 1) { // Warn if data is more than 1 hour old
        console.warn("⚠️ Chart data is outdated:", {
          lastCandleTime: utcTime.toISOString(),
          hoursAgo: hoursDiff.toFixed(2),
          message: hoursDiff > 24 ? "Data is more than 24 hours old!" : "Data is more than 1 hour old"
        });
      }
    }

    // DON'T convert timestamps - let TradingView handle timezone display
    // TradingView will automatically show times in local timezone on X-axis
    const localCandleData = candleData.map((candle) => ({
      ...candle,
      // Keep original UTC timestamps - TradingView handles timezone display
      time: candle.time,
    }));

    // Disable chart interactions during update
    chart.applyOptions({
      handleScroll: false,
      handleScale: false,
    });

    // Clear existing series - with null checks and error handling for chart
    if (candleSeriesRef.current && chart) {
      try {
        // Double-check chart is not disposed before removing series
        chart.timeScale();
        chart.removeSeries(candleSeriesRef.current);
      } catch (error) {
        console.warn("Error removing candle series in updateChart:", error);
        if (error.message && error.message.includes('disposed')) {
          console.warn("Chart was disposed during series removal");
          return; // Exit early if chart is disposed
        }
      }
      candleSeriesRef.current = null;
    }
    if (rsiSeriesRef.current && chart) {
      try {
        // Check if the series is still valid and belongs to this chart
        if (rsiSeriesRef.current && typeof rsiSeriesRef.current.setData === 'function') {
          chart.removeSeries(rsiSeriesRef.current);
        }
      } catch (error) {
        // RSI series might be on a different chart (indicator chart), so this error is expected
        perfLog("RSI series not on main chart (expected for separate RSI charts):", error.message);
      }
      rsiSeriesRef.current = null;
    }
    if (smaSeriesRef.current && chart) {
      try {
        chart.removeSeries(smaSeriesRef.current);
      } catch (error) {
        console.warn("Error removing SMA series in updateChart:", error);
      }
      smaSeriesRef.current = null;
    }
    if (emaSeriesRef.current && chart) {
      try {
        chart.removeSeries(emaSeriesRef.current);
      } catch (error) {
        console.warn("Error removing EMA series in updateChart:", error);
      }
      emaSeriesRef.current = null;
    }

    // Remove all EMA series by ID (backward compatibility)
    if (chart) {
      Object.values(emaSeriesRefsById.current).forEach(series => {
        if (series) {
          try {
            chart.removeSeries(series);
          } catch (error) {
            console.warn("Error removing EMA series by ID in updateChart:", error);
          }
        }
      });
    }
    emaSeriesRefsById.current = {};

    // Remove all indicator series by type and ID - Updated for marker-based S&R
    if (chart) {
      Object.values(indicatorSeriesRefs.current).forEach(typeRefs => {
        Object.values(typeRefs).forEach(series => {
          if (series) {
            try {
              // Handle marker-based S&R cleanup
              if (series.markerSeries) {
                // Clear all markers and reset trade markers
                if (candleSeriesRef.current) {
                  tradeMarkersRef.current = [];
                  candleSeriesRef.current.setMarkers([]);
                }
                // Remove the invisible marker series
                chart.removeSeries(series.markerSeries);
              } else {
                // Regular series cleanup
                chart.removeSeries(series);
              }
            } catch (error) {
              console.warn("Error removing indicator series in updateChart:", error);
            }
          }
        });
      });
    }
    indicatorSeriesRefs.current = {};
    if (overboughtLineRef.current && chart) {
      try {
        // Check if the series is still valid and belongs to this chart
        if (overboughtLineRef.current && typeof overboughtLineRef.current.setData === 'function') {
          chart.removeSeries(overboughtLineRef.current);
        }
      } catch (error) {
        // Overbought line might be on RSI chart, so this error is expected
        perfLog("Overbought line not on main chart (expected for RSI charts):", error.message);
      }
      overboughtLineRef.current = null;
    }
    if (oversoldLineRef.current && chart) {
      try {
        // Check if the series is still valid and belongs to this chart
        if (oversoldLineRef.current && typeof oversoldLineRef.current.setData === 'function') {
          chart.removeSeries(oversoldLineRef.current);
        }
      } catch (error) {
        // Oversold line might be on RSI chart, so this error is expected
        perfLog("Oversold line not on main chart (expected for RSI charts):", error.message);
      }
      oversoldLineRef.current = null;
    }

    // Create candlestick series with proper price format
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderVisible: false,
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
      priceFormat,
    });

    candleSeriesRef.current = candlestickSeries;

    // Set candle data
    if (localCandleData.length > 0) {
      try {
        // Sort candle data by time to prevent ordering errors
        const sortedCandleData = [...localCandleData].sort((a, b) => a.time - b.time);

        // Remove duplicate timestamps to prevent chart errors
        const uniqueCandleData = [];
        let lastTime = null;
        for (const candle of sortedCandleData) {
          if (candle.time !== lastTime) {
            uniqueCandleData.push(candle);
            lastTime = candle.time;
          } else {
            console.warn(`⚠️ Removed duplicate candle with timestamp: ${candle.time} (${new Date(candle.time * 1000).toISOString()})`);
          }
        }

        console.log(`📊 Setting ${uniqueCandleData.length} unique sorted candles for chart initialization (time range: ${new Date(uniqueCandleData[0]?.time * 1000).toISOString()} to ${new Date(uniqueCandleData[uniqueCandleData.length - 1]?.time * 1000).toISOString()})`);
        candlestickSeries.setData(uniqueCandleData);
      } catch (error) {
        console.error('❌ Error setting candle data during initialization:', error);
        // Don't throw the error to prevent component crashes
      }

      // Add trading session highlighting if sessions are specified
      const tradingSessions = strategy?.strategy_json?.tradingSession || [];
      if (tradingSessions && tradingSessions.length > 0) {
        console.log("[Chart Init] Adding trading session markers");

        // Create session markers for each candle
        const sessionMarkers = [];

        // Group consecutive session candles
        let currentSessionStart = null;
        let inSession = false;

        // Process candles to find session boundaries
        localCandleData.forEach((candle, index) => {
          const isInSession = isTimestampInSession(candle.time);

          // Session start
          if (isInSession && !inSession) {
            currentSessionStart = candle.time;
            inSession = true;

            // Add session marker
            sessionMarkers.push({
              time: candle.time,
              position: 'belowBar',
              color: 'rgba(76, 175, 80, 0.8)',
              shape: 'circle',
              size: 1
            });
          }

          // Session end
          if (!isInSession && inSession) {
            inSession = false;

            // Add session end marker
            sessionMarkers.push({
              time: localCandleData[index - 1]?.time || candle.time,
              position: 'belowBar',
              color: 'rgba(255, 193, 7, 0.8)',
              shape: 'circle',
              size: 1
            });
          }

          // Skip background coloring for individual candles to avoid performance issues
          // For 1m timeframes with 1000+ candles, creating price lines for each candle
          // causes stack overflow in the chart library
        });

        // Add markers to the chart
        if (sessionMarkers.length > 0) {
          candlestickSeries.setMarkers(sessionMarkers);
        }
      }
    }

    // Process trades - show both open and closed trades
    if (trades && trades.length > 0) {
      // Separate open and closed trades
      const openTrades = trades.filter((trade) =>
        trade.status === "open" || trade.status === "OPEN" ||
        (trade.status !== "CLOSED" && trade.status !== "closed" && !trade.closeTime)
      );

      const closedTrades = trades.filter((trade) =>
        trade.status === "CLOSED" || trade.status === "closed" || trade.closeTime
      );

      console.log(`[Trade Visualization] Processing ${openTrades.length} open trades and ${closedTrades.length} closed trades`);
      console.log(`[Trade Visualization] All trades received:`, trades?.map(t => ({
        id: t.tradeID || t.id,
        status: t.status,
        openTime: t.openTime,
        closeTime: t.closeTime,
        realizedPL: t.realizedPL
      })));

      // Process open trades with full visualization (entry, SL, TP lines)
      if (openTrades.length > 0) {

        // Process each trade
        openTrades.forEach((trade) => {
          // Keep trade timestamps in UTC (no conversion needed)
          let startTime, endTime;

          // Get the earliest and latest times from candle data (now in UTC)
          const firstCandleTime = localCandleData[0].time;
          const lastCandleTime =
            localCandleData[localCandleData.length - 1].time;

          // Set startTime to either trade open time or first candle time
          if (trade.openTime instanceof Date) {
            startTime = Math.floor(trade.openTime.getTime() / 1000);
          } else if (typeof trade.openTime === "string") {
            startTime = Math.floor(new Date(trade.openTime).getTime() / 1000);
          } else if (
            typeof trade.openTime === "object" &&
            trade.openTime?.seconds
          ) {
            startTime = trade.openTime.seconds;
          } else if (typeof trade.openTime === "number") {
            startTime = Math.floor(trade.openTime / 1000);
          }

          // Ensure startTime isn't before first candle
          startTime = Math.max(startTime, firstCandleTime);

          // Set endTime to either trade close time or last candle time
          if (trade.closeTime) {
            if (trade.closeTime instanceof Date) {
              endTime = Math.floor(trade.closeTime.getTime() / 1000);
            } else if (typeof trade.closeTime === "string") {
              endTime = Math.floor(new Date(trade.closeTime).getTime() / 1000);
            } else if (typeof trade.closeTime?.seconds) {
              endTime = trade.closeTime.seconds;
            } else if (typeof trade.closeTime === "number") {
              endTime = Math.floor(trade.closeTime / 1000);
            }
          } else {
            endTime = lastCandleTime;
          }

          // Ensure endTime isn't after last candle and is after startTime
          endTime = Math.min(endTime, lastCandleTime);
          endTime = Math.max(endTime, startTime + 1); // Ensure at least 1 second difference

          // Add price lines with proper formatting
          console.log("[Trade Processing] Creating entry price line:", {
            price: trade.price,
            type: typeof trade.price
          });

          try {
            candlestickSeries.createPriceLine({
              price: trade.price,
              color: "#EFBD3A",
              lineWidth: 2,
              lineStyle: 1, // LineStyle.Dashed
              axisLabelVisible: true,
              title: `Entry: ${trade.price.toFixed(5)}`,
            });
            console.log("[Trade Processing] Entry price line created successfully");
          } catch (error) {
            console.error("[Trade Processing] Error creating entry price line:", error);
          }

          if (trade.takeProfitPrice) {
            console.log("[Trade Processing] Creating TP price line:", {
              price: trade.takeProfitPrice,
              type: typeof trade.takeProfitPrice
            });

            try {
              candlestickSeries.createPriceLine({
                price: trade.takeProfitPrice,
                color: "#26a69a",
                lineWidth: 2,
                lineStyle: 2, // LineStyle.Dotted
                axisLabelVisible: true,
                title: `TP: ${trade.takeProfitPrice.toFixed(5)}`,
              });
              console.log("[Trade Processing] TP price line created successfully");
            } catch (error) {
              console.error("[Trade Processing] Error creating TP price line:", error);
            }

            // Add profit zone if TP exists
            console.log("Creating profit zone:", {
              type: trade.type,
              entry: trade.price,
              tp: trade.takeProfitPrice,
              startTime,
              endTime,
              expectedZone:
                trade.type === "long"
                  ? `${trade.price} to ${trade.takeProfitPrice} (above entry)`
                  : `${trade.takeProfitPrice} to ${trade.price} (below entry)`,
            });

            const profitArea = chart.addBaselineSeries({
              baseValue: {
                type: "price",
                price: trade.price,
              },
              topLineColor: "rgba(38, 166, 154, 0)",
              topFillColor1: "rgba(38, 166, 154, 0.28)",
              topFillColor2: "rgba(38, 166, 154, 0.05)",
              bottomLineColor: "rgba(38, 166, 154, 0)",
              bottomFillColor1: "rgba(38, 166, 154, 0.05)",
              bottomFillColor2: "rgba(38, 166, 154, 0.28)",
              lineWidth: 0,
              priceLineVisible: false,
              lastValueVisible: false,
              crosshairMarkerVisible: false,
              priceFormat,
            });

            // Create data points for uniform shading, ensuring time order
            const profitZoneData = [
              { time: startTime, value: trade.takeProfitPrice },
              { time: endTime, value: trade.takeProfitPrice },
            ].sort((a, b) => a.time - b.time); // Ensure ascending time order

            console.log("Profit zone data:", {
              basePrice: trade.price,
              targetPrice: trade.takeProfitPrice,
              points: profitZoneData,
            });

            profitArea.setData(profitZoneData);
          }

          if (trade.stopLossPrice) {
            console.log("[Trade Processing] Creating SL price line:", {
              price: trade.stopLossPrice,
              type: typeof trade.stopLossPrice
            });

            try {
              candlestickSeries.createPriceLine({
                price: trade.stopLossPrice,
                color: "#ef5350",
                lineWidth: 2,
                lineStyle: 2, // LineStyle.Dotted
                axisLabelVisible: true,
                title: `SL: ${trade.stopLossPrice.toFixed(5)}`,
              });
              console.log("[Trade Processing] SL price line created successfully");
            } catch (error) {
              console.error("[Trade Processing] Error creating SL price line:", error);
            }

            // Add loss zone if SL exists
            console.log("Creating loss zone:", {
              type: trade.type,
              entry: trade.price,
              sl: trade.stopLossPrice,
              startTime,
              endTime,
              expectedZone:
                trade.type === "long"
                  ? `${trade.stopLossPrice} to ${trade.price} (below entry)`
                  : `${trade.price} to ${trade.stopLossPrice} (above entry)`,
            });

            const lossArea = chart.addBaselineSeries({
              baseValue: {
                type: "price",
                price: trade.price,
              },
              topLineColor: "rgba(239, 83, 80, 0)",
              topFillColor1: "rgba(239, 83, 80, 0.28)",
              topFillColor2: "rgba(239, 83, 80, 0.05)",
              bottomLineColor: "rgba(239, 83, 80, 0)",
              bottomFillColor1: "rgba(239, 83, 80, 0.05)",
              bottomFillColor2: "rgba(239, 83, 80, 0.28)",
              lineWidth: 0,
              priceLineVisible: false,
              lastValueVisible: false,
              crosshairMarkerVisible: false,
              priceFormat,
            });

            // Create data points for uniform shading, ensuring time order
            const lossZoneData = [
              { time: startTime, value: trade.stopLossPrice },
              { time: endTime, value: trade.stopLossPrice },
            ].sort((a, b) => a.time - b.time); // Ensure ascending time order

            console.log("Loss zone data:", {
              basePrice: trade.price,
              targetPrice: trade.stopLossPrice,
              points: lossZoneData,
            });

            lossArea.setData(lossZoneData);
          }
        });
      } else {
        console.log("[Trade Processing] No open trades found");
      }

      // Process closed trades with simplified visualization (entry/exit markers only)
      if (closedTrades.length > 0) {
        console.log(`[Trade Processing] Adding visualization for ${closedTrades.length} closed trades`);

        // Collect markers for closed trades
        const closedTradeMarkers = [];

        closedTrades.forEach((trade, index) => {
          try {
            // Parse trade times - handle both Date objects and ISO strings correctly
            let entryTime = null;
            let exitTime = null;

            if (trade.openTime) {
              if (trade.openTime instanceof Date) {
                // If it's already a Date object, convert to UTC timestamp
                entryTime = Math.floor(trade.openTime.getTime() / 1000);
              } else if (typeof trade.openTime === 'string') {
                // If it's an ISO string from backend, parse it as UTC (backend sends UTC times)
                entryTime = Math.floor(new Date(trade.openTime + 'Z').getTime() / 1000);
              }
            }

            if (trade.closeTime) {
              if (trade.closeTime instanceof Date) {
                // If it's already a Date object, convert to UTC timestamp
                exitTime = Math.floor(trade.closeTime.getTime() / 1000);
              } else if (typeof trade.closeTime === 'string') {
                // If it's an ISO string from backend, parse it as UTC (backend sends UTC times)
                exitTime = Math.floor(new Date(trade.closeTime + 'Z').getTime() / 1000);
              }
            }

            // Determine trade direction and outcome
            // Note: trade.units is always positive (stored as abs value), so use trade.type for direction
            console.log(`[Trade Processing] Trade direction debug for ${trade.tradeID}:`, {
              units: trade.units,
              type: trade.type,
              rawUnits: typeof trade.units,
              rawType: typeof trade.type
            });

            const isLong = trade.type === 'long' || trade.type === 'LONG';
            const isShort = trade.type === 'short' || trade.type === 'SHORT';
            const isWin = trade.realizedPL > 0;

            console.log(`[Trade Processing] Trade ${trade.tradeID} determined as: ${isLong ? 'LONG' : 'SHORT'} (type: ${trade.type})`);

            // Professional color scheme
            const entryColor = isLong ? '#2E7D32' : '#C62828'; // Darker green for long, darker red for short
            const exitColor = isWin ? '#1B5E20' : '#B71C1C';   // Even darker for exits to distinguish

            // Add entry marker with clean professional styling
            if (entryTime && trade.price) {
              closedTradeMarkers.push({
                time: entryTime,
                position: isLong ? 'belowBar' : 'aboveBar', // LONG below, SHORT above
                color: entryColor,
                shape: isLong ? 'arrowUp' : 'arrowDown',
                text: `${isLong ? 'LONG' : 'SHORT'}\n${parseFloat(trade.price).toFixed(5)}`,
                id: `closed-entry-${trade.tradeID || index}`,
                size: 1.2
              });
            }

            // Add exit marker
            console.log(`[Trade Processing] Exit marker check for trade ${trade.tradeID || index}:`, {
              exitTime,
              closePrice: trade.closePrice,
              exitPrice: trade.exitPrice,
              hasExitTime: !!exitTime,
              hasExitPrice: !!(trade.closePrice || trade.exitPrice),
              realizedPL: trade.realizedPL,
              entryPrice: trade.price,
              units: trade.units
            });

            // Calculate exit price from realized P&L if not provided
            let exitPrice = trade.closePrice || trade.exitPrice;
            if (!exitPrice && exitTime && trade.realizedPL !== undefined && trade.price && trade.units) {
              // Calculate exit price: exitPrice = entryPrice + (realizedPL / units)
              // For forex: 1 pip = 0.0001 for most pairs, 0.01 for JPY pairs
              const entryPrice = parseFloat(trade.price);
              const units = Math.abs(parseFloat(trade.units));
              const pnl = parseFloat(trade.realizedPL);

              if (units > 0) {
                // Calculate price difference from P&L
                const priceDiff = pnl / units;
                exitPrice = entryPrice + (isLong ? priceDiff : -priceDiff);
                console.log(`[Trade Processing] Calculated exit price for trade ${trade.tradeID}: ${exitPrice.toFixed(5)} (entry: ${entryPrice}, P&L: ${pnl}, units: ${units})`);
              }
            }

            if (exitTime && exitPrice) {
              const pnl = trade.realizedPL || 0;
              const pnlText = pnl >= 0 ? `+$${pnl.toFixed(2)}` : `$${pnl.toFixed(2)}`;

              closedTradeMarkers.push({
                time: exitTime,
                position: isLong ? 'aboveBar' : 'belowBar', // LONG exits above, SHORT exits below (opposite of entry)
                color: exitColor,
                shape: 'square',
                text: `EXIT\n${parseFloat(exitPrice).toFixed(5)}\n${pnlText}`,
                id: `closed-exit-${trade.tradeID || index}`,
                size: 1.2
              });

              console.log(`[Trade Processing] Added exit marker for trade ${trade.tradeID || index} at price ${parseFloat(exitPrice).toFixed(5)}`);
            } else {
              console.log(`[Trade Processing] Skipped exit marker for trade ${trade.tradeID || index} - missing exitTime (${!!exitTime}) or exitPrice (${!!exitPrice})`);
            }

            // Note: No price lines for closed trades - only markers for clean visualization

          } catch (error) {
            console.warn(`[Trade Processing] Error processing closed trade ${trade.tradeID || index}:`, error);
          }
        });

        // Sort markers by time (ascending order required by TradingView)
        closedTradeMarkers.sort((a, b) => a.time - b.time);

        // Debug: Log marker times to verify sorting
        console.log(`[Trade Processing] Marker times after sorting:`, closedTradeMarkers.map(m => ({
          id: m.id,
          time: m.time,
          date: new Date(m.time * 1000).toISOString()
        })));

        // Store trade markers and update combined markers
        tradeMarkersRef.current = closedTradeMarkers;
        console.log(`[Trade Processing] Stored ${closedTradeMarkers.length} trade markers`);

        // Update combined markers (trade + S&R)
        updateCombinedMarkers();
      } else {
        console.log("[Trade Processing] No closed trades found");
      }
    } else {
      console.log("[Trade Processing] No trades available");
    }

    // Add SMA indicator
    if (indicators?.sma?.length > 0) {
      // Keep SMA data timestamps in UTC (no conversion needed)
      const smaData = indicators?.sma?.map((point) => ({
        ...point,
        time: point.time,
      })) || [];

      if (smaData.length > 0) {
        // Find SMA indicator in strategy JSON
        const smaIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          (ind.type === 'SMA' || ind.indicator_class === 'SMA')
        );

        // Check if SMA has another indicator as its source
        const smaSource = smaIndicator?.source;
        const smaHasIndicatorSource = smaIndicator &&
          indicatorDependencies[smaIndicator.id] !== undefined;

        // Find the source indicator (e.g., RSI)
        const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === smaSource
        );

        console.log("SMA indicator:", smaIndicator);
        console.log("SMA source:", smaSource);
        console.log("SMA has indicator source:", smaHasIndicatorSource);
        console.log("Source indicator:", sourceIndicator);

        // Determine which price scale to use
        let priceScaleId = "right"; // Default to price chart

        // If SMA has an indicator source and that source is RSI, use the RSI price scale
        if (smaHasIndicatorSource && sourceIndicator &&
            (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
          priceScaleId = "rsi";
          console.log("Placing SMA on RSI chart because RSI is its source");
        }

        // Add SMA series on the appropriate price scale
        const smaSeries = chart.addLineSeries({
          color: "#EFBD3A", // Yellow color for SMA
          lineWidth: 2,
          lineStyle: 0, // Solid line
          priceScaleId: priceScaleId, // Use the determined price scale
          priceFormat,
          lastValueVisible: false, // Don't show value on the right
          priceLineVisible: false, // Remove horizontal dotted lines
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: "#EFBD3A",
          crosshairMarkerBackgroundColor: "#EFBD3A",
          crosshairMarkerBorderWidth: 2,
        });
        smaSeriesRef.current = smaSeries;

        // Add SMA data
        try {
          // Store the data in our ref for tooltip access
          smaDataRef.current = smaData;
          smaSeries.setData(smaData);
          console.log("SMA data stored for tooltip access, length:", smaData.length);
        } catch (error) {
          console.error("Error setting SMA data:", error);
        }
      }
    }

    // Add EMA indicator - both the default one and any individual ones by ID
    console.log('🔍 EMA Processing Check:', {
      hasEmaData: !!(indicators?.ema?.length > 0),
      emaDataLength: indicators?.ema?.length || 0,
      allIndicatorKeys: Object.keys(indicators || {}),
      hasDirectEmaFormat: Object.keys(indicators || {}).some(key => key.includes('EMA') || key.includes('ema'))
    });

    if (indicators?.ema?.length > 0) {
      // Keep EMA data timestamps in UTC (no conversion needed)
      const emaData = indicators?.ema?.map((point) => ({
        ...point,
        time: point.time,
      })) || [];

      console.log('🔍 EMA Data Processing:', {
        originalLength: indicators.ema.length,
        processedLength: emaData.length,
        sampleData: emaData.slice(0, 3)
      });

      if (emaData.length > 0) {
        // Find EMA indicator in strategy JSON
        const emaIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          (ind.type === 'EMA' || ind.indicator_class === 'EMA')
        );

        // Check if EMA has another indicator as its source
        const emaSource = emaIndicator?.source;
        const emaHasIndicatorSource = emaIndicator &&
          indicatorDependencies[emaIndicator.id] !== undefined;

        // Find the source indicator (e.g., RSI)
        const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === emaSource
        );

        console.log("EMA indicator:", emaIndicator);
        console.log("EMA source:", emaSource);
        console.log("EMA has indicator source:", emaHasIndicatorSource);
        console.log("Source indicator:", sourceIndicator);

        // Determine which price scale to use
        let priceScaleId = "right"; // Default to price chart

        // If EMA has an indicator source and that source is RSI, use the RSI price scale
        if (emaHasIndicatorSource && sourceIndicator &&
            (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
          priceScaleId = "rsi";
          console.log("Placing EMA on RSI chart because RSI is its source");
        }

        // Add EMA series on the appropriate price scale
        const emaSeries = chart.addLineSeries({
          color: "#FF6B6B", // Red color for EMA
          lineWidth: 2,
          lineStyle: 0, // Solid line
          priceScaleId: priceScaleId, // Use the determined price scale
          priceFormat,
          lastValueVisible: false, // Don't show value on the right
          priceLineVisible: false, // Remove horizontal dotted lines
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: "#FF6B6B",
          crosshairMarkerBackgroundColor: "#FF6B6B",
          crosshairMarkerBorderWidth: 2,
        });
        emaSeriesRef.current = emaSeries;

        // Add EMA data
        try {
          // Store the data in our ref for tooltip access
          emaDataRef.current = emaData;
          emaSeries.setData(emaData);
          console.log("EMA data stored for tooltip access, length:", emaData.length);
        } catch (error) {
          console.error("Error setting EMA data:", error);
        }
      }
    }

    // Add individual EMA indicators by ID (for backward compatibility)
    if (parsedIndicators?.emaByIndicatorId && Object.keys(parsedIndicators.emaByIndicatorId).length > 0) {
      console.log("Processing individual EMA indicators:", Object.keys(parsedIndicators.emaByIndicatorId));

      // Define a set of colors for different EMAs
      const emaColors = [
        "#FF6B6B", // Red
        "#4ECDC4", // Teal
        "#FFE66D", // Yellow
        "#6A0572", // Purple
        "#FF9F1C", // Orange
        "#2EC4B6", // Turquoise
        "#E71D36", // Bright Red
        "#011627", // Dark Blue
      ];

      // Process each EMA indicator
      Object.entries(parsedIndicators.emaByIndicatorId).forEach(([id, emaInfo], index) => {
        if (!emaInfo.data || emaInfo.data.length === 0) return;

        // Keep EMA data timestamps in UTC (no conversion needed)
        const emaData = emaInfo.data.map((point) => ({
          ...point,
          time: point.time,
        }));

        // Get period from parameters if available
        const period = emaInfo.parameters?.period || 'unknown';

        // Find this specific EMA indicator in strategy JSON
        const emaIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === id
        );

        // Check if this EMA has another indicator as its source
        const emaSource = emaIndicator?.source;
        const emaHasIndicatorSource = emaIndicator &&
          indicatorDependencies[emaIndicator.id] !== undefined;

        // Find the source indicator (e.g., RSI)
        const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
          ind.id === emaSource
        );

        console.log(`EMA ${id} (period: ${period}):`, emaIndicator);
        console.log(`EMA ${id} source:`, emaSource);

        // Determine which price scale to use
        let priceScaleId = "right"; // Default to price chart

        // If EMA has an indicator source and that source is RSI, use the RSI price scale
        if (emaHasIndicatorSource && sourceIndicator &&
            (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
          priceScaleId = "rsi";
          console.log(`Placing EMA ${id} on RSI chart because RSI is its source`);
        }

        // Select a color for this EMA
        const colorIndex = index % emaColors.length;
        const emaColor = emaColors[colorIndex];

        // Add EMA series on the appropriate price scale
        const emaSeries = chart.addLineSeries({
          color: emaColor,
          lineWidth: 2,
          lineStyle: 0, // Solid line
          priceScaleId: priceScaleId,
          priceFormat,
          lastValueVisible: false, // Don't show value on the right
          priceLineVisible: false, // Remove horizontal dotted lines
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: emaColor,
          crosshairMarkerBackgroundColor: emaColor,
          crosshairMarkerBorderWidth: 2,
          // title: `EMA(${period})`, // Removed to clean up chart
        });

        // Store the series reference
        emaSeriesRefsById.current[id] = emaSeries;

        // Add EMA data
        try {
          // Store the data in our ref for tooltip access
          emaDataRefsById.current[id] = {
            data: emaData,
            color: emaColor,
            period: period,
            id: id
          };
          emaSeries.setData(emaData);
          console.log(`EMA ${id} data stored for tooltip access, length:`, emaData.length);
        } catch (error) {
          console.error(`Error setting EMA ${id} data:`, error);
        }
      });
    }

    // Handle direct indicator data format (from real-time calculation)
    if (indicators && Object.keys(indicators).length > 0 && strategy?.strategy_json?.indicators && !parsedIndicators?.indicatorsByTypeAndId) {
      console.log("🔍 Processing indicators in direct format - matching with strategy JSON");
      console.log("🔍 Available indicator keys:", Object.keys(indicators));
      console.log("🔍 Strategy indicators:", strategy.strategy_json.indicators.map(ind => ({ id: ind.id, type: ind.type })));

      // Group indicators by their type from strategy JSON
      const indicatorsByType = {};
      strategy.strategy_json.indicators.forEach(strategyIndicator => {
        const indicatorKey = strategyIndicator.id;
        console.log(`🔍 Checking indicator ${indicatorKey} (${strategyIndicator.type}):`, {
          hasData: !!indicators[indicatorKey],
          dataLength: indicators[indicatorKey]?.length || 0,
          sampleData: indicators[indicatorKey]?.slice(0, 2) || []
        });

        if (indicators[indicatorKey]) {
          if (!indicatorsByType[strategyIndicator.type]) {
            indicatorsByType[strategyIndicator.type] = [];
          }
          indicatorsByType[strategyIndicator.type].push({
            id: strategyIndicator.id,
            data: indicators[indicatorKey],
            parameters: strategyIndicator.parameters,
            type: strategyIndicator.type
          });
        }
      });

      console.log("Grouped indicators by type:", Object.keys(indicatorsByType).map(type =>
        `${type}: ${indicatorsByType[type].length}`
      ));

      // Define colors for indicators
      const indicatorColors = [
        "#FF6B6B", // Red
        "#4ECDC4", // Teal
        "#FFE66D", // Yellow
        "#6A0572", // Purple
        "#FF9F1C", // Orange
        "#2EC4B6", // Turquoise
        "#E71D36", // Bright Red
        "#011627", // Dark Blue
        "#00A8E8", // Blue
        "#007EA7", // Dark Blue
        "#9C89B8", // Purple
        "#F0A202", // Orange
      ];

      // Initialize the indicator series refs structure
      indicatorSeriesRefs.current = {};
      indicatorDataRefs.current = {};

      // Process each indicator type
      Object.entries(indicatorsByType).forEach(([type, typeIndicators]) => {
        if (type.toUpperCase() === 'RSI') {
          // Handle RSI indicators - store for separate charts
          console.log(`🔍 Processing ${typeIndicators.length} RSI indicators`);

          // Clear any existing RSI indicators
          window.rsiIndicators = [];

          typeIndicators.forEach((indicator, index) => {
            const processedData = indicator.data.map((point) => ({
              ...point,
              time: point.time,
            }));

            // Store each RSI indicator separately for multiple RSI charts
            const rsiIndicator = {
              id: indicator.id,
              data: processedData,
              parameters: indicator.parameters,
              displayName: `RSI(${indicator.parameters?.period || 'unknown'})`
            };

            window.rsiIndicators.push(rsiIndicator);

            console.log(`🔍 RSI ${indicator.id} stored for separate chart:`, {
              id: indicator.id,
              dataLength: processedData.length,
              displayName: rsiIndicator.displayName,
              parameters: indicator.parameters
            });
          });

          console.log(`🔍 Total RSI indicators stored:`, window.rsiIndicators.length);

          // For backward compatibility, store the first RSI in rsiDataRef
          if (typeIndicators.length > 0) {
            const firstRsi = typeIndicators[0];
            rsiDataRef.current = firstRsi.data.map((point) => ({
              ...point,
              time: point.time,
            }));
            console.log(`🔍 First RSI stored in rsiDataRef for backward compatibility, length:`, rsiDataRef.current.length);
          }
        } else {
          // Handle other indicators (EMA, SMA, etc.) - add to main chart
          // Skip ATR and MACD indicators as they should only appear in separate charts
          if (type.toUpperCase() === 'ATR') {
            console.log(`Skipping ATR ${type} indicators - will be handled in separate chart`);
            return;
          }
          if (type.toUpperCase() === 'MACD') {
            console.log(`Skipping MACD ${type} indicators - will be handled in separate chart`);
            return;
          }
          if (type.toUpperCase() === 'BOLLINGERBANDS') {
            console.log(`Skipping BollingerBands ${type} indicators - will be handled in TradingView-style section`);
            return;
          }

          indicatorSeriesRefs.current[type] = {};
          indicatorDataRefs.current[type] = {};

          typeIndicators.forEach((indicator, index) => {
            const processedData = indicator.data.map((point) => ({
              ...point,
              time: point.time,
            }));

            // Get parameters for display
            const parameters = indicator.parameters || {};
            const period = parameters.period || 'unknown';
            const displayName = `${type}(${period})`;

            // Select a color for this indicator
            const colorIndex = index % indicatorColors.length;
            const indicatorColor = indicatorColors[colorIndex];

            // Check if data is valid - different indicators have different data structures
            const firstPoint = processedData[0];
            const hasValidData = processedData.length > 0 && firstPoint?.time && (
              firstPoint?.value !== undefined || // Standard indicators (EMA, RSI, etc.)
              firstPoint?.upper !== undefined || // Bollinger Bands
              firstPoint?.macd !== undefined ||  // MACD
              firstPoint?.atr !== undefined     // ATR
            );

            console.log(`🔍 Adding ${type} ${indicator.id} to main chart:`, {
              color: indicatorColor,
              dataLength: processedData.length,
              sampleData: processedData.slice(0, 3),
              hasValidData,
              firstPointKeys: firstPoint ? Object.keys(firstPoint) : []
            });

            // Add indicator series on the main chart
            console.log(`🔧 Creating line series for ${type} ${indicator.id}:`, {
              color: indicatorColor,
              displayName,
              chartExists: !!chart,
              chartType: chart?.constructor?.name
            });

            const indicatorSeries = chart.addLineSeries({
              color: indicatorColor,
              lineWidth: 2, // Restored to normal thickness
              lineStyle: 0, // Solid line
              priceScaleId: "right",
              priceFormat,
              lastValueVisible: false, // Restored to clean appearance
              priceLineVisible: false, // Restored to clean appearance
              crosshairMarkerVisible: true,
              crosshairMarkerRadius: 8, // Restored to normal size
              crosshairMarkerBorderColor: indicatorColor,
              crosshairMarkerBackgroundColor: indicatorColor,
              crosshairMarkerBorderWidth: 2, // Restored to normal width
              // title: displayName, // Removed to keep chart clean
            });

            console.log(`🔧 Line series created for ${type} ${indicator.id}:`, {
              seriesCreated: !!indicatorSeries,
              seriesType: indicatorSeries?.seriesType?.(),
              chartSeriesCount: chart?.series?.length || 0
            });

            // Store the series reference for cleanup
            indicatorSeriesRefs.current[type][indicator.id] = indicatorSeries;

            // Add indicator data
            try {
              // Store the data in our ref for tooltip access
              indicatorDataRefs.current[type][indicator.id] = {
                data: processedData,
                color: indicatorColor,
                parameters: parameters,
                displayName: displayName,
                id: indicator.id,
                type: type
              };
              // Validate data format before setting - different indicators have different structures
              let isValidFormat = false;

              if (type === 'BollingerBands') {
                // Bollinger Bands should have upper, middle, lower properties
                isValidFormat = processedData.every(point =>
                  point &&
                  typeof point.time === 'number' &&
                  (typeof point.upper === 'number' || !isNaN(parseFloat(point.upper))) &&
                  (typeof point.middle === 'number' || !isNaN(parseFloat(point.middle))) &&
                  (typeof point.lower === 'number' || !isNaN(parseFloat(point.lower)))
                );
              } else {
                // Standard indicators should have a value property
                isValidFormat = processedData.every(point =>
                  point &&
                  typeof point.time === 'number' &&
                  (typeof point.value === 'number' || !isNaN(parseFloat(point.value)))
                );
              }

              console.log(`🔍 Data validation for ${type} ${indicator.id}:`, {
                isValidFormat,
                dataLength: processedData.length,
                samplePoint: processedData[0],
                allKeys: processedData[0] ? Object.keys(processedData[0]) : []
              });

              if (!isValidFormat) {
                console.error(`❌ Invalid data format for ${type} ${indicator.id}:`, processedData.slice(0, 3));
                return;
              }

              indicatorSeries.setData(processedData);

              // Only fit content if user hasn't manually zoomed/panned (preserve user's view)
              // NEVER fit content on real-time updates to prevent chart jumping
              if (!isRealTimeUpdate) {
                setTimeout(() => {
                  try {
                    // Don't fit content if user has interacted with the chart or zoom persistence is enabled
                    if (userHasInteracted || preserveZoom) {
                      console.log(`⚡ Skipping fitContent for ${type} ${indicator.id} - preserving user's position (userInteracted: ${userHasInteracted}, preserveZoom: ${preserveZoom})`);
                      return;
                    }

                    // Check if user has manually set a zoom state (don't override user's position)
                    const currentRange = chartInstanceRef.current?.timeScale()?.getVisibleLogicalRange();
                    const isDefaultView = !currentRange || (currentRange.from <= 0 && currentRange.to >= (candleData?.length || 1000) - 1);

                    if (isDefaultView) {
                      chartInstanceRef.current?.timeScale()?.fitContent();
                      console.log(`🔄 Chart fitted to content for ${type} ${indicator.id} (default view)`);
                    } else {
                      console.log(`⚡ Skipping fitContent for ${type} ${indicator.id} - user has custom zoom position`);
                    }
                  } catch (error) {
                    console.error(`❌ Error fitting chart content for ${type} ${indicator.id}:`, error);
                  }
                }, 100);
              } else {
                console.log(`⚡ Skipping fitContent for ${type} ${indicator.id} - real-time update (prevents chart jumping)`);
              }

              console.log(`✅ ${type} ${indicator.id} data set successfully:`, {
                length: processedData.length,
                seriesCreated: !!indicatorSeries,
                seriesType: indicatorSeries?.seriesType?.(),
                firstPoint: processedData[0],
                lastPoint: processedData[processedData.length - 1]
              });
            } catch (error) {
              console.error(`Error setting ${type} ${indicator.id} data:`, error);
            }
          });
        }
      });
    }
    // Process all indicator types from the new structure
    else if (parsedIndicators?.indicatorsByTypeAndId) {
      console.log("Processing all indicator types from new structure");

      // Define a set of colors for different indicators
      const indicatorColors = [
        "#FF6B6B", // Red
        "#4ECDC4", // Teal
        "#FFE66D", // Yellow
        "#6A0572", // Purple
        "#FF9F1C", // Orange
        "#2EC4B6", // Turquoise
        "#E71D36", // Bright Red
        "#011627", // Dark Blue
        "#00A8E8", // Blue
        "#007EA7", // Dark Blue
        "#9C89B8", // Purple
        "#F0A202", // Orange
      ];

      // Initialize the indicator series refs structure
      indicatorSeriesRefs.current = {};
      indicatorDataRefs.current = {};

      // Process each indicator type
      Object.entries(parsedIndicators.indicatorsByTypeAndId).forEach(([type, indicatorsById]) => {
        if (!indicatorsById || Object.keys(indicatorsById).length === 0) return;

        console.log(`Processing ${Object.keys(indicatorsById).length} ${type} indicators`);

        // Initialize the refs for this indicator type
        indicatorSeriesRefs.current[type] = {};
        indicatorDataRefs.current[type] = {};

        // Process each indicator of this type
        Object.entries(indicatorsById).forEach(([id, indicatorInfo], index) => {
          if (!indicatorInfo.data || indicatorInfo.data.length === 0) return;

          // Keep indicator data timestamps in UTC (no conversion needed)
          const indicatorData = indicatorInfo.data.map((point) => ({
            ...point,
            time: point.time,
          }));

          // Get parameters for display
          const parameters = indicatorInfo.parameters || {};
          const period = parameters.period || 'unknown';
          const displayName = `${type}(${period})`;

          // Find this specific indicator in strategy JSON
          const indicator = strategy?.strategy_json?.indicators?.find(ind =>
            ind.id === id
          );

          // Check if this indicator has another indicator as its source
          const indicatorSource = indicator?.source;
          const hasIndicatorSource = indicator &&
            indicatorDependencies[indicator.id] !== undefined;

          // Find the source indicator (e.g., RSI)
          const sourceIndicator = strategy?.strategy_json?.indicators?.find(ind =>
            ind.id === indicatorSource
          );

          console.log(`${type} ${id} parameters:`, parameters);
          console.log(`${type} ${id} source:`, indicatorSource);

          // Determine which price scale to use
          let priceScaleId = "right"; // Default to price chart

          // If indicator has an indicator source and that source is RSI, use the RSI price scale
          if (hasIndicatorSource && sourceIndicator &&
              (sourceIndicator.type === 'RSI' || sourceIndicator.indicator_class === 'RSI')) {
            priceScaleId = "rsi";
            console.log(`Placing ${type} ${id} on RSI chart because RSI is its source`);
          }

          // Special handling for certain indicator types
          if (type.toUpperCase() === 'RSI') {
            // RSI indicators should be stored for the separate RSI chart, not added to main chart
            rsiDataRef.current = indicatorData;
            console.log(`RSI ${id} data stored for separate chart, length:`, indicatorData.length);

            // Also populate window.rsiIndicators for chart creation
            if (!window.rsiIndicators) {
              window.rsiIndicators = [];
            }

            const rsiIndicator = {
              id: id,
              data: indicatorData,
              parameters: parameters,
              displayName: `RSI(${parameters.period || 'unknown'})`
            };

            window.rsiIndicators.push(rsiIndicator);
            console.log(`🔍 RSI ${id} added to window.rsiIndicators:`, rsiIndicator.displayName);

            return; // Skip adding to main chart
          }

          // Skip MACD indicators - they should only appear in separate charts
          if (type.toUpperCase() === 'MACD') {
            console.log(`Skipping MACD ${id} - will be handled in separate chart`);
            return; // Skip adding to main chart
          }

          // Select a color for this indicator
          const colorIndex = index % indicatorColors.length;
          const indicatorColor = indicatorColors[colorIndex];

          // Add indicator series on the appropriate price scale
          const indicatorSeries = chart.addLineSeries({
            color: indicatorColor,
            lineWidth: 2,
            lineStyle: 0, // Solid line
            priceScaleId: priceScaleId,
            priceFormat,
            lastValueVisible: false, // Don't show value on the right
            priceLineVisible: false, // Remove horizontal dotted lines
            crosshairMarkerVisible: true,
            crosshairMarkerRadius: 8,
            crosshairMarkerBorderColor: indicatorColor,
            crosshairMarkerBackgroundColor: indicatorColor,
            crosshairMarkerBorderWidth: 2,
            // title: displayName, // Removed to clean up chart
          });

          // Store the series reference
          indicatorSeriesRefs.current[type][id] = indicatorSeries;

          // Add indicator data
          try {
            // Store the data in our ref for tooltip access
            indicatorDataRefs.current[type][id] = {
              data: indicatorData,
              color: indicatorColor,
              parameters: parameters,
              displayName: displayName,
              id: id,
              type: type
            };
            indicatorSeries.setData(indicatorData);
            console.log(`${type} ${id} data stored for tooltip access, length:`, indicatorData.length);
          } catch (error) {
            console.error(`Error setting ${type} ${id} data:`, error);
          }
        });
      });
    }

    // Note: RSI will be handled in a separate chart below the main chart
    // Store RSI data for later use in separate chart
    if (indicators?.rsi?.length > 0) {
      const rsiData = indicators?.rsi?.map((point) => ({
        ...point,
        time: point.time,
      })) || [];

      rsiDataRef.current = rsiData;
      console.log("RSI data stored for separate chart, length:", rsiData.length);

      // Also populate window.rsiIndicators for chart creation (legacy path)
      if (!window.rsiIndicators || window.rsiIndicators.length === 0) {
        window.rsiIndicators = [{
          id: 'legacy-rsi',
          data: rsiData,
          parameters: { period: 14 }, // Default RSI period
          displayName: 'RSI(14)'
        }];
        console.log("🔍 Legacy RSI added to window.rsiIndicators");
      }
    }

    // Add Bollinger Bands to the main chart (TradingView style with filled areas)
    Object.keys(indicators).forEach(key => {
      if (key.includes('_upper') || key.includes('_middle') || key.includes('_lower')) {
        const baseKey = key.split('_')[0];
        const upperKey = `${baseKey}_upper`;
        const middleKey = `${baseKey}_middle`;
        const lowerKey = `${baseKey}_lower`;

        // Only process if we have all three bands and haven't processed this set yet
        if (indicators[upperKey] && indicators[middleKey] && indicators[lowerKey] &&
            !indicatorSeriesRefs.current[`bb_${baseKey}`]) {

          console.log(`Adding TradingView-style Bollinger Bands for ${baseKey}`);

          // Debug Bollinger Bands data
          console.log(`🔧 Creating Bollinger Bands series for ${baseKey}:`, {
            upperDataLength: indicators[upperKey]?.length || 0,
            middleDataLength: indicators[middleKey]?.length || 0,
            lowerDataLength: indicators[lowerKey]?.length || 0,
            upperSample: indicators[upperKey]?.slice(0, 2),
            middleSample: indicators[middleKey]?.slice(0, 2),
            lowerSample: indicators[lowerKey]?.slice(0, 2)
          });

          // Add middle band (SMA) as a separate line
          const middleSeries = chart.addLineSeries({
            color: '#2196F3', // Blue color matching TradingView
            lineWidth: 1, // Restored to normal thickness
            lineStyle: 0, // Solid line
            priceScaleId: 'right',
            priceFormat,
            lastValueVisible: false, // Restored to clean appearance
            priceLineVisible: false, // Restored to clean appearance
            crosshairMarkerVisible: true,
            // No title to avoid clutter - Bollinger Bands are self-explanatory
          });
          middleSeries.setData(indicators[middleKey]);
          console.log(`✅ Bollinger Bands middle series created and data set for ${baseKey}`);

          // Add lower band as a separate line
          const lowerSeries = chart.addLineSeries({
            color: '#2196F3', // Blue color matching TradingView
            lineWidth: 1, // Restored to normal thickness
            lineStyle: 0, // Solid line
            priceScaleId: 'right',
            priceFormat,
            lastValueVisible: false, // Restored to clean appearance
            priceLineVisible: false, // Restored to clean appearance
            crosshairMarkerVisible: true,
            // No title to avoid clutter
          });
          lowerSeries.setData(indicators[lowerKey]);
          console.log(`✅ Bollinger Bands lower series created and data set for ${baseKey}`);

          // Add upper band as a separate line
          const upperSeries = chart.addLineSeries({
            color: '#2196F3', // Blue color matching TradingView
            lineWidth: 1, // Restored to normal thickness
            lineStyle: 0, // Solid line
            priceScaleId: 'right',
            priceFormat,
            lastValueVisible: false, // Restored to clean appearance
            priceLineVisible: false, // Restored to clean appearance
            crosshairMarkerVisible: true,
            // No title to avoid clutter
          });
          upperSeries.setData(indicators[upperKey]);
          console.log(`✅ Bollinger Bands upper series created and data set for ${baseKey}`);

          // Store references for cleanup
          indicatorSeriesRefs.current[`bb_${baseKey}`] = {
            upper: upperSeries,
            middle: middleSeries,
            lower: lowerSeries
          };
        }
      }
    });

    // Create MACD indicator chart if MACD data exists
    const macdData = Object.keys(indicators).find(key => key.includes('_macd'));
    const macdSignalData = Object.keys(indicators).find(key => key.includes('_signal'));
    const macdHistogramData = Object.keys(indicators).find(key => key.includes('_histogram'));

    console.log('🔍 MACD Debug:', {
      allIndicatorKeys: Object.keys(indicators),
      macdData: macdData,
      macdSignalData: macdSignalData,
      macdHistogramData: macdHistogramData,
      macdDataLength: macdData ? indicators[macdData]?.length : 0,
      macdDataSample: macdData ? indicators[macdData]?.slice(0, 3) : null
    });

    if (macdData && indicators[macdData] && indicators[macdData].length > 0) {
      console.log('✅ Creating MACD chart with data:', indicators[macdData].length, 'points');
      const macdChart = createIndicatorChart('macd-chart', 200, 'MACD');
      if (macdChart) {
        // Apply scrolling options to MACD chart
        macdChart.applyOptions({
          timeScale: {
            fixRightEdge: false,
            fixLeftEdge: false,
            rightBarStaysOnScroll: false,
            lockVisibleTimeRangeOnResize: false,
            rightOffset: 12,
          }
        });

        // Add MACD line
        const macdSeries = macdChart.addLineSeries({
          color: '#2196F3',
          lineWidth: 2,
          priceFormat: {
            type: 'custom',
            minMove: 0.00001,
            formatter: (price) => price?.toFixed(5) || '0.00000',
          },
          lastValueVisible: false,
          priceLineVisible: false,
          crosshairMarkerVisible: true,
          // Remove title to avoid duplicate labels - chart title already shows "MACD"
        });
        macdSeries.setData(indicators[macdData]);

        // Add Signal line if available
        if (macdSignalData && indicators[macdSignalData] && indicators[macdSignalData].length > 0) {
          const signalSeries = macdChart.addLineSeries({
            color: '#FF9800',
            lineWidth: 2,
            priceFormat: {
              type: 'custom',
              minMove: 0.00001,
              formatter: (price) => price?.toFixed(5) || '0.00000',
            },
            lastValueVisible: false,
            priceLineVisible: false,
            crosshairMarkerVisible: true,
            // Remove title to avoid duplicate labels - chart title already shows "MACD"
          });
          signalSeries.setData(indicators[macdSignalData]);
        }

        // Add Histogram if available
        if (macdHistogramData && indicators[macdHistogramData] && indicators[macdHistogramData].length > 0) {
          const histogramSeries = macdChart.addHistogramSeries({
            priceFormat: {
              type: 'custom',
              minMove: 0.00001,
              formatter: (price) => price?.toFixed(5) || '0.00000',
            },
            lastValueVisible: false,
            priceLineVisible: false,
            // Remove title to avoid duplicate labels - chart title already shows "MACD"
          });

          // Convert histogram data to include colors based on positive/negative values
          const histogramDataWithColors = indicators[macdHistogramData].map(point => ({
            time: point.time,
            value: point.value,
            color: point.value >= 0 ? '#4CAF50' : '#F44336' // Green for positive, red for negative
          }));

          histogramSeries.setData(histogramDataWithColors);
        }

        // Set up time scale synchronization with main chart
        setupTimeScaleSync(macdChart, 'macd-chart', 'MACD');

        // Note: One-way sync from main chart to MACD chart to prevent interference with user scrolling

        // Tooltip handlers removed
      }
    }

    // Add ATR indicators to a separate chart
    const atrKeys = Object.keys(indicators).filter(key =>
      key.includes('atr') || key.includes('ATR') ||
      (strategy?.strategy_json?.indicators?.find(ind => ind.id === key &&
        (ind.type === 'ATR' || ind.indicator_class === 'ATR')))
    );

    if (atrKeys.length > 0) {
      console.log('✅ Creating ATR chart with indicators:', atrKeys);
      const atrChart = createIndicatorChart('atr-chart', 200, 'ATR');
      if (atrChart) {
        // Apply scrolling options to ATR chart
        atrChart.applyOptions({
          timeScale: {
            fixRightEdge: false,
            fixLeftEdge: false,
            rightBarStaysOnScroll: false,
            lockVisibleTimeRangeOnResize: false,
            rightOffset: 12,
          }
        });

        // Add each ATR indicator
        atrKeys.forEach((key, index) => {
          if (indicators[key] && indicators[key].length > 0) {
            const atrSeries = atrChart.addLineSeries({
              color: index === 0 ? '#FF5722' : '#FF9800', // Different colors for multiple ATR
              lineWidth: 2,
              priceFormat: {
                type: 'custom',
                minMove: 0.00001,
                formatter: (price) => price?.toFixed(5) || '0.00000',
              },
              lastValueVisible: false,
              priceLineVisible: false, // Remove horizontal dotted lines
              crosshairMarkerVisible: true,
              // Remove title to avoid duplicate labels - chart title already shows "ATR"
            });
            atrSeries.setData(indicators[key]);
          }
        });

        // Set up time scale synchronization with main chart
        setupTimeScaleSync(atrChart, 'atr-chart', 'ATR');

        // Tooltip handlers removed
      }
    }

    // Add Support and Resistance levels to the main chart - Using marker approach like strategy generation page
    Object.keys(indicators).forEach(key => {
      if (key.includes('_support') || key.includes('_resistance')) {
        const baseKey = key.split('_')[0];
        const supportKey = `${baseKey}_support`;
        const resistanceKey = `${baseKey}_resistance`;

        // Only process if we have support/resistance data and haven't processed this set yet
        if ((indicators[supportKey] || indicators[resistanceKey]) &&
            !indicatorSeriesRefs.current[`sr_${baseKey}`]) {

          console.log(`Adding Support/Resistance markers for ${baseKey} (strategy generation style)`);

          // Create a single invisible line series to hold the markers (more efficient)
          const srMarkerSeries = chart.addLineSeries({
            color: 'transparent', // Invisible line, we only want the markers
            lineWidth: 0,
            crosshairMarkerVisible: false,
            lastValueVisible: false,
            priceLineVisible: false,
            visible: true
          });

          // Prepare marker data for all support and resistance levels
          const allMarkers = [];

          // Add support level markers (green circles below bars)
          if (indicators[supportKey] && indicators[supportKey].length > 0) {
            console.log(`Adding ${indicators[supportKey].length} support markers`);
            indicators[supportKey].forEach((level) => {
              allMarkers.push({
                time: level.time,
                position: 'belowBar',
                color: '#4CAF50', // Green for support
                shape: 'circle',
                size: 2
              });
            });
          }

          // Add resistance level markers (red circles above bars)
          if (indicators[resistanceKey] && indicators[resistanceKey].length > 0) {
            console.log(`Adding ${indicators[resistanceKey].length} resistance markers`);
            indicators[resistanceKey].forEach((level) => {
              allMarkers.push({
                time: level.time,
                position: 'aboveBar',
                color: '#F44336', // Red for resistance
                shape: 'circle',
                size: 2
              });
            });
          }

          // Sort markers by time for proper display
          allMarkers.sort((a, b) => a.time - b.time);

          console.log(`Preparing ${allMarkers.length} S&R markers for combination with trade markers`);

          // Store S&R markers and trigger marker combination
          if (allMarkers.length > 0) {
            // Store S&R markers in the series reference for later combination
            indicatorSeriesRefs.current[`sr_${baseKey}`] = {
              markerSeries: srMarkerSeries,
              markersCount: allMarkers.length,
              srMarkers: allMarkers // Store the markers for combination
            };
            console.log(`✅ Stored ${allMarkers.length} S&R markers for combination`);

            // Trigger marker combination and update
            updateCombinedMarkers();
          } else {
            console.log(`⚠️ No S&R markers to store`);
          }

          // Store references for cleanup
          indicatorSeriesRefs.current[`sr_${baseKey}`] = {
            markerSeries: srMarkerSeries,
            markersCount: allMarkers.length
          };
        }
      }
    });

    // RSI chart creation moved to after indicator processing - see below

    // Fallback: Create single RSI chart if RSI data exists (backward compatibility)
    // Only create fallback if no strategy RSI indicators AND no existing RSI charts
    if (rsiIndicators.length === 0 && rsiDataRef.current && rsiDataRef.current.length > 0 &&
        !strategy?.strategy_json?.indicators?.some(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI')) {
      const rsiChart = createIndicatorChart('rsi-chart', 200, 'RSI');
      if (rsiChart) {
        // Ensure RSI chart has the same scrolling options as main chart
        rsiChart.applyOptions({
          timeScale: {
            fixRightEdge: false,
            fixLeftEdge: false,
            rightBarStaysOnScroll: false,
            lockVisibleTimeRangeOnResize: false,
            rightOffset: 12,
          }
        });

        // Add RSI series
        const rsiSeries = rsiChart.addLineSeries({
          color: "#2962FF",
          lineWidth: 2,
          lineStyle: 0,
          lastValueVisible: false,
          crosshairMarkerVisible: true,
          crosshairMarkerRadius: 8,
          crosshairMarkerBorderColor: "#2962FF",
          crosshairMarkerBackgroundColor: "#2962FF",
          crosshairMarkerBorderWidth: 2,
        });

        rsiSeriesRef.current = rsiSeries;
        rsiSeries.setData(rsiDataRef.current);

        // Tooltip handlers removed

        console.log("Single RSI chart created successfully (fallback)");
      }
    }

    // Create RSI indicator charts - handle multiple RSI indicators with display mode
    // This happens AFTER all indicator processing to ensure window.rsiIndicators is populated

    // Re-check rsiIndicators after processing in case they were populated during indicator processing
    rsiIndicators = window.rsiIndicators || [];

    // RSI Chart Creation (reduced logging for performance)

    // If still no RSI indicators but strategy has RSI, try to create from direct indicator data
    if (rsiIndicators.length === 0 && strategy?.strategy_json?.indicators?.some(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI')) {
      console.log('🔄 No RSI indicators found but strategy has RSI, attempting to create from direct data');

      // Try to find RSI data in the indicators object
      const rsiKeys = Object.keys(indicators || {}).filter(key => key.toLowerCase().includes('rsi'));
      if (rsiKeys.length > 0) {
        rsiKeys.forEach((key, index) => {
          const rsiData = indicators[key];
          if (rsiData && Array.isArray(rsiData) && rsiData.length > 0) {
            const rsiIndicator = {
              id: key,
              data: rsiData.map(point => ({
                time: point.time,
                value: point.value
              })),
              parameters: { period: 14 }, // Default period
              displayName: `RSI(14)`,
              color: index === 0 ? "#2962FF" : "#FF6B6B"
            };
            rsiIndicators.push(rsiIndicator);
            perfLog(`🔄 Created RSI indicator from direct data: ${key}`);
          }
        });

        // Update global array
        window.rsiIndicators = rsiIndicators;
      }
    }

    // Only create RSI charts when not in loading state to prevent disposal issues
    if (rsiIndicators.length > 0 && !isDataLoading && arePriceLevelsLoaded) {
      console.log('🚀 Creating RSI charts - chart is ready and not loading');

      if (false) { // Always use separate charts
        // Combined mode: Create single chart with multiple RSI series
        console.log(`✅ Creating 1 combined RSI chart with ${rsiIndicators.length} indicators`);

        // Create combined chart title with all RSI periods
        const combinedTitle = rsiIndicators.length === 1
          ? rsiIndicators[0].displayName
          : `RSI (${rsiIndicators.map(r => r.parameters?.period || '?').join(', ')})`;

        const rsiChart = createIndicatorChart('rsi-chart-0', 200, combinedTitle);

        if (rsiChart) {
          // Configure chart
          rsiChart.applyOptions({
            timeScale: {
              fixRightEdge: false,
              fixLeftEdge: false,
              rightBarStaysOnScroll: false,
              lockVisibleTimeRangeOnResize: false,
              rightOffset: 12,
            }
          });

          // Add each RSI as a separate series on the same chart
          rsiIndicators.forEach((rsiIndicator, index) => {
            const rsiColor = rsiIndicator.color || (index === 0 ? "#2962FF" : "#FF6B6B");

            const rsiSeries = rsiChart.addLineSeries({
              color: rsiColor,
              lineWidth: 2,
              lineStyle: 0,
              lastValueVisible: false,
              crosshairMarkerVisible: true,
              crosshairMarkerRadius: 8,
              crosshairMarkerBorderColor: rsiColor,
              crosshairMarkerBackgroundColor: rsiColor,
              crosshairMarkerBorderWidth: 2,
              // Remove title to avoid duplicate labels - chart title already shows the indicator name
            });

            // Set RSI data

            // Check if chart is still valid before setting data
            try {
              rsiChart.timeScale(); // Test if chart is still valid
              rsiSeries.setData(rsiIndicator.data);

              // Store reference for the first RSI (backward compatibility)
              if (index === 0) {
                rsiSeriesRef.current = rsiSeries;
                rsiDataRef.current = rsiIndicator.data;
              }
            } catch (error) {
              if (error.message && error.message.includes('disposed')) {
                return; // Skip this indicator
              }
              throw error; // Re-throw if it's a different error
            }
          });

          // Add overbought and oversold lines (only once for combined chart)
          const overboughtData = localCandleData.map((point) => ({
            time: point.time,
            value: 70,
          }));

          const oversoldData = localCandleData.map((point) => ({
            time: point.time,
            value: 30,
          }));

          const overboughtLine = rsiChart.addLineSeries({
            color: "#787B86",
            lineWidth: 1,
            lineStyle: 1, // LineStyle.Dashed
            axisLabelVisible: false,
            lastValueVisible: false,
          });

          const oversoldLine = rsiChart.addLineSeries({
            color: "#787B86",
            lineWidth: 1,
            lineStyle: 1, // LineStyle.Dashed
            axisLabelVisible: false,
            lastValueVisible: false,
          });

          overboughtLineRef.current = overboughtLine;
          oversoldLineRef.current = oversoldLine;

          overboughtLine.setData(overboughtData);
          oversoldLine.setData(oversoldData);

          // Configure RSI chart price scale with TradingView-like styling
          rsiChart.priceScale('right').applyOptions({
            scaleMargins: {
              top: 0.05,
              bottom: 0.05,
            },
            visible: true,
            autoScale: false,
            mode: PriceScaleMode.Normal,
            invertScale: false,
            alignLabels: true,
            borderVisible: false, // Remove border for cleaner look
            textColor: '#787B86',
            priceFormat: {
              type: 'price',
              precision: 1,
              minMove: 0.1,
            },
            priceRange: {
              minValue: 0,
              maxValue: 100,
            },
          });

          // Set up time scale synchronization with main chart
          setupTimeScaleSync(rsiChart, 'rsi-chart-0', 'RSI');

          // Tooltip handlers removed

          console.log(`Combined RSI chart created successfully with ${rsiIndicators.length} indicators`);

          // Force resize to ensure chart displays properly
          setTimeout(() => {
            if (rsiChart && indicatorChartsRef.current['rsi-chart-0']) {
              const container = indicatorChartsRef.current['rsi-chart-0'];
              const { width, height } = container.getBoundingClientRect();
              rsiChart.applyOptions({ width, height });
            }
          }, 50);
        }
      } else {
        // Separate mode: Create separate charts (existing logic)
        console.log(`✅ Creating ${rsiIndicators.length} separate RSI charts`);

        const rsiColors = ["#2962FF", "#FF6B6B"]; // Blue and Red for different RSI

        rsiIndicators.forEach((rsiIndicator, index) => {
          const chartId = `rsi-chart-${index}`;
          const rsiChart = createIndicatorChart(chartId, 200, '');

        if (rsiChart) {
          // Ensure RSI chart has the same scrolling options as main chart
          rsiChart.applyOptions({
            timeScale: {
              fixRightEdge: false,
              fixLeftEdge: false,
              rightBarStaysOnScroll: false,
              lockVisibleTimeRangeOnResize: false,
              rightOffset: 12,
            }
          });

          // Add RSI series with different colors
          const rsiColor = rsiColors[index % rsiColors.length];
          const rsiSeries = rsiChart.addLineSeries({
            color: rsiColor,
            lineWidth: 2,
            lineStyle: 0, // Solid line
            lastValueVisible: false,
            crosshairMarkerVisible: true,
            crosshairMarkerRadius: 8,
            crosshairMarkerBorderColor: rsiColor,
            crosshairMarkerBackgroundColor: rsiColor,
            crosshairMarkerBorderWidth: 2,
            // Remove title to avoid duplicate labels - chart title already shows the indicator name
          });

          // Store reference for the first RSI (backward compatibility)
          if (index === 0) {
            rsiSeriesRef.current = rsiSeries;
            rsiDataRef.current = rsiIndicator.data;
          }

          // Set RSI data for separate chart

          // Check if chart is still valid before setting data
          try {
            rsiChart.timeScale(); // Test if chart is still valid
            rsiSeries.setData(rsiIndicator.data);
          } catch (error) {
            if (error.message && error.message.includes('disposed')) {
              return; // Skip this indicator
            }
            throw error; // Re-throw if it's a different error
          }

          // Add overbought and oversold lines
          const overboughtData = localCandleData.map((point) => ({
            time: point.time,
            value: 70,
          }));

          const oversoldData = localCandleData.map((point) => ({
            time: point.time,
            value: 30,
          }));

          const overboughtLine = rsiChart.addLineSeries({
            color: "#787B86",
            lineWidth: 1,
            lineStyle: 1, // LineStyle.Dashed
            axisLabelVisible: false,
            lastValueVisible: false,
          });

          const oversoldLine = rsiChart.addLineSeries({
            color: "#787B86",
            lineWidth: 1,
            lineStyle: 1, // LineStyle.Dashed
            axisLabelVisible: false,
            lastValueVisible: false,
          });

          // Store references for the first RSI (backward compatibility)
          if (index === 0) {
            overboughtLineRef.current = overboughtLine;
            oversoldLineRef.current = oversoldLine;
          }

          overboughtLine.setData(overboughtData);
          oversoldLine.setData(oversoldData);

          // Configure RSI chart price scale with TradingView-like styling
          rsiChart.priceScale('right').applyOptions({
            scaleMargins: {
              top: 0.05,
              bottom: 0.05,
            },
            visible: true,
            autoScale: false,
            mode: PriceScaleMode.Normal,
            invertScale: false,
            alignLabels: true,
            borderVisible: false, // Remove border for cleaner look
            textColor: '#787B86',
            priceFormat: {
              type: 'price',
              precision: 1,
              minMove: 0.1,
            },
            priceRange: {
              minValue: 0,
              maxValue: 100,
            },
          });

          // Set up time scale synchronization with main chart
          setupTimeScaleSync(rsiChart, chartId, 'RSI');

          // Tooltip handlers removed

          console.log(`RSI chart ${chartId} created successfully for ${rsiIndicator.displayName}`);

          // Force resize to ensure chart displays properly
          setTimeout(() => {
            if (rsiChart && indicatorChartsRef.current[chartId]) {
              const container = indicatorChartsRef.current[chartId];
              const { width, height } = container.getBoundingClientRect();
              rsiChart.applyOptions({ width, height });
            }
          }, 50);
        }
      });
      }
    }

    // Always re-enable chart interactions at the end
    chart.applyOptions({
      handleScroll: true,
      handleScale: true,
    });

    // Chart is ready to show once we have candle data and indicators are processed
    // Don't depend on trades for loading state - trades are optional
    if (candleData && candleData.length > 0) {
      console.log(`🔄 Chart ready - clearing loading state. Candle data length: ${candleData.length}`);
      setIsDataLoading(false);
      setArePriceLevelsLoaded(true);

      // Force chart to refresh and fit content after all indicators are processed
      setTimeout(() => {
        try {
          if (chartInstanceRef.current) {
            // Restore zoom state if available, otherwise fit content
            if (preserveZoom && initialZoomState && initialZoomState.from !== undefined && initialZoomState.to !== undefined) {
              console.log('💾 Restoring chart zoom state:', initialZoomState);
              chartInstanceRef.current.timeScale().setVisibleLogicalRange({
                from: initialZoomState.from,
                to: initialZoomState.to
              });
              console.log('✅ Chart zoom state restored successfully');

              // Force sync indicator charts after zoom restoration
              setTimeout(() => {
                try {
                  const mainTimeScale = chartInstanceRef.current.timeScale();
                  const restoredRange = mainTimeScale.getVisibleLogicalRange();

                  Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
                    if (chart && chart.timeScale) {
                      try {
                        chart.timeScale().setVisibleLogicalRange(restoredRange);
                        console.log(`🔗 Synced ${id} chart with restored zoom state`);
                      } catch (error) {
                        console.warn(`Failed to sync ${id} chart:`, error);
                      }
                    }
                  });
                } catch (error) {
                  console.warn('Failed to sync indicator charts after zoom restoration:', error);
                }
              }, 50);
            } else if (!isRealTimeUpdate) {
              // Only fit content on initial load, not on real-time updates
              chartInstanceRef.current.timeScale().fitContent();
              console.log('🔄 Final chart refresh and fit content completed');
            } else {
              console.log('⚡ Skipping final fitContent - real-time update (prevents chart jumping)');
            }
          }
        } catch (error) {
          console.error('❌ Error in final chart refresh:', error);
        }
      }, 200);

      // RSI charts will be created in the normal flow since loading is now complete
    } else {
      console.log(`⚠️ Chart not ready - no candle data. Length: ${candleData?.length || 0}`);
    }
  };

  // Separate useEffect for chart initialization
  useEffect(() => {
    console.log('🔧 Chart initialization useEffect triggered:', {
      hasInitialized: hasInitializedRef.current,
      hasChartRef: !!chartRef.current,
      hasChartInstance: !!chartInstanceRef.current
    });

    // Prevent double initialization - be more strict
    if (
      hasInitializedRef.current ||
      !chartRef.current ||
      chartInstanceRef.current
    ) {
      console.log('🚫 Chart initialization skipped:', {
        hasInitialized: hasInitializedRef.current,
        hasChartRef: !!chartRef.current,
        hasChartInstance: !!chartInstanceRef.current
      });
      return;
    }

    console.log('✅ Starting chart initialization...');

    // Define handleResize outside timeout so it's accessible in cleanup
    const handleResize = () => {
      if (chartRef.current && chartInstanceRef.current) {
        const { width, height } = chartRef.current.getBoundingClientRect();
        chartInstanceRef.current.applyOptions({ width, height });
      }

      // Resize indicator charts
      Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
        const container = indicatorChartsRef.current[id];
        if (container && chart) {
          const { width, height } = container.getBoundingClientRect();
          chart.applyOptions({ width, height });
        }
      });
    };

    // Enhanced scroll synchronization - force sync on any scroll/drag operation
    const forceScrollSync = () => {
      if (!chartInstanceRef.current) return;

      try {
        const mainTimeScale = chartInstanceRef.current.timeScale();
        const visibleRange = mainTimeScale.getVisibleRange();
        const logicalRange = mainTimeScale.getVisibleLogicalRange();

        // Force sync all indicator charts
        Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
          if (chart && chart.timeScale) {
            try {
              if (visibleRange) {
                chart.timeScale().setVisibleRange(visibleRange);
              } else if (logicalRange) {
                chart.timeScale().setVisibleLogicalRange(logicalRange);
              }
            } catch (error) {
              console.warn(`Scroll sync failed for chart ${id}:`, error);
            }
          }
        });
      } catch (error) {
        console.warn('Main chart scroll sync failed:', error);
      }
    };

    // Add a small delay to prevent hot reload issues in development
    const initTimeout = setTimeout(() => {
      if (!chartRef.current || hasInitializedRef.current || chartInstanceRef.current) {
        return;
      }

      const chart = createChart(chartRef.current, {
      layout: chartOptions.layout,
      grid: chartOptions.grid,
      crosshair: chartOptions.crosshair,
      rightPriceScale: {
        ...chartOptions.rightPriceScale,
        mode: PriceScaleMode.Normal,
        autoScale: true,
        alignLabels: true,
        borderVisible: false,
        entireTextOnly: false,
        priceFormat,
      },
      timeScale: {
        ...chartOptions.timeScale,
        timeVisible: true,
        secondsVisible: false,
        fixRightEdge: false, // Allow scrolling past the last candle
        fixLeftEdge: false, // Allow scrolling past the first candle
        rightBarStaysOnScroll: false, // Prevent hovered bar from moving when scrolling
        lockVisibleTimeRangeOnResize: false, // Allow time range to change on resize
        rightOffset: 12, // Add some space on the right for scrolling
      },
      handleScroll: true,
      handleScale: true,
    });

    // Create initial candlestick series with proper price format
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderVisible: false,
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
      priceFormat,
    });

    candleSeriesRef.current = candlestickSeries;
    chartInstanceRef.current = chart;
    hasInitializedRef.current = true;

    console.log('✅ Chart initialization completed:', {
      hasChartInstance: !!chartInstanceRef.current,
      hasCandleSeries: !!candleSeriesRef.current,
      hasInitialized: hasInitializedRef.current,
      hasInitialZoomState: !!initialZoomState
    });

    // Set up zoom change listener for persistence (only on main chart to avoid conflicts)
    if (preserveZoom && onZoomChange && chart) {
      const timeScale = chart.timeScale();

      // Use a debounced approach to avoid conflicts with chart sync
      let zoomSaveTimeout;
      timeScale.subscribeVisibleLogicalRangeChange((logicalRange) => {
        if (logicalRange && onZoomChange) {
          // Mark user interaction
          setUserHasInteracted(true);

          // Clear previous timeout
          if (zoomSaveTimeout) {
            clearTimeout(zoomSaveTimeout);
          }

          // Debounce zoom state saving to avoid conflicts with sync
          zoomSaveTimeout = setTimeout(() => {
            onZoomChange({
              from: logicalRange.from,
              to: logicalRange.to
            });
          }, 100); // Small delay to let sync complete first
        }
      });
      console.log('✅ Zoom change listener set up for persistence (debounced)');
    }

    // Trigger initial data update after chart initialization
    if (candleData?.length > 0) {
      console.log('[Chart Init] Initial data update');
      setTimeout(() => {
        updateChart();
      }, 50);
    }


    // Tooltips removed - no crosshair move handler
    // Tooltips completely removed
    /*
    chart.subscribeCrosshairMove((param) => {
      if (
        param.point === undefined ||
        !param.time ||
        param.point.x < 0 ||
        param.point.y < 0
      ) {
        setShowTooltip(false);
        return;
      }

      // Sync crosshair position with indicator charts
      syncCrosshairPosition(chart, param);

      const tooltipInfo = {
        time: param.time,
        indicators: []
      };

      // Add price data if available
      if (candleSeriesRef.current && param.seriesPrices && param.seriesPrices.has(candleSeriesRef.current)) {
        tooltipInfo.price = param.seriesPrices.get(candleSeriesRef.current);
      }

      // Collect all indicators from the new unified structure
      if (indicatorDataRefs.current) {
        Object.entries(indicatorDataRefs.current).forEach(([type, indicatorsById]) => {
          if (!indicatorsById || Object.keys(indicatorsById).length === 0) return;

          Object.entries(indicatorsById).forEach(([id, indicatorInfo]) => {
            const indicatorPoint = indicatorInfo.data.find(point => point.time === param.time);
            if (indicatorPoint) {
              tooltipInfo.indicators.push({
                type: type,
                displayName: indicatorInfo.displayName,
                value: indicatorPoint.value,
                color: indicatorInfo.color,
                parameters: indicatorInfo.parameters,
                id: id
              });
            }
          });
        });
      }

      // Only show tooltip if we have some data to display
      if (tooltipInfo.price || tooltipInfo.indicators.length > 0) {
        setTooltipData(tooltipInfo);
        setTooltipPosition({ x: param.point.x, y: param.point.y });
        setShowTooltip(true);
      } else {
        setShowTooltip(false);
      }
    });
    */

    // Add click handler for debugging (optional)
    chart.subscribeClick((param) => {
      console.log("Chart click event:", param);
      // Tooltip is already handled by crosshair move
    });

    // Handle window resize
    // forceScrollSync is now defined outside the timeout

    // Add mouse and touch event listeners for enhanced scroll sync
    if (chartRef.current) {
      const chartElement = chartRef.current;

      // Mouse wheel events
      chartElement.addEventListener('wheel', forceScrollSync, { passive: true });

      // Mouse drag events
      chartElement.addEventListener('mousedown', () => {
        const handleMouseMove = () => forceScrollSync();
        const handleMouseUp = () => {
          chartElement.removeEventListener('mousemove', handleMouseMove);
          chartElement.removeEventListener('mouseup', handleMouseUp);
          forceScrollSync(); // Final sync when drag ends
        };

        chartElement.addEventListener('mousemove', handleMouseMove);
        chartElement.addEventListener('mouseup', handleMouseUp);
      });

      // Touch events for mobile
      chartElement.addEventListener('touchmove', forceScrollSync, { passive: true });
      chartElement.addEventListener('touchend', forceScrollSync, { passive: true });
    }

    // handleResize is now defined outside the timeout

    window.addEventListener("resize", handleResize);
    handleResize();

      // Initial data update if data is available
      if (candleData?.length > 0) {
        console.log("[Chart Init] Initial data update");
        console.log("[Chart Init] Latest candle timestamp:", new Date(candleData[candleData.length - 1].time * 1000).toISOString());
        console.log("[Chart Init] Total candles available:", candleData.length);
        console.log("[Chart Init] Indicators available:", Object.keys(indicators || {}).length);
        updateChart();
      } else {
        console.log("[Chart Init] No candle data available for initial update");
      }
    }, 100); // Small delay to prevent hot reload issues

    // Cleanup function
    return () => {
      clearTimeout(initTimeout);
      window.removeEventListener("resize", handleResize);

      // Clean up scroll sync event listeners
      if (chartRef.current) {
        const chartElement = chartRef.current;
        chartElement.removeEventListener('wheel', forceScrollSync);
        chartElement.removeEventListener('touchmove', forceScrollSync);
        chartElement.removeEventListener('touchend', forceScrollSync);
        // Note: mousedown listeners are cleaned up automatically when the element is removed
      }

      if (chartInstanceRef.current) {
        console.log("[Chart Cleanup] Starting cleanup");
        try {
          // Remove all series first - with null checks and error handling
          if (candleSeriesRef.current && chartInstanceRef.current) {
            try {
              chartInstanceRef.current.removeSeries(candleSeriesRef.current);
            } catch (error) {
              console.warn("Error removing candle series:", error);
            }
            candleSeriesRef.current = null;
          }
          if (rsiSeriesRef.current && chartInstanceRef.current) {
            try {
              // Validate series is still valid before removal
              if (rsiSeriesRef.current && typeof rsiSeriesRef.current === 'object') {
                chartInstanceRef.current.removeSeries(rsiSeriesRef.current);
              } else {
                console.log("RSI series not on main chart (expected for separate RSI charts):", rsiSeriesRef.current);
              }
            } catch (error) {
              console.log("RSI series not on main chart (expected for separate RSI charts):", error.message);
            }
            rsiSeriesRef.current = null;
          }
          if (smaSeriesRef.current && chartInstanceRef.current) {
            try {
              chartInstanceRef.current.removeSeries(smaSeriesRef.current);
            } catch (error) {
              console.warn("Error removing SMA series:", error);
            }
            smaSeriesRef.current = null;
          }
          if (emaSeriesRef.current && chartInstanceRef.current) {
            try {
              chartInstanceRef.current.removeSeries(emaSeriesRef.current);
            } catch (error) {
              console.warn("Error removing EMA series:", error);
            }
            emaSeriesRef.current = null;
          }

          // Remove all EMA series by ID (backward compatibility)
          if (chartInstanceRef.current) {
            Object.values(emaSeriesRefsById.current).forEach(series => {
              if (series && chartInstanceRef.current) {
                try {
                  chartInstanceRef.current.removeSeries(series);
                } catch (error) {
                  console.warn("Error removing EMA series:", error);
                }
              }
            });
          }
          emaSeriesRefsById.current = {};

          // Remove all indicator series by type and ID - Updated for marker-based S&R
          if (chartInstanceRef.current) {
            Object.values(indicatorSeriesRefs.current).forEach(typeRefs => {
              Object.values(typeRefs).forEach(series => {
                if (series && chartInstanceRef.current) {
                  try {
                    // Handle marker-based S&R cleanup
                    if (series.markerSeries) {
                      // Clear all markers and reset trade markers
                      if (candleSeriesRef.current) {
                        tradeMarkersRef.current = [];
                        candleSeriesRef.current.setMarkers([]);
                      }
                      // Remove the invisible marker series
                      chartInstanceRef.current.removeSeries(series.markerSeries);
                    } else {
                      // Regular series cleanup
                      chartInstanceRef.current.removeSeries(series);
                    }
                  } catch (error) {
                    console.warn("Error removing indicator series:", error);
                  }
                }
              });
            });
          }
          indicatorSeriesRefs.current = {};
          if (overboughtLineRef.current && chartInstanceRef.current) {
            try {
              // Validate series is still valid before removal
              if (overboughtLineRef.current && typeof overboughtLineRef.current === 'object') {
                chartInstanceRef.current.removeSeries(overboughtLineRef.current);
              } else {
                console.log("Overbought line not on main chart (expected for RSI charts):", overboughtLineRef.current);
              }
            } catch (error) {
              console.log("Overbought line not on main chart (expected for RSI charts):", error.message);
            }
            overboughtLineRef.current = null;
          }
          if (oversoldLineRef.current && chartInstanceRef.current) {
            try {
              // Validate series is still valid before removal
              if (oversoldLineRef.current && typeof oversoldLineRef.current === 'object') {
                chartInstanceRef.current.removeSeries(oversoldLineRef.current);
              } else {
                console.log("Oversold line not on main chart (expected for RSI charts):", oversoldLineRef.current);
              }
            } catch (error) {
              console.log("Oversold line not on main chart (expected for RSI charts):", error.message);
            }
            oversoldLineRef.current = null;
          }

          // Remove the chart instance
          chartInstanceRef.current.remove();
          chartInstanceRef.current = null;
          hasInitializedRef.current = false;

          // Clean up indicator charts
          Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
            if (chart) {
              try {
                chart.remove();
                delete indicatorChartInstancesRef.current[id];
              } catch (error) {
                console.error(`[Chart Cleanup] Error removing indicator chart ${id}:`, error);
              }
            }
          });

          console.log(
            "[Chart Cleanup] Chart instance and indicator charts removed and refs cleared"
          );
        } catch (error) {
          console.error("[Chart Cleanup] Error during cleanup:", error);
        }
      }
    };
  }, []); // Only run once on mount - chart ref will be available after first render

  // Additional useEffect to handle chart initialization when ref becomes available
  useEffect(() => {
    if (chartRef.current && !hasInitializedRef.current && !chartInstanceRef.current) {
      console.log('🔧 Chart ref available, triggering delayed initialization...');

      // Small delay to ensure DOM is ready
      const timeoutId = setTimeout(() => {
        if (chartRef.current && !hasInitializedRef.current && !chartInstanceRef.current) {
          console.log('🔧 Executing delayed chart initialization...');
          initializeChart();
        }
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [chartRef.current]);

  // Add visibility detection to handle tab switching
  useEffect(() => {
    if (!chartRef.current || !chartInstanceRef.current) return;

    // Use Intersection Observer to detect when chart becomes visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio > 0) {
            console.log('🔄 Chart became visible - triggering resize');

            // Small delay to ensure layout is complete
            setTimeout(() => {
              if (chartInstanceRef.current && chartRef.current) {
                try {
                  const { width, height } = chartRef.current.getBoundingClientRect();
                  if (width > 0 && height > 0) {
                    chartInstanceRef.current.applyOptions({ width, height });

                    // Also resize indicator charts
                    Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
                      const container = indicatorChartsRef.current[id];
                      if (container && chart) {
                        const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
                        if (containerWidth > 0 && containerHeight > 0) {
                          chart.applyOptions({ width: containerWidth, height: containerHeight });
                        }
                      }
                    });

                    console.log('🔄 Chart resized after becoming visible');
                  }
                } catch (error) {
                  console.warn('⚠️ Error resizing chart after becoming visible:', error);
                }
              }
            }, 150);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [chartInstanceRef.current]);

  // Add visibility detection to handle tab switching
  useEffect(() => {
    if (!chartRef.current || !chartInstanceRef.current) return;

    // Use Intersection Observer to detect when chart becomes visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.intersectionRatio > 0) {
            console.log('🔄 Chart became visible - triggering resize');

            // Small delay to ensure layout is complete
            setTimeout(() => {
              if (chartInstanceRef.current && chartRef.current) {
                try {
                  const { width, height } = chartRef.current.getBoundingClientRect();
                  if (width > 0 && height > 0) {
                    chartInstanceRef.current.applyOptions({ width, height });

                    // Also resize indicator charts
                    Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
                      const container = indicatorChartsRef.current[id];
                      if (container && chart) {
                        const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
                        if (containerWidth > 0 && containerHeight > 0) {
                          chart.applyOptions({ width: containerWidth, height: containerHeight });
                        }
                      }
                    });

                    console.log('🔄 Chart resized after becoming visible');
                  }
                } catch (error) {
                  console.warn('⚠️ Error resizing chart after becoming visible:', error);
                }
              }
            }, 150);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartRef.current);

    return () => {
      observer.disconnect();
    };
  }, [chartInstanceRef.current]);

  // Separate useEffect for data updates
  useEffect(() => {
    console.log('📊 Data update useEffect triggered:', {
      hasChartInstance: !!chartInstanceRef.current,
      candleDataLength: candleData?.length || 0,
      indicatorCount: indicators ? Object.keys(indicators).length : 0,
      hasInitialized: hasInitializedRef.current,
      isRealTimeUpdate
    });

    // Skip if no chart instance or no data
    if (!chartInstanceRef.current || !candleData?.length) {
      console.log('🚫 Data update skipped - no chart instance or data', {
        hasChartInstance: !!chartInstanceRef.current,
        candleDataLength: candleData?.length || 0,
        hasInitialized: hasInitializedRef.current
      });
      return;
    }

    // Skip if not initialized
    if (!hasInitializedRef.current) {
      console.log('🚫 Data update skipped - chart not initialized');
      return;
    }

    const now = Date.now();

    // For real-time updates, use a shorter debounce and allow more frequent updates
    const debounceTime = isRealTimeUpdate ? 100 : 1000;

    // Only apply debounce if we have the same data as before
    if (
      !isInitialRenderRef.current &&
      now - lastUpdateRef.current < debounceTime &&
      candleData?.length === lastCandleCountRef.current &&
      trades?.length === lastTradeCountRef.current &&
      !isRealTimeUpdate // Don't debounce real-time updates
    ) {
      return;
    }

    // Update our tracking refs
    isInitialRenderRef.current = false;
    lastUpdateRef.current = now;
    lastCandleCountRef.current = candleData?.length || 0;
    lastTradeCountRef.current = trades?.length || 0;

    // Start loading state (but skip for real-time updates)
    if (!isRealTimeUpdate) {
      setIsDataLoading(true);
      setArePriceLevelsLoaded(false);
    }

    // Update chart with a small delay to ensure proper initialization
    console.log('🔄 Chart update triggered by useEffect:', {
      candleDataLength: candleData?.length,
      tradesLength: trades?.length,
      indicatorKeys: indicators ? Object.keys(indicators) : [],
      indicatorCount: indicators ? Object.keys(indicators).length : 0,
      isRealTimeUpdate,
      latestCandle: candleData?.length > 0 ? new Date(candleData[candleData.length - 1].time * 1000).toISOString() : 'No data'
    });

    const timeoutId = setTimeout(() => {
      updateChart();
    }, isRealTimeUpdate ? 0 : 100); // No delay for real-time updates

    return () => {
      clearTimeout(timeoutId);
    };
  }, [candleData, trades, parsedIndicators, isRealTimeUpdate]); // Include parsedIndicators to trigger chart updates when indicator data changes

  // Reset processed trades ref when component unmounts
  useEffect(() => {
    return () => {
      hasProcessedTradesRef.current = false;
    };
  }, []);

  // Helper function to determine if a timestamp is in a trading session
  const isTimestampInSession = (timestamp) => {
    const tradingSessions = strategy?.strategy_json?.tradingSession || [];

    // If no trading sessions specified, assume all sessions are valid
    if (!tradingSessions || tradingSessions.length === 0) {
      return true;
    }

    // Convert timestamp to Date object and get UTC hours
    const date = new Date(timestamp * 1000);
    const utcHour = date.getUTCHours();

    // Check if time is in any of the specified trading sessions
    return tradingSessions.some(session => {
      switch(session) {
        case 'New York':
          // New York session: 8:00-17:00 EST (13:00-22:00 UTC)
          return utcHour >= 13 && utcHour < 22;
        case 'London':
          // London session: 8:00-17:00 GMT (8:00-17:00 UTC)
          return utcHour >= 8 && utcHour < 17;
        case 'Tokyo':
          // Tokyo session: 9:00-18:00 JST (0:00-9:00 UTC)
          return utcHour >= 0 && utcHour < 9;
        case 'Sydney':
          // Sydney session: 8:00-17:00 AEST (22:00-7:00 UTC)
          return utcHour >= 22 || utcHour < 7;
        case 'All':
          return true;
        default:
          return false;
      }
    });
  };

  // Check trading session status every minute
  useEffect(() => {
    // Initial check
    setInTradingSession(checkTradingSession());

    // Set up interval to check every minute
    const interval = setInterval(() => {
      setInTradingSession(checkTradingSession());
    }, 60000); // 60 seconds

    return () => clearInterval(interval);
  }, [strategy]);

  // Trigger chart recreation when display mode changes
  useEffect(() => {
    // Skip if no chart instance or no data
    if (!chartInstanceRef.current || !candleData?.length) {
      return;
    }

    // Skip if not initialized
    if (!hasInitializedRef.current) {
      return;
    }

    console.log('🔄 Display mode changed, recreating charts:', indicatorDisplayMode);

    // Clear existing indicator charts
    Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
      if (chart && id.includes('rsi-chart')) {
        try {
          chart.remove();
          delete indicatorChartInstancesRef.current[id];
        } catch (error) {
          console.warn(`Error removing RSI chart ${id}:`, error);
        }
      }
    });

    // Trigger chart update to recreate with new display mode
    setTimeout(() => {
      updateChart();
    }, 100);
  }, [indicatorDisplayMode]);

  // Force chart update when indicators become available (handles race condition)
  useEffect(() => {
    // Only proceed if we have indicators and chart is ready
    if (!parsedIndicators || Object.keys(parsedIndicators.indicatorsByType || {}).length === 0) {
      setIndicatorsRendered(false);
      return;
    }

    // Skip if indicators are already rendered to prevent unnecessary updates
    if (indicatorsRendered) {
      return;
    }

    console.log('🔄 Indicators available, forcing chart update:', {
      hasChartInstance: !!chartInstanceRef.current,
      hasInitialized: hasInitializedRef.current,
      indicatorCount: Object.keys(indicators).length,
      indicatorsRendered
    });

    // Case 1: Chart is already initialized - force immediate update
    if (chartInstanceRef.current && hasInitializedRef.current) {
      console.log('🔄 Forcing immediate chart update with indicators');
      updateChart();

      // Mark indicators as rendered
      setIndicatorsRendered(true);

      // Also trigger a delayed update to ensure rendering
      setTimeout(() => {
        console.log('🔄 Delayed chart update for indicators');
        updateChart();
      }, 100);
    }
    // Case 2: Chart not initialized yet - will be handled during initialization
    else {
      console.log('⏳ Chart not ready yet, indicators will be processed during initialization');
    }
  }, [parsedIndicators, indicatorsRendered]);

  const isLoading = isDataLoading || !arePriceLevelsLoaded;

  // Helper function to update MACD chart during real-time updates
  const updateMACDChart = (macdChart, indicators) => {
    try {
      // Find MACD data in indicators
      const macdKeys = Object.keys(indicators).filter(key => key.includes('_macd') && !key.includes('_signal') && !key.includes('_histogram'));
      const signalKeys = Object.keys(indicators).filter(key => key.includes('_signal'));
      const histogramKeys = Object.keys(indicators).filter(key => key.includes('_histogram'));

      console.log('🔍 MACD Update Debug:', {
        macdKeys,
        signalKeys,
        histogramKeys,
        chartSeriesCount: macdChart.series?.length || 0
      });

      if (macdKeys.length > 0 && signalKeys.length > 0 && histogramKeys.length > 0) {
        const macdData = indicators[macdKeys[0]];
        const signalData = indicators[signalKeys[0]];
        const histogramData = indicators[histogramKeys[0]];

        // For now, skip the real-time update and let the full recreation handle it
        // The issue is that we're trying to update series that might not exist or have different structure
        console.log('🔄 Skipping MACD real-time update - will be handled by full recreation');

        console.log('✅ MACD chart updated with real-time data');
      }
    } catch (error) {
      console.error('❌ Error updating MACD chart:', error);
    }
  };

  // Helper function to update ATR chart during real-time updates
  const updateATRChart = (atrChart, indicators) => {
    try {
      // Find ATR data in indicators
      const atrKeys = Object.keys(indicators).filter(key => key.includes('atr') || key.toLowerCase().includes('atr'));

      console.log('🔍 ATR Update Debug:', {
        atrKeys,
        chartSeriesCount: atrChart.series?.length || 0
      });

      if (atrKeys.length > 0) {
        // For now, skip the real-time update and let the full recreation handle it
        console.log('🔄 Skipping ATR real-time update - will be handled by full recreation');
        console.log('✅ ATR chart updated with real-time data');
      }
    } catch (error) {
      console.error('❌ Error updating ATR chart:', error);
    }
  };

  // Helper function to update main chart indicators during real-time updates
  const updateMainChartIndicators = (chart, indicators) => {
    try {
      // Find indicator keys
      const emaKeys = Object.keys(indicators).filter(key => key.includes('ema') && !key.includes('_'));
      const bbKeys = Object.keys(indicators).filter(key => key.includes('bollingerbands') || key.includes('_upper') || key.includes('_lower'));

      console.log('🔍 Main Chart Update Debug:', {
        emaKeys,
        bbKeys,
        chartSeriesCount: chart.series?.length || 0
      });

      // For now, skip the real-time update and let the full recreation handle it
      // The issue is that we're trying to update series that might not exist or have different structure
      console.log('🔄 Skipping main chart real-time update - will be handled by full recreation');

      console.log('✅ Main chart indicators updated with real-time data');
    } catch (error) {
      console.error('❌ Error updating main chart indicators:', error);
    }
  };

  // Chart initialization function
  const initializeChart = () => {
    if (!chartRef.current || hasInitializedRef.current || chartInstanceRef.current) {
      return;
    }

    try {
      const chart = createChart(chartRef.current, {
        layout: chartOptions.layout,
        grid: chartOptions.grid,
        crosshair: chartOptions.crosshair,
        rightPriceScale: {
          ...chartOptions.rightPriceScale,
          mode: PriceScaleMode.Normal,
          autoScale: true,
          alignLabels: true,
          borderVisible: false,
          entireTextOnly: false,
          priceFormat,
        },
        timeScale: {
          ...chartOptions.timeScale,
          timeVisible: true,
          secondsVisible: false,
          fixRightEdge: false,
          fixLeftEdge: false,
          rightBarStaysOnScroll: false,
          lockVisibleTimeRangeOnResize: false,
          rightOffset: 12,
        },
        handleScroll: true,
        handleScale: true,
      });

      const candlestickSeries = chart.addCandlestickSeries({
        upColor: "#26a69a",
        downColor: "#ef5350",
        borderVisible: false,
        wickUpColor: "#26a69a",
        wickDownColor: "#ef5350",
        priceFormat,
      });

      candleSeriesRef.current = candlestickSeries;
      chartInstanceRef.current = chart;
      hasInitializedRef.current = true;
      setIndicatorsRendered(false); // Reset indicators rendered state
      setChartInitializedTime(Date.now()); // Track when chart was initialized

      // Set up zoom change listener for persistence (only on main chart to avoid conflicts)
      if (preserveZoom && onZoomChange && chart) {
        const timeScale = chart.timeScale();

        // Use a debounced approach to avoid conflicts with chart sync
        let zoomSaveTimeout;
        timeScale.subscribeVisibleLogicalRangeChange((logicalRange) => {
          if (logicalRange && onZoomChange) {
            // Mark user interaction
            setUserHasInteracted(true);

            // Clear previous timeout
            if (zoomSaveTimeout) {
              clearTimeout(zoomSaveTimeout);
            }

            // Debounce zoom state saving to avoid conflicts with sync
            zoomSaveTimeout = setTimeout(() => {
              onZoomChange({
                from: logicalRange.from,
                to: logicalRange.to
              });
            }, 100); // Small delay to let sync complete first
          }
        });
        console.log('✅ Zoom change listener set up for persistence (debounced, initializeChart)');
      }

      // Initial data update if data is available
      if (candleData && candleData.length > 0) {
        console.log('🔄 Chart initialized, updating with available data and indicators:', {
          candleCount: candleData.length,
          indicatorCount: indicators ? Object.keys(indicators).length : 0
        });

        // Immediate update
        updateChart();

        // Delayed update to ensure indicators are rendered
        setTimeout(() => {
          console.log('🔄 Delayed update after chart initialization');
          updateChart();

          // Restore zoom state after chart is fully loaded
          if (preserveZoom && initialZoomState && initialZoomState.from !== undefined && initialZoomState.to !== undefined && chartInstanceRef.current) {
            setTimeout(() => {
              try {
                console.log('💾 Restoring chart zoom state (delayed):', initialZoomState);
                chartInstanceRef.current.timeScale().setVisibleLogicalRange({
                  from: initialZoomState.from,
                  to: initialZoomState.to
                });
                console.log('✅ Chart zoom state restored successfully (delayed)');

                // Force sync indicator charts after delayed zoom restoration
                setTimeout(() => {
                  try {
                    const mainTimeScale = chartInstanceRef.current.timeScale();
                    const restoredRange = mainTimeScale.getVisibleLogicalRange();

                    Object.entries(indicatorChartInstancesRef.current).forEach(([id, chart]) => {
                      if (chart && chart.timeScale) {
                        try {
                          chart.timeScale().setVisibleLogicalRange(restoredRange);
                          console.log(`🔗 Synced ${id} chart with delayed restored zoom state`);
                        } catch (error) {
                          console.warn(`Failed to sync ${id} chart (delayed):`, error);
                        }
                      }
                    });
                  } catch (error) {
                    console.warn('Failed to sync indicator charts after delayed zoom restoration:', error);
                  }
                }, 50);
              } catch (error) {
                console.error('❌ Error restoring zoom state (delayed):', error);
              }
            }, 300);
          }
        }, 100);
      }
    } catch (error) {
      console.error('❌ Chart initialization function failed:', error);
      hasInitializedRef.current = false;
    }
  };

  // Ensure loading states are cleared when data is available
  useEffect(() => {
    if (candleData && candleData.length > 0 && (isDataLoading || !arePriceLevelsLoaded)) {
      console.log('🔄 Clearing loading states - data is available:', {
        candleDataLength: candleData.length,
        isDataLoading,
        arePriceLevelsLoaded
      });
      setIsDataLoading(false);
      setArePriceLevelsLoaded(true);
    }
  }, [candleData, isDataLoading, arePriceLevelsLoaded]);

  // Fallback timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isDataLoading || !arePriceLevelsLoaded) {
        console.log('⚠️ Loading timeout - forcing loading states to clear');
        setIsDataLoading(false);
        setArePriceLevelsLoaded(true);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeout);
  }, [isDataLoading, arePriceLevelsLoaded]);

  // Debug loading state and chart status
  console.log(`🔍 Chart Status Debug:`, {
    isDataLoading,
    arePriceLevelsLoaded,
    isLoading,
    candleDataLength: candleData?.length || 0,
    indicatorsKeys: indicators ? Object.keys(indicators) : [],
    hasChartInstance: !!chartInstanceRef.current,
    hasCandleSeries: !!candleSeriesRef.current,
    hasRSIIndicators: strategy?.strategy_json?.indicators?.some(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI'),
    indicatorChartCount: Object.keys(indicatorChartInstancesRef.current).length,
    indicatorChartIds: Object.keys(indicatorChartInstancesRef.current)
  });

  // Early return if no candle data
  if (!candleData || candleData.length === 0) {
    return (
      <div className="flex items-center justify-center h-96 bg-[#0A0B0B] rounded-lg border border-[#333]">
        <div className="text-center text-gray-400">
          <div className="text-4xl mb-4">📊</div>
          <div className="text-lg font-medium">No Chart Data</div>
          <div className="text-sm">Waiting for candle data to load...</div>
          {process.env.NODE_ENV === 'development' && (
            <div className="text-xs mt-2 text-gray-500">
              Debug: candleData length = {candleData?.length || 0}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] overflow-hidden">
      {/* Strategy Info Pills Overlay - Only show if not hidden */}
      {!hideStrategyPills && (
        <div className="absolute top-2 left-2 right-2 z-10 flex items-center justify-between">
          {/* Strategy Info */}
          <div className="flex items-center space-x-4">
            <div className="px-3 py-1 rounded-full bg-[#0A0B0B]/80 backdrop-blur-sm">
              <span className="text-sm font-medium text-[#FEFEFF]">
                {strategyInfo?.name || "Strategy"}
              </span>
            </div>
            <div className="px-3 py-1 rounded-full bg-[#0A0B0B]/80 backdrop-blur-sm flex items-center space-x-2">
              <span className="text-sm font-medium text-[#FEFEFF]">
                {instrument}
              </span>
              <span className="text-xs text-[#EFBD3A]">
                {displayTimeframe || strategyInfo?.timeframe}
              </span>
            </div>
          </div>

          {/* Market Status and Trading Session Indicators */}
          <div className="flex items-center space-x-2">
            {/* Enhanced Trading Session Indicator */}
            {strategy?.strategy_json?.tradingSession && strategy.strategy_json.tradingSession.length > 0 && (() => {
              const sessionInfo = getComprehensiveTradingSessionInfo();

              // Create a more informative session text
              let sessionText = '';
              if (sessionInfo.inTradingSession) {
                if (sessionInfo.tradingSessions.includes('All')) {
                  // For "All" sessions, show currently active sessions
                  if (sessionInfo.activeSessions.length > 0) {
                    sessionText = sessionInfo.activeSessions.length > 1
                      ? `${sessionInfo.activeSessions.map(s => s.name).join(' + ')} Active`
                      : `${sessionInfo.activeSessions[0].name} Session`;
                  } else {
                    sessionText = 'In Trading Session';
                  }
                } else {
                  // For specific sessions, show which ones are active
                  const activeRelevantSessions = sessionInfo.relevantSessions.filter(s => s.isActive);
                  if (activeRelevantSessions.length > 0) {
                    sessionText = activeRelevantSessions.length > 1
                      ? `${activeRelevantSessions.map(s => s.name).join(' + ')} Active`
                      : `${activeRelevantSessions[0].name} Session`;
                  } else {
                    sessionText = 'In Trading Session';
                  }
                }
              } else {
                sessionText = 'Outside Session';
              }

              return (
                <div className={`flex items-center space-x-2 px-3 py-1 rounded-full backdrop-blur-sm ${sessionInfo.inTradingSession ? 'bg-green-900/30 border border-green-500/30' : 'bg-yellow-900/30 border border-yellow-500/30'}`}>
                  <div className={`w-2 h-2 rounded-full ${sessionInfo.inTradingSession ? 'bg-green-400 animate-pulse' : 'bg-yellow-400'}`}></div>
                  <span className={`text-xs font-medium ${sessionInfo.inTradingSession ? 'text-green-400' : 'text-yellow-400'}`}>
                    {sessionText}
                  </span>
                  {/* Market Activity Indicator - only show when in session */}
                  {sessionInfo.inTradingSession && (
                    <span className={`text-xs ${
                      sessionInfo.marketActivity === 'High' ? 'text-green-300' :
                      sessionInfo.marketActivity === 'Medium' ? 'text-yellow-300' : 'text-gray-400'
                    }`}>
                      • {sessionInfo.marketActivity}
                    </span>
                  )}
                </div>
              );
            })()}

            {/* Market Status Indicator */}
            {marketStatus && (
              <div className="flex items-center space-x-2 px-3 py-1 rounded-full bg-[#0A0B0B]/80 backdrop-blur-sm">
              <div
                className={`w-2 h-2 rounded-full ${
                  marketStatus.is_open ? "bg-[#EFBD3A]" : "bg-red-500"
                }`}
              />
              <span className="text-sm font-medium text-[#FEFEFF]">
                {marketStatus.is_open ? "Market Open" : "Market Closed"}
              </span>
              <span className="text-xs text-[#EFBD3A]">
                {(() => {
                  if (!marketStatus.last_updated) return "N/A";
                  if (marketStatus.last_updated.seconds) {
                    return new Date(
                      marketStatus.last_updated.seconds * 1000
                    ).toLocaleTimeString();
                  }
                  const timestamp = marketStatus.last_updated;
                  if (typeof timestamp === "number") {
                    return new Date(timestamp).toLocaleTimeString();
                  }
                  return "N/A";
                })()}
              </span>
            </div>
          )}
          </div>
        </div>
      )}

      {/* Loading Overlay - Render on top */}
      {isLoading && (
        <div className="absolute inset-0 bg-[#0A0B0B]/80 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="flex flex-col items-center space-y-4 bg-[#0A0B0B]/40 p-8 rounded-lg backdrop-blur-sm relative overflow-hidden">
            {/* Animated gradient background */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#0A0B0B]/0 via-[#EFBD3A]/10 to-[#0A0B0B]/0 -skew-x-12 animate-shimmer" />

            {/* Main spinner */}
            <div className="w-16 h-16 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin relative">
              {/* Inner spinner */}
              <div className="absolute inset-1 border-3 border-[#EFBD3A]/30 border-t-transparent rounded-full animate-spin-slow" />
            </div>

            {/* Loading text with animated dots */}
            <div className="flex flex-col items-center space-y-2">
              <span className="text-lg font-medium text-[#FEFEFF] relative">
                Loading chart data
                <span className="animate-ellipsis">.</span>
                <span className="animate-ellipsis animation-delay-300">.</span>
                <span className="animate-ellipsis animation-delay-600">.</span>
              </span>
              <span className="text-sm text-[#FEFEFF]/70 animate-pulse">
                {!arePriceLevelsLoaded
                  ? "Waiting for price levels..."
                  : "Finalizing..."}
              </span>
            </div>

            {/* Progress bar */}
            <div className="w-48 h-1 bg-[#1a1a1a] rounded-full overflow-hidden mt-4">
              <div className="h-full bg-[#EFBD3A] animate-progress rounded-full" />
            </div>
          </div>
        </div>
      )}

      {/* Main Price Chart Section */}
      <div className="relative w-full h-[600px] bg-[#0A0B0B]">
        <div
          ref={chartRef}
          className="w-full h-full min-h-[600px] bg-[#0A0B0B]"
          style={{
            opacity: isLoading ? 0.5 : 1,
            transition: "opacity 0.3s ease-in-out",
          }}
        />



        {/* Chart Legend for overlaid indicators - Always visible */}
        {(strategy?.strategy_json?.indicators?.filter(ind => ind.type === 'EMA' || ind.indicator_class === 'EMA').length > 0 ||
          strategy?.strategy_json?.indicators?.some(ind => ind.type === 'BollingerBands' || ind.indicator_class === 'BollingerBands') ||
          strategy?.strategy_json?.indicators?.some(ind => ind.type === 'SUPPORT_RESISTANCE' || ind.indicator_class === 'SUPPORT_RESISTANCE') ||
          strategy?.riskManagement?.stopLossMethod === 'indicator' && strategy?.riskManagement?.indicatorBasedSL?.indicator === 'support_resistance' ||
          // Show legend if support/resistance data is actually present in indicators
          (indicators && Object.keys(indicators).some(key => key.includes('_support') || key.includes('_resistance')))) && (
          <div className="absolute bottom-4 left-4 bg-[#0A0B0B]/90 backdrop-blur-sm rounded-lg p-3 z-50">
            <div className="text-xs font-medium text-[#FEFEFF] mb-2">Indicators</div>
            {/* Debug: Log what indicators are present */}
            {console.log('🔍 Legend Debug - Available indicators:', indicators ? Object.keys(indicators) : 'none')}
            <div className="space-y-1">
              {/* Show EMA indicators */}
              {strategy?.strategy_json?.indicators?.filter(ind => ind.type === 'EMA' || ind.indicator_class === 'EMA').map((emaIndicator, index) => {
                const period = emaIndicator.parameters?.period || emaIndicator.period || '?';
                // Use the same colors as defined in the chart creation
                const emaColors = ["#FF6B6B", "#4ECDC4", "#FFE66D", "#6A0572", "#FF9F1C"];
                const color = emaColors[index % emaColors.length];
                return (
                  <div key={`ema-legend-${index}`} className="flex items-center space-x-2">
                    <div className="w-3 h-0.5" style={{ backgroundColor: color }}></div>
                    <span className="text-xs text-[#FEFEFF]/70">EMA({period})</span>
                  </div>
                );
              })}

              {/* Show Bollinger Bands if present */}
              {strategy?.strategy_json?.indicators?.some(ind => ind.type === 'BollingerBands' || ind.indicator_class === 'BollingerBands') && (
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-0.5 bg-[#2196F3]"></div>
                  <span className="text-xs text-[#FEFEFF]/70">Bollinger Bands</span>
                </div>
              )}

              {/* Show Support & Resistance if present */}
              {(strategy?.strategy_json?.indicators?.some(ind => ind.type === 'SUPPORT_RESISTANCE' || ind.indicator_class === 'SUPPORT_RESISTANCE') ||
                strategy?.riskManagement?.stopLossMethod === 'indicator' && strategy?.riskManagement?.indicatorBasedSL?.indicator === 'support_resistance' ||
                // Show legend if support/resistance data is actually present in indicators
                (indicators && Object.keys(indicators).some(key => key.includes('_support') || key.includes('_resistance')))) && (
                <>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-[#4CAF50]"></div>
                    <span className="text-xs text-[#FEFEFF]/70">Support Levels</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 rounded-full bg-[#F44336]"></div>
                    <span className="text-xs text-[#FEFEFF]/70">Resistance Levels</span>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Tooltip */}
        {showTooltip && tooltipData && (
          <div
            className="absolute z-50 bg-[#131722] border-2 border-[#2a2e39] rounded-md shadow-lg p-3 pointer-events-none"
            style={{
              left: Math.min(tooltipPosition.x + 15, window.innerWidth - 220),
              top: Math.min(tooltipPosition.y - 20, window.innerHeight - 200),
              maxWidth: '220px',
              minWidth: '180px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
              transition: 'opacity 0.1s ease-in-out',
              opacity: 0.95
            }}
          >
            {/* Timestamp */}
            {tooltipData.time && (
              <div className="mb-2 text-center">
                <div className="text-[#787b86] text-xs">
                  {(() => {
                    // The tooltipData.time is in UTC seconds, convert to local time
                    // JavaScript Date constructor automatically handles timezone conversion
                    const date = new Date(tooltipData.time * 1000);

                    // Format as local time with timezone info
                    return date.toLocaleString(undefined, {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                      timeZoneName: 'short',
                      hour12: false
                    });
                  })()}
                </div>
              </div>
            )}

            {/* Price data */}
            {tooltipData.price && (
              <div className="mb-2">
                <div className="text-[#d1d4dc] text-xs font-medium mb-1">Price</div>
                {typeof tooltipData.price === 'object' ? (
                  <div className="grid grid-cols-2 gap-x-3 gap-y-1 text-xs">
                    <div className="text-[#787b86]">Open:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.open?.toFixed(5) || 'N/A'}</div>
                    <div className="text-[#787b86]">High:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.high?.toFixed(5) || 'N/A'}</div>
                    <div className="text-[#787b86]">Low:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.low?.toFixed(5) || 'N/A'}</div>
                    <div className="text-[#787b86]">Close:</div>
                    <div className="text-[#d1d4dc] font-medium">{tooltipData.price.close?.toFixed(5) || 'N/A'}</div>
                  </div>
                ) : (
                  <div className="text-[#d1d4dc] font-medium">{tooltipData.price?.toFixed(5) || 'N/A'}</div>
                )}
              </div>
            )}

            {/* Enhanced Indicators */}
            {tooltipData.indicators && tooltipData.indicators.length > 0 && (
              <div className="border-t border-[#2a2e39] pt-3 mt-3">
                <div className="text-[#e5e7eb] text-xs font-semibold mb-2 flex items-center">
                  <div className="w-2 h-2 bg-[#3b82f6] rounded-full mr-2"></div>
                  Indicators
                </div>
                <div className="space-y-2">
                  {tooltipData.indicators.map((indicator, index) => (
                    <div key={`${indicator.type}-${indicator.id}-${index}`} className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div
                          className="w-2 h-2 rounded-full mr-2"
                          style={{ backgroundColor: indicator.color }}
                        ></div>
                        <div className="text-[#e5e7eb] text-xs font-medium">
                          {indicator.displayName}
                        </div>
                      </div>
                      <div
                        className="font-semibold text-sm"
                        style={{ color: indicator.color }}
                      >
                        {indicator.type === 'RSI' || indicator.type === 'ATR'
                          ? indicator.value?.toFixed(2)
                          : indicator.value?.toFixed(5)
                        }
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Indicator Chart Tooltips */}
        {Object.entries(indicatorTooltips).map(([chartId, tooltip]) =>
          tooltip.visible && tooltip.indicators && tooltip.indicators.length > 0 && (
            <div
              key={chartId}
              className="absolute z-50 bg-[#131722] border border-[#2a2e39] rounded-lg shadow-xl p-3 pointer-events-none"
              style={{
                left: Math.min(tooltip.position.x + 15, window.innerWidth - 220),
                top: Math.max(tooltip.position.y - 10, 10),
                maxWidth: '220px',
                minWidth: '180px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.6)',
                backdropFilter: 'blur(8px)',
                background: 'linear-gradient(135deg, #131722 0%, #1a1e2e 100%)',
                border: '1px solid rgba(255, 255, 255, 0.1)'
              }}
            >
              {/* Timestamp */}
              {tooltip.time && (
                <div className="mb-3 text-center border-b border-[#2a2e39] pb-2">
                  <div className="text-[#9ca3af] text-xs font-medium">
                    {new Date(tooltip.time * 1000).toLocaleString(undefined, {
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                      timeZoneName: 'short'
                    })}
                  </div>
                </div>
              )}

              {/* Indicator Values */}
              <div className="space-y-2">
                {tooltip.indicators.map((indicator, index) => (
                  <div key={`${indicator.type}-${indicator.id}-${index}`} className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: indicator.color }}
                      ></div>
                      <div className="text-[#e5e7eb] text-xs font-medium">
                        {indicator.displayName}
                      </div>
                    </div>
                    <div
                      className="font-semibold text-sm"
                      style={{ color: indicator.color }}
                    >
                      {indicator.type === 'RSI' || indicator.type === 'ATR'
                        ? indicator.value?.toFixed(2)
                        : indicator.value?.toFixed(5)
                      }
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        )}
      </div>



      {/* MACD Indicator Chart Section */}
      {(Object.keys(indicators).some(key => key.includes('_macd'))) && (
        <>
          <div className="border-t border-[#1a1a1a]"></div>
          <div className="p-4">
            <div className="mb-3 flex items-center space-x-3">
              <span className="text-sm font-medium text-[#FEFEFF]">MACD</span>
              {(() => {
                // Get latest MACD values
                const macdKeys = Object.keys(indicators).filter(key => key.includes('_macd') && !key.includes('_signal') && !key.includes('_histogram'));
                const signalKeys = Object.keys(indicators).filter(key => key.includes('_signal'));
                const histogramKeys = Object.keys(indicators).filter(key => key.includes('_histogram'));

                const macdData = macdKeys.length > 0 && indicators[macdKeys[0]] ? indicators[macdKeys[0]] : null;
                const signalData = signalKeys.length > 0 && indicators[signalKeys[0]] ? indicators[signalKeys[0]] : null;
                const histogramData = histogramKeys.length > 0 && indicators[histogramKeys[0]] ? indicators[histogramKeys[0]] : null;

                const latestMacd = macdData && macdData.length > 0 ? macdData[macdData.length - 1]?.value : null;
                const latestSignal = signalData && signalData.length > 0 ? signalData[signalData.length - 1]?.value : null;
                const latestHistogram = histogramData && histogramData.length > 0 ? histogramData[histogramData.length - 1]?.value : null;

                return (
                  <>
                    {latestMacd !== null && (
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-[#FEFEFF]/70">MACD:</span>
                        <span className="px-2 py-1 text-xs font-medium rounded" style={{ backgroundColor: '#2196F3', color: 'white' }}>
                          {latestMacd.toFixed(6)}
                        </span>
                      </div>
                    )}
                    {latestSignal !== null && (
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-[#FEFEFF]/70">Signal:</span>
                        <span className="px-2 py-1 text-xs font-medium rounded" style={{ backgroundColor: '#FF9800', color: 'white' }}>
                          {latestSignal.toFixed(6)}
                        </span>
                      </div>
                    )}
                    {latestHistogram !== null && (
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-[#FEFEFF]/70">Histogram:</span>
                        <span className="px-2 py-1 text-xs font-medium rounded" style={{
                          backgroundColor: latestHistogram >= 0 ? '#4CAF50' : '#F44336',
                          color: 'white'
                        }}>
                          {latestHistogram.toFixed(6)}
                        </span>
                      </div>
                    )}
                  </>
                );
              })()}
            </div>
            <div className="relative w-full h-[200px]">
          <div
            ref={(el) => {
              if (el) {
                indicatorChartsRef.current['macd-chart'] = el;
              }
            }}
            className="w-full h-full bg-[#0A0B0B]"
            style={{
              opacity: isLoading ? 0.5 : 1,
              transition: "opacity 0.3s ease-in-out",
            }}
          />
            </div>
          </div>
        </>
      )}

      {/* RSI Indicator Charts Section */}
      {strategy?.strategy_json?.indicators?.filter(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI').length > 0 && (
        <div className="border-t border-[#1a1a1a]"></div>
      )}

      {strategy?.strategy_json?.indicators?.filter(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI').map((rsiIndicator, index) => {
        const period = rsiIndicator.parameters?.period ||
                      rsiIndicator.parameters?.length ||
                      rsiIndicator.parameters?.timeperiod ||
                      rsiIndicator.period ||
                      rsiIndicator.length ||
                      rsiIndicator.timeperiod;

        // Get latest RSI value
        const rsiData = indicators[rsiIndicator.id];
        const latestValue = rsiData && rsiData.length > 0 ? rsiData[rsiData.length - 1]?.value : null;

        return (
          <div key={`rsi-chart-${index}`} className="p-4">
            {/* Indicator Label Above Chart with Value */}
            <div className="mb-3 flex items-center space-x-3">
              <span className="text-sm font-medium text-[#FEFEFF]">
                RSI({period || '?'})
              </span>
              {latestValue !== null && (
                <span
                  className="px-2 py-1 text-xs font-medium rounded"
                  style={{
                    backgroundColor: index === 0 ? '#2962FF' : '#FF6B6B',
                    color: 'white'
                  }}
                >
                  {latestValue.toFixed(1)}
                </span>
              )}
            </div>

          <div className="relative w-full h-[200px]">
            <div
              ref={(el) => {
                if (el) {
                  indicatorChartsRef.current[`rsi-chart-${index}`] = el;
                }
              }}
              className="w-full h-full bg-[#0A0B0B]"
              style={{
                opacity: isLoading ? 0.5 : 1,
                transition: "opacity 0.3s ease-in-out",
              }}
            />
          </div>
        </div>
        );
      }) || []}

      {/* Fallback: Single RSI Chart for backward compatibility */}
      {indicators?.rsi?.length > 0 && (!strategy?.strategy_json?.indicators?.some(ind => ind.type === 'RSI' || ind.indicator_class === 'RSI')) && (
        <div className="relative w-full h-[200px] mt-1">
          <div
            ref={(el) => {
              if (el) {
                indicatorChartsRef.current['rsi-chart'] = el;
              }
            }}
            className="w-full h-full bg-[#0A0B0B]"
            style={{
              opacity: isLoading ? 0.5 : 1,
              transition: "opacity 0.3s ease-in-out",
            }}
          />
        </div>
      )}

      {/* ATR Indicator Chart Section */}
      {(Object.keys(indicators).some(key =>
        key.includes('atr') || key.includes('ATR') ||
        (strategy?.strategy_json?.indicators?.find(ind => ind.id === key &&
          (ind.type === 'ATR' || ind.indicator_class === 'ATR')))
      )) && (
        <>
          <div className="border-t border-[#1a1a1a]"></div>
          <div className="p-4">
            <div className="mb-3 flex items-center space-x-3">
              <span className="text-sm font-medium text-[#FEFEFF]">ATR</span>
              {(() => {
                // Get latest ATR values - be more specific to avoid duplicates
                const atrKeys = Object.keys(indicators).filter(key => {
                  // Only include keys that are actual ATR indicator IDs from the strategy
                  const isStrategyATR = strategy?.strategy_json?.indicators?.find(ind =>
                    ind.id === key && (ind.type === 'ATR' || ind.indicator_class === 'ATR')
                  );
                  // Or keys that contain 'atr' but are not duplicates
                  const isATRKey = (key.toLowerCase().includes('atr')) && indicators[key] && Array.isArray(indicators[key]);

                  return isStrategyATR || isATRKey;
                });

                // Remove duplicates by using a Set based on the indicator configuration
                const uniqueATRs = [];
                const seenPeriods = new Set();

                atrKeys.forEach(key => {
                  const atrData = indicators[key];
                  if (!atrData || atrData.length === 0) return;

                  // Get the latest ATR value
                  const latestATR = atrData[atrData.length - 1]?.value;
                  if (latestATR === null || latestATR === undefined) return;

                  // Get ATR period from strategy configuration
                  const atrIndicator = strategy?.strategy_json?.indicators?.find(ind =>
                    ind.id === key || (ind.type === 'ATR' || ind.indicator_class === 'ATR')
                  );
                  const period = atrIndicator?.parameters?.period || atrIndicator?.period || 14;

                  // Only add if we haven't seen this period before
                  const periodKey = `ATR(${period})`;
                  if (!seenPeriods.has(periodKey)) {
                    seenPeriods.add(periodKey);
                    uniqueATRs.push({
                      key,
                      period,
                      value: latestATR
                    });
                  }
                });

                return (
                  <>
                    {uniqueATRs.map((atr, index) => (
                      <div key={atr.key} className="flex items-center space-x-1">
                        <span className="text-xs text-[#FEFEFF]/70">ATR({atr.period}):</span>
                        <span className="px-2 py-1 text-xs font-medium rounded" style={{
                          backgroundColor: '#FF5722',
                          color: 'white'
                        }}>
                          {atr.value.toFixed(5)}
                        </span>
                      </div>
                    ))}
                  </>
                );
              })()}
            </div>
            <div className="relative w-full h-[200px]">
              <div
                ref={(el) => {
                  if (el) {
                    indicatorChartsRef.current['atr-chart'] = el;
                  }
                }}
                className="w-full h-full bg-[#0A0B0B]"
                style={{
                  opacity: isLoading ? 0.5 : 1,
                  transition: "opacity 0.3s ease-in-out",
                }}
              />
            </div>
          </div>
        </>
      )}

      <style jsx>{`
        @keyframes shimmer {
          0% {
            transform: translateX(-100%) skewX(-12deg);
          }
          100% {
            transform: translateX(200%) skewX(-12deg);
          }
        }
        @keyframes progress {
          0% {
            width: 0%;
          }
          50% {
            width: 70%;
          }
          100% {
            width: 98%;
          }
        }
        @keyframes ellipsis {
          0% {
            opacity: 0;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0;
          }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
        .animate-progress {
          animation: progress 3s ease-in-out infinite;
        }
        .animate-ellipsis {
          animation: ellipsis 1s infinite;
          opacity: 0;
        }
        .animation-delay-300 {
          animation-delay: 0.3s;
        }
        .animation-delay-600 {
          animation-delay: 0.6s;
        }
        .animate-spin-slow {
          animation: spin 2s linear infinite;
        }
      `}</style>
    </div>
  );
}
