import React, { useEffect, useRef, useState, useMemo, useCallback } from "react";
import {
  create<PERSON>hart,
  CrosshairMode,
  PriceScaleMode,
} from "lightweight-charts";

// Performance optimized chart component with zoom state preservation
const PerformanceOptimizedChart = ({
  candleData,
  indicators,
  trades,
  timeframe,
  chartTimeframe = "1h",
  instrument,
  marketStatus,
  strategyInfo,
  strategy,
  isRealTimeUpdate = false,
  preserveZoom = true,
  onZoomChange = null,
  initialZoomState = null,
  ...otherProps
}) => {
  // Chart references
  const chartRef = useRef();
  const chartInstanceRef = useRef();
  const candleSeriesRef = useRef();
  const isInitializedRef = useRef(false);
  const lastUpdateRef = useRef(0);
  const zoomStateRef = useRef(initialZoomState);
  
  // State
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Memoized chart options for better performance
  const chartOptions = useMemo(() => ({
    layout: {
      background: { color: '#0A0B0B' },
      textColor: '#FEFEFF',
    },
    grid: {
      vertLines: { color: '#1E222D' },
      horzLines: { color: '#1E222D' },
    },
    crosshair: {
      mode: CrosshairMode.Normal,
    },
    rightPriceScale: {
      borderColor: '#1E222D',
      scaleMargins: {
        top: 0.1,
        bottom: 0.1,
      },
      mode: PriceScaleMode.Normal,
      autoScale: true,
      alignLabels: true,
      borderVisible: false,
    },
    timeScale: {
      borderColor: '#1E222D',
      timeVisible: true,
      secondsVisible: false,
      fixRightEdge: false,
      fixLeftEdge: false,
      rightBarStaysOnScroll: false,
      lockVisibleTimeRangeOnResize: false,
      rightOffset: 12,
    },
    handleScroll: {
      mouseWheel: true,
      pressedMouseMove: true,
      horzTouchDrag: true,
      vertTouchDrag: true,
    },
    handleScale: {
      axisPressedMouseMove: true,
      mouseWheel: true,
      pinch: true,
    },
  }), []);

  // Memoized price format
  const priceFormat = useMemo(() => {
    const digits = instrument?.includes('JPY') ? 3 : 5;
    return {
      type: 'price',
      precision: digits,
      minMove: 1 / Math.pow(10, digits),
    };
  }, [instrument]);

  // Initialize chart
  const initializeChart = useCallback(() => {
    if (!chartRef.current || isInitializedRef.current) return;

    try {
      const chart = createChart(chartRef.current, {
        ...chartOptions,
        width: chartRef.current.clientWidth,
        height: chartRef.current.clientHeight,
        rightPriceScale: {
          ...chartOptions.rightPriceScale,
          priceFormat,
        },
      });

      chartInstanceRef.current = chart;
      
      // Create candlestick series
      const candleSeries = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
      });

      candleSeriesRef.current = candleSeries;

      // Store chart instance globally for zoom state access
      window.chartInstance = chart;

      // Handle zoom changes
      if (preserveZoom) {
        chart.timeScale().subscribeVisibleLogicalRangeChange((newRange) => {
          if (newRange) {
            zoomStateRef.current = {
              from: newRange.from,
              to: newRange.to,
            };
            
            if (onZoomChange) {
              onZoomChange(zoomStateRef.current);
            }
          }
        });
      }

      isInitializedRef.current = true;
      setError(null);
      
      console.log('📊 Chart initialized successfully');
    } catch (err) {
      console.error('❌ Chart initialization error:', err);
      setError('Failed to initialize chart');
    }
  }, [chartOptions, priceFormat, preserveZoom, onZoomChange]);

  // Update chart data
  const updateChartData = useCallback(() => {
    if (!chartInstanceRef.current || !candleSeriesRef.current || !candleData?.length) {
      return;
    }

    try {
      // Transform candle data
      const transformedData = candleData.map(candle => ({
        time: candle.time,
        open: parseFloat(candle.open),
        high: parseFloat(candle.high),
        low: parseFloat(candle.low),
        close: parseFloat(candle.close),
      }));

      // Update candle series
      candleSeriesRef.current.setData(transformedData);

      // Restore zoom state if available
      if (preserveZoom && zoomStateRef.current) {
        const timeScale = chartInstanceRef.current.timeScale();
        timeScale.setVisibleLogicalRange({
          from: zoomStateRef.current.from,
          to: zoomStateRef.current.to,
        });
      }

      setIsLoading(false);
      console.log(`📊 Chart updated with ${transformedData.length} candles`);
    } catch (err) {
      console.error('❌ Chart update error:', err);
      setError('Failed to update chart data');
    }
  }, [candleData, preserveZoom]);

  // Debounced update function
  const debouncedUpdate = useCallback(() => {
    const now = Date.now();
    const debounceTime = isRealTimeUpdate ? 100 : 500;
    
    if (now - lastUpdateRef.current < debounceTime) {
      return;
    }
    
    lastUpdateRef.current = now;
    updateChartData();
  }, [updateChartData, isRealTimeUpdate]);

  // Handle resize
  const handleResize = useCallback(() => {
    if (chartInstanceRef.current && chartRef.current) {
      chartInstanceRef.current.applyOptions({
        width: chartRef.current.clientWidth,
        height: chartRef.current.clientHeight,
      });
    }
  }, []);

  // Initialize chart on mount
  useEffect(() => {
    initializeChart();
    
    // Add resize listener
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      
      // Cleanup chart
      if (chartInstanceRef.current) {
        chartInstanceRef.current.remove();
        chartInstanceRef.current = null;
      }
      
      isInitializedRef.current = false;
      
      // Clear global reference
      if (window.chartInstance === chartInstanceRef.current) {
        window.chartInstance = null;
      }
    };
  }, [initializeChart, handleResize]);

  // Update data when candleData changes
  useEffect(() => {
    if (isInitializedRef.current && candleData?.length) {
      debouncedUpdate();
    }
  }, [candleData, debouncedUpdate]);

  // Set initial zoom state
  useEffect(() => {
    if (initialZoomState) {
      zoomStateRef.current = initialZoomState;
    }
  }, [initialZoomState]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-[#0A0B0B] text-red-400">
        <div className="text-center">
          <div className="text-2xl mb-2">⚠️</div>
          <div>{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-[#0A0B0B]/80 z-10">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 border-2 border-[#EFBD3A] border-t-transparent rounded-full animate-spin mb-2"></div>
            <div className="text-[#EFBD3A] text-sm">Loading chart...</div>
          </div>
        </div>
      )}
      
      <div
        ref={chartRef}
        className="w-full h-full"
        style={{
          minHeight: '400px',
          backgroundColor: '#0A0B0B',
        }}
      />
    </div>
  );
};

export default PerformanceOptimizedChart;
