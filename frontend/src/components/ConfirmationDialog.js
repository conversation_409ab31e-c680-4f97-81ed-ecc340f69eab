import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, Clock, Square, Pause } from 'lucide-react';

const ConfirmationDialog = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title, 
  message, 
  confirmText = "Confirm", 
  cancelText = "Cancel",
  type = "default", // "pause", "stop", "default"
  loading = false 
}) => {
  const getTypeConfig = (type) => {
    switch (type) {
      case 'pause':
        return {
          icon: <Pause className="w-6 h-6" />,
          iconBg: 'bg-yellow-500/20',
          iconColor: 'text-yellow-400',
          confirmBg: 'bg-yellow-600 hover:bg-yellow-700',
          borderColor: 'border-yellow-500/30'
        };
      case 'stop':
        return {
          icon: <Square className="w-6 h-6" />,
          iconBg: 'bg-red-500/20',
          iconColor: 'text-red-400',
          confirmBg: 'bg-red-600 hover:bg-red-700',
          borderColor: 'border-red-500/30'
        };
      default:
        return {
          icon: <AlertTriangle className="w-6 h-6" />,
          iconBg: 'bg-blue-500/20',
          iconColor: 'text-blue-400',
          confirmBg: 'bg-blue-600 hover:bg-blue-700',
          borderColor: 'border-blue-500/30'
        };
    }
  };

  const config = getTypeConfig(type);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={loading ? undefined : onClose}
        />

        {/* Dialog */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className={`relative bg-[#0F1011] border ${config.borderColor} rounded-xl p-6 max-w-md w-full mx-4 shadow-2xl`}
        >
          {/* Header */}
          <div className="flex items-center space-x-3 mb-4">
            <div className={`p-2 rounded-lg ${config.iconBg}`}>
              <div className={config.iconColor}>
                {config.icon}
              </div>
            </div>
            <h3 className="text-lg font-semibold text-white">
              {title}
            </h3>
          </div>

          {/* Message */}
          <div className="mb-6">
            {typeof message === 'string' ? (
              <p className="text-[#FEFEFF]/70 leading-relaxed">
                {message}
              </p>
            ) : (
              message
            )}
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              disabled={loading}
              className="flex-1 px-4 py-2 bg-[#1a1a1a] hover:bg-[#2a2a2a] text-[#FEFEFF]/70 rounded-lg transition-colors disabled:opacity-50"
            >
              {cancelText}
            </button>
            <button
              onClick={() => {
                console.log("Confirm button clicked, loading:", loading);
                onConfirm();
              }}
              disabled={loading}
              className={`flex-1 px-4 py-2 ${config.confirmBg} text-white rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center`}
            >
              {loading ? (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              ) : (
                confirmText
              )}
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

// Specific dialog components for common use cases
export const PauseConfirmationDialog = ({ isOpen, onClose, onConfirm, loading = false }) => (
  <ConfirmationDialog
    isOpen={isOpen}
    onClose={onClose}
    onConfirm={onConfirm}
    loading={loading}
    type="pause"
    title="Pause Trading Agent"
    message={
      <div className="space-y-3">
        <p className="text-[#FEFEFF]/70">
          Are you sure you want to pause this trading agent?
        </p>
        <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <Clock className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-yellow-300 font-medium mb-1">
                24-Hour Auto-Stop Warning
              </p>
              <p className="text-yellow-200/80">
                The agent will automatically stop and close all positions after 24 hours if not resumed. This helps save resources.
              </p>
            </div>
          </div>
        </div>
        <p className="text-[#FEFEFF]/60 text-sm">
          All open positions will be closed immediately when paused.
        </p>
      </div>
    }
    confirmText="Pause Agent"
    cancelText="Cancel"
  />
);

export const StopConfirmationDialog = ({ isOpen, onClose, onConfirm, loading = false }) => (
  <ConfirmationDialog
    isOpen={isOpen}
    onClose={onClose}
    onConfirm={onConfirm}
    loading={loading}
    type="stop"
    title="Stop Trading Agent"
    message={
      <div className="space-y-3">
        <p className="text-[#FEFEFF]/70">
          Are you sure you want to permanently stop this trading agent?
        </p>
        <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p className="text-red-300 font-medium mb-1">
                Permanent Action
              </p>
              <p className="text-red-200/80">
                This action cannot be undone. All open positions will be closed and the agent will be permanently deleted.
              </p>
            </div>
          </div>
        </div>
        <p className="text-[#FEFEFF]/60 text-sm">
          You'll need to deploy a new agent to resume trading with this strategy.
        </p>
      </div>
    }
    confirmText="Stop Agent"
    cancelText="Cancel"
  />
);

export default ConfirmationDialog;
