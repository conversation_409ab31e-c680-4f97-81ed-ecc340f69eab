import { useEffect, useRef, useState, useCallback } from 'react';
import { createChart } from 'lightweight-charts';
import { calculateSMA, calculateEMA, calculateSupportResistance } from '../utils/indicatorCalculations';

const StrategyChart = ({
  forexPair,
  timeframe,
  userTimezone = 'UTC',
  selectedTradingSessions = [],
  data = null,
  isLoading = false,
  error = null,
  indicators = [],
  onIndicatorAdd = null,
  trades = []
}) => {
  console.log('StrategyChart render', {
    hasData: !!data?.length,
    dataLength: data?.length,
    isLoading,
    error,
    indicatorsLength: indicators.length,
    tradesLength: trades?.length,
    hasTrades: !!trades?.length
  });
  // Refs for chart instances
  const mainChartRef = useRef(null);
  const mainChartInstanceRef = useRef(null);
  const candlestickSeriesRef = useRef(null);
  const volumeSeriesRef = useRef(null);
  const indicatorSeriesRef = useRef({});
  const indicatorChartsRef = useRef({});
  const resizeObserverRef = useRef(null);
  const mainChartSeriesRef = useRef({});
  const isDisposedRef = useRef(false);

  // State
  const [showVolume, setShowVolume] = useState(false);
  const [indicatorGroups, setIndicatorGroups] = useState({});
  const [mainChartIndicators, setMainChartIndicators] = useState({});
  const [isCreatingChart, setIsCreatingChart] = useState(false);
  const [showPreferenceDialog, setShowPreferenceDialog] = useState(false);
  const [pendingIndicator, setPendingIndicator] = useState(null);
  const [indicatorDisplayPrefs, setIndicatorDisplayPrefs] = useState({});
  const [mainChartSeries, setMainChartSeries] = useState({}); // Track series for cleanup
  const [indicatorsReady, setIndicatorsReady] = useState(false); // Track if indicators are processed

  // Group indicators by type and display preference
  const groupIndicators = useCallback(() => {
    console.log('🔄 Grouping indicators:', {
      totalIndicators: indicators.length,
      indicatorTypes: indicators.map(i => i.type),
      indicatorIds: indicators.map(i => i.id)
    });

    const groups = {};
    const mainChart = {
      SMA: [],
      EMA: [],
      BollingerBands: [],
      SupportResistance: []
    };

    indicators.forEach(indicator => {
      console.log(`Processing indicator: ${indicator.type} (ID: ${indicator.id})`);

      if (['SMA', 'EMA', 'BollingerBands', 'SupportResistance'].includes(indicator.type)) {
        mainChart[indicator.type].push(indicator);
        console.log(`Added ${indicator.type} to main chart`);
      } else {
        // For other indicators (RSI, MACD, ATR), check display preferences
        const pref = indicatorDisplayPrefs[indicator.id];
        console.log(`Indicator ${indicator.type} preference:`, pref);

        if (pref === 'existing') {
          // Find existing group of same type
          let existingGroupId = null;
          for (const [groupId, groupIndicators] of Object.entries(groups)) {
            if (groupIndicators.some(ind => ind.type === indicator.type)) {
              existingGroupId = groupId;
              break;
            }
          }

          if (existingGroupId) {
            groups[existingGroupId].push(indicator);
            console.log(`Added ${indicator.type} to existing group ${existingGroupId}`);
          } else {
            // No existing group, create new one
            const newGroupId = `${indicator.type}-${Date.now()}`;
            groups[newGroupId] = [indicator];
            console.log(`Created new group ${newGroupId} for ${indicator.type} (no existing found)`);
          }
        } else {
          // Create new group (default or 'new' preference)
          const newGroupId = `${indicator.type}-${indicator.id}`;
          groups[newGroupId] = [indicator];
          console.log(`Created new group ${newGroupId} for ${indicator.type}`);
        }
      }
    });

    console.log('📊 Final grouping result:', {
      mainChartIndicators: {
        SMA: mainChart.SMA.length,
        EMA: mainChart.EMA.length,
        BollingerBands: mainChart.BollingerBands.length,
        SupportResistance: mainChart.SupportResistance.length
      },
      indicatorGroups: Object.keys(groups).length,
      groupDetails: Object.entries(groups).map(([id, indicators]) => ({
        id,
        type: indicators[0]?.type,
        count: indicators.length
      }))
    });

    setMainChartIndicators(mainChart);
    setIndicatorGroups(groups);

    // Mark indicators as ready after processing
    setIndicatorsReady(true);
    console.log('✅ Indicators processed and ready for chart creation');
  }, [indicators, indicatorDisplayPrefs]);

  // Check if there are existing windows for an indicator type
  const hasExistingWindows = useCallback((indicatorType) => {
    return Object.values(indicatorGroups).some(group =>
      group.some(indicator => indicator.type === indicatorType)
    );
  }, [indicatorGroups]);

  // Handle indicator preference selection
  const handleIndicatorPreference = useCallback((indicator, preference) => {
    setIndicatorDisplayPrefs(prev => ({
      ...prev,
      [indicator.id]: preference
    }));

    setShowPreferenceDialog(false);
    setPendingIndicator(null);

    if (onIndicatorAdd) {
      onIndicatorAdd(true);
    }
  }, [onIndicatorAdd]);

  // Watch for new indicators and show preference dialog if needed
  useEffect(() => {
    const newIndicators = indicators.filter(indicator =>
      !indicatorDisplayPrefs[indicator.id] &&
      ['RSI', 'MACD', 'ATR'].includes(indicator.type)
    );

    if (newIndicators.length > 0) {
      const newIndicator = newIndicators[0];

      // Check if there are ALREADY EXISTING indicators of the same type
      // (not including the current new indicator)
      const existingIndicatorsOfSameType = indicators.filter(ind =>
        ind.type === newIndicator.type &&
        ind.id !== newIndicator.id &&
        indicatorDisplayPrefs[ind.id] // Already processed
      );

      console.log(`New ${newIndicator.type} indicator detected`, {
        existingCount: existingIndicatorsOfSameType.length,
        shouldShowDialog: existingIndicatorsOfSameType.length > 0
      });

      if (existingIndicatorsOfSameType.length > 0) {
        // There are existing indicators of the same type, show dialog
        setPendingIndicator(newIndicator);
        setShowPreferenceDialog(true);
      } else {
        // No existing indicators of this type, default to new window
        setIndicatorDisplayPrefs(prev => ({
          ...prev,
          [newIndicator.id]: 'new'
        }));
        console.log(`Auto-assigned 'new' preference for first ${newIndicator.type} indicator`);
      }
    }
  }, [indicators, indicatorDisplayPrefs]);

  // Helper function to calculate RSI
  const calculateRSI = useCallback((data, period = 14) => {
    if (!data || data.length < period + 1) return [];

    const gains = [];
    const losses = [];

    for (let i = 1; i < data.length; i++) {
      const change = data[i].close - data[i - 1].close;
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    const result = [];
    let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period;
    let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period;

    for (let i = period; i < data.length; i++) {
      if (avgLoss === 0) {
        result.push({ time: data[i].time, value: 100 });
      } else {
        const rs = avgGain / avgLoss;
        const rsi = 100 - (100 / (1 + rs));
        result.push({ time: data[i].time, value: rsi });
      }

      if (i < gains.length) {
        avgGain = (avgGain * (period - 1) + gains[i]) / period;
        avgLoss = (avgLoss * (period - 1) + losses[i]) / period;
      }
    }

    return result;
  }, []);

  // Helper function to calculate MACD
  const calculateMACD = useCallback((data, params = { fast: 12, slow: 26, signal: 9 }) => {
    if (!data || data.length < params.slow + params.signal) {
      console.warn('Insufficient data for MACD calculation', {
        dataLength: data?.length,
        requiredLength: params.slow + params.signal
      });
      return null;
    }

    const { fast, slow, signal } = params;
    console.log('MACD calculation with params:', { fast, slow, signal, dataLength: data.length });

    // Calculate EMAs
    const calculateEMA = (data, period) => {
      const multiplier = 2 / (period + 1);
      let ema = data[0].close;
      const result = [{ time: data[0].time, value: ema }];

      for (let i = 1; i < data.length; i++) {
        ema = (data[i].close - ema) * multiplier + ema;
        result.push({ time: data[i].time, value: ema });
      }

      return result;
    };

    const fastEMA = calculateEMA(data, fast);
    const slowEMA = calculateEMA(data, slow);

    // Calculate MACD line
    const macdLine = [];
    for (let i = slow - 1; i < data.length; i++) {
      if (fastEMA[i] && slowEMA[i]) {
        macdLine.push({
          time: data[i].time,
          value: fastEMA[i].value - slowEMA[i].value
        });
      }
    }

    if (macdLine.length === 0) {
      console.warn('No MACD data calculated');
      return null;
    }

    // Calculate Signal line (EMA of MACD)
    const signalLine = [];
    if (macdLine.length >= signal) {
      const multiplier = 2 / (signal + 1);
      let ema = macdLine[0].value;
      signalLine.push({ time: macdLine[0].time, value: ema });

      for (let i = 1; i < macdLine.length; i++) {
        ema = (macdLine[i].value - ema) * multiplier + ema;
        signalLine.push({ time: macdLine[i].time, value: ema });
      }
    }

    // Calculate Histogram (MACD - Signal)
    const histogram = [];
    const minLength = Math.min(macdLine.length, signalLine.length);

    for (let i = 0; i < minLength; i++) {
      if (macdLine[i] && signalLine[i] && macdLine[i].time === signalLine[i].time) {
        histogram.push({
          time: macdLine[i].time,
          value: macdLine[i].value - signalLine[i].value
        });
      }
    }

    const result = {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram
    };

    console.log('MACD calculation complete:', {
      macdLength: macdLine.length,
      signalLength: signalLine.length,
      histogramLength: histogram.length,
      sampleMacd: macdLine.slice(0, 3),
      sampleSignal: signalLine.slice(0, 3),
      sampleHistogram: histogram.slice(0, 3)
    });

    return result;
  }, []);

  // Helper function to calculate ATR
  const calculateATR = useCallback((data, period = 14) => {
    if (!data || data.length < period + 1) return [];

    const trueRanges = [];

    for (let i = 1; i < data.length; i++) {
      const high = data[i].high;
      const low = data[i].low;
      const prevClose = data[i - 1].close;

      const tr1 = high - low;
      const tr2 = Math.abs(high - prevClose);
      const tr3 = Math.abs(low - prevClose);

      trueRanges.push(Math.max(tr1, tr2, tr3));
    }

    const result = [];
    let atr = trueRanges.slice(0, period).reduce((sum, tr) => sum + tr, 0) / period;

    for (let i = period; i < data.length; i++) {
      result.push({ time: data[i].time, value: atr });

      if (i < trueRanges.length) {
        atr = (atr * (period - 1) + trueRanges[i]) / period;
      }
    }

    return result;
  }, []);

  // Helper function to calculate Bollinger Bands
  const calculateBollingerBands = useCallback((data, period = 20, devfactor = 2) => {
    if (!data || data.length < period) return [];

    const result = [];

    for (let i = period - 1; i < data.length; i++) {
      // Calculate SMA for the period
      const slice = data.slice(i - period + 1, i + 1);
      const sma = slice.reduce((sum, candle) => sum + candle.close, 0) / period;

      // Calculate standard deviation
      const variance = slice.reduce((sum, candle) => {
        const diff = candle.close - sma;
        return sum + (diff * diff);
      }, 0) / period;

      const stdDev = Math.sqrt(variance);

      result.push({
        time: data[i].time,
        upper: sma + (stdDev * devfactor),
        middle: sma,
        lower: sma - (stdDev * devfactor)
      });
    }

    return result;
  }, []);

  // Create indicator chart
  const createIndicatorChart = useCallback((groupId, height = 200, type) => {
    console.log(`createIndicatorChart called for ${groupId}`, {
      hasContainer: !!indicatorChartsRef.current[groupId],
      containerKeys: Object.keys(indicatorChartsRef.current)
    });

    const container = indicatorChartsRef.current[groupId];
    if (!container) {
      console.warn(`No container found for group ${groupId}`);
      return null;
    }

    console.log(`Container found for ${groupId}, creating chart...`);

    // Remove existing chart
    if (container.chartInstance) {
      try {
        container.chartInstance.remove();
      } catch (error) {
        console.warn('Error removing indicator chart:', error);
      }
    }

    const chart = createChart(container, {
      width: container.clientWidth,
      height: height,
      layout: {
        background: { color: '#0A0B0B' },
        textColor: '#FEFEFF',
      },
      grid: {
        vertLines: { color: '#1E222D' },
        horzLines: { color: '#1E222D' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#1E222D',
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
      },
      timeScale: {
        borderColor: '#1E222D',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    container.chartInstance = chart;
    console.log(`Chart instance created and stored for ${groupId}`);
    return chart;
  }, []);

  // Create main chart
  const createMainChart = useCallback(() => {
    console.log('createMainChart called', {
      hasRef: !!mainChartRef.current,
      hasData: !!data?.length,
      isCreating: isCreatingChart,
      isDisposed: isDisposedRef.current
    });

    if (!mainChartRef.current || !data?.length || isCreatingChart || isDisposedRef.current) return;

    setIsCreatingChart(true);

    try {
      // Remove existing chart safely
      if (mainChartInstanceRef.current) {
        try {
          mainChartInstanceRef.current.remove();
        } catch (error) {
          console.warn('Error removing existing chart:', error);
        }
        mainChartInstanceRef.current = null;
        candlestickSeriesRef.current = null;
        volumeSeriesRef.current = null;
      }

    const chart = createChart(mainChartRef.current, {
      width: mainChartRef.current.clientWidth,
      height: 400,
      layout: {
        background: { color: '#0A0B0B' },
        textColor: '#FEFEFF',
      },
      grid: {
        vertLines: { color: '#1E222D' },
        horzLines: { color: '#1E222D' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#1E222D',
        scaleMargins: {
          top: 0.1,
          bottom: 0.1,
        },
      },
      timeScale: {
        borderColor: '#1E222D',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    mainChartInstanceRef.current = chart;
    console.log('Main chart created successfully');

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
      priceFormat: {
        type: 'price',
        precision: 5,
        minMove: 0.00001,
      },
    });

    candlestickSeriesRef.current = candlestickSeries;
    candlestickSeries.setData(data);
    console.log('Candlestick data set, data length:', data.length);

    // Add volume series if needed
    if (showVolume) {
      const volumeSeries = chart.addHistogramSeries({
        color: '#26a69a',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: '',
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      });

      const volumeData = data.map(d => ({
        time: d.time,
        value: d.volume,
        color: d.close >= d.open ? '#26a69a55' : '#ef535055'
      }));

      volumeSeries.setData(volumeData);
      volumeSeriesRef.current = volumeSeries;
    }

    // Add trade markers
    console.log('🎯 Trade markers processing:', {
      isArray: Array.isArray(trades),
      tradesLength: trades?.length,
      hasData: !!data?.length,
      sampleTrade: trades?.[0]
    });

    // DETAILED DEBUG: Log all trade data
    if (Array.isArray(trades) && trades.length > 0) {
      console.log('🔍 DETAILED TRADE DEBUG:');
      console.log('All trades:', trades);
      trades.slice(0, 3).forEach((trade, idx) => {
        console.log(`Trade ${idx} detailed:`, {
          raw_trade: trade,
          has_entry_time: !!trade.entry_time,
          has_exit_time: !!trade.exit_time,
          entry_time_type: typeof trade.entry_time,
          exit_time_type: typeof trade.exit_time,
          entry_time_value: trade.entry_time,
          exit_time_value: trade.exit_time,
          has_entry_price: !!trade.entry_price,
          has_exit_price: !!trade.exit_price,
          entry_price: trade.entry_price,
          exit_price: trade.exit_price,
          type: trade.type,
          trade_type: trade.trade_type,
          net_pnl: trade.net_pnl
        });
      });
    }

    if (Array.isArray(trades) && trades.length > 0) {
      console.log('✅ Processing trade markers for', trades.length, 'trades');
      const markers = [];

      // Show ALL trades - no limits
      console.log(`📊 Showing all ${trades.length} trades`);

      trades.forEach((trade, idx) => {
        try {
          // Parse trade times - handle both Date objects and ISO strings correctly
          let entryTime = null;
          let exitTime = null;

          if (trade.entry_time) {
            if (trade.entry_time instanceof Date) {
              // If it's already a Date object, convert to UTC timestamp
              entryTime = Math.floor(trade.entry_time.getTime() / 1000);
            } else if (typeof trade.entry_time === 'string') {
              // If it's an ISO string from backend, parse it as UTC (backend sends UTC times)
              entryTime = Math.floor(new Date(trade.entry_time + 'Z').getTime() / 1000);
            }
          }

          if (trade.exit_time) {
            if (trade.exit_time instanceof Date) {
              // If it's already a Date object, convert to UTC timestamp
              exitTime = Math.floor(trade.exit_time.getTime() / 1000);
            } else if (typeof trade.exit_time === 'string') {
              // If it's an ISO string from backend, parse it as UTC (backend sends UTC times)
              exitTime = Math.floor(new Date(trade.exit_time + 'Z').getTime() / 1000);
            }
          }

          console.log(`Trade ${idx}:`, {
            entry_time: trade.entry_time,
            exit_time: trade.exit_time,
            entryTime,
            exitTime,
            entry_price: trade.entry_price,
            exit_price: trade.exit_price,
            type: trade.type,
            net_pnl: trade.net_pnl
          });

          const isLong = trade.type === 'long';
          const isWin = trade.net_pnl >= 0;
          const entryColor = isLong ? '#2E7D32' : '#C62828'; // Professional darker colors
          const exitColor = isWin ? '#1B5E20' : '#B71C1C';   // Even darker for exits

          if (entryTime && trade.entry_price) {
            markers.push({
              time: entryTime,
              position: isLong ? 'belowBar' : 'aboveBar',
              color: entryColor,
              shape: isLong ? 'arrowUp' : 'arrowDown',
              text: `${isLong ? 'LONG' : 'SHORT'}\n${trade.entry_price.toFixed(5)}`,
              id: `entry-${idx}`,
              size: 1.2
            });
            console.log(`✅ Added entry marker for trade ${idx}`);
          }

          if (exitTime && trade.exit_price) {
            const pnlText = trade.net_pnl >= 0 ? `+$${trade.net_pnl.toFixed(2)}` : `$${trade.net_pnl.toFixed(2)}`;
            markers.push({
              time: exitTime,
              position: isLong ? 'aboveBar' : 'belowBar',
              color: exitColor,
              shape: 'square',
              text: `EXIT\n${trade.exit_price.toFixed(5)}\n${pnlText}`,
              id: `exit-${idx}`,
              size: 1.2
            });
            console.log(`✅ Added exit marker for trade ${idx}`);
          }
        } catch (error) {
          console.error(`❌ Error processing trade ${idx}:`, error);
        }
      });

      console.log('📊 Total markers created:', markers.length);
      if (markers.length > 0) {
        candlestickSeries.setMarkers(markers);
        console.log('✅ Trade markers set on candlestick series');
      } else {
        console.log('⚠️ No markers to set');
      }
    } else {
      console.log('❌ Trade markers not processed:', {
        isArray: Array.isArray(trades),
        length: trades?.length,
        hasData: !!data?.length
      });
    }

    // Add main chart indicators (SMA, EMA, Bollinger Bands, Support & Resistance)

    // Clear existing main chart series for cleanup
    const currentSeries = {};

    // Add SMA indicators
    if (mainChartIndicators.SMA && Array.isArray(mainChartIndicators.SMA)) {
      mainChartIndicators.SMA.forEach((indicator, index) => {
        if (!indicator || !indicator.parameters) return;

        try {
          const smaData = calculateSMA(
            data,
            indicator.parameters.period || 14,
            indicator.parameters.source || 'close'
          );

          if (smaData && smaData.length > 0) {
            console.log(`Adding SMA(${indicator.parameters.period || 14}) with ${smaData.length} data points`);

            const smaSeries = chart.addLineSeries({
              color: indicator.parameters.color || '#FF6B35', // Orange color for SMA
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              title: `SMA(${indicator.parameters.period || 14})`,
              priceLineVisible: false,
              lastValueVisible: false,
            });

            smaSeries.setData(smaData);
            currentSeries[`SMA-${indicator.id}`] = smaSeries;
            console.log(`SMA(${indicator.parameters.period || 14}) added successfully`);
          }
        } catch (error) {
          console.error('Error adding SMA:', error);
        }
      });
    }

    // Add EMA indicators
    if (mainChartIndicators.EMA && Array.isArray(mainChartIndicators.EMA)) {
      mainChartIndicators.EMA.forEach((indicator, index) => {
        if (!indicator || !indicator.parameters) return;

        try {
          const emaData = calculateEMA(
            data,
            indicator.parameters.period || 14,
            indicator.parameters.source || 'close'
          );

          if (emaData && emaData.length > 0) {
            console.log(`Adding EMA(${indicator.parameters.period || 14}) with ${emaData.length} data points`);

            const emaSeries = chart.addLineSeries({
              color: indicator.parameters.color || '#00D2FF', // Cyan color for EMA
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              title: `EMA(${indicator.parameters.period || 14})`,
              priceLineVisible: false,
              lastValueVisible: false,
            });

            emaSeries.setData(emaData);
            currentSeries[`EMA-${indicator.id}`] = emaSeries;
            console.log(`EMA(${indicator.parameters.period || 14}) added successfully`);
          }
        } catch (error) {
          console.error('Error adding EMA:', error);
        }
      });
    }

    // Add Bollinger Bands
    if (mainChartIndicators.BollingerBands && Array.isArray(mainChartIndicators.BollingerBands)) {
      mainChartIndicators.BollingerBands.forEach((indicator) => {
        if (!indicator || !indicator.parameters) return;

        try {
          const bbData = calculateBollingerBands(
            data,
            indicator.parameters.period || 20,
            indicator.parameters.devfactor || 2
          );

          if (bbData && bbData.length > 0) {
            console.log(`Adding Bollinger Bands with ${bbData.length} data points`);

            // Create middle band (SMA)
            const middleSeries = chart.addLineSeries({
              color: '#2962FF',
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              title: '',
              priceLineVisible: false,
              lastValueVisible: false,
            });

            // Create upper band
            const upperSeries = chart.addLineSeries({
              color: '#2962FF',
              lineWidth: 1,
              lineStyle: 2, // Dashed line
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              priceLineVisible: false,
              lastValueVisible: false,
            });

            // Create lower band
            const lowerSeries = chart.addLineSeries({
              color: '#2962FF',
              lineWidth: 1,
              lineStyle: 2, // Dashed line
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
              priceLineVisible: false,
              lastValueVisible: false,
            });

            // Set data for all bands
            middleSeries.setData(bbData.map(d => ({ time: d.time, value: d.middle })));
            upperSeries.setData(bbData.map(d => ({ time: d.time, value: d.upper })));
            lowerSeries.setData(bbData.map(d => ({ time: d.time, value: d.lower })));

            // Store series for cleanup
            currentSeries[`BB-middle-${indicator.id}`] = middleSeries;
            currentSeries[`BB-upper-${indicator.id}`] = upperSeries;
            currentSeries[`BB-lower-${indicator.id}`] = lowerSeries;

            console.log('Bollinger Bands added successfully');
          }
        } catch (error) {
          console.error('Error adding Bollinger Bands:', error);
        }
      });
    }

    // Add Support & Resistance indicators - DEBUGGING VERSION
    if (mainChartIndicators.SupportResistance && Array.isArray(mainChartIndicators.SupportResistance)) {
      console.log('🔍 Adding Support & Resistance indicators (debugging mode)');
      console.log('🔍 S&R indicators array:', mainChartIndicators.SupportResistance);

      // Temporarily disable safety lock for debugging
      // if (window.isCreatingSRLines) {
      //   console.log('🚫 S&R creation already in progress, skipping');
      //   return;
      // }

      // window.isCreatingSRLines = true;

      try {
        mainChartIndicators.SupportResistance.forEach((indicator, idx) => {
          console.log(`🔍 Processing S&R indicator ${idx}:`, indicator);
          if (!indicator || !indicator.parameters) {
            console.log(`⚠️ Skipping S&R indicator ${idx} - missing data`);
            return;
          }

          try {
            const leftBars = Math.min(indicator.parameters.leftBars || indicator.parameters.left_bars || indicator.parameters.left || 10, 20);
            const rightBars = Math.min(indicator.parameters.rightBars || indicator.parameters.right_bars || indicator.parameters.right || 10, 20);

            console.log(`🔍 Starting SAFE S&R calculation with left=${leftBars}, right=${rightBars}, dataLength=${data?.length}`);
            console.log(`🔍 Sample data format:`, data?.slice(0, 2)); // Show first 2 candles

            // Safety checks
            if (!data || !Array.isArray(data) || data.length < 50) {
              console.log('❌ Insufficient data for S&R calculation');
              return;
            }

            // ASYNC S&R calculation to prevent chart disposal
            console.log(`🚀 About to call calculateSupportResistance ASYNC with data length: ${data.length}`);
            console.log(`🔍 S&R calculation data time range: ${data[0]?.time} to ${data[data.length - 1]?.time}`);
            console.log(`🔍 S&R calculation data dates: ${new Date(data[0]?.time * 1000).toISOString()} to ${new Date(data[data.length - 1]?.time * 1000).toISOString()}`);

            // Use setTimeout to make calculation async and prevent blocking
            setTimeout(async () => {
              try {
                if (isDisposedRef.current) {
                  console.log('🚫 Chart disposed, skipping S&R calculation');
                  return;
                }

                console.log('🚀 Starting async S&R calculation...');
                const startTime = performance.now();

                // Add timeout protection
                const calculationPromise = new Promise((resolve) => {
                  const srData = calculateSupportResistance(data, leftBars, rightBars);
                  resolve(srData);
                });

                const timeoutPromise = new Promise((_, reject) => {
                  setTimeout(() => reject(new Error('S&R calculation timeout')), 5000);
                });

                const srData = await Promise.race([calculationPromise, timeoutPromise]);

                const endTime = performance.now();
                const calculationTime = endTime - startTime;
                console.log(`🎯 calculateSupportResistance returned:`, srData?.length || 0, 'levels');

                if (isDisposedRef.current) {
                  console.log('🚫 Chart disposed during calculation, skipping result processing');
                  return;
                }

                if (srData && srData.length > 0) {
                  const srTimeRange = {
                    first: Math.min(...srData.map(d => d.time)),
                    last: Math.max(...srData.map(d => d.time))
                  };
                  console.log(`🔍 S&R levels time range: ${srTimeRange.first} to ${srTimeRange.last}`);
                  console.log(`🔍 S&R levels dates: ${new Date(srTimeRange.first * 1000).toISOString()} to ${new Date(srTimeRange.last * 1000).toISOString()}`);
                }

                console.log(`⏱️ S&R calculation took ${calculationTime.toFixed(2)}ms`);

                // Safety check for calculation time
                if (calculationTime > 1000) {
                  console.warn('⚠️ S&R calculation took too long, skipping');
                  return;
                }

            if (srData && Array.isArray(srData) && srData.length > 0) {
                  // Final check that chart still exists before processing results
                  if (isDisposedRef.current || !chart) {
                    console.log('🚫 Chart disposed before S&R result processing, skipping');
                    return;
                  }

                  console.log(`🎯 S&R Raw Data Sample:`, srData.slice(0, 3)); // Show first 3 items
                  console.log(`Adding Support & Resistance with ${srData.length} levels`);

              // Separate support and resistance levels
              const supportLevels = srData.filter(d => d.type === 'support');
              const resistanceLevels = srData.filter(d => d.type === 'resistance');

              console.log(`🎯 RAW PIVOT COUNTS: ${supportLevels.length} support, ${resistanceLevels.length} resistance (total: ${srData.length})`);
              console.log(`🎯 USER LOOKBACK SETTINGS: left=${leftBars}, right=${rightBars}`);

              // Get current price for filtering
              const currentPrice = data[data.length - 1]?.close || 0;
              console.log(`📊 Current price: ${currentPrice.toFixed(5)}`);

              // Debug all levels first
              console.log(`📊 Total support levels found: ${supportLevels.length}`);
              console.log(`📊 Total resistance levels found: ${resistanceLevels.length}`);

              if (supportLevels.length > 0) {
                console.log(`📊 First 3 support levels:`, supportLevels.slice(0, 3).map(l => `${l.value.toFixed(5)} (${l.value < currentPrice ? 'BELOW' : 'ABOVE'} current)`));
              }

              if (resistanceLevels.length > 0) {
                console.log(`📊 First 3 resistance levels:`, resistanceLevels.slice(0, 3).map(l => `${l.value.toFixed(5)} (${l.value > currentPrice ? 'ABOVE' : 'BELOW'} current)`));
              }

            // TRADINGVIEW-STYLE S&R - Show ALL pivot points using efficient rendering
            console.log(`📊 Raw pivot counts: ${supportLevels.length} support + ${resistanceLevels.length} resistance`);
            console.log(`🚀 Using TradingView-style approach: showing ALL pivot points with optimized rendering`);

            // Filter out only invalid levels, keep ALL valid pivot points
            const allSupportLevels = supportLevels
              .filter(level => level && level.value > 0 && !isNaN(level.value))
              .sort((a, b) => a.time - b.time);

            const allResistanceLevels = resistanceLevels
              .filter(level => level && level.value > 0 && !isNaN(level.value))
              .sort((a, b) => a.time - b.time);

            console.log(`🎯 FINAL S&R LEVELS: ${allSupportLevels.length} support + ${allResistanceLevels.length} resistance = ${allSupportLevels.length + allResistanceLevels.length} total levels`);

            console.log(`📊 DEBUGGING S&R LEVELS:`);
            console.log(`📊 Showing ALL ${allSupportLevels.length} support levels (no filtering)`);
            console.log(`📊 Showing ALL ${allResistanceLevels.length} resistance levels (no filtering)`);

            // Debug: Show the actual time ranges of the source data
            if (supportLevels.length > 0) {
              const supportSorted = supportLevels.sort((a, b) => a.time - b.time);
              console.log(`📊 Source support time range: ${new Date(supportSorted[0].time * 1000).toISOString()} to ${new Date(supportSorted[supportSorted.length - 1].time * 1000).toISOString()}`);
            }

            if (resistanceLevels.length > 0) {
              const resistanceSorted = resistanceLevels.sort((a, b) => a.time - b.time);
              console.log(`📊 Source resistance time range: ${new Date(resistanceSorted[0].time * 1000).toISOString()} to ${new Date(resistanceSorted[resistanceSorted.length - 1].time * 1000).toISOString()}`);
            }

            // Show time distribution of selected levels
            if (allSupportLevels.length > 0) {
              const supportTimeRange = {
                first: new Date(allSupportLevels[0].time * 1000).toISOString().substring(0, 10),
                last: new Date(allSupportLevels[allSupportLevels.length - 1].time * 1000).toISOString().substring(0, 10)
              };
              console.log(`📊 Support levels time range: ${supportTimeRange.first} to ${supportTimeRange.last}`);
            }

            if (allResistanceLevels.length > 0) {
              const resistanceTimeRange = {
                first: new Date(allResistanceLevels[0].time * 1000).toISOString().substring(0, 10),
                last: new Date(allResistanceLevels[allResistanceLevels.length - 1].time * 1000).toISOString().substring(0, 10)
              };
              console.log(`📊 Resistance levels time range: ${resistanceTimeRange.first} to ${resistanceTimeRange.last}`);
            }

            // Validate candlestickSeries exists for createPriceLine
            if (!candlestickSeries) {
              console.error('❌ Candlestick series not available for price lines');
              return;
            }

            // Simple validation
            if (!candlestickSeries) {
              console.error('❌ No candlestick series available');
              return;
            }

            console.log(`📊 Creating ${allSupportLevels.length} support and ${allResistanceLevels.length} resistance lines`);

            // OPTIMIZED S&R RENDERING - Use markers instead of individual line series
            console.log(`📊 Creating OPTIMIZED S&R display for ${allSupportLevels.length} support and ${allResistanceLevels.length} resistance levels`);

            // Create a single line series for all S&R markers (much more efficient)
            const srMarkerSeries = chart.addLineSeries({
              color: 'transparent', // Invisible line, we only want the markers
              lineWidth: 0,
              crosshairMarkerVisible: false,
              lastValueVisible: false,
              priceLineVisible: false,
              visible: true
            });

            // Prepare marker data for ALL support and resistance levels
            const allMarkers = [];

            // Add support level markers
            allSupportLevels.forEach((level) => {
              allMarkers.push({
                time: level.time,
                position: 'belowBar',
                color: '#4CAF50', // Green for support
                shape: 'circle',
                size: 2
              });
            });

            // Add resistance level markers
            allResistanceLevels.forEach((level) => {
              allMarkers.push({
                time: level.time,
                position: 'aboveBar',
                color: '#F44336', // Red for resistance
                shape: 'circle',
                size: 2
              });
            });

            // Sort markers by time
            allMarkers.sort((a, b) => a.time - b.time);

            console.log(`📊 Created ${allMarkers.length} S&R markers (${allSupportLevels.length} support + ${allResistanceLevels.length} resistance)`);
            console.log(`📊 Sample markers:`, allMarkers.slice(0, 3));

            // Set markers on the candlestick series (more efficient than individual lines)
            if (allMarkers.length > 0) {
              candlestickSeries.setMarkers(allMarkers);
              console.log(`✅ Successfully set ${allMarkers.length} markers on candlestick series`);
            } else {
              console.log(`⚠️ No markers to set`);
            }

            // Store reference for cleanup
            currentSeries['sr-markers'] = srMarkerSeries;

            // Clean up any existing S&R series
            Object.keys(currentSeries).forEach(key => {
              if (key.startsWith('support-') || key.startsWith('resistance-') || key === 'sr-markers') {
                try {
                  if (key.includes('priceline')) {
                    candlestickSeries.removePriceLine(currentSeries[key]);
                  } else {
                    chart.removeSeries(currentSeries[key]);
                  }
                  delete currentSeries[key];
                } catch (error) {
                  // Ignore cleanup errors
                }
              }
            });

            console.log('✅ Support & Resistance markers added successfully - showing ALL pivot points!');

            // CRITICAL: Fix time range mismatch - set visible range to data range
            try {
              const dataTimeRange = {
                from: data[0]?.time,
                to: data[data.length - 1]?.time
              };

              console.log(`🔧 Setting visible range to data range: ${dataTimeRange.from} to ${dataTimeRange.to}`);

              // Set the visible range to match the data
              chart.timeScale().setVisibleRange(dataTimeRange);

              // Then fit content to show all series
              setTimeout(() => {
                chart.timeScale().fitContent();
                console.log('🔧 Applied fitContent() after setting visible range');
              }, 100);

            } catch (error) {
              console.error('❌ Error fixing time range:', error);
            }
          }
          } catch (error) {
            console.error('Error adding Support & Resistance:', error);
          }
        });
              } catch (error) {
                console.error('🚫 S&R async calculation failed:', error);
              }
            }, 0); // Execute immediately but asynchronously
          } catch (error) {
            console.error('Error in S&R indicator processing:', error);
          }
        }

        // Store current series for cleanup
        setMainChartSeries(currentSeries);

        chart.timeScale().fitContent();
    } catch (error) {
      console.error('Error creating chart:', error);
    } finally {
      setIsCreatingChart(false);
    }
  }, [data, showVolume, trades, mainChartIndicators, calculateBollingerBands, calculateSMA, calculateEMA, calculateSupportResistance, setMainChartSeries]);

  // Create and update indicator charts
  const updateIndicatorCharts = useCallback(() => {
    if (isDisposedRef.current) return; // Don't create charts if disposed

    console.log('updateIndicatorCharts called', { groupCount: Object.keys(indicatorGroups).length, groups: indicatorGroups });

    Object.entries(indicatorGroups).forEach(([groupId, indicators]) => {
      if (!indicators || indicators.length === 0 || isDisposedRef.current) return;

      console.log(`Processing group ${groupId} with ${indicators.length} indicators:`, indicators.map(i => i.type));

      const indicatorType = indicators[0].type;
      const chart = createIndicatorChart(groupId, indicatorType === 'MACD' ? 250 : 200, indicatorType);
      if (!chart) {
        console.warn(`Failed to create chart for group ${groupId}`);
        return;
      }

      console.log(`Chart created successfully for group ${groupId}`);

      indicators.forEach((indicator) => {
        console.log(`Processing indicator ${indicator.type} with id ${indicator.id}`);

        if (indicator.type === 'RSI') {
          const rsiData = calculateRSI(data, indicator.parameters?.period || 14);
          console.log(`RSI data calculated:`, { length: rsiData?.length, hasData: !!rsiData });
          if (rsiData && rsiData.length > 0) {
            const series = chart.addLineSeries({
              color: '#2962FF',
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 0,
                minMove: 1,
              },
            });
            series.setData(rsiData);
            console.log(`RSI series created and data set for ${indicator.id}`);

            // Add overbought/oversold lines
            const overboughtLine = chart.addLineSeries({
              color: '#787B86',
              lineWidth: 1,
              lineStyle: 1, // Dashed
            });
            const oversoldLine = chart.addLineSeries({
              color: '#787B86',
              lineWidth: 1,
              lineStyle: 1, // Dashed
            });

            const overboughtData = data.map(point => ({ time: point.time, value: 70 }));
            const oversoldData = data.map(point => ({ time: point.time, value: 30 }));

            overboughtLine.setData(overboughtData);
            oversoldLine.setData(oversoldData);
          }
        } else if (indicator.type === 'MACD') {
          console.log('Calculating MACD with parameters:', indicator.parameters);
          const macdData = calculateMACD(data, indicator.parameters);
          console.log('MACD data calculated:', {
            hasData: !!macdData,
            macdLength: macdData?.macd?.length,
            signalLength: macdData?.signal?.length,
            histogramLength: macdData?.histogram?.length
          });
          if (macdData) {
            console.log('Creating MACD chart with data:', {
              macdPoints: macdData.macd.length,
              signalPoints: macdData.signal.length,
              histogramPoints: macdData.histogram.length
            });

            // MACD line (blue)
            const macdSeries = chart.addLineSeries({
              color: '#2962FF', // Blue
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 6,
                minMove: 0.000001,
              },
              title: `MACD (${indicator.parameters?.fast || 12}, ${indicator.parameters?.slow || 26})`,
            });
            macdSeries.setData(macdData.macd);
            console.log('MACD line series created and data set');
            console.log('MACD sample data:', macdData.macd.slice(-5));

            // Signal line (bright red for contrast)
            const signalSeries = chart.addLineSeries({
              color: '#FF0000', // Bright red for maximum contrast
              lineWidth: 3, // Make it thicker to ensure visibility
              priceFormat: {
                type: 'price',
                precision: 6,
                minMove: 0.000001,
              },
              title: `Signal (${indicator.parameters?.signal || 9})`,
            });
            signalSeries.setData(macdData.signal);
            console.log('Signal line series created and data set');
            console.log('Signal sample data:', macdData.signal.slice(-5));

            // Check if signal series was actually added
            console.log('Chart series count after adding signal:', chart.options());

            // Histogram
            const histogramSeries = chart.addHistogramSeries({
              priceFormat: {
                type: 'price',
                precision: 6,
                minMove: 0.000001,
              },
              title: 'Histogram',
            });

            // Color histogram bars based on positive/negative values
            const coloredHistogramData = macdData.histogram.map(point => ({
              time: point.time,
              value: point.value,
              color: point.value >= 0 ? '#26a69a' : '#ef5350' // Green for positive, red for negative
            }));

            histogramSeries.setData(coloredHistogramData);
            console.log('Histogram series created and data set');
            console.log('Histogram sample data:', coloredHistogramData.slice(-5));

            // Add zero line for reference
            const zeroLineSeries = chart.addLineSeries({
              color: '#787B86',
              lineWidth: 1,
              lineStyle: 1, // Dashed
              priceFormat: {
                type: 'price',
                precision: 6,
                minMove: 0.000001,
              },
              priceLineVisible: false,
              lastValueVisible: false,
            });

            const zeroLineData = macdData.macd.map(point => ({
              time: point.time,
              value: 0
            }));

            zeroLineSeries.setData(zeroLineData);
            console.log('Zero line added to MACD chart');
          }
        } else if (indicator.type === 'ATR') {
          const atrData = calculateATR(data, indicator.parameters?.period || 14);
          if (atrData && atrData.length > 0) {
            const series = chart.addLineSeries({
              color: '#FFB300',
              lineWidth: 2,
              priceFormat: {
                type: 'price',
                precision: 5,
                minMove: 0.00001,
              },
            });
            series.setData(atrData);
          }
        }
      });

      chart.timeScale().fitContent();
    });
  }, [indicatorGroups, data, createIndicatorChart, calculateRSI, calculateMACD, calculateATR]);

  // Update charts when data changes
  useEffect(() => {
    console.log('Chart update effect triggered', {
      hasData: !!data?.length,
      hasRef: !!mainChartRef.current,
      isDisposed: isDisposedRef.current,
      indicatorsCount: indicators.length,
      tradesCount: trades?.length || 0,
      hasTrades: !!trades?.length,
      indicatorGroupsCount: Object.keys(indicatorGroups).length,
      mainChartIndicatorsCount: Object.values(mainChartIndicators).flat().length
    });

    // Only create charts when we have both data AND indicators are processed
    const hasProcessedIndicators = indicators.length === 0 || indicatorsReady;

    console.log('Chart creation conditions:', {
      hasData: !!data?.length,
      hasRef: !!mainChartRef.current,
      hasProcessedIndicators,
      indicatorsReady,
      indicatorsLength: indicators.length,
      groupsCount: Object.keys(indicatorGroups).length,
      mainIndicatorsCount: Object.values(mainChartIndicators).flat().length
    });

    // Reset disposed state when we have new data (in case of StrictMode double-mounting)
    if (data?.length && mainChartRef.current && hasProcessedIndicators) {
      if (isDisposedRef.current) {
        console.log('Resetting disposal state');
      }
      isDisposedRef.current = false; // Reset disposal state

      // Add a small delay to prevent rapid re-creation
      const timeoutId = setTimeout(() => {
        console.log('Creating main chart...');
        createMainChart();
        // Add extra delay for indicator charts to ensure DOM is ready
        setTimeout(() => {
          console.log('Creating indicator charts...');
          updateIndicatorCharts();
        }, 50);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [data?.length, indicators, trades, indicatorGroups, mainChartIndicators, indicatorsReady, updateIndicatorCharts]); // Added indicatorsReady to dependencies

  // Group indicators when they change
  useEffect(() => {
    console.log('Grouping indicators...', { indicatorsCount: indicators.length });
    setIndicatorsReady(false); // Reset ready state when indicators change
    groupIndicators();
  }, [indicators, groupIndicators]);

  // Update indicator charts when indicator groups change (after initial creation)
  useEffect(() => {
    console.log('Indicator groups changed effect', {
      hasData: !!data?.length,
      groupsCount: Object.keys(indicatorGroups).length,
      isDisposed: isDisposedRef.current,
      hasMainChart: !!mainChartInstanceRef.current
    });

    // Only update indicator charts if main chart already exists and we have data
    if (data?.length && mainChartInstanceRef.current && Object.keys(indicatorGroups).length > 0 && !isDisposedRef.current) {
      console.log('Updating indicator charts due to group changes...');
      const timeoutId = setTimeout(() => {
        updateIndicatorCharts();
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [indicatorGroups, data?.length, updateIndicatorCharts]);

  // Update main chart when main chart indicators change
  useEffect(() => {
    console.log('Main chart indicators changed:', {
      smaCount: mainChartIndicators.SMA?.length || 0,
      emaCount: mainChartIndicators.EMA?.length || 0,
      bbCount: mainChartIndicators.BollingerBands?.length || 0,
      srCount: mainChartIndicators.SupportResistance?.length || 0,
      hasData: !!data?.length,
      hasChart: !!mainChartInstanceRef.current
    });

    if (data?.length && mainChartInstanceRef.current && !isCreatingChart) {
      console.log('Recreating main chart due to indicator changes...');
      setTimeout(() => {
        createMainChart();
      }, 50);
    }
  }, [mainChartIndicators, data?.length, isCreatingChart]); // Removed createMainChart to prevent infinite loop

  // Update indicator charts when groups change
  useEffect(() => {
    if (Object.keys(indicatorGroups).length > 0 && data?.length) {
      console.log('Indicator groups changed, updating charts...');
      setTimeout(() => {
        updateIndicatorCharts();
      }, 100);
    }
  }, [indicatorGroups, data?.length, updateIndicatorCharts]);

  // Setup resize observer
  useEffect(() => {
    let resizeTimeout;

    const handleResize = () => {
      if (isDisposedRef.current || isCreatingChart) return; // Don't resize disposed or creating charts

      // Debounce resize operations
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        if (isDisposedRef.current) return; // Check again after timeout

        if (mainChartRef.current && mainChartInstanceRef.current) {
          try {
            mainChartInstanceRef.current.applyOptions({
              width: mainChartRef.current.clientWidth,
            });
          } catch (error) {
            console.warn('Error resizing main chart:', error);
          }
        }

        // Resize indicator charts
        Object.entries(indicatorChartsRef.current).forEach(([, container]) => {
          if (container && container.chartInstance) {
            try {
              container.chartInstance.applyOptions({
                width: container.clientWidth,
              });
            } catch (error) {
              console.warn('Error resizing indicator chart:', error);
            }
          }
        });
      }, 100); // 100ms debounce
    };

    const resizeObserver = new ResizeObserver(handleResize);
    if (mainChartRef.current) {
      resizeObserver.observe(mainChartRef.current);
    }

    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(resizeTimeout);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Cleanup only on actual unmount
  useEffect(() => {
    // Don't set up cleanup immediately, only when component actually unmounts
    const cleanup = () => {
      console.log('Component unmounting, cleaning up charts');
      isDisposedRef.current = true; // Mark as disposed first

      if (mainChartInstanceRef.current) {
        try {
          mainChartInstanceRef.current.remove();
        } catch (error) {
          console.warn('Error during chart cleanup:', error);
        }
        mainChartInstanceRef.current = null;
        candlestickSeriesRef.current = null;
        volumeSeriesRef.current = null;
      }

      // Cleanup indicator charts
      Object.values(indicatorChartsRef.current).forEach(container => {
        if (container && container.chartInstance) {
          try {
            container.chartInstance.remove();
          } catch (error) {
            console.warn('Error during indicator chart cleanup:', error);
          }
        }
      });
      indicatorChartsRef.current = {};
    };

    // Return cleanup function
    return cleanup;
  }, []);

  // Early return for error state
  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-red-400">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 relative w-full overflow-hidden" style={{ minWidth: '300px', maxWidth: '100%' }}>
      {showPreferenceDialog && pendingIndicator && (
        <div className="fixed inset-0 flex items-center justify-center" style={{ zIndex: 9999 }}>
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={() => {
              setShowPreferenceDialog(false);
              setPendingIndicator(null);
              if (onIndicatorAdd) {
                onIndicatorAdd(false);
              }
            }}
          />
          <div className="relative bg-[#1a1a1a] rounded-xl p-6 w-full max-w-md mx-4 shadow-2xl border border-[#2a2a2a]">
            <h3 className="text-xl font-bold text-[#FEFEFF] mb-4">
              Add {pendingIndicator.type} Indicator
            </h3>
            <p className="text-[#FEFEFF]/80 mb-6">
              How would you like to display the new {pendingIndicator.type}
              {pendingIndicator.type === 'RSI' && ` (${pendingIndicator.parameters?.period || 14})`}
              {pendingIndicator.type === 'MACD' &&
                ` (${pendingIndicator.parameters?.fast || 12}, ${pendingIndicator.parameters?.slow || 26}, ${pendingIndicator.parameters?.signal || 9})`
              }
              {pendingIndicator.type === 'ATR' && ` (${pendingIndicator.parameters?.period || 14})`}
              {' '}indicator?
            </p>
            <div className="space-y-4">
              {hasExistingWindows(pendingIndicator.type) && (
                <button
                  onClick={() => handleIndicatorPreference(pendingIndicator, 'existing')}
                  className="w-full px-4 py-3 bg-[#2a2a2a] hover:bg-[#3a3a3a] text-[#FEFEFF] rounded-lg transition-colors hover:shadow-lg"
                >
                  Add to existing {pendingIndicator.type} window
                </button>
              )}
              <button
                onClick={() => handleIndicatorPreference(pendingIndicator, 'new')}
                className="w-full px-4 py-3 bg-[#2a2a2a] hover:bg-[#3a3a3a] text-[#FEFEFF] rounded-lg transition-colors hover:shadow-lg"
              >
                Create new window
              </button>
              <button
                onClick={() => {
                  setShowPreferenceDialog(false);
                  setPendingIndicator(null);
                  if (onIndicatorAdd) {
                    onIndicatorAdd(false);
                  }
                }}
                className="w-full px-4 py-3 bg-red-500/10 hover:bg-red-500/20 text-red-500 rounded-lg transition-colors hover:shadow-lg"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="relative w-full" style={{ minWidth: '300px', maxWidth: '100%' }}>
        {isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-[#0A0B0B]/80 z-10">
            <div className="w-12 h-12 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin mb-4"></div>
            <div className="text-[#EFBD3A] text-sm">Loading chart data...</div>
          </div>
        )}
        <div
          ref={mainChartRef}
          className="w-full rounded-lg overflow-hidden"
          style={{
            height: '400px',
            position: 'relative',
            minWidth: '300px',
            maxWidth: '100%',
            resize: 'horizontal',
            backgroundColor: '#0A0B0B' // Add background color to see the container
          }}
        />
      </div>

      {/* Render indicator charts */}
      {Object.entries(indicatorGroups).map(([groupId, groupIndicators]) => (
        <div key={groupId} className="relative w-full" style={{ minWidth: '300px', maxWidth: '100%' }}>
          <div className="mb-2 text-sm text-[#FEFEFF]/80">
            {groupIndicators[0]?.type} ({groupIndicators.length})
          </div>
          <div
            ref={el => indicatorChartsRef.current[groupId] = el}
            className="w-full rounded-lg overflow-hidden"
            style={{
              height: groupIndicators[0]?.type === 'MACD' ? '250px' : '200px',
              position: 'relative',
              minWidth: '300px',
              maxWidth: '100%',
              resize: 'horizontal'
            }}
          >
            {/* Loading overlay for indicator charts */}
            {isLoading && (
              <div className="absolute inset-0 bg-[#0A0B0B]/80 backdrop-blur-sm flex items-center justify-center z-20">
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-8 h-8 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
                  <div className="text-sm text-[#FEFEFF]/80">Loading indicator data...</div>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default StrategyChart;
