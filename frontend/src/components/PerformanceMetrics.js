import React from 'react';
import { motion } from 'framer-motion';

const PerformanceMetrics = ({ metrics, accountBalance, historicalTrades, openTrades }) => {
  // Calculate unrealized P&L from open trades
  const calculateUnrealizedPL = () => {
    if (!openTrades || openTrades.length === 0) {
      return 0;
    }

    const total = openTrades.reduce((sum, trade) => {
      const unrealizedPL = typeof trade.unrealizedPL === 'string' ? parseFloat(trade.unrealizedPL || 0) : (trade.unrealizedPL || 0);
      return sum + unrealizedPL;
    }, 0);

    return total;
  };

  // Get unrealized P&L
  const unrealizedPL = calculateUnrealizedPL();

  // Calculate additional metrics from historical trades if not provided
  const calculateMetricsFromTrades = () => {
    if (!historicalTrades || historicalTrades.length === 0) {
      return {};
    }

    // Filter closed and open trades
    const closedTrades = historicalTrades.filter(trade =>
      trade.status === 'CLOSED' || trade.status === 'closed' ||
      (trade.closeTime && trade.realizedPL !== undefined)
    );

    const openTrades = historicalTrades.filter(trade =>
      trade.status === 'OPEN' || trade.status === 'open' ||
      (trade.openTime && trade.unrealizedPL !== undefined && !trade.closeTime)
    );
    const totalTrades = closedTrades.length + openTrades.length;

    // Calculate winning and losing trades
    const winningTrades = closedTrades.filter(trade => {
      const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
      return realizedPL > 0;
    }).length;

    const losingTrades = closedTrades.filter(trade => {
      const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
      return realizedPL <= 0;
    }).length;

    // Calculate win rate
    const winRate = totalTrades > 0 ? winningTrades / totalTrades : 0;

    // Calculate total realized P&L
    const totalRealizedPL = closedTrades.reduce((sum, trade) => {
      const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
      return sum + realizedPL;
    }, 0);

    // Calculate average win and loss
    const winningTradesData = closedTrades.filter(trade => {
      const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
      return realizedPL > 0;
    });

    const losingTradesData = closedTrades.filter(trade => {
      const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL) : trade.realizedPL;
      return realizedPL < 0;
    });

    const averageWin = winningTradesData.length > 0
      ? winningTradesData.reduce((sum, trade) => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return sum + realizedPL;
        }, 0) / winningTradesData.length
      : 0;

    const averageLoss = losingTradesData.length > 0
      ? Math.abs(losingTradesData.reduce((sum, trade) => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return sum + realizedPL;
        }, 0)) / losingTradesData.length
      : 0;

    // Calculate largest win and loss
    const largestWin = winningTradesData.length > 0
      ? Math.max(...winningTradesData.map(trade => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return realizedPL;
        }))
      : 0;

    const largestLoss = losingTradesData.length > 0
      ? Math.abs(Math.min(...losingTradesData.map(trade => {
          const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
          return realizedPL;
        })))
      : 0;

    // Calculate profit factor
    const totalWinAmount = winningTradesData.reduce((sum, trade) => {
      const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
      return sum + realizedPL;
    }, 0);

    const totalLossAmount = Math.abs(losingTradesData.reduce((sum, trade) => {
      const realizedPL = typeof trade.realizedPL === 'string' ? parseFloat(trade.realizedPL || 0) : (trade.realizedPL || 0);
      return sum + realizedPL;
    }, 0));

    // Calculate profit factor - handle case where there are no losing trades
    const profitFactor = totalLossAmount > 0 ?
      totalWinAmount / totalLossAmount :
      totalWinAmount > 0 ?
        'PERFECT' : // Use a special value for perfect profit factor (no losing trades)
        0;

    // Calculate average trade
    const averageTrade = totalTrades > 0 ? totalRealizedPL / totalTrades : 0;

    return {
      totalTrades,
      winningTrades,
      losingTrades,
      winRate,
      totalRealizedPL,
      averageWin,
      averageLoss,
      largestWin,
      largestLoss,
      profitFactor,
      averageTrade,
      lastUpdated: new Date().toISOString()
    };
  };

  // Merge calculated metrics with provided metrics
  const calculatedMetrics = calculateMetricsFromTrades();

  // Prioritize calculated metrics over provided metrics
  const combinedMetrics = {
    ...metrics,           // Start with provided metrics as base
    ...calculatedMetrics  // Override with calculated metrics where available
  };

  // Ensure we have a totalRealizedPL value
  if (combinedMetrics.totalRealizedPL === undefined || combinedMetrics.totalRealizedPL === null) {
    if (metrics.totalRealizedPL !== undefined && metrics.totalRealizedPL !== null) {
      combinedMetrics.totalRealizedPL = metrics.totalRealizedPL;
    } else if (calculatedMetrics.totalRealizedPL !== undefined && calculatedMetrics.totalRealizedPL !== null) {
      combinedMetrics.totalRealizedPL = calculatedMetrics.totalRealizedPL;
    } else {
      combinedMetrics.totalRealizedPL = 0;
    }
  }

  // Use combined metrics for display
  const displayMetrics = combinedMetrics;
  // Format currency values
  const formatCurrency = (value) => {
    if (value === undefined || value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Format percentage values
  const formatPercentage = (value) => {
    if (value === undefined || value === null) return 'N/A';
    return `${(value * 100).toFixed(2)}%`;
  };

  // Calculate color based on value (positive/negative)
  const getValueColor = (value) => {
    if (value === undefined || value === null) return 'text-gray-400';
    return value >= 0 ? 'text-green-400' : 'text-red-400';
  };

  // Calculate background color based on value (positive/negative)
  const getBackgroundColor = (value) => {
    if (value === undefined || value === null) return 'bg-[#0A0B0B]/50';
    return value >= 0 ? 'bg-green-900/20' : 'bg-red-900/20';
  };

  // Calculate border color based on value (positive/negative)
  const getBorderColor = (value) => {
    if (value === undefined || value === null) return 'border-[#1a1a1a]';
    return value >= 0 ? 'border-green-500/30' : 'border-red-500/30';
  };

  return (
    <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] overflow-hidden">
      <div className="px-4 py-3 border-b border-[#1a1a1a] flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-[#FEFEFF]">Performance Metrics</h2>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-[#EFBD3A] bg-[#EFBD3A]/10 px-2 py-1 rounded-full">
              Last updated: {displayMetrics.lastUpdated ? new Date(displayMetrics.lastUpdated).toLocaleTimeString() : 'N/A'}
            </span>
          </div>
        </div>
        <div className="text-xs text-[#FEFEFF]/60 bg-[#0A0B0B]/50 px-3 py-2 rounded-md">
          <span className="font-medium text-[#EFBD3A]">Note:</span> These metrics only include realized P&L from closed trades. Unrealized P&L from open positions is not included.
        </div>
      </div>

      <div className="p-4">
        {/* Main metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Account Balance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-[#1a1a1a] rounded-lg p-4 border border-[#333]"
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Account Balance</h3>
              <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-white">{formatCurrency(accountBalance)}</p>
          </motion.div>

          {/* Total P&L */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className={`rounded-lg p-4 border ${getBorderColor(displayMetrics.totalRealizedPL)} ${getBackgroundColor(displayMetrics.totalRealizedPL)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Realized P&L</h3>
              <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <p className={`text-2xl font-bold ${getValueColor(displayMetrics.totalRealizedPL)}`}>
              {formatCurrency(displayMetrics.totalRealizedPL)}
            </p>
            {displayMetrics.totalReturn !== undefined && (
              <p className={`text-sm ${getValueColor(displayMetrics.totalReturn)}`}>
                {displayMetrics.totalReturn > 0 ? '+' : ''}{formatPercentage(displayMetrics.totalReturn)}
              </p>
            )}
          </motion.div>

          {/* Unrealized P&L */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.15 }}
            className={`rounded-lg p-4 border ${getBorderColor(unrealizedPL)} ${getBackgroundColor(unrealizedPL)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Unrealized P&L</h3>
              <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <p className={`text-2xl font-bold ${getValueColor(unrealizedPL)}`}>
              {formatCurrency(unrealizedPL)}
            </p>
            <p className="text-sm text-gray-400">
              {openTrades?.length || 0} open positions
            </p>
          </motion.div>

          {/* Win Rate */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="bg-[#1a1a1a] rounded-lg p-4 border border-[#333]"
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Win Rate</h3>
              <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-white">{formatPercentage(displayMetrics.winRate || 0)}</p>
            <p className="text-sm text-gray-400">{displayMetrics.totalTrades || 0} total trades</p>
          </motion.div>

          {/* Profit Factor */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
            className="bg-[#1a1a1a] rounded-lg p-4 border border-[#333]"
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-400">Profit Factor</h3>
              <svg className="w-5 h-5 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-white">
              {displayMetrics.profitFactor === 'PERFECT' ?
                '100%' : // Display 100% for perfect profit factor (no losing trades)
                typeof displayMetrics.profitFactor === 'number' ?
                  displayMetrics.profitFactor.toFixed(2) : 'N/A'}
            </p>
            <p className="text-sm text-gray-400">Profit / Loss ratio</p>
          </motion.div>
        </div>

        {/* Detailed metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Trade Statistics */}
          <div className="bg-[#1a1a1a] rounded-lg p-4 border border-[#333]">
            <h3 className="text-sm font-medium text-gray-400 mb-4">Trade Statistics</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Total Trades</span>
                <span className="text-sm font-medium text-white">{displayMetrics.totalTrades || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Winning Trades</span>
                <span className="text-sm font-medium text-green-400">{displayMetrics.winningTrades || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Losing Trades</span>
                <span className="text-sm font-medium text-red-400">{displayMetrics.losingTrades || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Average Win</span>
                <span className="text-sm font-medium text-green-400">{formatCurrency(displayMetrics.averageWin)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Average Loss</span>
                <span className="text-sm font-medium text-red-400">{formatCurrency(displayMetrics.averageLoss)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Largest Win</span>
                <span className="text-sm font-medium text-green-400">{formatCurrency(displayMetrics.largestWin)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Largest Loss</span>
                <span className="text-sm font-medium text-red-400">{formatCurrency(displayMetrics.largestLoss)}</span>
              </div>
            </div>
          </div>

          {/* Risk Metrics */}
          <div className="bg-[#1a1a1a] rounded-lg p-4 border border-[#333]">
            <h3 className="text-sm font-medium text-gray-400 mb-4">Risk Metrics</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Max Drawdown</span>
                <span className="text-sm font-medium text-red-400">{formatPercentage(displayMetrics.maxDrawdown || 0)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Sharpe Ratio</span>
                <span className="text-sm font-medium text-white">{displayMetrics.sharpeRatio?.toFixed(2) || 'N/A'}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Profit Factor</span>
                <span className="text-sm font-medium text-white">
                  {displayMetrics.profitFactor === 'PERFECT' ?
                    '100%' : // Display 100% for perfect profit factor (no losing trades)
                    typeof displayMetrics.profitFactor === 'number' ?
                      displayMetrics.profitFactor.toFixed(2) : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Win/Loss Ratio</span>
                <span className="text-sm font-medium text-white">
                  {displayMetrics.winLossRatio?.toFixed(2) || 'N/A'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Expectancy</span>
                <span className={`text-sm font-medium ${getValueColor(displayMetrics.expectancy)}`}>
                  {displayMetrics.expectancy ? formatCurrency(displayMetrics.expectancy) : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Average Trade</span>
                <span className={`text-sm font-medium ${getValueColor(displayMetrics.averageTrade)}`}>
                  {displayMetrics.averageTrade ? formatCurrency(displayMetrics.averageTrade) : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Total Commissions</span>
                <span className="text-sm font-medium text-red-400">
                  {formatCurrency(displayMetrics.totalCommissions || 0)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceMetrics;
