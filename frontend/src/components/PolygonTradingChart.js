/**
 * Simplified Trading Chart using direct Polygon API calls
 * Replaces WebSocket + GCS approach with simple buffer refresh strategy
 * Matches the backend trade-bot implementation exactly
 */

import React, { useEffect, useRef, useState, useCallback } from "react";
import OptimizedTradingChart from "./OptimizedTradingChart";
import { calculateIndicatorsFromStrategy } from "../utils/indicatorCalculations";
import polygonApiService from "../services/polygonApiService";

const PolygonTradingChart = ({
  candleData: initialCandleData,
  indicators: passedIndicators,
  trades,
  timeframe: displayTimeframe,
  chartTimeframe = "1h",
  instrument,
  marketStatus,
  strategyInfo,
  strategy,
  enablePolling = true,
  skipInitialFetch = false,
  onDataUpdate = null,
  onIndicatorsCalculated = null,
  hideDisplayPills = false,
  hideStrategyPills = false,
  // Zoom preservation props
  preserveZoom = false,
  onZoomChange = null,
  initialZoomState = null,
  ...otherProps
}) => {
  // State management
  const [candleData, setCandleData] = useState(initialCandleData || []);
  const [dataStatus, setDataStatus] = useState('disconnected');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [updateCount, setUpdateCount] = useState(0);
  const [error, setError] = useState(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Refs for cleanup
  const pollingIntervalRef = useRef(null);
  const mountedRef = useRef(true);

  console.log(`🔍 Frontend: Component render - mounted=${mountedRef.current}, instrument=${instrument}, chartTimeframe=${chartTimeframe}`);

  // Calculate indicators from strategy
  const indicators = React.useMemo(() => {
    console.log('🔍 PolygonTradingChart: Calculating indicators', {
      candleDataLength: candleData?.length || 0,
      hasStrategy: !!strategy,
      hasStrategyJson: !!strategy?.strategy_json,
      hasIndicators: !!strategy?.strategy_json?.indicators,
      indicatorCount: strategy?.strategy_json?.indicators?.length || 0
    });

    if (!candleData || candleData.length === 0) {
      console.log('🔍 PolygonTradingChart: No candle data, returning empty indicators');
      return {};
    }

    if (!strategy || !strategy.strategy_json || !strategy.strategy_json.indicators) {
      console.log('🔍 PolygonTradingChart: No strategy or indicators config, returning empty indicators');
      return {};
    }

    try {
      console.log('🔍 PolygonTradingChart: Starting indicator calculation...');
      const calculatedIndicators = calculateIndicatorsFromStrategy(candleData, strategy);

      console.log('🔍 PolygonTradingChart: Indicators calculated', {
        indicatorKeys: Object.keys(calculatedIndicators),
        indicatorCount: Object.keys(calculatedIndicators).length,
        sampleIndicator: Object.keys(calculatedIndicators)[0] ? {
          key: Object.keys(calculatedIndicators)[0],
          dataLength: calculatedIndicators[Object.keys(calculatedIndicators)[0]]?.length || 0
        } : null
      });

      return calculatedIndicators;
    } catch (error) {
      console.error('❌ PolygonTradingChart: Error calculating indicators:', error);
      return {};
    }
  }, [candleData, strategy]);

  // Call the callback when indicators change (outside of render)
  React.useEffect(() => {
    if (onIndicatorsCalculated && Object.keys(indicators).length > 0) {
      onIndicatorsCalculated(indicators);
    }
  }, [indicators, onIndicatorsCalculated]);

  // Get timeframe-specific wait time (matches backend exactly)
  const getWaitTime = useCallback((timeframe) => {
    const timeframeMinutes = {
      '1m': 1,
      '3m': 3,
      '5m': 5,
      '15m': 15,
      '30m': 30,
      '1h': 60,
      '4h': 240,
      '1d': 1440
    }[timeframe] || 60;

    // Wait time based on timeframe (optimized based on actual Polygon API performance)
    let waitSeconds;
    if (timeframeMinutes <= 1) {
      waitSeconds = 10; // Reduced from 30s to 10s based on actual performance
    } else if (timeframeMinutes <= 3) {
      waitSeconds = 90; // 90 seconds for 3m
    } else {
      waitSeconds = 120; // 120 seconds for 5m+
    }

    console.log(`🔍 Frontend getWaitTime: timeframe=${timeframe}, timeframeMinutes=${timeframeMinutes}, waitSeconds=${waitSeconds}`);
    return waitSeconds * 1000; // Convert to milliseconds
  }, []);

  // Get polling interval (timeframe period in milliseconds)
  const getPollingInterval = useCallback((timeframe) => {
    const timeframeMinutes = {
      '1m': 1,
      '3m': 3,
      '5m': 5,
      '15m': 15,
      '30m': 30,
      '1h': 60,
      '4h': 240,
      '1d': 1440
    }[timeframe] || 60;

    const intervalMs = timeframeMinutes * 60 * 1000;
    console.log(`🔍 Frontend getPollingInterval: timeframe=${timeframe}, timeframeMinutes=${timeframeMinutes}, intervalMs=${intervalMs}`);
    return intervalMs; // Convert to milliseconds
  }, []);

  // Fetch initial data
  const fetchInitialData = useCallback(async () => {
    if (!instrument || !chartTimeframe) return;

    try {
      console.log(`🔄 Frontend: Fetching initial data for ${instrument} ${chartTimeframe}`);
      setDataStatus('connecting');
      setError(null);

      const result = await polygonApiService.getCandles(instrument, chartTimeframe, 1000);

      if (!mountedRef.current) return;

      if (result.status === 'success' && result.candles.length > 0) {
        setCandleData(result.candles);
        setDataStatus('connected');
        setLastUpdate(new Date());
        setUpdateCount(prev => prev + 1);
        setIsInitialLoad(false);

        console.log(`✅ Frontend: Initial data loaded - ${result.candles.length} candles`);
        console.log(`📊 Frontend: Latest candle: ${new Date(result.candles[result.candles.length - 1].time * 1000).toISOString()}`);

        if (onDataUpdate) {
          onDataUpdate(result.candles);
        }
      } else {
        setError(result.message || 'Failed to fetch initial data');
        setDataStatus('error');
      }
    } catch (error) {
      console.error('❌ Frontend: Error fetching initial data:', error);
      if (mountedRef.current) {
        setError(error.message);
        setDataStatus('error');
      }
    }
  }, [instrument, chartTimeframe, onDataUpdate]);

  // Fetch updated data (polling)
  const fetchUpdatedData = useCallback(async () => {
    console.log(`🔍 Frontend: fetchUpdatedData called - instrument=${instrument}, chartTimeframe=${chartTimeframe}, mounted=${mountedRef.current}`);

    if (!instrument || !chartTimeframe || !mountedRef.current) {
      console.log(`❌ Frontend: Skipping fetch - missing requirements`);
      return;
    }

    try {
      const now = new Date();
      console.log(`🔄 Frontend: Polling for updates at ${now.toLocaleTimeString()} - ${instrument} ${chartTimeframe}`);

      const result = await polygonApiService.getCandles(instrument, chartTimeframe, 1000);
      console.log(`📡 Frontend: Polygon API result:`, result.status, result.candles?.length || 0, 'candles');

      if (!mountedRef.current) {
        console.log(`❌ Frontend: Component unmounted during fetch`);
        return;
      }

      if (result.status === 'success' && result.candles.length > 0) {
        const latestCandle = result.candles[result.candles.length - 1];
        const currentLatest = candleData.length > 0 ? candleData[candleData.length - 1] : null;

        console.log(`🔍 Frontend: Comparing candles:`);
        console.log(`   📊 Latest from Polygon: ${new Date(latestCandle.time * 1000).toLocaleTimeString()}`);
        console.log(`   📊 Current in buffer: ${currentLatest ? new Date(currentLatest.time * 1000).toLocaleTimeString() : 'none'}`);

        // Check if we have new data
        if (!currentLatest || latestCandle.time > currentLatest.time) {
          console.log(`📈 Frontend: New data available - updating buffer`);
          setCandleData(result.candles);
          setLastUpdate(new Date());
          setUpdateCount(prev => prev + 1);
          setDataStatus('connected');
          setError(null);

          if (onDataUpdate) {
            onDataUpdate(result.candles);
          }
        } else {
          console.log(`📊 Frontend: No new data available yet (same timestamp)`);
        }
      } else {
        console.warn(`⚠️ Frontend: Polling failed: ${result.message}`);
        setError(result.message || 'Polling failed');
      }
    } catch (error) {
      console.error('❌ Frontend: Error during polling:', error);
      if (mountedRef.current) {
        setError(error.message);
        setDataStatus('error');
      }
    }
  }, [instrument, chartTimeframe, candleData, onDataUpdate]);

  // Start polling with backend-style timing
  const startPolling = useCallback(() => {
    if (!enablePolling || pollingIntervalRef.current) return;

    const pollingInterval = getPollingInterval(chartTimeframe);
    const waitTime = getWaitTime(chartTimeframe);

    console.log(`🔄 Frontend: Starting polling every ${pollingInterval/1000} seconds with ${waitTime/1000}s wait for ${chartTimeframe}`);
    console.log(`🔍 Frontend Debug: chartTimeframe=${chartTimeframe}, pollingInterval=${pollingInterval}ms, waitTime=${waitTime}ms`);

    // Backend-style polling: wait after each period, then fetch
    const pollWithWait = () => {
      console.log(`⏰ Frontend: Period completed for ${chartTimeframe}, waiting ${waitTime/1000}s for Polygon processing...`);
      // Wait for Polygon to process the candle
      setTimeout(() => {
        console.log(`⏳ Frontend: Waited ${waitTime/1000}s for Polygon to process ${chartTimeframe} candle - now fetching`);
        fetchUpdatedData();
      }, waitTime);
    };

    // Start first poll after initial wait
    console.log(`🚀 Frontend: Starting first poll for ${chartTimeframe}`);
    pollWithWait();

    // Set up interval for subsequent polls
    console.log(`🔄 Frontend: Setting up interval to repeat every ${pollingInterval/1000}s for ${chartTimeframe}`);
    pollingIntervalRef.current = setInterval(pollWithWait, pollingInterval);
    console.log(`✅ Frontend: Created polling interval ${pollingIntervalRef.current} for ${chartTimeframe}`);
  }, [enablePolling, chartTimeframe, getPollingInterval, getWaitTime, fetchUpdatedData]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      console.log(`🛑 Frontend: Stopping polling interval ${pollingIntervalRef.current} for ${instrument} ${chartTimeframe}`);
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
      console.log(`✅ Frontend: Successfully stopped polling for ${instrument} ${chartTimeframe}`);
    } else {
      console.log(`ℹ️ Frontend: No polling interval to stop for ${instrument} ${chartTimeframe}`);
    }
  }, [instrument, chartTimeframe]);

  // Initialize data and polling - FIXED: Only depend on stable values
  useEffect(() => {
    console.log(`🔄 Frontend: useEffect triggered for ${instrument} ${chartTimeframe}, enablePolling=${enablePolling}, skipInitialFetch=${skipInitialFetch}`);

    if (skipInitialFetch) {
      console.log(`⏭️ Frontend: Skipping initial fetch, using provided candleData for ${instrument} ${chartTimeframe}`);
      // Use the provided initial candle data and set up polling if needed
      if (initialCandleData && initialCandleData.length > 0) {
        setCandleData(initialCandleData);
        setDataStatus('connected');
        setLastUpdate(new Date());
        setIsInitialLoad(false);
        console.log(`✅ Frontend: Using provided candle data - ${initialCandleData.length} candles`);
        console.log(`📅 Frontend: Trading period data range:`, {
          firstCandle: new Date(initialCandleData[0].time * 1000).toISOString(),
          lastCandle: new Date(initialCandleData[initialCandleData.length - 1].time * 1000).toISOString(),
          totalCandles: initialCandleData.length
        });
      } else {
        console.warn(`⚠️ Frontend: skipInitialFetch=true but no initialCandleData provided`);
      }

      if (enablePolling) {
        startPolling();
      }
    } else {
      fetchInitialData().then(() => {
        if (enablePolling) {
          startPolling();
        }
      });
    }

    return () => {
      console.log(`🧹 Frontend: useEffect cleanup for ${instrument} ${chartTimeframe}`);
      stopPolling();
    };
  }, [instrument, chartTimeframe, enablePolling, skipInitialFetch]); // Removed initialCandleData to prevent restart on data updates

  // Cleanup on unmount - FIXED: No dependencies needed
  useEffect(() => {
    // Set mounted to true when component mounts
    mountedRef.current = true;
    console.log(`🚀 Frontend: Component mounted for ${instrument} ${chartTimeframe}, mounted=${mountedRef.current}`);

    return () => {
      console.log(`🧹 Frontend: Component unmounting for ${instrument} ${chartTimeframe}`);
      mountedRef.current = false;
      stopPolling();
    };
  }, []); // FIXED: Empty dependency array for unmount cleanup only

  // Status indicator component
  const StatusIndicator = () => {
    const getStatusColor = () => {
      switch (dataStatus) {
        case 'connected': return 'bg-green-500';
        case 'connecting': return 'bg-yellow-500';
        case 'error': return 'bg-red-500';
        default: return 'bg-gray-500';
      }
    };

    const getStatusText = () => {
      switch (dataStatus) {
        case 'connected': return 'Polygon API Connected';
        case 'connecting': return 'Connecting to Polygon...';
        case 'error': return 'Connection Error';
        default: return 'Disconnected';
      }
    };

    return (
      <div className="absolute top-4 right-4 z-10 bg-black bg-opacity-70 rounded-lg px-3 py-2">
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${getStatusColor()}`}></div>
          <span className="text-white text-xs">{getStatusText()}</span>
        </div>
      </div>
    );
  };

  // Data info component
  const DataInfo = () => {
    if (!lastUpdate || isInitialLoad) return null;

    return (
      <div className="absolute top-16 right-4 z-10 bg-black bg-opacity-70 rounded-lg px-3 py-2">
        <div className="text-white text-xs space-y-1">
          <div>📊 Candles: {candleData.length}</div>
          <div>🔄 Updates: {updateCount}</div>
          <div>⏰ Last: {lastUpdate.toLocaleTimeString()}</div>
          {candleData.length > 0 && (
            <div>📈 Latest: {new Date(candleData[candleData.length - 1].time * 1000).toLocaleTimeString()}</div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="relative w-full h-full">
      {/* Only show error indicator */}
      {error && (
        <div className="absolute top-4 right-4 z-10 bg-red-600 bg-opacity-90 rounded-lg px-3 py-2">
          <span className="text-white text-xs">⚠️ {error}</span>
        </div>
      )}

      {/* Chart component */}
      <OptimizedTradingChart
        candleData={candleData}
        indicators={indicators}
        trades={trades}
        timeframe={displayTimeframe}
        chartTimeframe={chartTimeframe}
        instrument={instrument}
        marketStatus={marketStatus}
        strategyInfo={strategyInfo}
        strategy={strategy}
        isRealTimeUpdate={!isInitialLoad}
        hideDisplayPills={hideDisplayPills}
        hideStrategyPills={hideStrategyPills}
        // Pass through zoom preservation props
        preserveZoom={preserveZoom}
        onZoomChange={onZoomChange}
        initialZoomState={initialZoomState}
        {...otherProps}
      />
    </div>
  );
};

export default PolygonTradingChart;
