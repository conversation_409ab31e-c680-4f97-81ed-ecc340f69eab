import React, { useState, useEffect } from 'react';
import { doc, onSnapshot } from 'firebase/firestore';
import { db } from '../../firebaseConfig';
import { Check, X, Minus, AlertTriangle, Lightbulb, RotateCcw, Clock, Activity, CheckCircle, XCircle } from 'lucide-react';

// Custom hook for live candle progress calculation
const useCandleProgress = (timeframe) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [progress, setProgress] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);

      if (timeframe) {
        // Parse timeframe (e.g., "1m", "5m", "15m", "1h", "4h")
        const multiplier = parseInt(timeframe.match(/\d+/)?.[0] || '1');
        const unit = timeframe.match(/[a-zA-Z]+/)?.[0] || 'm';

        let timeframeMinutes;
        if (unit === 'm') {
          timeframeMinutes = multiplier;
        } else if (unit === 'h') {
          timeframeMinutes = multiplier * 60;
        } else if (unit === 'd') {
          timeframeMinutes = multiplier * 1440;
        } else {
          timeframeMinutes = 1; // Default to 1 minute
        }

        // Calculate the current period boundaries
        let periodStart;
        if (timeframeMinutes < 60) {
          // For minute timeframes, align to the timeframe boundary
          const periodStartMinute = Math.floor(now.getMinutes() / timeframeMinutes) * timeframeMinutes;
          periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), periodStartMinute, 0, 0);
        } else if (timeframeMinutes === 60) {
          // For 1 hour, align to the hour
          periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), 0, 0, 0);
        } else if (timeframeMinutes === 240) {
          // For 4 hours, align to 4-hour boundaries (0, 4, 8, 12, 16, 20)
          const periodStartHour = Math.floor(now.getHours() / 4) * 4;
          periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), periodStartHour, 0, 0, 0);
        } else if (timeframeMinutes === 1440) {
          // For 1 day, align to midnight
          periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
        } else {
          // Default to hour alignment
          periodStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), 0, 0, 0);
        }

        const nextPeriod = new Date(periodStart.getTime() + timeframeMinutes * 60 * 1000);

        // Calculate progress and time remaining
        const totalDuration = timeframeMinutes * 60 * 1000; // in milliseconds
        const elapsed = now - periodStart;
        const remaining = Math.max(0, nextPeriod - now);

        const newProgress = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
        setProgress(newProgress);
        setTimeRemaining(Math.floor(remaining / 1000)); // in seconds
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [timeframe]);

  const formatTimeRemaining = (seconds) => {
    if (seconds <= 0) return "0s";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  return {
    currentTime,
    progress,
    timeRemaining,
    formatTimeRemaining
  };
};

const TradeBotThinking = ({ userId, strategyId, botStatus }) => {
  const [thinkingData, setThinkingData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Use live candle progress calculation
  const candleProgress = useCandleProgress(thinkingData?.candle_timing?.timeframe);

  useEffect(() => {
    if (!userId || !strategyId) return;

    const strategyRef = doc(db, 'users', userId, 'submittedStrategies', strategyId);

    const unsubscribe = onSnapshot(strategyRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        if (data.thinking) {
          setThinkingData(data.thinking);
        }
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, [userId, strategyId]);

  // Don't show thinking data for stopped bots
  if (botStatus === 'stopped') {
    return null;
  }

  if (loading) {
    return (
      <div className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center">
            <RotateCcw className="w-4 h-4 text-blue-400 animate-spin" />
          </div>
          <h3 className="text-lg font-semibold text-white">Trade Agent Analysis</h3>
        </div>
        <div className="text-gray-400">Loading analysis...</div>
      </div>
    );
  }

  // Handle error case from backend - show user-friendly message
  if (thinkingData && thinkingData.error) {
    return (
      <div className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-6 h-6 rounded-full bg-yellow-500/20 flex items-center justify-center">
            <AlertTriangle className="w-4 h-4 text-yellow-400" />
          </div>
          <h3 className="text-lg font-semibold text-white">Trade Agent Analysis</h3>
        </div>
        <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
          <div className="text-yellow-400 text-sm font-medium mb-2">Analysis Temporarily Unavailable</div>
          <div className="text-gray-300 text-sm">
            The agent is currently processing market data and trade analysis.
            This information will be available shortly.
          </div>
          <div className="text-gray-500 text-xs mt-2">
            The agent continues to monitor the market and execute trades normally.
          </div>
        </div>
      </div>
    );
  }

  if (!thinkingData) {
    return (
      <div className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-6 h-6 rounded-full bg-gray-500/20 flex items-center justify-center">
            <Lightbulb className="w-4 h-4 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-white">Trade Agent Analysis</h3>
        </div>
        <div className="text-gray-400">No analysis data available</div>
      </div>
    );
  }

  // Special handling for margin error display
  if (thinkingData.margin_error || thinkingData.bot_status === "insufficient_margin") {
    return (
      <div className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-5 h-5 text-red-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">{thinkingData.explanation?.title || 'Agent Stopped - Insufficient Margin'}</h3>
            <div className="text-sm text-gray-400">Position sizing calculation and solutions</div>
          </div>
        </div>

        {/* Summary */}
        <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4 mb-6">
          <h4 className="text-sm font-medium text-white mb-2">Why the agent stopped:</h4>
          <p className="text-sm text-gray-300">{thinkingData.explanation?.summary}</p>
        </div>

        {/* Position Sizing Calculation */}
        {thinkingData.explanation?.calculation_steps && (
          <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4 mb-6">
            <h4 className="text-sm font-medium text-white mb-3">Position Sizing Calculation:</h4>
            <div className="space-y-2">
              {thinkingData.explanation.calculation_steps.map((step, index) => (
                <div key={index} className="flex items-center text-sm">
                  <div className="w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mr-3">
                    <span className="text-blue-400 text-xs font-medium">{index + 1}</span>
                  </div>
                  <span className="text-gray-300">{step}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Margin Details */}
        {thinkingData.margin_details && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-3 text-center">
              <div className="text-xs text-gray-400 mb-1">Required Margin</div>
              <div className="text-lg font-semibold text-red-400">
                ${thinkingData.margin_details.required_margin?.toLocaleString()}
              </div>
            </div>
            <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-3 text-center">
              <div className="text-xs text-gray-400 mb-1">Available Margin</div>
              <div className="text-lg font-semibold text-yellow-400">
                ${thinkingData.margin_details.available_margin?.toLocaleString()}
              </div>
            </div>
            <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-3 text-center">
              <div className="text-xs text-gray-400 mb-1">Shortfall</div>
              <div className="text-lg font-semibold text-red-400">
                ${thinkingData.margin_details.shortfall?.toLocaleString()}
              </div>
            </div>
          </div>
        )}

        {/* Solutions */}
        {thinkingData.explanation?.solutions && (
          <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
            <h4 className="text-sm font-medium text-white mb-3">💡 Solutions to fix this:</h4>
            <div className="space-y-2">
              {thinkingData.explanation.solutions.map((solution, index) => (
                <div key={index} className="flex items-start text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="text-gray-300">{solution}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Timestamp */}
        <div className="mt-4 text-xs text-gray-500 text-center">
          Last updated: {new Date(thinkingData.timestamp).toLocaleString()} (Local Time)
        </div>
      </div>
    );
  }

  const formatValue = (value, isMACD = false) => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'number') {
      // Use more decimal places for MACD values
      if (isMACD) {
        return value.toFixed(6);
      }
      return value.toFixed(4);
    }
    return String(value);
  };

  const formatRuleValue = (value, description = '') => {
    if (value === null || value === undefined) return 'N/A';
    if (typeof value === 'number') {
      // Use 6 decimal places for MACD components
      if (description.toLowerCase().includes('macd')) {
        return value.toFixed(6);
      }
      // Use 5 decimal places for price values (typically > 1)
      if (value > 1) {
        return value.toFixed(5);
      }
      // Use 4 decimal places for other indicators
      return value.toFixed(4);
    }
    return String(value);
  };

  const getStatusColor = (result) => {
    if (result === true) return 'text-green-400 bg-green-500/10 border-green-500/20';
    if (result === false) return 'text-red-400 bg-red-500/10 border-red-500/20';
    return 'text-gray-400 bg-gray-500/10 border-gray-500/20';
  };

  const getIndicatorColor = (key) => {
    const keyLower = key.toLowerCase();
    if (keyLower.includes('macd') && keyLower.includes('signal')) {
      return '#FF9800'; // Orange for MACD Signal
    } else if (keyLower.includes('macd') && keyLower.includes('histogram')) {
      return '#4CAF50'; // Green for MACD Histogram (could be red for negative values, but we'll keep it simple)
    } else if (keyLower.includes('macd')) {
      return '#2196F3'; // Blue for MACD Line
    } else if (keyLower.includes('rsi')) {
      return '#9C27B0'; // Purple for RSI
    } else if (keyLower.includes('ema')) {
      return '#FF5722'; // Deep Orange for EMA
    } else if (keyLower.includes('bollinger')) {
      return '#607D8B'; // Blue Grey for Bollinger Bands
    }
    return '#FEFEFF'; // Default white
  };

  const getStatusIcon = (result) => {
    if (result === true) {
      return <Check className="w-4 h-4" />;
    }
    if (result === false) {
      return <X className="w-4 h-4" />;
    }
    return <Minus className="w-4 h-4" />;
  };

  const getIndicatorShortName = (indicatorId) => {
    // Convert indicator ID to short readable name for comparison display
    if (indicatorId === 'price') return 'Price';

    // For MACD components, we need to check the rule's macdComponent
    if (indicatorId && indicatorId.includes('1746646401769ml6p3j64kie')) {
      return 'MACD'; // Will be refined by component type in backend
    }
    if (indicatorId && indicatorId.includes('1746646401770ml6p3j64kie')) {
      return 'EMA';
    }

    return 'Indicator';
  };

  const renderConditionAnalysis = (analysis, title, tradeType) => {
    if (!analysis || !analysis.individual_results || analysis.individual_results.length === 0) {
      return (
        <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
          <h4 className="text-sm font-medium text-white mb-2">{title}</h4>
          <div className="text-gray-400 text-sm">No conditions defined</div>
        </div>
      );
    }

    const finalResultColor = analysis.final_result 
      ? 'text-green-400 bg-green-500/10 border-green-500/20' 
      : 'text-red-400 bg-red-500/10 border-red-500/20';

    return (
      <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-medium text-white">{title}</h4>
          <div className={`px-2 py-1 rounded text-xs border ${finalResultColor}`}>
            {analysis.final_result ? 'TRIGGERED' : 'NOT TRIGGERED'}
          </div>
        </div>

        {/* Individual Rules */}
        <div className="space-y-3 mb-4">
          {analysis.individual_results?.map((rule, index) => (
            <div key={rule.rule_id || index} className="bg-[#0A0B0B] border border-[#1a1a1a] rounded p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm text-gray-300">{rule.description}</div>
                <div className={`flex items-center space-x-1 px-2 py-1 rounded text-xs border ${getStatusColor(rule.result)}`}>
                  {getStatusIcon(rule.result)}
                  <span>{rule.result ? 'TRUE' : 'FALSE'}</span>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-xs text-gray-400">
                {rule.is_crossing && rule.compare_type === 'indicator' ? (
                  // For indicator-to-indicator crossing, show the actual indicator names
                  <>
                    <span>{rule.indicator1_name} Prev: <span style={{ color: getIndicatorColor(rule.indicator1_name) }}>{formatRuleValue(rule.previous_value, rule.description)}</span></span>
                    <span>{rule.indicator1_name} Now: <span style={{ color: getIndicatorColor(rule.indicator1_name) }}>{formatRuleValue(rule.current_value, rule.description)}</span></span>
                    <span>{rule.indicator2_name}: <span style={{ color: getIndicatorColor(rule.indicator2_name) }}>{formatRuleValue(rule.compare_value, rule.description)}</span></span>
                  </>
                ) : rule.is_crossing ? (
                  // For crossing conditions, use the indicator name if available, otherwise use Price
                  <>
                    <span>{rule.indicator1_name || 'Price'} Prev: <span style={{ color: getIndicatorColor(rule.indicator1_name || 'price') }}>{formatRuleValue(rule.previous_value, rule.description)}</span></span>
                    <span>{rule.indicator1_name || 'Price'} Now: <span style={{ color: getIndicatorColor(rule.indicator1_name || 'price') }}>{formatRuleValue(rule.current_value, rule.description)}</span></span>
                    <span>{rule.indicator2_name || 'Target'}: <span style={{ color: getIndicatorColor(rule.indicator2_name || '') }}>{formatRuleValue(rule.compare_value, rule.description)}</span></span>
                  </>
                ) : (
                  // For non-crossing comparisons
                  <>
                    <span>{rule.indicator1_name || 'Current'}: <span style={{ color: getIndicatorColor(rule.indicator1_name || '') }}>{formatRuleValue(rule.current_value, rule.description)}</span></span>
                    <span>{rule.indicator2_name || 'Target'}: <span style={{ color: getIndicatorColor(rule.indicator2_name || '') }}>{formatRuleValue(rule.compare_value, rule.description)}</span></span>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Group Logic */}
        <div className="flex items-center justify-between text-xs">
          <div className="text-gray-400">
            Logic: <span className="text-white font-medium">{analysis.group_operator}</span>
          </div>
          <div className="text-gray-400">
            {analysis.reason}
          </div>
        </div>
      </div>
    );
  };



  return (
    <div className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-lg p-6">
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-6 h-6 rounded-full bg-blue-500/20 flex items-center justify-center">
          <Lightbulb className="w-4 h-4 text-blue-400" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white">Trade Agent Analysis</h3>
          <div className="text-sm text-gray-400">
            {thinkingData.evaluation_mode === 'exit' ? 'Checking exit conditions' : 'Checking entry conditions'}
            {thinkingData.has_open_trades && thinkingData.open_trade_types?.length > 0 && (
              <span className="ml-2 px-2 py-0.5 bg-yellow-500/20 text-yellow-400 rounded text-xs">
                {thinkingData.open_trade_types.join(', ').toUpperCase()} POSITION OPEN
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Candle Timing Information */}
      {thinkingData.candle_timing && !thinkingData.candle_timing.error && (
        <div className="mb-6 bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-blue-400" />
              <span className="text-sm font-medium text-white">
                Last analysis at {new Date(thinkingData.timestamp).toLocaleString()} (Local Time)
              </span>
              <span className="text-xs text-gray-500">
                ({thinkingData.candle_timing.timeframe} timeframe)
              </span>
            </div>
            <div className="text-xs text-gray-400">
              {thinkingData.candle_timing.timeframe} timeframe
            </div>
          </div>

          {/* Live Progress Bar */}
          <div className="mb-2">
            <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
              <span>Progress to next {thinkingData.candle_timing.timeframe} candle</span>
              <span>{candleProgress.formatTimeRemaining(candleProgress.timeRemaining)} remaining</span>
            </div>
            <div className="w-full bg-[#0A0B0B] rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-blue-400 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${candleProgress.progress}%` }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1 text-center">
              {candleProgress.progress.toFixed(1)}% complete
            </div>
          </div>

          {/* Live Clock Display */}
          <div className="text-xs text-gray-500 text-center">
            Current time: {candleProgress.currentTime.toLocaleTimeString()} (Local Time)
          </div>
        </div>
      )}

      {/* Current Market Info */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-3">
          <div className="text-xs text-gray-400 mb-1">Current Price</div>
          <div className="text-lg font-semibold text-white">{formatValue(thinkingData.current_price)}</div>
        </div>

        {/* Show MACD components specifically */}
        {Object.entries(thinkingData.current_indicators || {})
          .filter(([key, value]) => {
            // Show MACD Signal, MACD, and MACD Histogram specifically
            const keyLower = key.toLowerCase();
            return keyLower.includes('macd');
          })
          .sort(([keyA], [keyB]) => {
            // Sort to ensure consistent order: MACD Signal, MACD, MACD Histogram
            if (keyA.toLowerCase().includes('signal')) return -1;
            if (keyB.toLowerCase().includes('signal')) return 1;
            if (keyA.toLowerCase().includes('histogram')) return 1;
            if (keyB.toLowerCase().includes('histogram')) return -1;
            return 0;
          })
          .slice(0, 3)
          .map(([key, value]) => {
          // The backend now provides properly formatted key names with actual strategy parameters
          let displayName = key;
          let isMACD = false;

          // Check if this is a MACD-related indicator for proper decimal formatting
          if (key.toLowerCase().includes('macd') || key.toLowerCase().includes('signal') || key.toLowerCase().includes('histogram')) {
            isMACD = true;
          }

          // If the key is already formatted from backend (contains parentheses), use it as is
          // This means the backend _get_display_key_name method worked correctly
          if (key.includes('(')) {
            displayName = key; // Use the properly formatted name from backend
          } else {
            // Fallback formatting for any keys that didn't get processed by backend
            if (key.toLowerCase() === 'signal') {
              isMACD = true;
              displayName = 'MACD Signal (12, 26, 9)';
            } else if (key.toLowerCase() === 'histogram') {
              isMACD = true;
              displayName = 'MACD Histogram (12, 26, 9)';
            } else if (key.toLowerCase() === 'macd') {
              isMACD = true;
              displayName = 'MACD (12, 26, 9)';
            } else {
              // Clean up the key for display
              displayName = key.replace(/_/g, ' ').replace(/[0-9]+[a-z]+/gi, '').trim();
              if (displayName.toLowerCase().includes('ema')) {
                displayName = 'EMA (20)';
              } else if (displayName.toLowerCase().includes('rsi')) {
                displayName = 'RSI (14)';
              } else if (displayName.toLowerCase().includes('bollinger') || displayName.toLowerCase().includes('bb ')) {
                // The backend now sends properly formatted names, so just use them as-is
                // No need to transform further since backend handles the formatting
                displayName = key;
              }
            }
          }

          return (
            <div key={key} className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-3">
              <div className="text-xs text-gray-400 mb-1">{displayName}</div>
              <div
                className="text-lg font-semibold"
                style={{ color: getIndicatorColor(key) }}
              >
                {formatValue(value, isMACD)}
              </div>
            </div>
          );
        })}
      </div>

      {/* Frozen Entry Conditions (when trades are open) */}
      {thinkingData.has_open_trades && thinkingData.frozen_entry_conditions && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-white mb-4 flex items-center space-x-2">
            <Activity className="w-4 h-4 text-yellow-400" />
            <span>Entry Conditions (Frozen at Trade Open)</span>
          </h4>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {Object.entries(thinkingData.frozen_entry_conditions).map(([tradeType, conditions]) => (
              <div key={tradeType} className="bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h5 className="text-sm font-medium text-white capitalize">{tradeType} Entry Conditions</h5>
                  <div className="px-2 py-1 rounded text-xs border bg-yellow-500/10 border-yellow-500/20 text-yellow-400">
                    FROZEN
                  </div>
                </div>

                {conditions.note ? (
                  <div className="text-sm text-gray-400">{conditions.note}</div>
                ) : (
                  <div className="space-y-2">
                    {conditions.individual_results?.map((rule, index) => (
                      <div key={index} className="bg-[#0A0B0B] border border-[#1a1a1a] rounded p-3">
                        <div className="text-sm text-gray-300 mb-1">{rule.description}</div>
                        <div className="text-xs text-gray-500">
                          Triggered at trade entry: {rule.result ? 'TRUE' : 'FALSE'}
                        </div>
                      </div>
                    )) || <div className="text-sm text-gray-400">Entry conditions not available</div>}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Analysis Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Show conditions based on evaluation mode and open positions */}
        {thinkingData.evaluation_mode === 'exit' ? (
          // When evaluating exit conditions, only show conditions for open trade types
          <>
            {thinkingData.open_trade_types?.includes('long') && renderConditionAnalysis(
              thinkingData.long_analysis,
              'Long Exit Conditions',
              'long'
            )}
            {thinkingData.open_trade_types?.includes('short') && renderConditionAnalysis(
              thinkingData.short_analysis,
              'Short Exit Conditions',
              'short'
            )}
          </>
        ) : (
          // When evaluating entry conditions, show both long and short entry conditions
          <>
            {renderConditionAnalysis(
              thinkingData.long_analysis,
              'Long Entry Conditions',
              'long'
            )}
            {renderConditionAnalysis(
              thinkingData.short_analysis,
              'Short Entry Conditions',
              'short'
            )}
          </>
        )}
      </div>

      {/* Position Sizing & Risk Management */}
      {thinkingData.position_info && (
        <div className="mt-6 bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
          <h4 className="text-sm font-medium text-white mb-4">Position Sizing & Risk Management</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-xs text-gray-400 mb-1">Position Size</div>
              <div className="text-sm font-semibold text-white">
                {thinkingData.position_info.position_size?.toLocaleString()} units
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-400 mb-1">Stop Loss</div>
              <div className="text-sm font-semibold text-red-400">
                {formatValue(thinkingData.position_info.stop_loss_pips)} pips
              </div>
              <div className="text-xs text-gray-500">
                ${formatValue(thinkingData.position_info.stop_loss_dollars)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-400 mb-1">Take Profit</div>
              <div className="text-sm font-semibold text-green-400">
                {formatValue(thinkingData.position_info.take_profit_pips)} pips
              </div>
              <div className="text-xs text-gray-500">
                ${formatValue(thinkingData.position_info.take_profit_dollars)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-400 mb-1">Risk</div>
              <div className="text-sm font-semibold text-yellow-400">
                {formatValue(thinkingData.position_info.risk_percentage)}%
              </div>
              <div className="text-xs text-gray-500">
                of ${thinkingData.position_info.account_balance?.toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Final Signal */}
      {thinkingData.signal_result && (
        <div className="mt-6 bg-[#0F1011] border border-[#1a1a1a] rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-white">Final Decision</h4>
            <div className={`px-3 py-1 rounded text-sm border ${
              thinkingData.signal_result.action && thinkingData.signal_result.action !== 'None'
                ? 'text-green-400 bg-green-500/10 border-green-500/20'
                : 'text-gray-400 bg-gray-500/10 border-gray-500/20'
            }`}>
              {thinkingData.signal_result.action || 'NO ACTION'}
            </div>
          </div>
          <div className="text-sm text-gray-400 mt-2">
            {thinkingData.signal_result.reason || 'No signals generated'}
          </div>
        </div>
      )}

      {/* Timestamp */}
      <div className="mt-4 text-xs text-gray-500 text-center">
        Last updated: {thinkingData.timestamp ? new Date(thinkingData.timestamp).toLocaleString() : 'Invalid Date'}
      </div>
    </div>
  );
};

export default TradeBotThinking;
