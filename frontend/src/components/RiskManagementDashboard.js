import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, Shield, Clock } from 'lucide-react';

const RiskManagementDashboard = ({
  accountBalance = 0,
  totalProfit = 0,
  totalLoss = 0,
  dailyLoss = 0,
  riskManagement = {},
  botRuntime = null, // Fallback if not in riskManagement
  tradingPeriod = 'Trading period'
}) => {
  console.log('🔍 RiskManagementDashboard - Props:', {
    accountBalance,
    totalProfit,
    totalLoss,
    dailyLoss,
    riskManagement,
    botRuntime
  });
  // Format currency values
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(Math.abs(value));
  };

  // Calculate risk management targets and progress
  const calculateRiskMetrics = () => {
    // Use Firebase metrics if available, otherwise calculate from percentages
    const metrics = riskManagement?.metrics || {};
    const parameters = riskManagement?.parameters || {};

    console.log('🔍 RiskManagementDashboard - Firebase metrics:', metrics);
    console.log('🔍 RiskManagementDashboard - Firebase parameters:', parameters);

    // Use absolute values from Firebase if available, otherwise calculate
    const totalProfitTarget = metrics.totalProfitTargetAbs ||
      (accountBalance * parseFloat(parameters.totalProfitTarget?.replace('%', '') || 20)) / 100;
    const totalLossLimit = metrics.totalLossLimitAbs ||
      (accountBalance * parseFloat(parameters.totalLossLimit?.replace('%', '') || 10)) / 100;
    const dailyLossLimit = metrics.dailyLossLimitAbs ||
      (accountBalance * parseFloat(parameters.maxDailyLoss?.replace('%', '') || 5)) / 100;

    console.log('🔍 RiskManagementDashboard - Calculated targets:', {
      totalProfitTarget,
      totalLossLimit,
      dailyLossLimit
    });

    // Use actual values from Firebase metrics if available
    const actualTotalProfit = metrics.totalProfit !== undefined ? metrics.totalProfit : totalProfit;
    const actualTotalLoss = metrics.totalLoss !== undefined ? metrics.totalLoss : totalLoss;
    const actualDailyLoss = metrics.dailyLoss !== undefined ? metrics.dailyLoss : dailyLoss;
    const actualAccountBalance = metrics.accountBalance !== undefined ? metrics.accountBalance : accountBalance;

    console.log('🔍 RiskManagementDashboard - Actual values:', {
      actualTotalProfit,
      actualTotalLoss,
      actualDailyLoss,
      actualAccountBalance
    });

    // Calculate progress percentages
    const totalProfitProgress = totalProfitTarget > 0 ? Math.min((actualTotalProfit / totalProfitTarget) * 100, 100) : 0;
    const totalLossProgress = totalLossLimit > 0 ? Math.min((Math.abs(actualTotalLoss) / totalLossLimit) * 100, 100) : 0;
    const dailyLossProgress = dailyLossLimit > 0 ? Math.min((Math.abs(actualDailyLoss) / dailyLossLimit) * 100, 100) : 0;

    // Agent runtime progress - use Firebase metrics if available
    const configuredRuntimeDays = parameters.runtime || botRuntime || 7;
    let runtimeProgress = metrics.runtimeProgress || 0;
    let runtimeDisplay = '0 days';
    let runtimeTarget = '0 days';

    if (configuredRuntimeDays !== null && configuredRuntimeDays !== undefined) {
      const runtimeDays = parseInt(configuredRuntimeDays);
      const elapsedDays = metrics.elapsedDays || 0;
      const daysRemaining = metrics.daysRemaining || runtimeDays;

      if (runtimeDays > 0) {
        // Use actual elapsed days from Firebase
        runtimeDisplay = `${elapsedDays.toFixed(1)} days`;
        runtimeTarget = `${runtimeDays} days`;
        runtimeProgress = metrics.runtimeProgress || 0;
      }
    }

    console.log('🔍 RiskManagementDashboard - Runtime values:', {
      configuredRuntimeDays,
      runtimeProgress,
      runtimeDisplay,
      runtimeTarget
    });

    // Get percentage values for display
    const totalProfitTargetPercent = parseFloat(parameters.totalProfitTarget?.replace('%', '') || 20);
    const totalLossLimitPercent = parseFloat(parameters.totalLossLimit?.replace('%', '') || 10);
    const dailyLossLimitPercent = parseFloat(parameters.maxDailyLoss?.replace('%', '') || 5);

    return {
      totalProfitTarget,
      totalLossLimit,
      dailyLossLimit,
      totalProfitProgress,
      totalLossProgress,
      dailyLossProgress,
      runtimeProgress,
      runtimeDisplay,
      runtimeTarget,
      totalProfitTargetPercent,
      totalLossLimitPercent,
      dailyLossLimitPercent,
      actualTotalProfit,
      actualTotalLoss,
      actualDailyLoss,
      actualAccountBalance
    };
  };

  const metrics = calculateRiskMetrics();

  // Risk management items configuration with wireframe-matching icons
  const riskItems = [
    {
      title: 'TOTAL PROFIT TARGET',
      current: formatCurrency(metrics.actualTotalProfit),
      target: formatCurrency(metrics.totalProfitTarget),
      progress: metrics.totalProfitProgress,
      progressText: `${metrics.totalProfitProgress.toFixed(1)}% complete`,
      subtitle: `${metrics.totalProfitTargetPercent}% of balance`,
      color: 'bg-green-500',
      icon: <TrendingUp className="w-4 h-4" />,
      iconColor: 'text-green-400'
    },
    {
      title: 'TOTAL LOSS LIMIT',
      current: formatCurrency(metrics.actualTotalLoss),
      target: formatCurrency(metrics.totalLossLimit),
      progress: metrics.totalLossProgress,
      progressText: `${metrics.totalLossProgress.toFixed(1)}% of limit`,
      subtitle: `${metrics.totalLossLimitPercent}% of balance`,
      color: 'bg-red-500',
      icon: <TrendingDown className="w-4 h-4" />,
      iconColor: 'text-red-400'
    },
    {
      title: 'DAILY LOSS LIMIT',
      current: formatCurrency(metrics.actualDailyLoss),
      target: formatCurrency(metrics.dailyLossLimit),
      progress: metrics.dailyLossProgress,
      progressText: `${metrics.dailyLossProgress.toFixed(1)}% of limit`,
      subtitle: `${metrics.dailyLossLimitPercent}% of balance`,
      color: 'bg-orange-500',
      icon: <Shield className="w-4 h-4" />,
      iconColor: 'text-orange-400'
    },
    {
      title: 'AGENT RUNTIME',
      current: metrics.runtimeDisplay,
      target: metrics.runtimeTarget,
      progress: metrics.runtimeProgress,
      progressText: metrics.runtimeDisplay !== 'N/A' ? 'Runtime configured' : 'Not configured',
      subtitle: tradingPeriod,
      color: 'bg-blue-500',
      icon: <Clock className="w-4 h-4" />,
      iconColor: 'text-blue-400'
    }
  ];

  return (
    <div className="bg-[#0A0B0B] px-6 py-3">
      {/* Single Card Container */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] p-6"
      >
        {/* Section Header */}
        <div className="mb-4">
          <h2 className="text-sm font-medium text-[#FEFEFF]/80">Risk Management</h2>
        </div>

        {/* Risk Management Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {riskItems.map((item, index) => (
            <div key={item.title} className="space-y-3">
              {/* Header with Icon and Title */}
              <div className="flex items-center space-x-2">
                <div className={`${item.iconColor}`}>
                  {item.icon}
                </div>
                <h3 className="text-xs font-medium text-[#FEFEFF]/50 uppercase tracking-wide">
                  {item.title}
                </h3>
              </div>

              {/* Values */}
              <div className="space-y-1">
                <div className="flex items-baseline space-x-2">
                  <span className="text-lg font-bold text-[#FEFEFF]">
                    {item.current}
                  </span>
                  <span className="text-xs text-[#FEFEFF]/40">
                    / {item.target}
                  </span>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="w-full bg-[#0A0B0B] rounded-full h-1.5">
                  <motion.div
                    className={`h-1.5 rounded-full ${item.color}`}
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.min(item.progress, 100)}%` }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                  />
                </div>

                {/* Progress Text and Subtitle */}
                <div className="flex justify-between items-center text-xs">
                  <span className="text-[#FEFEFF]/60 font-medium">
                    {item.progressText}
                  </span>
                  <span className="text-[#FEFEFF]/40">
                    {item.subtitle}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default RiskManagementDashboard;
