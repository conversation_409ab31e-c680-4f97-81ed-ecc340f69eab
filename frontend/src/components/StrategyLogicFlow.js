import React, { useMemo } from 'react';

const StrategyLogicFlow = ({ strategy }) => {
  console.log("Strategy passed to StrategyLogicFlow:", strategy);

  if (!strategy) return null;

  // Parse the strategy if it's a string
  let parsedStrategy = strategy;
  if (typeof strategy === 'string') {
    try {
      parsedStrategy = JSON.parse(strategy);
      console.log("Parsed strategy JSON:", parsedStrategy);
    } catch (e) {
      console.error('Error parsing strategy JSON:', e);
      return <div className="text-red-500">Error parsing strategy JSON</div>;
    }
  }

  const {
    name,
    description,
    instruments,
    timeframe,
    tradingSession = [],
    indicators = [],
    entryRules = [],
    exitRules = [],
    riskManagement: initialRiskManagement = {},
    risk_management = {},
    parameters = {},
    entryLongGroupOperator = 'AND',
    entryShortGroupOperator = 'AND',
    exitLongGroupOperator = 'OR',
    exitShortGroupOperator = 'OR'
  } = parsedStrategy;

  // Combine risk management data from all possible sources
  const riskManagement = {
    ...initialRiskManagement,
    ...risk_management,
    ...(parameters.stopLoss || parameters.takeProfit ? parameters : {})
  };

  // Extract risk management values
  const riskPercentage = riskManagement?.riskPercentage || 'N/A';
  const riskRewardRatio = riskManagement?.riskRewardRatio || 'N/A';
  const stopLossMethod = riskManagement?.stopLossMethod || 'fixed';
  const fixedPips = riskManagement?.fixedPips || '50';
  const indicatorBasedSL = riskManagement?.indicatorBasedSL?.indicator || 'N/A';
  const lotSize = riskManagement?.lotSize || 'N/A';

  // Memoized indicator label function
  const getIndicatorLabel = useMemo(() => {
    const labelCache = new Map();

    return (val, rule, isSecondIndicator = false) => {
      const cacheKey = `${val}-${rule?.id || 'no-rule'}-${isSecondIndicator}`;

      if (labelCache.has(cacheKey)) {
        return labelCache.get(cacheKey);
      }

      let result;

      if (val === "price") {
        result = "Price";
      } else {
        const foundInSelected = indicators.find((ind) => ind.id === val);
        if (foundInSelected) {
          // Handle Bollinger Bands
          if (foundInSelected.type === 'BollingerBands' || foundInSelected.indicator_class === 'BollingerBands') {
            const bandProperty = isSecondIndicator ? rule?.band2 : rule?.band;
            if (bandProperty) {
              const bandLabel = bandProperty === 'upper' ? 'Upper Band' :
                               bandProperty === 'lower' ? 'Lower Band' : 'Middle Band';
              result = `${foundInSelected.type || foundInSelected.indicator_class} ${bandLabel} (${foundInSelected.parameters.period})`;
            } else {
              result = `${foundInSelected.type || foundInSelected.indicator_class} (${foundInSelected.parameters.period})`;
            }
          }
          // Handle MACD
          else if (foundInSelected.type === 'MACD' || foundInSelected.indicator_class === 'MACD') {
            const macdComponentProperty = isSecondIndicator ? rule?.macdComponent2 : rule?.macdComponent;
            if (macdComponentProperty) {
              const componentLabel = macdComponentProperty === 'signal' ? 'Signal Line' : 'MACD Line';
              result = `${foundInSelected.type || foundInSelected.indicator_class} ${componentLabel} (${foundInSelected.parameters.fast},${foundInSelected.parameters.slow},${foundInSelected.parameters.signal})`;
            } else {
              result = `${foundInSelected.type || foundInSelected.indicator_class} (${foundInSelected.parameters.fast},${foundInSelected.parameters.slow},${foundInSelected.parameters.signal})`;
            }
          }
          // Handle other indicators
          else {
            const paramStr = foundInSelected.parameters.period ?
              foundInSelected.parameters.period :
              Object.values(foundInSelected.parameters).join(',');
            result = `${foundInSelected.type || foundInSelected.indicator_class} (${paramStr})`;
          }
        } else {
          result = "N/A";
        }
      }

      labelCache.set(cacheKey, result);

      if (labelCache.size > 100) {
        const firstKey = labelCache.keys().next().value;
        labelCache.delete(firstKey);
      }

      return result;
    };
  }, [indicators]);

  return (
    <div className="bg-[#0F1011] rounded-xl p-6 border border-[#1a1a1a]">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-[#FEFEFF] mb-2">Strategy Logic Flow</h3>
        <p className="text-[#FEFEFF]/60 text-sm">Visual representation of your trading strategy conditions and execution</p>
      </div>

      {/* Entry Conditions Section */}
      <div className="mb-8">
        <h4 className="text-lg font-bold text-[#FEFEFF] mb-4 flex items-center gap-2">
          <span className="w-2 h-2 bg-[#EFBD3A] rounded-full"></span>
          Entry Conditions
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-end">
          {/* Long Entry Signal */}
          {entryRules.filter(r => r.tradeType === 'long').length > 0 && (
            <div className="flex flex-col h-full">
              <div className="flex items-center gap-3 mb-4">
                <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                <h5 className="text-lg font-bold text-green-400">Long Entry Signal</h5>
              </div>

              <div className="space-y-3 flex-grow">
                {entryRules.filter(r => r.tradeType === 'long').map((rule, index) => (
                  <div key={rule.id} className="relative">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-green-500/30 rounded-lg flex items-center justify-center border border-green-500/60 flex-shrink-0 mt-1">
                        <span className="text-green-300 font-bold text-sm">{index + 1}</span>
                      </div>
                      <div className="flex-1 bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
                        <div className="text-[#FEFEFF] font-medium">
                          {getIndicatorLabel(rule.indicator1, rule)}
                          <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                          {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                        </div>
                        {rule.indicator1 === 'price' && (
                          <div className="text-[#FEFEFF]/60 text-sm mt-1">
                            Reference: {rule.barRef}
                          </div>
                        )}
                      </div>
                    </div>
                    {index < entryRules.filter(r => r.tradeType === 'long').length - 1 && (
                      <div className="flex justify-center my-2">
                        <div className="bg-[#1a1a1a] text-[#FEFEFF] px-2 py-1 rounded text-xs font-medium">
                          {entryLongGroupOperator}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Arrow pointing down */}
              <div className="flex justify-center my-3">
                <svg className="w-4 h-4 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </div>

              {/* Execute Long Box */}
              <div className="bg-[#0A0B0B] border border-green-500/30 rounded-lg p-4 text-center min-h-[80px] flex flex-col justify-center">
                <div className="text-green-400 font-semibold text-base mb-1">Execute Long Position</div>
                <div className="text-[#FEFEFF]/60 text-sm">
                  Risk: {riskPercentage}% | Reward Ratio: {riskRewardRatio}:1
                </div>
              </div>
            </div>
          )}

          {/* Short Entry Signal */}
          {entryRules.filter(r => r.tradeType === 'short').length > 0 && (
            <div className="flex flex-col h-full">
              <div className="flex items-center gap-3 mb-4">
                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                </svg>
                <h5 className="text-lg font-bold text-red-400">Short Entry Signal</h5>
              </div>

              <div className="space-y-4 flex-grow">
                {entryRules.filter(r => r.tradeType === 'short').map((rule, index) => (
                  <div key={rule.id} className="relative">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-red-500/30 rounded-lg flex items-center justify-center border border-red-500/60 flex-shrink-0 mt-1">
                        <span className="text-red-300 font-bold text-sm">{index + 1}</span>
                      </div>
                      <div className="flex-1 bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
                        <div className="text-[#FEFEFF] font-medium">
                          {getIndicatorLabel(rule.indicator1, rule)}
                          <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                          {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                        </div>
                        {rule.indicator1 === 'price' && (
                          <div className="text-[#FEFEFF]/60 text-sm mt-1">
                            Reference: {rule.barRef}
                          </div>
                        )}
                      </div>
                    </div>
                    {index < entryRules.filter(r => r.tradeType === 'short').length - 1 && (
                      <div className="flex justify-center my-3">
                        <div className="bg-[#1a1a1a] text-[#FEFEFF] px-2 py-1 rounded text-xs font-medium">
                          {entryShortGroupOperator}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Arrow pointing down */}
              <div className="flex justify-center my-3">
                <svg className="w-4 h-4 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </div>

              {/* Execute Short Box */}
              <div className="bg-[#0A0B0B] border border-red-500/30 rounded-lg p-4 text-center min-h-[80px] flex flex-col justify-center">
                <div className="text-red-400 font-semibold text-base mb-1">Execute Short Position</div>
                <div className="text-[#FEFEFF]/60 text-sm">
                  Risk: {riskPercentage}% | Reward Ratio: {riskRewardRatio}:1
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Exit Conditions Section */}
      <div className="mb-8">
        <h4 className="text-lg font-bold text-[#FEFEFF] mb-4 flex items-center gap-2">
          <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
          Exit Conditions
        </h4>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-end">
          {/* Long Exit Signal */}
          {exitRules.filter(r => r.tradeType === 'long').length > 0 && (
            <div className="flex flex-col h-full">
              <div className="flex items-center gap-3 mb-4">
                <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <h5 className="text-lg font-bold text-green-400">Long Exit Signal</h5>
              </div>

              <div className="space-y-4 flex-grow">
                {exitRules.filter(r => r.tradeType === 'long').map((rule, index) => (
                  <div key={rule.id} className="relative">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-green-500/30 rounded-lg flex items-center justify-center border border-green-500/60 flex-shrink-0 mt-1">
                        <span className="text-green-300 font-bold text-sm">{index + 1}</span>
                      </div>
                      <div className="flex-1 bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
                        <div className="text-[#FEFEFF] font-medium">
                          {getIndicatorLabel(rule.indicator1, rule, false)}
                          <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                          {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                        </div>
                        {rule.indicator1 === 'price' && (
                          <div className="text-[#FEFEFF]/60 text-sm mt-1">
                            Reference: {rule.barRef}
                          </div>
                        )}
                      </div>
                    </div>
                    {index < exitRules.filter(r => r.tradeType === 'long').length - 1 && (
                      <div className="flex justify-center my-3">
                        <div className="bg-[#1a1a1a] text-[#FEFEFF] px-2 py-1 rounded text-xs font-medium">
                          {exitLongGroupOperator}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Arrow pointing down */}
              <div className="flex justify-center my-3">
                <svg className="w-4 h-4 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </div>

              {/* Close Long Box */}
              <div className="bg-[#0A0B0B] border border-green-500/30 rounded-lg p-4 text-center min-h-[80px] flex flex-col justify-center">
                <div className="text-green-400 font-semibold text-base mb-1">Close Long Position</div>
                <div className="text-[#FEFEFF]/60 text-sm">
                  Exit conditions met
                </div>
              </div>
            </div>
          )}

          {/* Short Exit Signal */}
          {exitRules.filter(r => r.tradeType === 'short').length > 0 && (
            <div className="flex flex-col h-full">
              <div className="flex items-center gap-3 mb-4">
                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <h5 className="text-lg font-bold text-red-400">Short Exit Signal</h5>
              </div>

              <div className="space-y-4 flex-grow">
                {exitRules.filter(r => r.tradeType === 'short').map((rule, index) => (
                  <div key={rule.id} className="relative">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-red-500/30 rounded-lg flex items-center justify-center border border-red-500/60 flex-shrink-0 mt-1">
                        <span className="text-red-300 font-bold text-sm">{index + 1}</span>
                      </div>
                      <div className="flex-1 bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
                        <div className="text-[#FEFEFF] font-medium">
                          {getIndicatorLabel(rule.indicator1, rule, false)}
                          <span className="text-[#EFBD3A] mx-2">{rule.operator}</span>
                          {rule.compareType === 'value' ? rule.value : getIndicatorLabel(rule.indicator2, rule, true)}
                        </div>
                        {rule.indicator1 === 'price' && (
                          <div className="text-[#FEFEFF]/60 text-sm mt-1">
                            Reference: {rule.barRef}
                          </div>
                        )}
                      </div>
                    </div>
                    {index < exitRules.filter(r => r.tradeType === 'short').length - 1 && (
                      <div className="flex justify-center my-3">
                        <div className="bg-[#1a1a1a] text-[#FEFEFF] px-2 py-1 rounded text-xs font-medium">
                          {exitShortGroupOperator}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Arrow pointing down */}
              <div className="flex justify-center my-3">
                <svg className="w-4 h-4 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
              </div>

              {/* Close Short Box */}
              <div className="bg-[#0A0B0B] border border-red-500/30 rounded-lg p-4 text-center min-h-[80px] flex flex-col justify-center">
                <div className="text-red-400 font-semibold text-base mb-1">Close Short Position</div>
                <div className="text-[#FEFEFF]/60 text-sm">
                  Exit conditions met
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Risk Management Summary */}
        <div className="mt-6">
          <div className="flex items-center gap-2 mb-3">
            <span className="w-2 h-2 bg-[#EFBD3A] rounded-full"></span>
            <h5 className="text-base font-bold text-[#FEFEFF]">Risk Management</h5>
          </div>

          <div className="bg-[#0A0B0B] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="flex flex-wrap gap-6 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-[#FEFEFF]/60">Stop Loss:</span>
                <span className="text-red-400 font-medium">
                  {stopLossMethod === 'fixed' ? `${fixedPips} pips` :
                   stopLossMethod === 'indicator' ? `${indicatorBasedSL}` :
                   `${lotSize} lots`}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-[#FEFEFF]/60">Take Profit:</span>
                <span className="text-green-400 font-medium">
                  {stopLossMethod === 'fixed' ? `${(fixedPips * riskRewardRatio).toFixed(0)} pips` :
                   `${riskRewardRatio}x SL`}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-[#FEFEFF]/60">Risk per Trade:</span>
                <span className="text-[#EFBD3A] font-medium">{riskPercentage}%</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-[#FEFEFF]/60">Risk-Reward Ratio:</span>
                <span className="text-[#EFBD3A] font-medium">{riskRewardRatio}:1</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StrategyLogicFlow;
