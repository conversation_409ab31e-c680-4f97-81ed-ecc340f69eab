import React, { useEffect } from 'react';

const formatNumber = (value, options = {}) => {
  if (value === undefined || value === null) return 'N/A';
  return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2, ...options });
};

const BacktestResults = ({ results, loading, error }) => {
  // Add more detailed debug console log
  useEffect(() => {
    if (results && results.performance) {
      console.log("BacktestResults received performance data:", {
        initial_value: results.performance.initial_value,
        final_value: results.performance.final_value,
        pnl: results.performance.pnl,
        roi_percent: results.performance.roi_percent,
        total_trades: results.performance.total_trades,
        win_rate: results.performance.win_rate
      });
    }
  }, [results]);

  if (loading) {
    return (
      <div className="p-6 bg-[#1a1a1a] rounded-xl border border-[#3a3a3a]">
        <h2 className="text-2xl font-bold text-[#FEFEFF] mb-4">Running Backtest...</h2>
        <div className="flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#EFBD3A]"></div>
        </div>
      </div>
    );
  }

  if (error && error !== "Completed") {
    return (
      <div className="p-6 bg-[#1a1a1a] rounded-xl border border-[#3a3a3a]">
        <h2 className="text-2xl font-bold text-[#FEFEFF] mb-4">Backtest Error</h2>
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  if (!results || !results.performance) {
    return null;
  }

  const { 
    initial_value = 0, 
    final_value = 0, 
    pnl = 0, 
    roi_percent = 0, 
    total_trades = 0, 
    win_rate = 0,
    winning_trades = 0,
    losing_trades = 0,
    average_win = 0,
    average_loss = 0,
    profit_factor = 0,
    best_trade = 0,
    worst_trade = 0,
    max_drawdown = 0,
    sharpe_ratio = 0,
    expectancy = 0
  } = results.performance;

  return (
    <div id="backtest-results" className="space-y-8">
      <div className="flex items-center gap-3 mb-6">
        <svg className="w-6 h-6 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h2 className="text-2xl font-bold text-[#FEFEFF]">Backtest Results</h2>
      </div>

      {/* Performance Summary */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-[#FEFEFF] flex items-center gap-2">
          <span className="w-2 h-2 bg-[#EFBD3A] rounded-full"></span>
          Performance Summary
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Initial Balance</h4>
              <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-[#FEFEFF]">${formatNumber(initial_value)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Final Balance</h4>
              <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-[#FEFEFF]">${formatNumber(final_value)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Total Return</h4>
              <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <p className={`text-2xl font-bold ${roi_percent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatNumber(roi_percent)}%
            </p>
          </div>
        </div>
      </div>

      {/* Trade Statistics */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-[#FEFEFF] flex items-center gap-2">
          <span className="w-2 h-2 bg-green-400 rounded-full"></span>
          Trade Statistics
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Total Trades</h4>
              <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-[#FEFEFF]">{total_trades || 0}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Winning Trades</h4>
              <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-green-400">{winning_trades || 0}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Losing Trades</h4>
              <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-red-400">{losing_trades || 0}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Win Rate</h4>
              <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-blue-400">{formatNumber(win_rate)}%</p>
          </div>
        </div>
      </div>

      {/* Risk & Performance Metrics */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-[#FEFEFF] flex items-center gap-2">
          <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
          Risk & Performance Metrics
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Average Win</h4>
              <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-green-400">${formatNumber(average_win)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Average Loss</h4>
              <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-red-400">${formatNumber(average_loss)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Profit Factor</h4>
              <svg className="w-4 h-4 text-[#EFBD3A]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-[#EFBD3A]">{formatNumber(profit_factor)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Best Trade</h4>
              <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-green-400">${formatNumber(best_trade)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Worst Trade</h4>
              <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-red-400">${formatNumber(worst_trade)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Max Drawdown</h4>
              <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-red-400">{formatNumber(max_drawdown)}%</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Sharpe Ratio</h4>
              <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-blue-400">{formatNumber(sharpe_ratio)}</p>
          </div>

          <div className="bg-[#2a2a2a] p-6 rounded-lg border border-[#3a3a3a]">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-[#FEFEFF]/60">Expectancy</h4>
              <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-purple-400">${formatNumber(expectancy)}</p>
          </div>
        </div>
      </div>

      {/* Equity Curve */}
      {results.equity_curve && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-[#FEFEFF] flex items-center gap-2">
            <span className="w-2 h-2 bg-[#EFBD3A] rounded-full"></span>
            Equity Curve
          </h3>
          <div className="bg-[#2a2a2a] p-4 rounded-lg border border-[#3a3a3a]">
            <img
              src={`data:image/png;base64,${results.equity_curve}`}
              alt="Equity Curve"
              className="w-full rounded-lg"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default BacktestResults; 