import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const RiskManagementPanel = ({ riskManagement, accountBalance, totalProfit, totalLoss, dailyLoss, metrics }) => {
  // State to store parsed risk management parameters
  const [parsedParams, setParsedParams] = useState({
    // New risk management structure
    riskPercentage: null,
    riskRewardRatio: null,
    stopLossMethod: 'fixed',
    fixedPips: null,
    indicatorBasedSL: null,
    lotSize: null,

    // Additional parameters
    maxDailyLoss: null,
    maxPositionSize: null,
    totalProfitTarget: null,
    totalLossLimit: null,
    runtime: null,

    // For backward compatibility
    riskPerTrade: null,
    stopLoss: null,
    takeProfit: null,
    stopLossUnit: 'percentage',
    takeProfitUnit: 'percentage'
  });

  // Parse risk management parameters
  const parsePercentage = (value) => {
    if (!value) return null;
    if (typeof value === 'string') {
      return parseFloat(value.replace('%', ''));
    }
    return value;
  };

  // Update parsed parameters when risk management data changes
  useEffect(() => {
    if (!riskManagement) return;

    console.log('Raw risk management parameters:', riskManagement);

    const newParams = {
      // New risk management structure
      riskPercentage: parsePercentage(riskManagement.riskPercentage),
      riskRewardRatio: riskManagement.riskRewardRatio ? parseFloat(riskManagement.riskRewardRatio) : null,
      stopLossMethod: riskManagement.stopLossMethod || 'fixed',
      fixedPips: riskManagement.fixedPips ? parseFloat(riskManagement.fixedPips) : null,
      indicatorBasedSL: riskManagement.indicatorBasedSL || null,
      lotSize: riskManagement.lotSize ? parseFloat(riskManagement.lotSize) : null,

      // Additional parameters
      maxDailyLoss: parsePercentage(riskManagement.maxDailyLoss),
      maxPositionSize: parsePercentage(riskManagement.maxPositionSize),
      totalProfitTarget: parsePercentage(riskManagement.totalProfitTarget),
      totalLossLimit: parsePercentage(riskManagement.totalLossLimit),
      runtime: riskManagement.runtime,

      // For backward compatibility
      riskPerTrade: parsePercentage(riskManagement.riskPerTrade),
      stopLoss: parsePercentage(riskManagement.stopLoss),
      takeProfit: parsePercentage(riskManagement.takeProfit),
      stopLossUnit: riskManagement.stopLossUnit || 'percentage',
      takeProfitUnit: riskManagement.takeProfitUnit || 'percentage'
    };

    console.log('Parsed risk management parameters:', newParams);
    setParsedParams(newParams);
  }, [riskManagement]);

  // If no risk management data is available, show a message
  if (!riskManagement) {
    return (
      <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] p-6">
        <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Risk Management</h3>
        <p className="text-gray-400">No risk management parameters configured for this strategy.</p>
      </div>
    );
  }

  // Destructure parsed parameters for easier access
  const {
    // New risk management structure
    riskPercentage,
    riskRewardRatio,
    stopLossMethod,
    fixedPips,
    indicatorBasedSL,
    lotSize,

    // Additional parameters
    maxDailyLoss,
    maxPositionSize,
    totalProfitTarget,
    totalLossLimit,
    runtime,

    // For backward compatibility
    riskPerTrade,
    stopLoss,
    takeProfit,
    stopLossUnit,
    takeProfitUnit
  } = parsedParams;

  // State to store processed metrics
  const [processedMetrics, setProcessedMetrics] = useState({
    actualTotalProfit: 0,
    actualTotalLoss: 0,
    actualDailyLoss: 0,
    actualAccountBalance: 0,
    totalProfitTargetAbs: 0,
    totalLossLimitAbs: 0,
    maxDailyLossAbs: 0,
    totalProfitProgress: 0,
    totalLossProgress: 0,
    dailyLossProgress: 0,
    runtimeProgress: 0,
    elapsedDays: 0,
    daysRemaining: 0
  });

  // Update processed metrics when inputs change
  useEffect(() => {
    // Use metrics if provided, otherwise calculate from props
    const actualMetrics = metrics || {};

    console.log('Risk Management Panel - Metrics:', actualMetrics);
    console.log('Risk Management Panel - Parameters:', riskManagement);
    console.log('Risk Management Panel - Account Balance:', accountBalance);

    // Get values from metrics if available, otherwise calculate
    const actualTotalProfit = actualMetrics.totalProfit !== undefined ? actualMetrics.totalProfit : totalProfit;
    const actualTotalLoss = actualMetrics.totalLoss !== undefined ? actualMetrics.totalLoss : totalLoss;
    const actualDailyLoss = actualMetrics.dailyLoss !== undefined ? actualMetrics.dailyLoss : dailyLoss;
    const actualAccountBalance = actualMetrics.accountBalance !== undefined ? actualMetrics.accountBalance : accountBalance;

    console.log('Risk Management Panel - Actual Values:', {
      totalProfit: actualTotalProfit,
      totalLoss: actualTotalLoss,
      dailyLoss: actualDailyLoss,
      accountBalance: actualAccountBalance
    });

    // Get absolute values from metrics if available
    const totalProfitTargetAbs = actualMetrics.totalProfitTargetAbs !== undefined
      ? actualMetrics.totalProfitTargetAbs
      : (actualAccountBalance * (totalProfitTarget / 100));

    const totalLossLimitAbs = actualMetrics.totalLossLimitAbs !== undefined
      ? actualMetrics.totalLossLimitAbs
      : (actualAccountBalance * (totalLossLimit / 100));

    const maxDailyLossAbs = actualMetrics.dailyLossLimitAbs !== undefined
      ? actualMetrics.dailyLossLimitAbs
      : (actualAccountBalance * (maxDailyLoss / 100));

    console.log('Risk Management Panel - Absolute Values:', {
      totalProfitTargetAbs,
      totalLossLimitAbs,
      maxDailyLossAbs
    });

    // Calculate progress percentages using absolute values
    const calculateProgress = (current, targetAbs) => {
      if (!current || !targetAbs || targetAbs === 0) return 0;
      const progress = (current / targetAbs) * 100;
      return Math.min(Math.max(progress, 0), 100); // Clamp between 0 and 100
    };

    const totalProfitProgress = calculateProgress(actualTotalProfit, totalProfitTargetAbs);
    const totalLossProgress = calculateProgress(actualTotalLoss, totalLossLimitAbs);
    const dailyLossProgress = calculateProgress(actualDailyLoss, maxDailyLossAbs);

    console.log('Risk Management Panel - Progress Values:', {
      totalProfitProgress,
      totalLossProgress,
      dailyLossProgress
    });

    // Get runtime progress from metrics
    const runtimeProgress = actualMetrics.runtimeProgress !== undefined ? actualMetrics.runtimeProgress : 0;
    const elapsedDays = actualMetrics.elapsedDays !== undefined ? actualMetrics.elapsedDays : 0;
    const daysRemaining = actualMetrics.daysRemaining !== undefined ? actualMetrics.daysRemaining : runtime;

    console.log('Risk Management Panel - Runtime Values:', {
      runtimeProgress,
      elapsedDays,
      daysRemaining
    });

    // Update state with all processed metrics
    setProcessedMetrics({
      actualTotalProfit,
      actualTotalLoss,
      actualDailyLoss,
      actualAccountBalance,
      totalProfitTargetAbs,
      totalLossLimitAbs,
      maxDailyLossAbs,
      totalProfitProgress,
      totalLossProgress,
      dailyLossProgress,
      runtimeProgress,
      elapsedDays,
      daysRemaining
    });
  }, [riskManagement, metrics, accountBalance, totalProfit, totalLoss, dailyLoss, totalProfitTarget, totalLossLimit, maxDailyLoss]);

  // Destructure processed metrics for easier access
  const {
    actualTotalProfit,
    actualTotalLoss,
    actualDailyLoss,
    actualAccountBalance,
    totalProfitTargetAbs,
    totalLossLimitAbs,
    maxDailyLossAbs,
    totalProfitProgress,
    totalLossProgress,
    dailyLossProgress,
    runtimeProgress,
    elapsedDays,
    daysRemaining
  } = processedMetrics;

  console.log('Risk Management Panel - Progress Values:', {
    totalProfitProgress,
    totalLossProgress,
    dailyLossProgress
  });

  // Runtime progress metrics are now part of processedMetrics

  // Use absolute values directly
  const totalProfitTargetValue = totalProfitTargetAbs;
  const totalLossLimitValue = totalLossLimitAbs;
  const maxDailyLossValue = maxDailyLossAbs;

  // Format currency
  const formatCurrency = (value) => {
    if (value === undefined || value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return (
    <div className="bg-[#0A0B0B] rounded-lg border border-[#1a1a1a] p-6">
      <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Risk Management</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Risk Parameters */}
        <div className="bg-[#1a1a1a] rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-400 mb-2">Risk Parameters</h4>
          <div className="space-y-2">
            {riskPercentage && (
              <div>
                <span className="text-gray-400">Risk Percentage:</span>
                <span className="text-white ml-2">{riskPercentage}% of balance</span>
              </div>
            )}
            {riskRewardRatio && (
              <div>
                <span className="text-gray-400">Risk-Reward Ratio:</span>
                <span className="text-white ml-2">1:{riskRewardRatio}</span>
              </div>
            )}
            {maxPositionSize && (
              <div>
                <span className="text-gray-400">Max Position Size:</span>
                <span className="text-white ml-2">{maxPositionSize}% of balance</span>
              </div>
            )}
          </div>
        </div>

        {/* Stop Loss Method */}
        <div className="bg-[#1a1a1a] rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-400 mb-2">Stop Loss Method</h4>
          <div className="space-y-2">
            {stopLossMethod && (
              <div>
                <span className="text-gray-400">Method:</span>
                <span className="text-white ml-2">
                  {stopLossMethod === 'fixed' ? 'Fixed Pips' :
                   stopLossMethod === 'indicator' ? 'Indicator-Based' :
                   stopLossMethod === 'risk' ? 'Risk-Based' :
                   stopLossMethod}
                </span>
              </div>
            )}
            {stopLossMethod === 'fixed' && fixedPips && (
              <div>
                <span className="text-gray-400">Fixed Pips:</span>
                <span className="text-white ml-2">{fixedPips} pips</span>
              </div>
            )}
            {stopLossMethod === 'risk' && lotSize && (
              <div>
                <span className="text-gray-400">Lot Size:</span>
                <span className="text-white ml-2">{lotSize}</span>
              </div>
            )}
            {/* Legacy stop loss and take profit percentages are no longer displayed
                 as they are redundant with Risk Percentage and Risk-Reward Ratio */}
          </div>
        </div>
      </div>

      {/* Progress Bars */}
      <div className="space-y-6">
        {/* Total Profit Target */}
        {totalProfitTarget && (
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-400">Total Profit Target</span>
              <span className="text-sm font-medium text-gray-400">
                {formatCurrency(actualTotalProfit)} / {formatCurrency(totalProfitTargetValue)}
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <motion.div
                className="bg-green-600 h-2.5 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${totalProfitProgress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-500">{totalProfitProgress.toFixed(1)}% complete</span>
              <span className="text-xs text-gray-500">{totalProfitTarget}% of balance</span>
            </div>
          </div>
        )}

        {/* Total Loss Limit */}
        {totalLossLimit && (
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-400">Total Loss Limit</span>
              <span className="text-sm font-medium text-gray-400">
                {formatCurrency(actualTotalLoss)} / {formatCurrency(totalLossLimitValue)}
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <motion.div
                className="bg-red-600 h-2.5 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${totalLossProgress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-500">{totalLossProgress.toFixed(1)}% of limit</span>
              <span className="text-xs text-gray-500">{totalLossLimit}% of balance</span>
            </div>
          </div>
        )}

        {/* Daily Loss Limit */}
        {maxDailyLoss && (
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-400">Daily Loss Limit</span>
              <span className="text-sm font-medium text-gray-400">
                {formatCurrency(actualDailyLoss)} / {formatCurrency(maxDailyLossValue)}
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <motion.div
                className="bg-yellow-600 h-2.5 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${dailyLossProgress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-500">{dailyLossProgress.toFixed(1)}% of limit</span>
              <span className="text-xs text-gray-500">{maxDailyLoss}% of balance</span>
            </div>
          </div>
        )}

        {/* Runtime Progress */}
        {runtime && (
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-400">Bot Runtime</span>
              <span className="text-sm font-medium text-gray-400">
                {elapsedDays.toFixed(1)} / {runtime} days
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2.5">
              <motion.div
                className="bg-blue-600 h-2.5 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${runtimeProgress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-500">{runtimeProgress.toFixed(1)}% complete</span>
              <span className="text-xs text-gray-500">{daysRemaining.toFixed(1)} days remaining</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RiskManagementPanel;
