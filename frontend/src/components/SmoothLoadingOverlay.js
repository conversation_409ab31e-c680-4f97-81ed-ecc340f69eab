import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const SmoothLoadingOverlay = ({ 
  isLoading, 
  message = "Updating data...", 
  showProgress = false,
  progress = 0,
  minimal = false 
}) => {
  if (minimal) {
    return (
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="absolute top-4 right-4 z-20"
          >
            <div className="bg-[#1a1a1a]/90 backdrop-blur-sm rounded-lg px-3 py-2 flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
              <span className="text-[#EFBD3A] text-sm font-medium">Updating</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0, backdropFilter: "blur(0px)" }}
          animate={{ opacity: 1, backdropFilter: "blur(4px)" }}
          exit={{ opacity: 0, backdropFilter: "blur(0px)" }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="absolute inset-0 bg-[#0A0B0B]/20 backdrop-blur-sm z-10 flex items-center justify-center"
        >
          <motion.div
            initial={{ scale: 0.8, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.8, y: 20 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="bg-[#1a1a1a]/95 backdrop-blur-md rounded-xl p-6 shadow-2xl border border-[#333]"
          >
            <div className="flex flex-col items-center space-y-4">
              {/* Animated loading spinner */}
              <div className="relative">
                <div className="w-12 h-12 border-4 border-[#333] rounded-full"></div>
                <div className="absolute inset-0 w-12 h-12 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin"></div>
              </div>
              
              {/* Loading message */}
              <div className="text-center">
                <div className="text-[#EFBD3A] font-semibold text-lg mb-1">
                  {message}
                </div>
                <div className="text-gray-400 text-sm">
                  Please wait while we fetch the latest data
                </div>
              </div>
              
              {/* Progress bar (optional) */}
              {showProgress && (
                <div className="w-64 bg-[#333] rounded-full h-2 overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-[#EFBD3A] to-[#F4D03F] rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.5, ease: "easeOut" }}
                  />
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Skeleton loader for chart area
export const ChartSkeleton = ({ className = "" }) => {
  return (
    <div className={`animate-pulse bg-[#1a1a1a] rounded-lg ${className}`}>
      <div className="h-full flex flex-col">
        {/* Chart header skeleton */}
        <div className="p-4 border-b border-[#333]">
          <div className="flex justify-between items-center">
            <div className="space-y-2">
              <div className="h-4 bg-[#333] rounded w-32"></div>
              <div className="h-3 bg-[#333] rounded w-24"></div>
            </div>
            <div className="flex space-x-2">
              <div className="h-8 bg-[#333] rounded w-16"></div>
              <div className="h-8 bg-[#333] rounded w-16"></div>
            </div>
          </div>
        </div>
        
        {/* Chart area skeleton */}
        <div className="flex-1 p-4">
          <div className="h-full bg-[#333] rounded-lg relative overflow-hidden">
            {/* Animated shimmer effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-[#444]/50 to-transparent animate-shimmer"></div>
            
            {/* Mock chart elements */}
            <div className="absolute bottom-4 left-4 right-4 h-1 bg-[#444] rounded"></div>
            <div className="absolute top-4 bottom-8 left-4 w-1 bg-[#444] rounded"></div>
            
            {/* Mock candlesticks */}
            <div className="absolute bottom-8 left-8 w-2 h-16 bg-[#26a69a] rounded opacity-30"></div>
            <div className="absolute bottom-8 left-12 w-2 h-12 bg-[#ef5350] rounded opacity-30"></div>
            <div className="absolute bottom-8 left-16 w-2 h-20 bg-[#26a69a] rounded opacity-30"></div>
            <div className="absolute bottom-8 left-20 w-2 h-8 bg-[#ef5350] rounded opacity-30"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Smooth transition wrapper for content updates
export const SmoothContentTransition = ({ 
  children, 
  isLoading, 
  loadingComponent = null,
  className = "" 
}) => {
  return (
    <div className={`relative ${className}`}>
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {loadingComponent || <ChartSkeleton className="h-full" />}
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SmoothLoadingOverlay;
