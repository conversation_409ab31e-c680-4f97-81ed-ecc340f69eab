import React from 'react';
import { motion } from 'framer-motion';
import { Percent, TrendingUp, Target, Users, Trophy, BarChart3 } from 'lucide-react';

const PerformanceMetricsRow = ({
  accountBalance = 0,
  totalPnL = 0,
  todaysTradeCount = 0,
  totalTrades = 0,
  winningTrades = 0,
  losingTrades = 0,
  winRate = 0,
  profitFactor = 0
}) => {
  // Format currency values
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  // Calculate today's P&L percentage change
  const calculateTodaysPnLPercentage = () => {
    if (accountBalance <= 0) return 0;
    return (totalPnL / accountBalance) * 100;
  };

  const todaysPnLPercentage = calculateTodaysPnLPercentage();

  // Get color for positive/negative values
  const getValueColor = (value) => {
    if (value > 0) return 'text-green-400';
    if (value < 0) return 'text-red-400';
    return 'text-[#FEFEFF]';
  };

  // Get profit factor status
  const getProfitFactorStatus = () => {
    if (profitFactor > 1.5) return { text: 'Highly Profitable', color: 'text-green-400' };
    if (profitFactor > 1.0) return { text: 'Profitable', color: 'text-green-400' };
    if (profitFactor === 1.0) return { text: 'Break-even', color: 'text-yellow-400' };
    if (profitFactor > 0) return { text: 'Losing', color: 'text-red-400' };
    return { text: 'No Data', color: 'text-gray-400' };
  };

  // Get win rate status
  const getWinRateStatus = () => {
    if (winRate >= 70) return { text: 'Excellent', color: 'text-green-400' };
    if (winRate >= 60) return { text: 'Good', color: 'text-green-400' };
    if (winRate >= 50) return { text: 'Break-even', color: 'text-yellow-400' };
    if (winRate > 0) return { text: 'Below Average', color: 'text-red-400' };
    return { text: 'No Data', color: 'text-gray-400' };
  };

  const profitFactorStatus = getProfitFactorStatus();
  const winRateStatus = getWinRateStatus();

  const metrics = [
    {
      title: 'P&L %',
      value: formatPercentage(todaysPnLPercentage),
      change: null,
      subtitle: `of account ${formatCurrency(accountBalance)}`,
      icon: <Percent className="w-6 h-6" />,
      iconBg: 'bg-[#3a3a3a]'
    },
    {
      title: "Total P&L",
      value: `${totalPnL >= 0 ? '+' : ''}${formatCurrency(totalPnL)}`,
      change: null,
      subtitle: `${todaysTradeCount} trades executed`,
      icon: <TrendingUp className="w-6 h-6" />,
      iconBg: 'bg-[#3a3a3a]'
    },
    {
      title: 'Total Trades',
      value: totalTrades.toString(),
      change: null,
      subtitle: `${winningTrades}W / ${losingTrades}L`,
      icon: <BarChart3 className="w-6 h-6" />,
      iconBg: 'bg-[#3a3a3a]'
    },
    {
      title: 'Win Rate',
      value: `${winRate.toFixed(1)}%`,
      change: null,
      subtitle: winRateStatus.text,
      subtitleColor: winRateStatus.color,
      icon: <Trophy className="w-6 h-6" />,
      iconBg: 'bg-[#3a3a3a]'
    },
    {
      title: 'Profit Factor',
      value: profitFactor.toFixed(2),
      change: null,
      subtitle: profitFactorStatus.text,
      subtitleColor: profitFactorStatus.color,
      icon: <Target className="w-6 h-6" />,
      iconBg: 'bg-[#3a3a3a]'
    }
  ];

  return (
    <div className="bg-[#0A0B0B] px-6 py-3">
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-[#0F1011] rounded-lg p-2.5 border border-[#1a1a1a] hover:border-[#1a1a1a] transition-colors"
          >
            {/* Header with title */}
            <div className="mb-2">
              <h3 className="text-xs font-medium text-[#FEFEFF]/50 uppercase tracking-wide">
                {metric.title}
              </h3>
            </div>

            {/* Main value with icon */}
            <div className="flex items-center justify-between mb-1">
              <div className={`text-2xl font-bold ${
                metric.title === 'P&L %' || metric.title === "Total P&L"
                  ? getValueColor(parseFloat(metric.value.replace(/[$,+%]/g, '')))
                  : 'text-[#FEFEFF]'
              }`}>
                {metric.value}
              </div>
              <div className={`w-8 h-8 bg-[#0A0B0B] rounded-md flex items-center justify-center`}>
                <div className="text-[#FEFEFF]/60">
                  {metric.icon}
                </div>
              </div>
            </div>

            {/* Change indicator and subtitle */}
            <div className="space-y-1">
              {metric.change && (
                <div className={`text-xs font-medium ${getValueColor(balanceChange)}`}>
                  {metric.change}
                </div>
              )}
              <div className={`text-xs ${
                metric.subtitleColor || 'text-[#FEFEFF]/40'
              }`}>
                {metric.subtitle}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default PerformanceMetricsRow;
