import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  LayoutDashboard,
  TrendingUp,
  ArrowLeftRight,
  Target,
  Globe,
  ScrollText,
  Activity,
  CheckCircle,
  XCircle,
  Pause,
  CheckCircle2,
  Square,
  Clock,
  Heart,
  Info,
  AlertTriangle,
  BarChart3,
  FileText,
  DollarSign,
  Zap,
  Wifi
} from 'lucide-react';
import WebSocketTradingChart from './WebSocketTradingChart';
import PerformanceDashboard from './PerformanceDashboard';
import StrategyLogicFlow from './StrategyLogicFlow';
import MarketConditionsPanel from './MarketConditionsPanel';
import UserLogs from './UserLogs';
import TradeBotThinking from './TradeBotThinking';
import { useLocalStorage } from '../hooks/useLocalStorage';

// Utility function to calculate trading period from historical trades
const calculateTradingPeriod = (historicalTrades) => {
  if (!historicalTrades || historicalTrades.length === 0) {
    return null;
  }

  // Get all trades with valid timestamps
  const tradesWithTimes = historicalTrades.filter(trade => {
    return trade.openTime || trade.closeTime;
  });

  if (tradesWithTimes.length === 0) {
    return null;
  }

  // Extract all timestamps (both open and close times)
  const timestamps = [];

  tradesWithTimes.forEach(trade => {
    // Add open time
    if (trade.openTime) {
      let openTime;
      if (trade.openTime instanceof Date) {
        openTime = trade.openTime;
      } else if (typeof trade.openTime === 'string') {
        openTime = new Date(trade.openTime);
      } else if (typeof trade.openTime === 'object' && trade.openTime.seconds) {
        openTime = new Date(trade.openTime.seconds * 1000);
      }
      if (openTime && !isNaN(openTime.getTime())) {
        timestamps.push(openTime);
      }
    }

    // Add close time if available
    if (trade.closeTime) {
      let closeTime;
      if (trade.closeTime instanceof Date) {
        closeTime = trade.closeTime;
      } else if (typeof trade.closeTime === 'string') {
        closeTime = new Date(trade.closeTime);
      } else if (typeof trade.closeTime === 'object' && trade.closeTime.seconds) {
        closeTime = new Date(trade.closeTime.seconds * 1000);
      }
      if (closeTime && !isNaN(closeTime.getTime())) {
        timestamps.push(closeTime);
      }
    }
  });

  if (timestamps.length === 0) {
    return null;
  }

  // Sort timestamps and get first and last
  timestamps.sort((a, b) => a.getTime() - b.getTime());
  const firstTradeTime = timestamps[0];
  const lastTradeTime = timestamps[timestamps.length - 1];

  // Add some buffer around the trading period (e.g., 1 day before and after)
  const bufferMs = 24 * 60 * 60 * 1000; // 1 day in milliseconds
  const startTime = new Date(firstTradeTime.getTime() - bufferMs);
  const endTime = new Date(lastTradeTime.getTime() + bufferMs);

  return {
    startTime,
    endTime,
    firstTradeTime,
    lastTradeTime,
    totalTrades: tradesWithTimes.length,
    tradingDuration: lastTradeTime.getTime() - firstTradeTime.getTime()
  };
};

const TradeBotTabs = ({
  currentPrice,
  indicators = [],
  strategy,
  historicalTrades,
  candleData,
  marketStatus,
  chartZoomState,
  setChartZoomState,
  accountBalance,
  openTrades = [],
  strategySummary = {},
  riskManagement = {},
  tradeLogs = [],
  botStatus,
  userId,
  strategyId,
  children
}) => {
  // Use localStorage to persist active tab per strategy
  const [activeTab, setActiveTab] = useLocalStorage(`tradeBotActiveTab_${strategyId}`, 'overview');

  // Add state to track when overview tab becomes visible to trigger chart resize
  const [overviewTabVisible, setOverviewTabVisible] = React.useState(activeTab === 'overview');

  // Track when overview tab becomes visible to trigger chart resize
  React.useEffect(() => {
    const wasVisible = overviewTabVisible;
    const isVisible = activeTab === 'overview';

    if (!wasVisible && isVisible) {
      // Overview tab just became visible - trigger chart resize after a short delay
      console.log('🔄 Overview tab became visible - triggering chart resize');
      setTimeout(() => {
        // Trigger resize event to make charts re-render properly
        window.dispatchEvent(new Event('resize'));

        // Also try to access global chart instances and resize them directly
        if (window.chartInstance) {
          try {
            const container = window.chartInstance.chartElement?.();
            if (container) {
              const { width, height } = container.getBoundingClientRect();
              if (width > 0 && height > 0) {
                window.chartInstance.applyOptions({ width, height });
                console.log('🔄 Manually resized main chart instance');
              }
            }
          } catch (error) {
            console.warn('⚠️ Error manually resizing chart:', error);
          }
        }

        // Additional fallback: trigger multiple resize events with delays
        setTimeout(() => window.dispatchEvent(new Event('resize')), 200);
        setTimeout(() => window.dispatchEvent(new Event('resize')), 500);
      }, 100);
    }

    setOverviewTabVisible(isVisible);
  }, [activeTab, overviewTabVisible]);

  // Calculate trading period from historical trades
  const tradingPeriod = useMemo(() => {
    return calculateTradingPeriod(historicalTrades);
  }, [historicalTrades]);

  // Determine if we should show charts based on agent status and trading history
  const shouldShowCharts = useMemo(() => {
    // For running bots, always show charts
    if (botStatus === 'running') {
      return true;
    }

    // For market_closed, not_in_session, or data_stale bots: always show charts if there are trades
    // These states should show historical data when market is closed or data is stale
    if (['market_closed', 'not_in_session', 'data_stale'].includes(botStatus)) {
      return tradingPeriod !== null;
    }

    // For stopped, paused, or insufficient_margin bots: show charts if there are trades
    if (['stopped', 'paused', 'insufficient_margin'].includes(botStatus)) {
      return tradingPeriod !== null;
    }

    // Default to showing charts
    return true;
  }, [botStatus, tradingPeriod]);

  // Count open trades for badge
  const openTradesCount = openTrades?.length || 0;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: <LayoutDashboard className="w-4 h-4" /> },
    { id: 'analytics', label: 'Analytics', icon: <TrendingUp className="w-4 h-4" /> },
    {
      id: 'trades',
      label: 'Trades',
      icon: <ArrowLeftRight className="w-4 h-4" />,
      badge: openTradesCount > 0 ? openTradesCount : null
    },
    { id: 'strategy', label: 'Strategy', icon: <Target className="w-4 h-4" /> },
    { id: 'market', label: 'Market', icon: <Globe className="w-4 h-4" /> },
    { id: 'logs', label: 'Logs', icon: <ScrollText className="w-4 h-4" /> }
  ];

  return (
    <div className="bg-gradient-to-r from-[#0A0B0B] to-[#0F1011] border-2 border-[#EFBD3A]/20 rounded-xl shadow-2xl mx-6 my-4">
      {/* Enhanced Tab Navigation */}
      <div className="mb-6">
        {/* Navigation Header */}
        <div className="px-6 py-4 border-b border-[#EFBD3A]/30 bg-gradient-to-r from-[#EFBD3A]/5 to-transparent">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
                <LayoutDashboard className="w-5 h-5 text-[#EFBD3A]" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-[#FEFEFF]">Trading Dashboard</h2>
                <p className="text-xs text-[#FEFEFF]/60">Navigate through different sections</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-[#FEFEFF]/60">Live Data</span>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-6 py-2">
          <div className="flex items-center space-x-1 bg-[#0A0B0B]/50 rounded-lg p-1 border border-[#1a1a1a]">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-all duration-300 rounded-lg relative group ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-[#EFBD3A] to-[#EFBD3A]/80 text-[#0A0B0B] shadow-lg transform scale-105'
                    : 'text-[#FEFEFF]/70 hover:text-[#FEFEFF] hover:bg-[#1a1a1a]/50 hover:scale-102'
                }`}
              >
                <div className={`transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'text-[#0A0B0B] transform scale-110'
                    : 'text-[#FEFEFF]/70 group-hover:text-[#EFBD3A] group-hover:scale-110'
                }`}>
                  {tab.icon}
                </div>
                <span className={`font-semibold ${
                  activeTab === tab.id ? 'text-[#0A0B0B]' : ''
                }`}>{tab.label}</span>
                {tab.badge && (
                  <div className={`ml-1 px-2 py-0.5 text-xs rounded-full min-w-[20px] h-[20px] flex items-center justify-center font-bold ${
                    activeTab === tab.id
                      ? 'bg-[#0A0B0B]/20 text-[#0A0B0B]'
                      : 'bg-blue-500 text-white'
                  }`}>
                    {tab.badge}
                  </div>
                )}
                {/* Active indicator */}
                {activeTab === tab.id && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-[#0A0B0B] rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {/* Overview Tab - Always render but conditionally show to preserve chart state */}
        <div className={activeTab === 'overview' ? 'block' : 'hidden'}>
          <OverviewTab
            currentPrice={currentPrice}
            indicators={indicators}
            strategy={strategy}
            candleData={candleData}
            marketStatus={marketStatus}
            chartZoomState={chartZoomState}
            setChartZoomState={setChartZoomState}
            historicalTrades={historicalTrades}
            openTrades={openTrades}
            botStatus={botStatus}
            tradingPeriod={tradingPeriod}
            shouldShowCharts={shouldShowCharts}
            userId={userId}
            strategyId={strategyId}
          />
        </div>
        
        {/* Analytics Tab */}
        <div className={activeTab === 'analytics' ? 'block' : 'hidden'}>
          <AnalyticsTab
            historicalTrades={historicalTrades}
            openTrades={openTrades}
            accountBalance={accountBalance}
            strategySummary={strategySummary}
            marketStatus={marketStatus}
          />
        </div>

        {/* Trades Tab */}
        <div className={activeTab === 'trades' ? 'block' : 'hidden'}>
          <TradesTab
            historicalTrades={historicalTrades}
            openTrades={openTrades}
          />
        </div>

        {/* Strategy Tab */}
        <div className={activeTab === 'strategy' ? 'block' : 'hidden'}>
          <StrategyTab
            strategy={strategy}
            riskManagement={riskManagement}
          />
        </div>

        {/* Market Tab */}
        <div className={activeTab === 'market' ? 'block' : 'hidden'}>
          <MarketTab
            marketStatus={marketStatus}
            strategy={strategy}
            currentPrice={currentPrice}
            botStatus={botStatus}
          />
        </div>

        {/* Logs Tab */}
        <div className={activeTab === 'logs' ? 'block' : 'hidden'}>
          <LogsTab
            tradeLogs={tradeLogs}
          />
        </div>
      </div>
    </div>
  );
};

// Overview Tab Component
const OverviewTab = ({
  currentPrice,
  indicators,
  strategy,
  candleData,
  marketStatus,
  chartZoomState,
  setChartZoomState,
  historicalTrades,
  openTrades,
  botStatus,
  tradingPeriod,
  shouldShowCharts,
  userId,
  strategyId
}) => {
  const [calculatedIndicators, setCalculatedIndicators] = React.useState({});

  return (
    <div className="space-y-6">
      {/* Open Trades Alert */}
      {openTrades && openTrades.length > 0 && (
        <div className="bg-gradient-to-r from-blue-500/15 to-blue-600/10 border-2 border-blue-500/40 rounded-xl p-5 shadow-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                <Activity className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-blue-400 mb-1">
                  {openTrades.length} Active Trade{openTrades.length > 1 ? 's' : ''}
                </h3>
                <div className="text-sm text-blue-300/80">
                  {openTrades.map(trade => {
                    const tradeType = trade.type === 'long' ? 'LONG' : 'SHORT';
                    const unrealizedPL = parseFloat(trade.unrealizedPL || 0);
                    return `${tradeType} ${trade.instrument} (${unrealizedPL >= 0 ? '+' : ''}$${unrealizedPL.toFixed(2)})`;
                  }).join(', ')}
                </div>
              </div>
            </div>
            <div className="text-right bg-[#0A0B0B]/50 rounded-lg px-4 py-3 border border-[#1a1a1a]/50">
              <div className="text-sm text-blue-400 font-medium mb-1">Total P&L</div>
              <div className={`text-2xl font-bold ${
                openTrades.reduce((sum, trade) => sum + parseFloat(trade.unrealizedPL || 0), 0) >= 0
                  ? 'text-green-400'
                  : 'text-red-400'
              }`}>
                {openTrades.reduce((sum, trade) => sum + parseFloat(trade.unrealizedPL || 0), 0) >= 0 ? '+' : ''}
                ${openTrades.reduce((sum, trade) => sum + parseFloat(trade.unrealizedPL || 0), 0).toFixed(2)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Current Price and Indicators Card - Show when agent is not stopped */}
      {botStatus !== "stopped" && botStatus !== "insufficient_margin" && (
        <CurrentPriceIndicators
          currentPrice={currentPrice}
          indicators={calculatedIndicators}
          strategy={strategy}
          candleData={candleData}
          botStatus={botStatus}
          marketStatus={marketStatus}
        />
      )}

      {/* Trading Chart or No Trades State */}
      {!shouldShowCharts ? (
        <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] p-12 text-center min-h-96 flex items-center justify-center">
          <div>
            <div className="bg-[#0A0B0B]/50 p-4 rounded-lg inline-flex mb-4">
              <Square className="w-12 h-12 text-[#FEFEFF]/40" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">
              {botStatus === "stopped"
                ? "Agent Stopped"
                : botStatus === "insufficient_margin"
                ? "Agent Stopped - Insufficient Margin"
                : botStatus === "market_closed"
                ? "Market Closed - No Trading History"
                : botStatus === "not_in_session"
                ? "Outside Trading Session - No Trading History"
                : botStatus === "paused"
                ? "Agent Paused - No Trading History"
                : botStatus === "data_stale"
                ? "Data Provider Issue - No Trading History"
                : "No Trading History"
              }
            </h3>
            <p className="text-[#FEFEFF]/70 mb-4">
              {botStatus === "stopped"
                ? "This trading agent has been stopped and cannot be resumed."
                : botStatus === "insufficient_margin"
                ? "The agent was stopped due to insufficient margin. Please check the analysis below for details and solutions."
                : botStatus === "market_closed"
                ? "No trades have been executed yet. Charts will appear once trading begins when the market reopens."
                : botStatus === "not_in_session"
                ? "No trades have been executed yet. Charts will appear once trading begins during the next trading session."
                : botStatus === "paused"
                ? "No trades have been executed yet. Charts will appear once trading begins when the agent is resumed."
                : botStatus === "data_stale"
                ? "No trades have been executed yet. Charts will appear once trading begins when the data provider starts providing fresh market data."
                : "No trades have been executed yet. Charts will appear once trading begins."
              }
            </p>
            <p className="text-sm text-[#FEFEFF]/50">
              {botStatus === "stopped"
                ? "You can start a new agent with another strategy if needed."
                : botStatus === "insufficient_margin"
                ? "Fix the margin issue and start a new agent to continue trading."
                : botStatus === "market_closed"
                ? "The agent will resume trading when the market reopens."
                : botStatus === "not_in_session"
                ? "The agent will resume trading during the next trading session."
                : botStatus === "paused"
                ? "Resume the agent to continue trading when conditions are met."
                : botStatus === "data_stale"
                ? "The agent will resume trading automatically when the data provider starts providing fresh market data."
                : "The agent is ready to trade when market conditions are met."
              }
            </p>
          </div>
        </div>
      ) : (
        <div className="bg-gradient-to-br from-[#0F1011] to-[#0A0B0B] rounded-xl border-2 border-[#1a1a1a] shadow-2xl relative overflow-hidden">
          <div className="p-4 border-b border-[#1a1a1a]">
            <StrategyInfoPills
              strategy={strategy}
              marketStatus={marketStatus}
              instrument={strategy?.human_readable_rules?.strategy_info?.instrument || "EUR/USD"}
              timeframe={strategy?.human_readable_rules?.strategy_info?.timeframe || "1h"}
              botStatus={botStatus}
            />

            {/* Trading Period Info for stopped/paused bots */}
            {['stopped', 'paused', 'insufficient_margin', 'market_closed', 'not_in_session'].includes(botStatus) && tradingPeriod && (
              <div className="mt-4 space-y-3">
                <div className="p-4 bg-gradient-to-r from-blue-900/20 to-blue-800/10 border-2 border-blue-500/40 rounded-xl shadow-lg">
                  <div className="flex items-center space-x-3 text-sm">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <Info className="w-5 h-5 text-blue-400" />
                    </div>
                    <div>
                      <span className="text-blue-400 font-semibold block">Trading Period:</span>
                      <span className="text-blue-300 text-xs">
                        {tradingPeriod.firstTradeTime.toLocaleDateString()} - {tradingPeriod.lastTradeTime.toLocaleDateString()}
                        <span className="text-blue-300/80 ml-2">({tradingPeriod.totalTrades} trades)</span>
                      </span>
                    </div>
                  </div>
                </div>

                {/* Historical Data Disclaimer */}
                <div className="p-3 bg-gradient-to-r from-amber-900/20 to-amber-800/10 border-2 border-amber-500/40 rounded-xl shadow-lg">
                  <div className="flex items-center space-x-3 text-xs">
                    <div className="w-6 h-6 bg-amber-500/20 rounded-lg flex items-center justify-center">
                      <AlertTriangle className="w-4 h-4 text-amber-400" />
                    </div>
                    <span className="text-amber-300 font-medium">
                      Chart shows historical trading data only - not live market data
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Chart Container */}
          <div className="w-full relative p-6 bg-gradient-to-b from-[#0A0B0B]/50 to-[#0F1011]/30">
            <div className="bg-[#0A0B0B]/80 rounded-xl border border-[#1a1a1a]/50 p-4 shadow-inner">
              {/* Debug: Log chart data being passed */}
              {console.log('🔍 Chart Debug:', {
                botStatus,
                tradingPeriod,
                candleDataLength: candleData?.length,
                skipInitialFetch: ['stopped', 'paused', 'market_closed', 'not_in_session'].includes(botStatus) && tradingPeriod,
                enablePolling: botStatus === 'running',
                candleDataSample: candleData?.slice(0, 3)?.map(c => ({
                  time: new Date(c.time * 1000).toISOString(),
                  close: c.close
                }))
              })}
            <WebSocketTradingChart
              candleData={candleData}
              trades={historicalTrades}
              timeframe={strategy?.human_readable_rules?.strategy_info?.timeframe || "1h"}
              preserveZoom={true}
              onZoomChange={setChartZoomState}
              initialZoomState={chartZoomState}
              chartTimeframe={strategy?.human_readable_rules?.strategy_info?.timeframe || "1h"}
              instrument={strategy?.human_readable_rules?.strategy_info?.instrument || "EUR/USD"}
              marketStatus={marketStatus}
              strategy={strategy}
              strategyInfo={strategy?.human_readable_rules?.strategy_info}
              enablePolling={botStatus === 'running'}
              skipInitialFetch={['stopped', 'paused', 'market_closed', 'not_in_session'].includes(botStatus) && tradingPeriod}
              onDataUpdate={(newCandles) => {
                console.log(`📊 Frontend: Chart received ${newCandles.length} updated candles`);
              }}
              onIndicatorsCalculated={setCalculatedIndicators}
              hideDisplayPills={true}
              hideStrategyPills={true}
            />

            {/* Market Closed Overlay */}
            {botStatus === "market_closed" && (
              <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
                <div className="text-center">
                  <div className="bg-purple-600/20 p-4 rounded-lg inline-flex mb-4">
                    <svg
                      className="w-12 h-12 text-purple-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Market Closed</h3>
                  <p className="text-[#FEFEFF]/70">
                    Trading is unavailable while the market is closed
                  </p>
                </div>
              </div>
            )}

            {/* Not In Session Overlay */}
            {botStatus === "not_in_session" && (
              <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
                <div className="text-center">
                  <div className="bg-blue-600/20 p-4 rounded-lg inline-flex mb-4">
                    <svg
                      className="w-12 h-12 text-blue-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Outside Trading Session</h3>
                  <p className="text-[#FEFEFF]/70">
                    Trading is paused until the next trading session begins
                  </p>
                </div>
              </div>
            )}

            {/* Paused Overlay */}
            {botStatus === "paused" && (
              <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
                <div className="text-center">
                  <div className="bg-yellow-600/20 p-4 rounded-lg inline-flex mb-4">
                    <svg
                      className="w-12 h-12 text-yellow-500"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Agent Paused</h3>
                  <p className="text-[#FEFEFF]/70 mb-3">
                    The trading agent has been paused and can be resumed at any time
                  </p>
                  <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3 max-w-sm">
                    <div className="flex items-start space-x-2">
                      <svg className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                      <div className="text-sm">
                        <p className="text-yellow-300 font-medium">
                          Auto-stop in 24 hours
                        </p>
                        <p className="text-yellow-200/80 text-xs">
                          Agent will automatically stop if not resumed within 24 hours
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Data Stale Overlay */}
            {botStatus === "data_stale" && (
              <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
                <div className="text-center">
                  <div className="bg-orange-600/20 p-4 rounded-lg inline-flex mb-4">
                    <svg
                      className="w-12 h-12 text-orange-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Data Provider Issue</h3>
                  <p className="text-[#FEFEFF]/70">
                    Trading is paused because the data provider is not providing fresh market data
                  </p>
                </div>
              </div>
            )}
            </div>
          </div>
        </div>
      )}

      {/* Trade Agent Analysis - Show when agent is running OR when stopped due to margin/error issues */}
      {(botStatus !== "stopped" && botStatus !== "paused") ||
       botStatus === "error" ||
       botStatus === "ERROR" ||
       botStatus === "insufficient_margin" ||
       botStatus?.toLowerCase() === "error" ? (
        <TradeBotThinking
          userId={userId}
          strategyId={strategyId}
          botStatus={botStatus}
        />
      ) : null}

      {/* Last Updated Section - Below all charts */}
      <div className="flex items-center justify-center mt-6 p-4 bg-[#0F1011] rounded-lg border border-[#1a1a1a]">
        <div className="flex items-center space-x-2 text-sm text-[#FEFEFF]/60">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span>Last updated: {new Date().toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
};

// Current Price and Indicators Component
const CurrentPriceIndicators = (props) => {
  const {
    currentPrice,
    indicators,
    strategy,
    candleData,
    botStatus = null,
    marketStatus = null
  } = props;
  // Define indicator colors that match chart colors exactly
  const getIndicatorColor = (indicatorType, index = 0) => {
    // EMA colors from OptimizedTradingChart.js
    const emaColors = [
      "#FF6B6B", // Red
      "#4ECDC4", // Teal
      "#FFE66D", // Yellow
      "#6A0572", // Purple
      "#FF9F1C", // Orange
      "#2EC4B6", // Turquoise
      "#E71D36", // Bright Red
      "#011627", // Dark Blue
    ];

    // RSI colors from OptimizedTradingChart.js
    const rsiColors = ["#2962FF", "#FF6B6B"]; // Blue and Red

    if (indicatorType === 'EMA') {
      return emaColors[index % emaColors.length];
    } else if (indicatorType === 'RSI') {
      return rsiColors[index % rsiColors.length];
    } else if (indicatorType === 'SMA') {
      return "#EFBD3A"; // Yellow for SMA
    } else if (indicatorType === 'MACD') {
      return "#F59E0B"; // Orange for MACD
    } else if (indicatorType === 'BB') {
      return "#EC4899"; // Pink for Bollinger Bands
    }

    return '#6B7280'; // Default gray
  };

  // Extract indicator values from strategy and calculated indicators
  const getIndicatorValues = () => {
    const indicatorValues = [];

    // Add current price first
    if (currentPrice) {
      indicatorValues.push({
        name: strategy?.human_readable_rules?.strategy_info?.instrument || 'EUR/USD',
        value: currentPrice.toFixed(5),
        type: 'price',
        color: '#FEFEFF'
      });
    }

    // Indicators is an object with ID-based keys and generic keys
    if (indicators && typeof indicators === 'object') {
      // Track counts for color indexing
      let emaCount = 0;
      let rsiCount = 0;

      // Get strategy indicators to match with calculated data
      const strategyIndicators = strategy?.strategy_json?.indicators || [];

      // Process each strategy indicator to find its calculated data
      strategyIndicators.forEach((strategyIndicator) => {
        const indicatorId = strategyIndicator.id;
        const indicatorType = strategyIndicator.type || strategyIndicator.indicator_class;
        // Extract period from parameters object
        const period = strategyIndicator.parameters?.period ||
                      strategyIndicator.parameters?.length ||
                      strategyIndicator.parameters?.timeperiod ||
                      strategyIndicator.period ||
                      strategyIndicator.length ||
                      strategyIndicator.timeperiod;

        console.log(`🔍 Processing indicator:`, {
          id: indicatorId,
          type: indicatorType,
          period: period,
          parameters: strategyIndicator.parameters
        });

        // Find the calculated data for this indicator
        const calculatedData = indicators[indicatorId];

        if (Array.isArray(calculatedData) && calculatedData.length > 0) {
          const latestPoint = calculatedData[calculatedData.length - 1];

          if (indicatorType === 'EMA' && latestPoint?.value !== undefined) {
            indicatorValues.push({
              name: period ? `EMA(${period})` : 'EMA',
              value: latestPoint.value.toFixed(5),
              type: 'indicator',
              color: getIndicatorColor('EMA', emaCount)
            });
            emaCount++;
          } else if (indicatorType === 'RSI' && latestPoint?.value !== undefined) {
            indicatorValues.push({
              name: period ? `RSI(${period})` : 'RSI',
              value: latestPoint.value.toFixed(1),
              type: 'indicator',
              color: getIndicatorColor('RSI', rsiCount)
            });
            rsiCount++;
          } else if (indicatorType === 'SMA' && latestPoint?.value !== undefined) {
            indicatorValues.push({
              name: period ? `SMA(${period})` : 'SMA',
              value: latestPoint.value.toFixed(5),
              type: 'indicator',
              color: getIndicatorColor('SMA')
            });
          } else if (indicatorType === 'MACD') {
            console.log('🔍 MACD Debug:', {
              indicatorId,
              indicatorType,
              latestPoint,
              hasMACD: latestPoint?.macd !== undefined,
              hasValue: latestPoint?.value !== undefined,
              hasSignal: latestPoint?.signal !== undefined,
              hasHistogram: latestPoint?.histogram !== undefined
            });

            if (latestPoint?.macd !== undefined) {
            // Add MACD line value
            indicatorValues.push({
              name: 'MACD',
              value: latestPoint.macd.toFixed(6),
              type: 'indicator',
              color: '#2196F3' // Blue - matches chart color
            });

            // Add Signal line value if available
            if (latestPoint.signal !== undefined) {
              indicatorValues.push({
                name: 'Signal',
                value: latestPoint.signal.toFixed(6),
                type: 'indicator',
                color: '#FF9800' // Orange - matches chart color
              });
            }

            // Add Histogram value if available
            if (latestPoint.histogram !== undefined) {
              indicatorValues.push({
                name: 'Histogram',
                value: latestPoint.histogram.toFixed(6),
                type: 'indicator',
                color: latestPoint.histogram >= 0 ? '#4CAF50' : '#F44336' // Green for positive, red for negative
              });
            }
            } // Missing closing brace for the MACD if statement
          }
        }
      });

      // Check for MACD indicators using semantic key patterns and group them
      const macdGroups = {};
      Object.keys(indicators).forEach(key => {
        const data = indicators[key];
        if (Array.isArray(data) && data.length > 0) {
          const latestPoint = data[data.length - 1];

          // Match semantic MACD pattern: MACD_12_26_9_001_COMPONENT
          const macdMatch = key.match(/^MACD_(\d+)_(\d+)_(\d+)_(\d+)_(.+)$/);
          if (macdMatch) {
            const [, fast, slow, signal, instance, component] = macdMatch;
            const baseKey = `MACD_${fast}_${slow}_${signal}_${instance}`;

            if (!macdGroups[baseKey]) {
              macdGroups[baseKey] = {
                name: `MACD (${fast},${slow},${signal})`,
                data: {}
              };
            }

            console.log('🔍 Found MACD semantic data:', { key, component, latestPoint });

            if (component === 'LINE') {
              macdGroups[baseKey].data.macd = latestPoint.value?.toFixed(6) || 'N/A';
            } else if (component === 'SIGNAL') {
              macdGroups[baseKey].data.signal = latestPoint.value?.toFixed(6) || 'N/A';
            } else if (component === 'HIST') {
              const histValue = latestPoint.value || 0;
              macdGroups[baseKey].data.histogram = histValue.toFixed(6);
              macdGroups[baseKey].data.histogramColor = histValue >= 0 ? '#4CAF50' : '#F44336';
            }
          }
        }
      });

      // Add grouped MACD indicators
      Object.values(macdGroups).forEach(macdGroup => {
        if (Object.keys(macdGroup.data).length > 0) {
          indicatorValues.push({
            name: macdGroup.name,
            type: 'macd-group',
            data: macdGroup.data
          });
        }
      });

      // Handle semantic key indicators that aren't MACD components
      Object.entries(indicators).forEach(([key, indicatorData]) => {
        if (Array.isArray(indicatorData) && indicatorData.length > 0) {
          const latestPoint = indicatorData[indicatorData.length - 1];

          // Skip if already processed as MACD group or if it's a MACD component
          const isMacdComponent = key.match(/^MACD_\d+_\d+_\d+_\d+_(LINE|SIGNAL|HIST)$/);
          const alreadyProcessed = key.includes('ByIndicatorId') ||
                                 isMacdComponent ||
                                 indicatorValues.some(iv => iv.name.toLowerCase().includes(key.toLowerCase()));

          if (!alreadyProcessed && latestPoint?.value !== undefined) {
            // Parse semantic keys: TYPE_PARAMS_INSTANCE or TYPE_PARAMS_INSTANCE_COMPONENT
            const rsiMatch = key.match(/^RSI_(\d+)_(\d+)$/);
            const emaMatch = key.match(/^EMA_(\d+)_(\d+)$/);
            const smaMatch = key.match(/^SMA_(\d+)_(\d+)$/);
            const bbMatch = key.match(/^BB_(\d+)_([^_]+)_(\d+)_(.+)$/);

            if (rsiMatch) {
              const [, period] = rsiMatch;
              indicatorValues.push({
                name: `RSI (${period})`,
                value: latestPoint.value.toFixed(1),
                type: 'indicator',
                color: getIndicatorColor('RSI', rsiCount)
              });
              rsiCount++;
            } else if (emaMatch) {
              const [, period] = emaMatch;
              indicatorValues.push({
                name: `EMA (${period})`,
                value: latestPoint.value.toFixed(5),
                type: 'indicator',
                color: getIndicatorColor('EMA', emaCount)
              });
              emaCount++;
            } else if (smaMatch) {
              const [, period] = smaMatch;
              indicatorValues.push({
                name: `SMA (${period})`,
                value: latestPoint.value.toFixed(5),
                type: 'indicator',
                color: getIndicatorColor('SMA')
              });
            } else if (bbMatch) {
              const [, period, devfactor, instance, component] = bbMatch;
              if (component === 'MIDDLE') {
                indicatorValues.push({
                  name: `BB Middle (${period},${devfactor})`,
                  value: latestPoint.value.toFixed(5),
                  type: 'indicator',
                  color: getIndicatorColor('BB')
                });
              } else if (component === 'UPPER') {
                indicatorValues.push({
                  name: `BB Upper (${period},${devfactor})`,
                  value: latestPoint.value.toFixed(5),
                  type: 'indicator',
                  color: getIndicatorColor('BB_UPPER')
                });
              } else if (component === 'LOWER') {
                indicatorValues.push({
                  name: `BB Lower (${period},${devfactor})`,
                  value: latestPoint.value.toFixed(5),
                  type: 'indicator',
                  color: getIndicatorColor('BB_LOWER')
                });
              }
            }
          }
        }
      });
    }

    return indicatorValues;
  };

  const indicatorValues = getIndicatorValues();

  return (
    <div className="bg-gradient-to-r from-[#0F1011] to-[#0A0B0B] rounded-xl p-5 border-2 border-[#1a1a1a] shadow-lg">
      <div className="flex items-center justify-start space-x-6">
        {indicatorValues.map((item, index) => (
          <motion.div
            key={`${item.name}-${index}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="flex items-center space-x-3 bg-[#0A0B0B]/50 rounded-lg px-4 py-3 border border-[#1a1a1a]/50"
          >
            {/* Icon for indicator type */}
            <div className="w-8 h-8 bg-[#EFBD3A]/20 rounded-lg flex items-center justify-center">
              {item.type === 'price' ? (
                <TrendingUp className="w-4 h-4 text-[#EFBD3A]" />
              ) : (
                <BarChart3 className="w-4 h-4 text-[#EFBD3A]" />
              )}
            </div>

            {/* Indicator info */}
            <div className="flex flex-col">
              <div className="text-xs font-medium text-[#FEFEFF]/60 uppercase tracking-wide">
                {item.name}
              </div>

              {/* Regular indicator */}
              {item.type !== 'macd-group' && (
                <div
                  className="text-lg font-bold"
                  style={{ color: item.color }}
                >
                  {item.value}
                </div>
              )}

              {/* Grouped MACD */}
              {item.type === 'macd-group' && (
                <div className="space-y-1">
                  {item.data.macd && (
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-[#FEFEFF]/60">M:</span>
                      <span className="px-1.5 py-0.5 text-xs font-medium rounded" style={{ backgroundColor: '#2196F3', color: 'white' }}>
                        {item.data.macd}
                      </span>
                    </div>
                  )}
                  {item.data.signal && (
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-[#FEFEFF]/60">S:</span>
                      <span className="px-1.5 py-0.5 text-xs font-medium rounded" style={{ backgroundColor: '#FF9800', color: 'white' }}>
                        {item.data.signal}
                      </span>
                    </div>
                  )}
                  {item.data.histogram && (
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-[#FEFEFF]/60">H:</span>
                      <span className="px-1.5 py-0.5 text-xs font-medium rounded" style={{ backgroundColor: item.data.histogramColor, color: 'white' }}>
                        {item.data.histogram}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Strategy Info Pills Component - Above Chart
const StrategyInfoPills = ({ strategy, marketStatus, instrument, timeframe, botStatus = null }) => {
  const [inTradingSession, setInTradingSession] = React.useState(true);

  // Check if current time is in any of the trading sessions
  const checkTradingSession = () => {
    const tradingSessions = strategy?.strategy_json?.tradingSession || [];

    // If no trading sessions specified, assume all sessions are valid
    if (!tradingSessions || tradingSessions.length === 0) {
      return true;
    }

    // If strategy includes all major sessions, always return true
    const hasAllSessions = ['New York', 'London', 'Tokyo', 'Sydney'].every(session =>
      tradingSessions.includes(session)
    );

    if (hasAllSessions) {
      return true;
    }

    const currentHour = new Date().getUTCHours();

    // Check if current time is in any of the specified trading sessions
    return tradingSessions.some(session => {
      switch(session) {
        case 'New York':
          return currentHour >= 13 && currentHour < 22;
        case 'London':
          return currentHour >= 8 && currentHour < 17;
        case 'Tokyo':
          return currentHour >= 0 && currentHour < 9;
        case 'Sydney':
          return currentHour >= 22 || currentHour < 7;
        case 'All':
          return true;
        default:
          return false;
      }
    });
  };

  React.useEffect(() => {
    setInTradingSession(checkTradingSession());
    const interval = setInterval(() => {
      setInTradingSession(checkTradingSession());
    }, 60000);
    return () => clearInterval(interval);
  }, [strategy]);

  return (
    <div className="flex items-center justify-between">
      {/* Left side - Chart icon and title */}
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
          <BarChart3 className="w-5 h-5 text-[#FEFEFF]" />
        </div>
        <div>
          <h2 className="text-xl font-bold text-[#FEFEFF]">{instrument} Live Chart</h2>
          <div className="flex items-center space-x-2 mt-1">
            <span className="px-2 py-1 bg-[#0A0B0B] text-[#FEFEFF] text-xs font-medium rounded">
              {timeframe} Timeframe
            </span>
            {strategy?.strategy_json?.tradingSession && strategy.strategy_json.tradingSession.length > 0 && (
              <span className={`px-2 py-1 text-xs font-medium rounded ${inTradingSession ? 'bg-[#0A0B0B] text-green-400' : 'bg-[#0A0B0B] text-yellow-400'}`}>
                {inTradingSession ? 'In Trading Session' : 'Outside Session'}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Right side - Agent and Market status */}
      <div className="flex items-center space-x-2">
        {(botStatus === "paused") ? (
          <>
            <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
            <span className="text-sm font-medium text-yellow-400">Agent Paused</span>
          </>
        ) : (botStatus === "data_stale") ? (
          <>
            <div className="w-2 h-2 rounded-full bg-orange-500"></div>
            <span className="text-sm font-medium text-orange-400">Data Stale</span>
          </>
        ) : marketStatus ? (
          <>
            <div className={`w-2 h-2 rounded-full ${marketStatus.is_open ? "bg-green-400" : "bg-purple-500"}`}></div>
            <span className={`text-sm font-medium ${marketStatus.is_open ? "text-[#FEFEFF]" : "text-purple-400"}`}>
              {marketStatus.is_open ? "Market Open" : "Market Closed"}
            </span>
          </>
        ) : null}
      </div>
    </div>
  );
};

// Chart Display Pills Component - Simplified (no RSI separate/combined)
const ChartDisplayPills = ({ strategy, calculatedIndicators }) => {
  // For now, just return null since we're removing the separate/combined functionality
  return null;
};

// Analytics Tab Component
const AnalyticsTab = ({
  historicalTrades,
  openTrades,
  accountBalance,
  strategySummary,
  marketStatus
}) => {
  return (
    <div className="space-y-6">
      {/* Performance Dashboard with Charts */}
      <PerformanceDashboard
        tradeHistory={historicalTrades}
        accountBalance={accountBalance}
        openTrades={openTrades}
        title="Performance Analytics"
        showTitle={false}
        marketStatus={marketStatus}
      />
    </div>
  );
};

// Trades Tab Component
const TradesTab = ({
  historicalTrades,
  openTrades
}) => {
  const [sortConfig, setSortConfig] = React.useState({ key: 'openTime', direction: 'desc' });
  const [filterStatus, setFilterStatus] = React.useState('all'); // 'all', 'open', 'closed'
  const [currentPage, setCurrentPage] = React.useState(1);
  const [tradesPerPage, setTradesPerPage] = React.useState(25);
  const [activePreset, setActivePreset] = React.useState('essential');
  const [visibleColumns, setVisibleColumns] = React.useState({
    openTime: true,
    tradeID: false,
    instrument: true,
    type: true,
    units: true,
    price: true,
    takeProfitPrice: false,
    stopLossPrice: false,
    initialMarginRequired: false,
    halfSpreadCost: false,
    commission: false,
    realizedPL: true,
    unrealizedPL: true,
    status: true,
    closeTime: true
  });

  // Combine historical trades and open trades
  const allTrades = React.useMemo(() => {
    const combined = [...(historicalTrades || [])];

    // Add open trades if they're not already in historical trades
    if (openTrades && openTrades.length > 0) {
      openTrades.forEach(openTrade => {
        const existsInHistory = combined.some(trade => trade.tradeID === openTrade.tradeID);
        if (!existsInHistory) {
          combined.push({
            ...openTrade,
            status: 'OPEN',
            realizedPL: 0,
            closeTime: null
          });
        }
      });
    }

    return combined;
  }, [historicalTrades, openTrades]);

  // Filter trades based on status
  const filteredTrades = React.useMemo(() => {
    if (filterStatus === 'all') return allTrades;
    if (filterStatus === 'open') return allTrades.filter(trade => trade.status?.toUpperCase() === 'OPEN');
    if (filterStatus === 'closed') return allTrades.filter(trade => trade.status?.toUpperCase() === 'CLOSED');
    return allTrades;
  }, [allTrades, filterStatus]);

  // Sort trades
  const sortedTrades = React.useMemo(() => {
    if (!sortConfig.key) return filteredTrades;

    return [...filteredTrades].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle date sorting
      if (sortConfig.key === 'openTime' || sortConfig.key === 'closeTime') {
        aValue = aValue ? new Date(aValue) : new Date(0);
        bValue = bValue ? new Date(bValue) : new Date(0);
      }

      // Handle numeric sorting
      if (typeof aValue === 'string' && !isNaN(parseFloat(aValue))) {
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      }

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredTrades, sortConfig]);

  // Pagination calculations
  const totalTrades = sortedTrades.length;
  const totalPages = Math.ceil(totalTrades / tradesPerPage);
  const startIndex = (currentPage - 1) * tradesPerPage;
  const endIndex = startIndex + tradesPerPage;
  const paginatedTrades = sortedTrades.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filterStatus, sortConfig, tradesPerPage]);

  const handleSort = (key) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const formatTime = (time) => {
    if (!time) return "N/A";
    try {
      let dateToFormat;

      if (time.seconds && time.nanoseconds) {
        dateToFormat = new Date(time.seconds * 1000 + time.nanoseconds / 1000000);
      } else if (time.toDate) {
        dateToFormat = time.toDate();
      } else if (time instanceof Date) {
        dateToFormat = time;
      } else if (typeof time === "string") {
        dateToFormat = new Date(time.replace("Z", "+00:00"));
      }

      if (!dateToFormat || isNaN(dateToFormat.getTime())) {
        return "Invalid Date";
      }

      return dateToFormat.toLocaleString(undefined, {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } catch (error) {
      return "Invalid Date";
    }
  };

  const tableHeaders = [
    { key: "openTime", label: "Open Time" },
    { key: "tradeID", label: "Trade ID" },
    { key: "instrument", label: "Instrument" },
    { key: "type", label: "Type" },
    { key: "units", label: "Units" },
    { key: "price", label: "Entry Price" },
    { key: "takeProfitPrice", label: "Take Profit" },
    { key: "stopLossPrice", label: "Stop Loss" },
    { key: "initialMarginRequired", label: "Margin" },
    { key: "halfSpreadCost", label: "Spread Cost" },
    { key: "commission", label: "Commission" },
    { key: "realizedPL", label: "Realized P&L" },
    { key: "unrealizedPL", label: "Unrealized P&L" },
    { key: "status", label: "Status" },
    { key: "closeTime", label: "Close Time" }
  ];

  // Column presets for different views
  const columnPresets = {
    essential: {
      openTime: true,
      tradeID: false,
      instrument: true,
      type: true,
      units: true,
      price: true,
      takeProfitPrice: false,
      stopLossPrice: false,
      initialMarginRequired: false,
      halfSpreadCost: false,
      commission: false,
      realizedPL: true,
      unrealizedPL: true,
      status: true,
      closeTime: true
    },
    detailed: {
      openTime: true,
      tradeID: true,
      instrument: true,
      type: true,
      units: true,
      price: true,
      takeProfitPrice: true,
      stopLossPrice: true,
      initialMarginRequired: false,
      halfSpreadCost: false,
      commission: false,
      realizedPL: true,
      unrealizedPL: true,
      status: true,
      closeTime: true
    },
    financial: {
      openTime: true,
      tradeID: false,
      instrument: true,
      type: true,
      units: true,
      price: true,
      takeProfitPrice: false,
      stopLossPrice: false,
      initialMarginRequired: true,
      halfSpreadCost: true,
      commission: true,
      realizedPL: true,
      unrealizedPL: true,
      status: true,
      closeTime: false
    }
  };

  const applyColumnPreset = (presetName) => {
    setVisibleColumns(columnPresets[presetName]);
    setActivePreset(presetName);
  };

  return (
    <div className="space-y-6">
      {/* Trade Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-[#FEFEFF]">{allTrades.length}</div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-[#FEFEFF]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Total Trades</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-[#3B82F6]">
              {allTrades.filter(t => t.status?.toUpperCase() === 'OPEN').length}
            </div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-[#3B82F6]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Open Trades</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-[#10B981]">
              {allTrades.filter(t => t.status?.toUpperCase() === 'CLOSED' && parseFloat(t.realizedPL || 0) > 0).length}
            </div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-[#10B981]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Winning Trades</div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-[#0F1011] rounded-lg p-4 border border-[#1a1a1a]"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="text-2xl font-bold text-[#EF4444]">
              {allTrades.filter(t => t.status?.toUpperCase() === 'CLOSED' && parseFloat(t.realizedPL || 0) < 0).length}
            </div>
            <div className="w-10 h-10 bg-[#0A0B0B] rounded-lg flex items-center justify-center">
              <XCircle className="w-5 h-5 text-[#EF4444]" />
            </div>
          </div>
          <div className="text-sm text-[#FEFEFF]/60">Losing Trades</div>
        </motion.div>
      </div>

      {/* Filters and Controls */}
      <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] p-4 space-y-4">
        {/* Top Row - Filters and Pagination */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side - Filters */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-[#FEFEFF]/60">Filter:</span>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-lg px-3 py-1 text-sm text-[#FEFEFF] focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Trades</option>
                <option value="open">Open Trades</option>
                <option value="closed">Closed Trades</option>
              </select>
            </div>

            {/* Trades Per Page */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-[#FEFEFF]/60">Show:</span>
              <select
                value={tradesPerPage}
                onChange={(e) => setTradesPerPage(Number(e.target.value))}
                className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-lg px-3 py-1 text-sm text-[#FEFEFF] focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={10}>10 per page</option>
                <option value={25}>25 per page</option>
                <option value={50}>50 per page</option>
                <option value={100}>100 per page</option>
              </select>
            </div>
          </div>
        </div>

        {/* Bottom Row - Column Controls */}
        <div className="space-y-3">
          {/* Column Presets */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <span className="text-sm text-[#FEFEFF]/60">View:</span>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => applyColumnPreset('essential')}
                className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                  activePreset === 'essential'
                    ? 'bg-blue-600 text-white'
                    : 'bg-[#0A0B0B] text-[#FEFEFF] hover:bg-[#1a1a1a]'
                }`}
              >
                Essential
              </button>
              <button
                onClick={() => applyColumnPreset('detailed')}
                className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                  activePreset === 'detailed'
                    ? 'bg-blue-600 text-white'
                    : 'bg-[#0A0B0B] text-[#FEFEFF] hover:bg-[#1a1a1a]'
                }`}
              >
                Detailed
              </button>
              <button
                onClick={() => applyColumnPreset('financial')}
                className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                  activePreset === 'financial'
                    ? 'bg-blue-600 text-white'
                    : 'bg-[#0A0B0B] text-[#FEFEFF] hover:bg-[#1a1a1a]'
                }`}
              >
                Financial
              </button>
            </div>
          </div>

          {/* Individual Column Toggles */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <span className="text-sm text-[#FEFEFF]/60">Columns:</span>
            <div className="flex flex-wrap gap-2">
              {tableHeaders.map(header => (
                <button
                  key={header.key}
                  onClick={() => setVisibleColumns(prev => ({ ...prev, [header.key]: !prev[header.key] }))}
                  className={`px-2 py-1 text-xs rounded transition-colors ${
                    visibleColumns[header.key]
                      ? 'bg-blue-600 text-white'
                      : 'bg-[#0A0B0B] text-[#FEFEFF]/60 hover:bg-[#1a1a1a]'
                  }`}
                >
                  {header.label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Pagination Info */}
        {totalTrades > 0 && (
          <div className="mt-4 pt-4 border-t border-[#3a3a3a] flex justify-between items-center text-sm text-[#FEFEFF]/60">
            <span>
              Showing {startIndex + 1} to {Math.min(endIndex, totalTrades)} of {totalTrades} trades
            </span>
            <span>
              Page {currentPage} of {totalPages}
            </span>
          </div>
        )}
      </div>

      {/* Trade History Table */}
      <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-[#1a1a1a]">
            <thead className="bg-[#0A0B0B]">
              <tr>
                {tableHeaders.map(header => {
                  if (!visibleColumns[header.key]) return null;
                  return (
                    <th
                      key={header.key}
                      onClick={() => handleSort(header.key)}
                      className="px-6 py-3 text-left text-xs font-medium text-[#FEFEFF]/80 uppercase tracking-wider cursor-pointer hover:bg-[#0F1011] transition-colors"
                    >
                      <div className="flex items-center space-x-1">
                        <span>{header.label}</span>
                        {sortConfig.key === header.key && (
                          <svg
                            className={`w-4 h-4 transform ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                          </svg>
                        )}
                      </div>
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody className="bg-[#0F1011] divide-y divide-[#1a1a1a]">
              {paginatedTrades.length === 0 ? (
                <tr>
                  <td colSpan={Object.values(visibleColumns).filter(Boolean).length} className="px-6 py-8 text-center text-[#FEFEFF]/60">
                    {totalTrades === 0 ? 'No trades found. Trades will appear here when the agent executes them.' : 'No trades match the current filter.'}
                  </td>
                </tr>
              ) : (
                paginatedTrades.map((trade, index) => (
                  <motion.tr
                    key={trade.tradeID || index}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="hover:bg-[#0A0B0B] transition-colors"
                  >
                    {visibleColumns.openTime && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]">
                        {formatTime(trade.openTime)}
                      </td>
                    )}
                    {visibleColumns.tradeID && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]/80">
                        {trade.tradeID}
                      </td>
                    )}
                    {visibleColumns.instrument && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]">
                        {trade.instrument}
                      </td>
                    )}
                    {visibleColumns.type && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          trade.type === 'long'
                            ? 'bg-[#10B981]/20 text-[#10B981]'
                            : 'bg-[#EF4444]/20 text-[#EF4444]'
                        }`}>
                          {trade.type === 'long' ? 'LONG' : 'SHORT'}
                        </span>
                      </td>
                    )}
                    {visibleColumns.units && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]">
                        {Math.abs(parseFloat(trade.units || 0)).toLocaleString()}
                      </td>
                    )}
                    {visibleColumns.price && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]">
                        {parseFloat(trade.price || 0).toFixed(5)}
                      </td>
                    )}
                    {visibleColumns.takeProfitPrice && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]/80">
                        {trade.takeProfitPrice ? parseFloat(trade.takeProfitPrice).toFixed(5) : 'N/A'}
                      </td>
                    )}
                    {visibleColumns.stopLossPrice && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]/80">
                        {trade.stopLossPrice ? parseFloat(trade.stopLossPrice).toFixed(5) : 'N/A'}
                      </td>
                    )}
                    {visibleColumns.initialMarginRequired && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]/80">
                        ${parseFloat(trade.initialMarginRequired || 0).toFixed(2)}
                      </td>
                    )}
                    {visibleColumns.halfSpreadCost && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]/80">
                        ${parseFloat(trade.halfSpreadCost || 0).toFixed(2)}
                      </td>
                    )}
                    {visibleColumns.commission && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]/80">
                        ${parseFloat(trade.commission || 0).toFixed(2)}
                      </td>
                    )}
                    {visibleColumns.realizedPL && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`font-semibold ${
                          parseFloat(trade.realizedPL || 0) >= 0 ? 'text-[#10B981]' : 'text-[#EF4444]'
                        }`}>
                          {parseFloat(trade.realizedPL || 0) >= 0 ? '+' : ''}${parseFloat(trade.realizedPL || 0).toFixed(2)}
                        </span>
                      </td>
                    )}
                    {visibleColumns.unrealizedPL && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`font-semibold ${
                          parseFloat(trade.unrealizedPL || 0) >= 0 ? 'text-[#10B981]' : 'text-[#EF4444]'
                        }`}>
                          {parseFloat(trade.unrealizedPL || 0) >= 0 ? '+' : ''}${parseFloat(trade.unrealizedPL || 0).toFixed(2)}
                        </span>
                      </td>
                    )}
                    {visibleColumns.status && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                          trade.status?.toUpperCase() === 'OPEN'
                            ? 'bg-[#3B82F6]/20 text-[#3B82F6]'
                            : 'bg-[#6B7280]/20 text-[#6B7280]'
                        }`}>
                          {trade.status?.toUpperCase() || 'UNKNOWN'}
                        </span>
                      </td>
                    )}
                    {visibleColumns.closeTime && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-[#FEFEFF]/80">
                        {trade.status?.toUpperCase() === 'CLOSED' ? formatTime(trade.closeTime) : 'N/A'}
                      </td>
                    )}
                  </motion.tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-[#3a3a3a] flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm bg-[#0A0B0B] text-[#FEFEFF] rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#1a1a1a] transition-colors"
              >
                First
              </button>
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm bg-[#0A0B0B] text-[#FEFEFF] rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#1a1a1a] transition-colors"
              >
                Previous
              </button>
            </div>

            <div className="flex items-center space-x-1">
              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                      currentPage === pageNum
                        ? 'bg-blue-600 text-white'
                        : 'bg-[#0A0B0B] text-[#FEFEFF] hover:bg-[#1a1a1a]'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm bg-[#0A0B0B] text-[#FEFEFF] rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#1a1a1a] transition-colors"
              >
                Next
              </button>
              <button
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm bg-[#0A0B0B] text-[#FEFEFF] rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[#1a1a1a] transition-colors"
              >
                Last
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Strategy Tab Component
const StrategyTab = ({
  strategy,
  riskManagement
}) => {
  return (
    <div className="space-y-6">
      {/* Strategy Review Header */}
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold text-[#FEFEFF]">Strategy Review</h3>
      </div>

      {/* Strategy Logic Flow */}
      {strategy?.strategy_json ? (
        <StrategyLogicFlow
          strategy={{
            ...strategy.strategy_json,
            riskManagement: {
              ...strategy.strategy_json.riskManagement,
              ...riskManagement?.parameters
            }
          }}
        />
      ) : (
        <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] p-8 text-center">
          <div className="text-[#FEFEFF]/60">
            <FileText className="w-16 h-16 mx-auto mb-4 text-[#FEFEFF]/40" />
            <h4 className="text-lg font-medium text-[#FEFEFF] mb-2">No Strategy Data</h4>
            <p className="text-sm">Strategy information will appear here once the agent is configured.</p>
          </div>
        </div>
      )}
    </div>
  );
};

// Market Tab Component
const MarketTab = ({
  marketStatus,
  strategy,
  currentPrice,
  botStatus
}) => {
  // Get current time for trading sessions
  const now = new Date();
  const utcHours = now.getUTCHours();
  const utcMinutes = now.getUTCMinutes();
  const currentUTCTime = `${utcHours.toString().padStart(2, '0')}:${utcMinutes.toString().padStart(2, '0')} UTC`;

  // Calculate time until market reopens (Sunday 21:00 UTC)
  const getMarketReopenTime = () => {
    const now = new Date();
    const currentDay = now.getUTCDay(); // 0 = Sunday, 1 = Monday, etc.
    const currentHour = now.getUTCHours();

    // Market opens Sunday 21:00 UTC
    let nextOpen = new Date(now);

    if (currentDay === 0 && currentHour < 21) {
      // It's Sunday before 21:00 UTC
      nextOpen.setUTCHours(21, 0, 0, 0);
    } else if (currentDay === 5 && currentHour >= 21) {
      // It's Friday after 21:00 UTC (market closes)
      nextOpen.setUTCDate(nextOpen.getUTCDate() + (7 - currentDay)); // Next Sunday
      nextOpen.setUTCHours(21, 0, 0, 0);
    } else if (currentDay === 6) {
      // It's Saturday
      nextOpen.setUTCDate(nextOpen.getUTCDate() + 1); // Next Sunday
      nextOpen.setUTCHours(21, 0, 0, 0);
    } else {
      // Market should be open, return null
      return null;
    }

    const timeDiff = nextOpen.getTime() - now.getTime();
    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    return {
      time: nextOpen,
      hoursUntil: hours,
      minutesUntil: minutes
    };
  };

  const marketReopenInfo = getMarketReopenTime();

  // Trading session data
  const tradingSessions = [
    {
      name: 'Sydney',
      hours: '21:00-06:00 UTC',
      isActive: (utcHours >= 21 || utcHours < 6),
      activity: 'High'
    },
    {
      name: 'Tokyo',
      hours: '00:00-09:00 UTC',
      isActive: (utcHours >= 0 && utcHours < 9),
      activity: 'High'
    },
    {
      name: 'London',
      hours: '08:00-17:00 UTC',
      isActive: (utcHours >= 8 && utcHours < 17),
      activity: 'Low'
    },
    {
      name: 'New York',
      hours: '13:00-22:00 UTC',
      isActive: (utcHours >= 13 && utcHours < 22),
      activity: 'Low'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Agent Stopped State */}
      {(botStatus === "stopped" || botStatus === "insufficient_margin") ? (
        <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] p-12 text-center">
          <div>
            <div className="bg-[#0A0B0B]/50 p-4 rounded-lg inline-flex mb-4">
              <Square className="w-12 h-12 text-[#FEFEFF]/40" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">
              {botStatus === "insufficient_margin" ? "Agent Stopped - Insufficient Margin" : "Agent Stopped"}
            </h3>
            <p className="text-[#FEFEFF]/70 mb-4">
              {botStatus === "insufficient_margin"
                ? "The agent was stopped due to insufficient margin. Please check the analysis below for details and solutions."
                : "This trading agent has been stopped and cannot be resumed."
              }
            </p>
            <p className="text-sm text-[#FEFEFF]/50">
              {botStatus === "insufficient_margin"
                ? "Fix the margin issue and start a new agent to continue trading."
                : "You can start a new agent with another strategy if needed."
              }
            </p>
          </div>
        </div>
      ) : botStatus === "paused" ? (
        <>
          {/* Paused State Banner */}
          <div className="bg-yellow-900/10 border border-yellow-500/30 rounded-lg p-6">
            <div className="flex items-center space-x-3">
              <Pause className="w-6 h-6 text-yellow-400" />
              <div className="flex-1">
                <h3 className="text-lg font-medium text-yellow-400">Agent Paused</h3>
                <p className="text-sm text-yellow-300/80 mb-2">The trading agent is paused and can be resumed at any time</p>
                <p className="text-xs text-yellow-200/60">
                  ⏰ Will automatically stop in 24 hours if not resumed
                </p>
              </div>
            </div>
          </div>

          {/* Current Price Card - Available when paused */}
          {currentPrice && (
            <div className={`rounded-lg border p-6 ${marketStatus?.is_open ? 'bg-[#0F1011] border-[#1a1a1a]' : 'bg-purple-900/10 border-purple-500/30'}`}>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-[#FEFEFF] mb-1">
                    {strategy?.human_readable_rules?.strategy_info?.instrument || "EUR/USD"}
                  </h4>
                  <p className={`text-sm ${marketStatus?.is_open ? 'text-[#FEFEFF]/60' : 'text-purple-400'}`}>
                    {marketStatus?.is_open ? 'Current Price' : 'Last Price (Market Closed)'}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-[#FEFEFF]">
                    {parseFloat(currentPrice).toFixed(5)}
                  </div>
                  <div className="text-sm text-[#FEFEFF]/60">
                    Last updated: {new Date().toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Market Reopening Info */}
          {marketStatus && !marketStatus.is_open && marketReopenInfo && (
            <div className="bg-purple-900/10 border border-purple-500/30 rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-purple-400 mb-1">Market Reopens</h4>
                  <p className="text-sm text-purple-300/80">Sunday 21:00 UTC</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-purple-400">
                    In {marketReopenInfo.hoursUntil}h {marketReopenInfo.minutesUntil}m
                  </div>
                  <div className="text-sm text-purple-300/60">
                    {marketReopenInfo.time.toLocaleDateString()} {marketReopenInfo.time.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <>
          {/* Current Price Card */}
          {currentPrice && (
            <div className={`rounded-lg border p-6 ${marketStatus?.is_open ? 'bg-[#0F1011] border-[#1a1a1a]' : 'bg-purple-900/10 border-purple-500/30'}`}>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-[#FEFEFF] mb-1">
                    {strategy?.human_readable_rules?.strategy_info?.instrument || "EUR/USD"}
                  </h4>
                  <p className={`text-sm ${marketStatus?.is_open ? 'text-[#FEFEFF]/60' : 'text-purple-400'}`}>
                    {marketStatus?.is_open ? 'Current Price' : 'Last Price (Market Closed)'}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-[#FEFEFF]">
                    {parseFloat(currentPrice).toFixed(5)}
                  </div>
                  <div className="text-sm text-[#FEFEFF]/60">
                    Last updated: {new Date().toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Market Reopening Info */}
          {marketStatus && !marketStatus.is_open && marketReopenInfo && (
            <div className="bg-purple-900/10 border border-purple-500/30 rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-lg font-semibold text-purple-400 mb-1">Market Reopens</h4>
                  <p className="text-sm text-purple-300/80">Sunday 21:00 UTC</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-purple-400">
                    In {marketReopenInfo.hoursUntil}h {marketReopenInfo.minutesUntil}m
                  </div>
                  <div className="text-sm text-purple-300/60">
                    {marketReopenInfo.time.toLocaleDateString()} {marketReopenInfo.time.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Market Analysis Section - Show when agent is not stopped */}
      {botStatus !== "stopped" && botStatus !== "insufficient_margin" && (
        <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] overflow-hidden">
          <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {botStatus === 'paused' ? (
                <Pause className={`w-5 h-5 text-yellow-400`} />
              ) : (
                <CheckCircle2 className={`w-5 h-5 ${marketStatus?.is_open ? 'text-green-400' : 'text-purple-400'}`} />
              )}
              <h2 className="text-xl font-bold text-[#FEFEFF]">Market Analysis</h2>
            </div>
            <span className={`text-xs px-3 py-1 rounded-full ${
              botStatus === 'paused'
                ? 'bg-yellow-900/20 text-yellow-400'
                : marketStatus?.is_open
                  ? 'bg-green-900/20 text-green-400'
                  : 'bg-purple-900/20 text-purple-400'
            }`}>
              {botStatus === 'paused' ? 'Agent Paused' : marketStatus?.is_open ? 'Safe to Trade' : 'Market Closed'}
            </span>
          </div>

        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Market Status */}
            <div className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {marketStatus?.is_open ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <Clock className="w-4 h-4 text-purple-400" />
                  )}
                  <span className="text-sm text-[#FEFEFF]/60">Market Status</span>
                </div>
                {marketStatus?.is_open ? (
                  <CheckCircle className="w-4 h-4 text-green-400" />
                ) : (
                  <Clock className="w-4 h-4 text-purple-400" />
                )}
              </div>
              <div className={`text-lg font-bold mb-1 ${marketStatus?.is_open ? 'text-green-400' : 'text-purple-400'}`}>
                {marketStatus?.is_open ? 'Open' : 'Closed'}
              </div>
              <div className="text-xs text-[#FEFEFF]/40">All systems operational</div>
            </div>

            {/* Market Activity */}
            <div className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-green-400" />
                  <span className="text-sm text-[#FEFEFF]/60">Market Activity</span>
                </div>
                <TrendingUp className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-lg font-bold text-green-400 mb-1">High</div>
              <div className="text-xs text-[#FEFEFF]/40">Active centers: Sydney, Tokyo</div>
            </div>

            {/* Trading Session */}
            <div className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-green-400" />
                  <span className="text-sm text-[#FEFEFF]/60">Trading Session</span>
                </div>
                <CheckCircle className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-lg font-bold text-green-400 mb-1">Active</div>
              <div className="text-xs text-[#FEFEFF]/40">All (Current UTC time is in session)</div>
            </div>

            {/* Spread */}
            <div className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-green-400" />
                  <span className="text-sm text-[#FEFEFF]/60">Spread</span>
                </div>
                <CheckCircle className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-lg font-bold text-[#FEFEFF] mb-1">
                {marketStatus?.spread_in_pips ? `${marketStatus.spread_in_pips.toFixed(1)} pips` : 'Unknown'}
              </div>
              <div className="text-xs text-[#FEFEFF]/40">Acceptable for trading</div>
            </div>
          </div>
        </div>
        </div>
      )}

      {/* Global Trading Sessions */}
      {botStatus !== "stopped" && botStatus !== "insufficient_margin" && (
        <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] overflow-hidden">
        <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center space-x-2">
          <Clock className="w-5 h-5 text-yellow-400" />
          <h2 className="text-xl font-bold text-[#FEFEFF]">Global Trading Sessions</h2>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {tradingSessions.map((session) => (
              <div key={session.name} className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-[#FEFEFF]">{session.name}</h3>
                  <div className={`w-2 h-2 rounded-full ${session.isActive ? 'bg-green-400' : 'bg-[#FEFEFF]/30'}`}></div>
                </div>
                <div className={`text-sm font-medium mb-1 ${session.isActive ? 'text-green-400' : 'text-[#FEFEFF]/50'}`}>
                  {session.isActive ? 'ACTIVE' : 'CLOSED'}
                </div>
                <div className="text-xs text-[#FEFEFF]/60 mb-2">{session.hours}</div>
                <div className="text-xs text-[#FEFEFF]/40">Activity: {session.activity}</div>
              </div>
            ))}
          </div>
        </div>
        </div>
      )}

      {/* Market Health */}
      {botStatus !== "stopped" && botStatus !== "insufficient_margin" && (
        <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] overflow-hidden">
        <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center space-x-2">
          <Heart className="w-5 h-5 text-green-400" />
          <h2 className="text-xl font-bold text-[#FEFEFF]">Market Health</h2>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Liquidity */}
            <div className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-[#FEFEFF]/60">Liquidity</span>
                <CheckCircle className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-lg font-bold text-green-400 mb-1">Excellent</div>
              <div className="text-xs text-[#FEFEFF]/40">Deep order book</div>
            </div>

            {/* Volatility */}
            <div className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-[#FEFEFF]/60">Volatility</span>
                <Zap className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-lg font-bold text-green-400 mb-1">Normal</div>
              <div className="text-xs text-[#FEFEFF]/40">Stable price action</div>
            </div>

            {/* Connection */}
            <div className="bg-[#0A0B0B] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-[#FEFEFF]/60">Connection</span>
                <Wifi className="w-4 h-4 text-green-400" />
              </div>
              <div className="text-lg font-bold text-green-400 mb-1">Stable</div>
              <div className="text-xs text-[#FEFEFF]/40">Low latency feed</div>
            </div>
          </div>

          {/* Last Updated */}
          <div className="flex items-center justify-center space-x-2 text-xs text-[#FEFEFF]/40">
            <Clock className="w-4 h-4" />
            <span>Last updated: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}</span>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
      )}
    </div>
  );
};

// Logs Tab Component
const LogsTab = ({
  tradeLogs
}) => {
  return (
    <div className="space-y-6">
      {/* Logs Header */}
      <div className="flex justify-between items-center">
        <h3 className="text-2xl font-bold text-[#FEFEFF]">Trade Agent Logs</h3>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-sm text-[#FEFEFF]/60">Live</span>
        </div>
      </div>

      {/* User Logs Component */}
      <UserLogs logs={tradeLogs} />
    </div>
  );
};

export default TradeBotTabs;
