import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Bell, RotateCcw, Play, Pause, Square } from 'lucide-react';
import { PauseConfirmationDialog, StopConfirmationDialog } from './ConfirmationDialog';

const TradeBotHeader = ({
  strategyName = "AI Trading Agent",
  instrument = "EUR/USD",
  timeframe = "2m",
  status = "stopped",
  autoRefresh = false,
  refreshInterval = 60000,
  onAutoRefreshToggle,
  onRefreshIntervalChange,
  onRefresh,
  onPause,
  onResume,
  onStop,
  actionLoading = false
}) => {
  const [showPauseDialog, setShowPauseDialog] = useState(false);
  const [showStopDialog, setShowStopDialog] = useState(false);

  const handlePauseClick = () => {
    setShowPauseDialog(true);
  };

  const handleStopClick = () => {
    setShowStopDialog(true);
  };

  const handleConfirmPause = async () => {
    setShowPauseDialog(false);
    if (onPause) {
      await onPause();
    }
  };

  const handleConfirmStop = async () => {
    setShowStopDialog(false);
    if (onStop) {
      await onStop();
    }
  };
  const getStatusConfig = (status) => {
    switch (status) {
      case 'running':
        return {
          text: 'Live',
          color: 'text-green-400',
          bgColor: 'bg-green-500/20',
          dotColor: 'bg-green-500',
          animate: true
        };
      case 'paused':
        return {
          text: 'Paused',
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-500/20',
          dotColor: 'bg-yellow-500',
          animate: false
        };
      case 'stopped':
        return {
          text: 'Stopped',
          color: 'text-[#FEFEFF]/60',
          bgColor: 'bg-[#FEFEFF]/10',
          dotColor: 'bg-[#FEFEFF]/60',
          animate: false
        };
      case 'data_stale':
        return {
          text: 'Data Stale',
          color: 'text-orange-400',
          bgColor: 'bg-orange-500/20',
          dotColor: 'bg-orange-500',
          animate: false
        };
      case 'market_closed':
        return {
          text: 'Market Closed',
          color: 'text-purple-400',
          bgColor: 'bg-purple-500/20',
          dotColor: 'bg-purple-500',
          animate: false
        };
      case 'error':
        return {
          text: 'Error',
          color: 'text-red-400',
          bgColor: 'bg-red-500/20',
          dotColor: 'bg-red-500',
          animate: true
        };
      case 'insufficient_margin':
        return {
          text: 'Insufficient Margin',
          color: 'text-red-400',
          bgColor: 'bg-red-500/20',
          dotColor: 'bg-red-500',
          animate: false
        };
      default:
        return {
          text: 'Unknown',
          color: 'text-[#FEFEFF]/60',
          bgColor: 'bg-[#FEFEFF]/10',
          dotColor: 'bg-[#FEFEFF]/60',
          animate: false
        };
    }
  };

  const statusConfig = getStatusConfig(status);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-[#0A0B0B] px-6 py-3"
    >
      <div className="flex items-center justify-between">
        {/* Left side - Strategy Info */}
        <div className="flex items-center space-x-6">
          <div className="text-[#FEFEFF] font-medium text-lg">
            {strategyName}
          </div>
          <div className="flex items-center space-x-2 text-[#FEFEFF]/60 text-sm">
            <span>{instrument}</span>
            <span>•</span>
            <span>{timeframe}</span>
          </div>

          {/* Status Indicator */}
          <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-full ${statusConfig.bgColor}`}>
            <div className={`w-2 h-2 rounded-full ${statusConfig.dotColor} ${statusConfig.animate ? 'animate-pulse' : ''}`} />
            <span className={`text-sm font-medium ${statusConfig.color}`}>
              {statusConfig.text}
            </span>
          </div>
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center space-x-3">
          {/* Auto-refresh Container - Styled like wireframe */}
          <div className="flex items-center bg-[#0F1011] rounded-xl px-4 py-2 border border-[#1a1a1a]">
            <span className="text-sm text-[#FEFEFF]/60 mr-4">Auto-refresh</span>

            {/* Toggle Switch */}
            <div className="relative inline-block w-11 h-6">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={onAutoRefreshToggle}
                className="sr-only"
                id="autoRefreshToggle"
              />
              <label
                htmlFor="autoRefreshToggle"
                className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors duration-200 ease-in-out ${
                  autoRefresh ? 'bg-[#EFBD3A]' : 'bg-[#1a1a1a]'
                }`}
              >
                <span
                  className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform duration-200 ease-in-out ${
                    autoRefresh ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </label>
            </div>

            {/* Refresh Interval Dropdown */}
            {autoRefresh && (
              <select
                value={refreshInterval}
                onChange={(e) => onRefreshIntervalChange && onRefreshIntervalChange(Number(e.target.value))}
                className="ml-3 bg-[#0F1011] border border-[#1a1a1a] rounded-md px-2 py-1 text-xs text-[#FEFEFF] focus:outline-none focus:ring-1 focus:ring-[#EFBD3A]"
              >
                <option value={30000}>30s</option>
                <option value={60000}>1m</option>
                <option value={300000}>5m</option>
                <option value={600000}>10m</option>
              </select>
            )}

            {/* Manual Refresh Button */}
            <button
              onClick={onRefresh}
              className="ml-3 p-1.5 bg-[#0F1011] hover:bg-[#1a1a1a] border border-[#1a1a1a] rounded-md text-[#FEFEFF]/60 hover:text-[#FEFEFF] transition-colors"
              title="Refresh data"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>

          {/* Notification Bell */}
          <button className="p-3 rounded-xl bg-[#0F1011] hover:bg-[#1a1a1a] border border-[#1a1a1a] transition-colors">
            <Bell className="w-5 h-5 text-[#FEFEFF]/60" />
          </button>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {status === 'running' && (
              <button
                onClick={handlePauseClick}
                disabled={actionLoading}
                className="p-3 bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-[#0A0B0B] rounded-xl transition-colors disabled:opacity-50"
                title="Pause Agent"
              >
                <Pause className="w-5 h-5" />
              </button>
            )}

            {status === 'paused' && (
              <button
                onClick={onResume}
                disabled={actionLoading}
                className="p-3 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-colors disabled:opacity-50"
                title="Resume Agent"
              >
                <Play className="w-5 h-5" />
              </button>
            )}

            {['running', 'paused', 'error'].includes(status) && (
              <button
                onClick={handleStopClick}
                disabled={actionLoading}
                className="p-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-colors disabled:opacity-50"
                title="Stop Agent"
              >
                <Square className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Confirmation Dialogs */}
      <PauseConfirmationDialog
        isOpen={showPauseDialog}
        onClose={() => setShowPauseDialog(false)}
        onConfirm={handleConfirmPause}
        loading={actionLoading}
      />

      <StopConfirmationDialog
        isOpen={showStopDialog}
        onClose={() => setShowStopDialog(false)}
        onConfirm={handleConfirmStop}
        loading={actionLoading}
      />
    </motion.div>
  );
};

export default TradeBotHeader;
