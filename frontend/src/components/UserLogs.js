import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

const UserLogs = ({ logs = [] }) => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [autoScroll, setAutoScroll] = useState(false);
  const logsEndRef = useRef(null);

  // Define log categories and their icons/colors
  const logCategories = {
    trade: {
      icon: (
        <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
      color: 'text-green-400',
      bgColor: 'bg-green-900/30',
      borderColor: 'border-green-500/30'
    },
    error: {
      icon: (
        <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'text-red-400',
      bgColor: 'bg-red-900/30',
      borderColor: 'border-red-500/30'
    },
    warning: {
      icon: (
        <svg className="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      ),
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-900/30',
      borderColor: 'border-yellow-500/30'
    },
    info: {
      icon: (
        <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'text-blue-400',
      bgColor: 'bg-blue-900/30',
      borderColor: 'border-blue-500/30'
    },
    status: {
      icon: (
        <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'text-purple-400',
      bgColor: 'bg-purple-900/30',
      borderColor: 'border-purple-500/30'
    }
  };

  // Categorize logs based on content
  const categorizeLog = (message) => {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('trade') || lowerMessage.includes('position') || lowerMessage.includes('order') ||
        lowerMessage.includes('buy') || lowerMessage.includes('sell') || lowerMessage.includes('executed')) {
      return 'trade';
    } else if (lowerMessage.includes('error') || lowerMessage.includes('failed') || lowerMessage.includes('❌')) {
      return 'error';
    } else if (lowerMessage.includes('warning') || lowerMessage.includes('caution') || lowerMessage.includes('⚠️')) {
      return 'warning';
    } else if (lowerMessage.includes('status') || lowerMessage.includes('running') ||
               lowerMessage.includes('paused') || lowerMessage.includes('stopped')) {
      return 'status';
    } else {
      return 'info';
    }
  };

  // Filter logs based on selected category and search term
  const filteredLogs = logs.filter(log => {
    const category = categorizeLog(log.message);
    const matchesFilter = filter === 'all' || category === filter;
    const matchesSearch = !searchTerm || log.message.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // Sort logs by timestamp (newest first)
  const sortedLogs = [...filteredLogs].sort((a, b) => {
    const timeA = new Date(a.timestamp).getTime();
    const timeB = new Date(b.timestamp).getTime();
    return timeB - timeA;
  });

  // Auto-scroll to bottom when new logs arrive, but only if we're already near the bottom
  useEffect(() => {
    if (autoScroll && logsEndRef.current) {
      const container = logsEndRef.current.parentElement;
      const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100;

      if (isNearBottom) {
        logsEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [logs, autoScroll]);

  return (
    <div className="bg-[#0F1011] rounded-lg border border-[#1a1a1a] overflow-hidden">
      <div className="px-4 py-3 border-b border-[#1a1a1a] flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
        <h2 className="text-lg font-semibold text-[#FEFEFF]">Activity Logs</h2>
        <div className="flex flex-wrap items-center gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-[#0A0B0B] border border-[#1a1a1a] rounded-md px-3 py-1 text-sm text-white w-full sm:w-auto focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <div className="flex items-center space-x-1 bg-[#0A0B0B] rounded-md p-1">
            <button
              onClick={() => setFilter('all')}
              className={`px-2 py-1 text-xs rounded-md ${
                filter === 'all' ? 'bg-[#EFBD3A]/20 text-[#EFBD3A]' : 'text-gray-400 hover:text-white'
              }`}
            >
              All
            </button>
            <button
              onClick={() => setFilter('trade')}
              className={`px-2 py-1 text-xs rounded-md ${
                filter === 'trade' ? 'bg-green-900/30 text-green-400' : 'text-gray-400 hover:text-white'
              }`}
            >
              Trades
            </button>
            <button
              onClick={() => setFilter('error')}
              className={`px-2 py-1 text-xs rounded-md ${
                filter === 'error' ? 'bg-red-900/30 text-red-400' : 'text-gray-400 hover:text-white'
              }`}
            >
              Errors
            </button>
            <button
              onClick={() => setFilter('status')}
              className={`px-2 py-1 text-xs rounded-md ${
                filter === 'status' ? 'bg-purple-900/30 text-purple-400' : 'text-gray-400 hover:text-white'
              }`}
            >
              Status
            </button>
          </div>
          <div className="flex items-center">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoScroll}
                onChange={() => setAutoScroll(!autoScroll)}
                className="sr-only"
              />
              <div className={`w-9 h-5 ${autoScroll ? 'bg-[#EFBD3A]' : 'bg-gray-600'} rounded-full transition-colors duration-200 flex items-center ${autoScroll ? 'justify-end' : 'justify-start'} px-0.5`}>
                <div className="w-4 h-4 bg-white rounded-full"></div>
              </div>
              <span className="ml-2 text-xs text-gray-400">Auto-scroll</span>
            </label>
          </div>
        </div>
      </div>
      <div className="space-y-2 max-h-96 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
        {sortedLogs.length > 0 ? (
          sortedLogs.map((log, index) => {
            const category = categorizeLog(log.message);
            const categoryConfig = logCategories[category];

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index < 5 ? index * 0.05 : 0 }}
                className={`${categoryConfig.bgColor} p-4 rounded-lg border ${categoryConfig.borderColor} hover:border-blue-500/30 transition-colors duration-200`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className={`${categoryConfig.color}`}>
                      {categoryConfig.icon}
                    </div>
                    <p className="text-sm text-[#FEFEFF]/70 font-medium">
                      {new Date(log.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${categoryConfig.bgColor} ${categoryConfig.color} font-medium`}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </span>
                </div>
                <p className="text-[#FEFEFF] text-sm leading-relaxed whitespace-pre-wrap break-words">
                  {log.message}
                </p>
              </motion.div>
            );
          })
        ) : (
          <div className="text-center py-8">
            <svg className="w-12 h-12 mx-auto text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-gray-500">No logs found</p>
            {(filter !== 'all' || searchTerm) && (
              <button
                onClick={() => {
                  setFilter('all');
                  setSearchTerm('');
                }}
                className="mt-2 text-blue-400 text-sm hover:underline"
              >
                Clear filters
              </button>
            )}
          </div>
        )}
        <div ref={logsEndRef} />
      </div>
      <div className="px-4 py-2 border-t border-[#1a1a1a] flex justify-between items-center">
        <span className="text-xs text-gray-500">
          Showing {sortedLogs.length} of {logs.length} logs
        </span>
        <button
          onClick={() => {
            setFilter('all');
            setSearchTerm('');
          }}
          className="text-xs text-[#EFBD3A] hover:underline"
          disabled={filter === 'all' && !searchTerm}
        >
          Reset filters
        </button>
      </div>
    </div>
  );
};

export default UserLogs;
