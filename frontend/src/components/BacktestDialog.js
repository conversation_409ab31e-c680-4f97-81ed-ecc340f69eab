import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import { auth } from '../../firebaseConfig';
import { USE_FIREBASE_EMULATOR } from '../config';

const RUN_BACKTEST_URL = USE_FIREBASE_EMULATOR
  ? "http://127.0.0.1:5001/oryntrade/us-central1/run_oryn_backtest"
  : "https://run-oryn-backtest-ihjc6tjxia-uc.a.run.app";
console.log('RUN_BACKTEST_URL:', RUN_BACKTEST_URL);

const TRADING_SESSIONS = [
  { value: "24/7", label: "All Trading Sessions" },
  { value: "London", label: "London Session (8:00-16:00 GMT)" },
  { value: "New York", label: "New York Session (13:00-21:00 GMT)" },
  { value: "Tokyo", label: "Tokyo Session (0:00-8:00 GMT)" },
  { value: "Sydney", label: "Sydney Session (22:00-6:00 GMT)" },
];

const BacktestDialog = ({ isOpen, onClose, strategy, onBacktestComplete }) => {
  // Strategy data is now pre-transformed by StrategyLibrary or passed directly from strategy-generation
  const strategyData = strategy;

  console.log('Strategy data received by BacktestDialog:', strategyData);

  const [backtestResults, setBacktestResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState('');
  const [showBacktestForm, setShowBacktestForm] = useState(true);
  const [showSessionsDropdown, setShowSessionsDropdown] = useState(false);
  const [backtestSettings, setBacktestSettings] = useState({
    startingBalance: 100000,
    timeframe: strategyData?.timeframe || '1h',
    tradingSession: strategyData?.tradingSession || [],
    years: 1
  });

  const [validationErrors, setValidationErrors] = useState({
    startingBalance: '',
    years: ''
  });

  // Helper function to get max years based on timeframe
  const getMaxYears = (timeframe) => {
    if (timeframe === '4h') return 2;
    if (timeframe === '1d') return 5;
    return 1; // For timeframes <= 1h
  };

  // Helper function to get period description
  const getPeriodDescription = (timeframe) => {
    const maxYears = getMaxYears(timeframe);
    if (maxYears === 1) return "For timeframes of 1 hour or less, maximum period is 1 year";
    if (maxYears === 2) return "For 4-hour timeframe, maximum period is 2 years";
    return "For daily timeframe, maximum period is 5 years";
  };

  // Helper function to validate settings
  const validateSettings = (settings) => {
    const errors = {
      startingBalance: '',
      years: ''
    };

    // Validate starting balance
    if (!settings.startingBalance || settings.startingBalance < 1000) {
      errors.startingBalance = 'Starting balance must be at least $1,000';
    }

    // Validate years based on timeframe
    const maxYears = getMaxYears(settings.timeframe);
    if (settings.years > maxYears) {
      errors.years = `Maximum period for ${settings.timeframe} timeframe is ${maxYears} year${maxYears > 1 ? 's' : ''}`;
    } else if (settings.years < 1) {
      errors.years = 'Minimum period is 1 year';
    }

    return errors;
  };

  // Helper function to check if form is valid
  const isFormValid = () => {
    return Object.values(validationErrors).every(error => !error) &&
           backtestSettings.startingBalance >= 1000 &&
           backtestSettings.years >= 1 &&
           backtestSettings.years <= getMaxYears(backtestSettings.timeframe);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    let newSettings;
    if (name === 'timeframe') {
      const maxYears = getMaxYears(value);
      newSettings = {
        ...backtestSettings,
        [name]: value,
        years: Math.min(backtestSettings.years, maxYears)
      };
    } else {
      newSettings = {
        ...backtestSettings,
        [name]: name === 'years' || name === 'startingBalance' ?
          (value === '' ? '' : Number(value)) : value
      };
    }

    setBacktestSettings(newSettings);
    setValidationErrors(validateSettings(newSettings));
  };

  const handleTradingSessionChange = (value) => {
    if (value === "24/7") {
      // If All Trading Sessions is toggled
      if (backtestSettings.tradingSession.includes("24/7")) {
        setBacktestSettings({ ...backtestSettings, tradingSession: [] });
      } else {
        setBacktestSettings({ ...backtestSettings, tradingSession: ["24/7"] });
      }
      return;
    }
    // For any other session, remove '24/7' and toggle the session
    let newSessions = backtestSettings.tradingSession.filter(s => s !== "24/7");
    if (newSessions.includes(value)) {
      newSessions = newSessions.filter(s => s !== value);
    } else {
      newSessions = [...newSessions, value];
    }
    // If after toggling, all sessions except '24/7' are selected, auto-select '24/7' instead
    if (newSessions.length === TRADING_SESSIONS.length - 1) {
      setBacktestSettings({ ...backtestSettings, tradingSession: ["24/7"] });
    } else {
      setBacktestSettings({ ...backtestSettings, tradingSession: newSessions });
    }
  };

  const getTradingSessionLabel = () => {
    // Handle both '24/7' and 'All' as 'All Trading Sessions'
    const allValues = ["24/7", "All"];
    if (!backtestSettings.tradingSession.length) return 'Select Trading Sessions';
    if (
      backtestSettings.tradingSession.length === 1 &&
      allValues.includes(backtestSettings.tradingSession[0])
    ) return 'All Trading Sessions';
    if (
      backtestSettings.tradingSession.length === TRADING_SESSIONS.length - 1 &&
      !backtestSettings.tradingSession.some(val => allValues.includes(val))
    ) return 'All Sessions';
    return backtestSettings.tradingSession
      .filter(val => !allValues.includes(val))
      .map(val => TRADING_SESSIONS.find(s => s.value === val)?.label || val)
      .join(', ');
  };



  const handleRunBacktest = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate strategy data
      if (!strategyData) {
        throw new Error('No strategy data available');
      }

      if (!strategyData.instruments) {
        throw new Error('Strategy instruments not specified');
      }

      // Skip fetching historical data in frontend - backend will handle it
      console.log('Preparing backtest request...');
      console.log('Strategy data:', strategyData);
      console.log('Instruments:', strategyData.instruments);
      console.log('Backtest settings:', backtestSettings);

      setStatus('Preparing backtest request...');
      setProgress(20);

      // Transform rule fields to match backend expectations
      const transformRule = (rule) => ({
        trade_type: rule.tradeType.toLowerCase(),
        indicator1: rule.indicator1,
        operator: rule.operator,
        compare_type: rule.compareType,
        indicator2: rule.indicator2 || null,
        value: rule.value || null,
        logical_operator: rule.logicalOperator || "AND",
        bar_ref: rule.barRef || "",
        band: rule.band || null,  // Include band property for Bollinger Bands (first indicator)
        band2: rule.band2 || null,  // Include band2 property for Bollinger Bands (second indicator)
        macd_component: rule.macdComponent || null,  // Include macdComponent property for MACD (first indicator)
        macd_component2: rule.macdComponent2 || null  // Include macdComponent2 property for MACD (second indicator)
      });

      // Transform the strategy data to match backend expectations
      const { entryRules, exitRules, riskManagement, tradingSession, ...restStrategy } = strategyData;

      // Log the original risk management data
      console.log("Original risk management data:", JSON.stringify(riskManagement, null, 2));

      // Transform the risk management data to match backend expectations
      let transformedRiskManagement;

      // Check if we have the new risk management format
      if (riskManagement?.riskRewardRatio || riskManagement?.stopLossMethod) {
        console.log("Using new risk management format");
        // Use the new risk management format
        transformedRiskManagement = {
          risk_percentage: riskManagement.riskPercentage || "1",
          risk_reward_ratio: riskManagement.riskRewardRatio || "2",
          stop_loss_method: riskManagement.stopLossMethod || "fixed",
          fixed_pips: riskManagement.stopLossMethod === 'fixed' ? String(riskManagement.fixedPips || "50") : "",
          indicator_based_sl: riskManagement.stopLossMethod === 'indicator' ? {
            indicator: riskManagement.indicatorBasedSL?.indicator || "",
            parameters: riskManagement.indicatorBasedSL?.parameters || {}
          } : undefined,
          lot_size: riskManagement.stopLossMethod === 'risk' ? riskManagement.lotSize : ""
        };

        // Log the transformed risk management data
        console.log("Transformed risk management data:", JSON.stringify(transformedRiskManagement, null, 2));
      } else {
        console.log("Using legacy risk management format");
        // Fall back to legacy format
        const stopLossValue = riskManagement?.stopLoss || "2";
        const takeProfitValue = riskManagement?.takeProfit || "4";
        const stopLossUnit = riskManagement?.stopLossUnit || "percentage";
        const takeProfitUnit = riskManagement?.takeProfitUnit || "percentage";

        transformedRiskManagement = {
          stop_loss: stopLossUnit === 'percentage' ? `${stopLossValue}%` : `${stopLossValue} pips`,
          take_profit: takeProfitUnit === 'percentage' ? `${takeProfitValue}%` : `${takeProfitValue} pips`,
          risk_percentage: "1",
          risk_reward_ratio: "2"
        };

        // Log the transformed risk management data
        console.log("Transformed legacy risk management data:", JSON.stringify(transformedRiskManagement, null, 2));
      }

      // Map trading sessions to backend-compatible format
      const mapTradingSession = (session) => {
        switch (session) {
          case '24/7':
            return 'All';
          case 'New York Session (13:30-22:00 GMT)':
            return 'New York';
          case 'London Session (8:00-16:30 GMT)':
            return 'London';
          case 'Tokyo Session (0:00-8:30 GMT)':
            return 'Tokyo';
          case 'Sydney Session (22:00-7:00 GMT)':
            return 'Sydney';
          default:
            return session; // Return as-is if already in correct format
        }
      };

      const mappedTradingSessions = backtestSettings.tradingSession && backtestSettings.tradingSession.length > 0
        ? backtestSettings.tradingSession.map(mapTradingSession)
        : ["All"];

      console.log('Original trading sessions:', backtestSettings.tradingSession);
      console.log('Mapped trading sessions:', mappedTradingSessions);

      const transformedStrategy = {
        ...restStrategy,
        timeframe: backtestSettings.timeframe,
        indicators: strategyData.indicators.map(indicator => ({
          id: indicator.id,
          type: indicator.indicator_class || indicator.type,
          parameters: indicator.parameters,
          source: indicator.source || 'price'
        })),
        entry_rules: entryRules.map(transformRule),
        exit_rules: exitRules.map(transformRule),
        risk_management: transformedRiskManagement,
        trading_sessions: mappedTradingSessions,
      };

      // Log the transformed strategy for debugging
      console.log('Transformed strategy:', JSON.stringify(transformedStrategy, null, 2));

      // Create an AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3600000); // 60 minute timeout (matches backend)

      let results;
      try {
        // Update status for user feedback
        setStatus('Fetching historical data and running backtest analysis...');
        setProgress(50);

        // Send backtest request with strategy and configuration
        const response = await fetch(RUN_BACKTEST_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            strategy: transformedStrategy,
            backtestConfig: {
              symbol: strategyData.instruments,
              timeframe: backtestSettings.timeframe,
              years: backtestSettings.years,
              startingBalance: backtestSettings.startingBalance
            }
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to run backtest');
        }

        setStatus('Processing results...');
        setProgress(90);

        results = await response.json();
      } catch (fetchError) {
        clearTimeout(timeoutId);

        if (fetchError.name === 'AbortError') {
          throw new Error('Backtest timed out after 60 minutes. This indicates an issue with the backtest processing. Please try again or contact support if the problem persists.');
        }

        throw fetchError;
      }

      setStatus('Backtest completed successfully!');
      setProgress(100);

      // Small delay to show completion status
      setTimeout(() => {
        onBacktestComplete(results);
        onClose();
      }, 500);
    } catch (error) {
      console.error('Backtest error:', error);
      setError(error.message || 'Failed to run backtest');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      setShowBacktestForm(true);
      setBacktestResults(null);
      setError(null);
      setProgress(0);
      setStatus('');
      setBacktestSettings(prev => ({
        ...prev,
        tradingSession: strategyData?.tradingSession || []
      }));
    }
  }, [isOpen, strategyData]);

  useEffect(() => {
    if (strategyData) {
      setBacktestSettings(prev => ({
        ...prev,
        timeframe: strategyData.timeframe || prev.timeframe,
        tradingSession: strategyData.tradingSession || prev.tradingSession
      }));
    }
  }, [strategyData]);

  // Add this CSS at the top of your component or in your global styles
  const numberInputStyles = `
    /* Remove spinner arrows for Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Remove spinner arrows for Firefox */
    input[type=number] {
      -moz-appearance: textfield;
    }
    /* Ensure all checkboxes in the Trading Sessions dropdown are the same size */
    .trading-session-checkbox {
      width: 1.25rem !important;
      height: 1.25rem !important;
      min-width: 1.25rem !important;
      min-height: 1.25rem !important;
      max-width: 1.25rem !important;
      max-height: 1.25rem !important;
    }
  `;

  if (!isOpen) return null;

  return (
    <>
      <style>{numberInputStyles}</style>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-[#0c0f1c] p-6 rounded-xl border border-white/10 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto"
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-[#FEFEFF]">Strategy Backtest</h2>
            <button
              onClick={onClose}
              className="text-[#FEFEFF]/60 hover:text-[#FEFEFF] transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {showBacktestForm && !isLoading && !backtestResults && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-[#FEFEFF]/60">
                    Starting Balance ($)
                  </label>
                  <div className="flex flex-col">
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#FEFEFF]/40">$</span>
                      <input
                        type="number"
                        name="startingBalance"
                        value={backtestSettings.startingBalance}
                        onChange={handleInputChange}
                        className={`w-full pl-8 pr-3 py-2 bg-white/5 border rounded-lg text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] ${
                          validationErrors.startingBalance ? 'border-red-500' : 'border-white/10'
                        }`}
                        min="1000"
                        step="1000"
                      />
                    </div>
                    {validationErrors.startingBalance && (
                      <p className="text-sm text-red-500 mt-1">{validationErrors.startingBalance}</p>
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-[#FEFEFF]/60">
                    Timeframe
                  </label>
                  <div className="relative">
                    <select
                      name="timeframe"
                      value={backtestSettings.timeframe}
                      onChange={handleInputChange}
                      className="w-full pl-3 pr-10 py-2 bg-white/5 border border-white/10 rounded-lg text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] appearance-none cursor-pointer"
                    >
                      <option value="1m">1 Minute</option>
                      <option value="5m">5 Minutes</option>
                      <option value="15m">15 Minutes</option>
                      <option value="30m">30 Minutes</option>
                      <option value="1h">1 Hour</option>
                      <option value="4h">4 Hours</option>
                      <option value="1d">1 Day</option>
                    </select>
                    <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                      <svg className="w-4 h-4 text-[#FEFEFF]/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-[#FEFEFF]/60">
                    Trading Sessions
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      className="w-full pl-3 pr-10 py-2 bg-gradient-to-r from-[#181c2a] to-[#23263a] border border-white/10 rounded-lg text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] appearance-none cursor-pointer flex justify-between items-center shadow-lg hover:shadow-xl transition-all"
                      onClick={() => setShowSessionsDropdown((v) => !v)}
                    >
                      <span className="truncate text-left font-medium text-[#FEFEFF]">{getTradingSessionLabel()}</span>
                      <svg className={`w-4 h-4 text-[#EFBD3A] ml-2 transition-transform ${showSessionsDropdown ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    {showSessionsDropdown && (
                      <div className="absolute z-50 w-full mt-1 bg-gradient-to-br from-[#181c2a] to-[#23263a] rounded-xl shadow-2xl border border-[#EFBD3A]/20 max-h-60 overflow-y-auto animate-fadeIn">
                        <div className="py-2">
                          {TRADING_SESSIONS.map((session, idx) => {
                            const isAll = session.value === "24/7";
                            const isAllSelected = backtestSettings.tradingSession.includes("24/7");
                            const isDisabled = isAll ? false : isAllSelected;
                            return (
                              <label
                                key={session.value}
                                className={`flex items-center px-5 py-3 cursor-pointer rounded-lg transition-all duration-150 mb-1
                                  ${isDisabled ? 'opacity-40 cursor-not-allowed' : 'hover:bg-[#EFBD3A]/10'}
                                  ${backtestSettings.tradingSession.includes(session.value) && !isDisabled ? 'bg-[#EFBD3A]/10 border-l-4 border-[#EFBD3A]' : ''}
                                `}
                                style={{ pointerEvents: isDisabled ? 'none' : 'auto' }}
                              >
                                <input
                                  type="checkbox"
                                  checked={backtestSettings.tradingSession.includes(session.value)}
                                  onChange={() => handleTradingSessionChange(session.value)}
                                  className="form-checkbox trading-session-checkbox text-[#EFBD3A] border-white/20 rounded focus:ring-[#EFBD3A] mr-4 transition-all duration-150"
                                  disabled={isDisabled}
                                />
                                <span className={`text-base font-medium ${isAll ? 'text-[#EFBD3A]' : 'text-[#FEFEFF]'}`}>{session.label}</span>
                              </label>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-[#FEFEFF]/60">
                  Backtest Period (Years)
                </label>
                <div className="flex flex-col space-y-4">
                  <div className="flex items-start gap-4">
                    <div className="relative flex flex-col">
                      <input
                        type="number"
                        name="years"
                        value={backtestSettings.years}
                        onChange={handleInputChange}
                        min="1"
                        max={getMaxYears(backtestSettings.timeframe)}
                        step="1"
                        className={`w-32 px-3 py-2 bg-white/5 border rounded-lg text-[#FEFEFF] focus:outline-none focus:border-[#EFBD3A] ${
                          validationErrors.years ? 'border-red-500' : 'border-white/10'
                        }`}
                        onKeyDown={(e) => {
                          // Allow backspace and delete
                          if (e.key === 'Backspace' || e.key === 'Delete') {
                            return;
                          }
                          // Allow numbers and navigation keys
                          if (!/[0-9]/.test(e.key) &&
                              !['ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
                            e.preventDefault();
                          }
                        }}
                      />
                      {validationErrors.years && (
                        <p className="text-sm text-red-500 mt-1">{validationErrors.years}</p>
                      )}
                    </div>
                    <span className="text-sm text-[#FEFEFF]/60 mt-2">
                      {getPeriodDescription(backtestSettings.timeframe)}
                    </span>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="text-sm text-[#FEFEFF]/60 italic">
                  <div className="flex items-start space-x-2">
                    <svg className="w-4 h-4 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>
                      The Timeframe and Trading Session settings you choose here will take precedence over the ones defined in your strategy. This allows you to test different configurations without modifying your original strategy.
                    </span>
                  </div>
                </div>

                {/* Performance Notice */}
                {backtestSettings.timeframe === '1m' && (
                  <div className="text-sm text-blue-400/80 italic">
                    <div className="flex items-start space-x-2">
                      <svg className="w-4 h-4 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>
                        <strong>1-Minute Backtest:</strong> Processing {backtestSettings.years} year{backtestSettings.years > 1 ? 's' : ''} of 1-minute data (~{(backtestSettings.years * 525600).toLocaleString()} candles). Large datasets may be automatically limited to the most recent 200k candles for optimal performance.
                      </span>
                    </div>
                  </div>
                )}

                {/* Memory optimization notice for very large datasets */}
                {backtestSettings.timeframe === '1m' && backtestSettings.years > 1 && (
                  <div className="text-sm text-amber-400/80 italic">
                    <div className="flex items-start space-x-2">
                      <svg className="w-4 h-4 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <span>
                        <strong>Large Dataset Notice:</strong> Multi-year 1-minute backtests require significant processing power. For faster results, consider using 5-minute or 15-minute timeframes, or reduce the time period to 1 year.
                      </span>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    setShowBacktestForm(false);
                    handleRunBacktest();
                  }}
                  disabled={!isFormValid()}
                  className={`px-6 py-3 bg-[#EFBD3A] text-black rounded-lg font-medium hover:bg-[#EFBD3A]/90 transition-colors flex items-center space-x-2 ${
                    !isFormValid() ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <span>Start Backtest</span>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </motion.button>
              </div>
            </div>
          )}

          {isLoading && (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 border-4 border-[#EFBD3A] border-t-transparent rounded-full animate-spin" />
                <span className="text-[#FEFEFF]/80">{status}</span>
              </div>
              <div className="w-full bg-white/5 rounded-full h-2">
                <div
                  className="bg-[#EFBD3A] h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}

          {error && (
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center space-x-2 text-red-500">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}

          {backtestResults && (
            <div className="space-y-8">
              {/* Performance Summary */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-[#FEFEFF]">Performance Summary</h3>
                  <div className="text-sm text-[#FEFEFF]/60">
                    Starting Balance: ${backtestSettings.startingBalance.toLocaleString()}
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Total Return</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      {backtestResults.performance?.total_return?.toFixed(2) || 'N/A'}%
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Final Value: ${backtestResults.performance?.final_value?.toLocaleString() || 'N/A'}
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        The total percentage return on your investment over the backtest period
                      </div>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Sharpe Ratio</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      {backtestResults.performance?.sharpe_ratio?.toFixed(2) || 'N/A'}
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Risk-Adjusted Return
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        Measures risk-adjusted return. Higher values indicate better risk-adjusted performance
                      </div>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Max Drawdown</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      {backtestResults.performance?.max_drawdown?.toFixed(2) || 'N/A'}%
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Peak to Trough
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        The largest percentage drop from peak to trough during the backtest period
                      </div>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Win Rate</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      {backtestResults.performance?.win_rate?.toFixed(2) || 'N/A'}%
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Winning Trades
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        Percentage of trades that resulted in a profit
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Trade Statistics */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-[#FEFEFF]">Trade Statistics</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Total Trades</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      {backtestResults.performance?.total_trades || 0}
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Executed Trades
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        Total number of trades executed during the backtest period
                      </div>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Average Win</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      ${backtestResults.performance?.average_win?.toFixed(2) || 'N/A'}
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Per Winning Trade
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        Average profit per winning trade
                      </div>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Average Loss</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      ${backtestResults.performance?.average_loss?.toFixed(2) || 'N/A'}
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Per Losing Trade
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        Average loss per losing trade
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Risk Metrics */}
              <div className="space-y-4">
                <h3 className="text-xl font-semibold text-[#FEFEFF]">Risk Metrics</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Profit Factor</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      {backtestResults.performance?.profit_factor?.toFixed(2) || 'N/A'}
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Gross Profit / Gross Loss
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        Ratio of gross profit to gross loss. Values above 1 indicate profitable trading
                      </div>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Best Trade</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      ${backtestResults.performance?.best_trade?.toFixed(2) || 'N/A'}
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Maximum Profit
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        The highest profit achieved in a single trade
                      </div>
                    </div>
                  </div>
                  <div className="p-6 bg-gradient-to-br from-white/5 to-white/10 border border-white/10 rounded-xl group relative hover:border-[#EFBD3A]/30 transition-all duration-300">
                    <div className="text-[#FEFEFF]/60 text-sm mb-1">Worst Trade</div>
                    <div className="text-3xl font-bold text-[#FEFEFF] mb-2">
                      ${backtestResults.performance?.worst_trade?.toFixed(2) || 'N/A'}
                    </div>
                    <div className="text-sm text-[#FEFEFF]/40">
                      Maximum Loss
                    </div>
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-[#0c0f1c] border border-white/10 rounded-lg p-2 text-xs text-[#FEFEFF]/80 w-48">
                        The largest loss incurred in a single trade
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </>
  );
};

export default BacktestDialog;