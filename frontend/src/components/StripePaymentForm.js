import { useState } from 'react';
import {
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import LoadingSpinner from './LoadingSpinner';

export default function StripePaymentForm({ onSuccess, onError, isLoading, setIsLoading }) {
  const stripe = useStripe();
  const elements = useElements();
  const [message, setMessage] = useState(null);

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      return;
    }

    setIsLoading(true);
    setMessage(null);

    const { error, paymentIntent } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        // Return URL after payment completion
        return_url: `${window.location.origin}/dashboard?payment_success=true`,
      },
      redirect: 'if_required',
    });

    if (error) {
      // This point will only be reached if there is an immediate error when
      // confirming the payment. Show error to your customer (for example, payment
      // details incomplete)
      setMessage(error.message);
      onError(error);
      setIsLoading(false);
    } else if (paymentIntent && paymentIntent.status === 'succeeded') {
      // Payment succeeded
      onSuccess(paymentIntent);
    } else {
      setMessage('An unexpected error occurred.');
      setIsLoading(false);
    }
  };

  const paymentElementOptions = {
    layout: 'tabs',
    defaultValues: {
      billingDetails: {
        name: '',
        email: '',
      }
    },
    style: {
      base: {
        fontSize: '16px',
        color: '#FEFEFF',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#fa755a',
        iconColor: '#fa755a',
      },
    },
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-[#FEFEFF] mb-4">Payment Details</h3>
        
        <div className="stripe-payment-element">
          <PaymentElement 
            options={paymentElementOptions}
            className="stripe-element"
          />
        </div>
        
        {message && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
            <p className="text-red-400 text-sm">{message}</p>
          </div>
        )}
      </div>

      <button
        disabled={isLoading || !stripe || !elements}
        type="submit"
        className="w-full bg-[#EFBD3A] hover:bg-[#EFBD3A]/90 text-black py-3 rounded-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
      >
        {isLoading ? (
          <LoadingSpinner size="small" />
        ) : (
          'Complete Payment'
        )}
      </button>

      <div className="text-center">
        <p className="text-[#FEFEFF]/60 text-sm">
          Your payment is secured by Stripe. We never store your card details.
        </p>
      </div>

      <style jsx>{`
        .stripe-payment-element {
          background: #0A0B0B;
          border: 1px solid #333;
          border-radius: 8px;
          padding: 16px;
        }
        
        .stripe-element {
          background: transparent;
        }
        
        /* Custom Stripe element styling */
        :global(.StripeElement) {
          background: #0A0B0B !important;
          border: 1px solid #333 !important;
          border-radius: 8px !important;
          padding: 12px !important;
          color: #FEFEFF !important;
        }
        
        :global(.StripeElement--focus) {
          border-color: #EFBD3A !important;
          box-shadow: 0 0 0 1px #EFBD3A !important;
        }
        
        :global(.StripeElement--invalid) {
          border-color: #fa755a !important;
        }
        
        :global(.StripeElement--webkit-autofill) {
          background: #0A0B0B !important;
        }
      `}</style>
    </form>
  );
}
