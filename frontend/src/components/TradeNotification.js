import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, XCircle, AlertTriangle, Info, X, TrendingUp, TrendingDown, Square } from 'lucide-react';

const TradeNotification = ({ notification, onClose }) => {
  const [visible, setVisible] = useState(true);
  
  useEffect(() => {
    // Auto-close notification after 5 seconds
    const timer = setTimeout(() => {
      setVisible(false);
    }, 5000);
    
    return () => {
      clearTimeout(timer);
    };
  }, []);

  const handleClose = () => {
    setVisible(false);
    if (onClose) {
      onClose();
    }
  };

  // Define notification types and their styles
  const notificationTypes = {
    trade_executed: {
      title: 'Trade Executed',
      icon: <Check className="w-6 h-6 text-green-500" />,
      bgColor: 'bg-green-900/20',
      borderColor: 'border-green-500/30',
      textColor: 'text-green-400'
    },
    trade_closed: {
      title: 'Trade Closed',
      icon: <Square className="w-6 h-6 text-blue-500" />,
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-500/30',
      textColor: 'text-blue-400'
    },
    profit: {
      title: 'Profit Realized',
      icon: <TrendingUp className="w-6 h-6 text-green-500" />,
      bgColor: 'bg-green-900/20',
      borderColor: 'border-green-500/30',
      textColor: 'text-green-400'
    },
    loss: {
      title: 'Loss Realized',
      icon: <TrendingDown className="w-6 h-6 text-red-500" />,
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400'
    },
    error: {
      title: 'Error',
      icon: <AlertTriangle className="w-6 h-6 text-red-500" />,
      bgColor: 'bg-red-900/20',
      borderColor: 'border-red-500/30',
      textColor: 'text-red-400'
    },
    warning: {
      title: 'Warning',
      icon: (
        <svg className="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
      ),
      bgColor: 'bg-yellow-900/20',
      borderColor: 'border-yellow-500/30',
      textColor: 'text-yellow-400'
    },
    info: {
      title: 'Information',
      icon: <Info className="w-6 h-6 text-blue-500" />,
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-500/30',
      textColor: 'text-blue-400'
    }
  };

  // Get config for notification type
  const config = notificationTypes[notification.type] || notificationTypes.info;

  // Format currency values
  const formatCurrency = (value) => {
    if (value === undefined || value === null) return '';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{ duration: 0.3 }}
          className={`${config.bgColor} border ${config.borderColor} rounded-lg p-4 mb-4 relative max-w-md w-full shadow-lg`}
        >
          <button
            onClick={handleClose}
            className="absolute top-2 right-2 text-gray-400 hover:text-white"
            aria-label="Close notification"
          >
            <X className="w-5 h-5" />
          </button>
          <div className="flex items-start">
            <div className="flex-shrink-0">{config.icon}</div>
            <div className="ml-3 flex-1">
              <h3 className={`text-lg font-medium ${config.textColor}`}>{config.title}</h3>
              <div className="mt-1 text-sm text-gray-300">{notification.message}</div>
              
              {/* Additional details for trade notifications */}
              {(notification.type === 'trade_executed' || notification.type === 'trade_closed') && notification.details && (
                <div className="mt-2 p-2 bg-black/30 rounded border border-gray-700">
                  <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                    {notification.details.instrument && (
                      <>
                        <span className="text-gray-400">Instrument:</span>
                        <span className="text-white font-medium">{notification.details.instrument}</span>
                      </>
                    )}
                    {notification.details.type && (
                      <>
                        <span className="text-gray-400">Direction:</span>
                        <span className={`font-medium ${notification.details.type.toLowerCase() === 'buy' ? 'text-green-400' : 'text-red-400'}`}>
                          {notification.details.type}
                        </span>
                      </>
                    )}
                    {notification.details.units && (
                      <>
                        <span className="text-gray-400">Units:</span>
                        <span className="text-white font-medium">{Math.abs(notification.details.units).toLocaleString()}</span>
                      </>
                    )}
                    {notification.details.price && (
                      <>
                        <span className="text-gray-400">Price:</span>
                        <span className="text-white font-medium">{notification.details.price}</span>
                      </>
                    )}
                    {notification.details.realizedPL !== undefined && (
                      <>
                        <span className="text-gray-400">P&L:</span>
                        <span className={`font-medium ${notification.details.realizedPL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {formatCurrency(notification.details.realizedPL)}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              )}
              
              {/* Timestamp */}
              <div className="mt-2 text-xs text-gray-500">
                {new Date(notification.timestamp).toLocaleTimeString()}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TradeNotification;
