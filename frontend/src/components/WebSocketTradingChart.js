/**
 * WebSocket Trading Chart Component
 * 
 * Replaces PolygonTradingChart with WebSocket-based real-time data updates.
 * Uses the WebSocket Data Distribution Service for real-time candle data.
 */

import React, { useEffect, useRef, useState, useCallback } from "react";
import OptimizedTradingChart from "./OptimizedTradingChart";
import { calculateIndicatorsFromStrategy } from "../utils/indicatorCalculations";
import polygonApiService from "../services/polygonApiService";
import websocketDataService from "../services/websocketDataService";

const WebSocketTradingChart = ({
  candleData: initialCandleData,
  indicators: passedIndicators,
  trades,
  timeframe: displayTimeframe,
  chartTimeframe = "1h",
  instrument,
  marketStatus,
  strategyInfo,
  strategy,
  enableRealTime = true,
  enablePolling = true, // Legacy prop for compatibility
  skipInitialFetch = false,
  onDataUpdate = null,
  onIndicatorsCalculated = null,
  hideDisplayPills = false,
  hideStrategyPills = false,
  // Zoom persistence props
  preserveZoom = false,
  onZoomChange = null,
  initialZoomState = null,
  // WebSocket status callback
  onWebSocketStatusChange = null,
  ...otherProps
}) => {
  // State management
  const [candleData, setCandleData] = useState(initialCandleData || []);
  const [dataStatus, setDataStatus] = useState('disconnected');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [updateCount, setUpdateCount] = useState(0);
  const [error, setError] = useState(null);
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [wsConnected, setWsConnected] = useState(false);

  // Refs
  const mountedRef = useRef(true);
  const subscriptionRef = useRef(null);
  const candleBufferRef = useRef([]);
  const onDataUpdateRef = useRef(onDataUpdate);
  const lastCandleTimestampRef = useRef(null); // Track last candle timestamp for catch-up
  const handleNewCandleRef = useRef(null); // Ref for handleNewCandle to prevent recreations
  const subscriptionActiveRef = useRef(false); // Track if subscription is actually active
  const cleanupInProgressRef = useRef(false); // Prevent multiple cleanup attempts
  const pageVisibleRef = useRef(true); // Track page visibility

  // Update refs when functions change
  useEffect(() => {
    onDataUpdateRef.current = onDataUpdate;
  }, [onDataUpdate]);

  // Format instrument for API calls
  const formatInstrument = useCallback((instrument) => {
    if (!instrument) return 'EUR/USD';
    return instrument.replace('/', '').toUpperCase(); // Convert EUR/USD to EURUSD
  }, []);

  const formattedInstrument = formatInstrument(instrument);

  // Use enablePolling for backward compatibility
  const realTimeEnabled = enableRealTime && enablePolling;

  console.log(`🔍 WebSocket Chart: Component props:`, {
    enableRealTime,
    enablePolling,
    realTimeEnabled,
    formattedInstrument,
    chartTimeframe
  });

  // Calculate indicators
  const indicators = React.useMemo(() => {
    console.log('🔍 WebSocket Chart: Calculating indicators', {
      hasStrategy: !!strategy,
      candleDataLength: candleData.length,
      hasPassedIndicators: !!(passedIndicators && Object.keys(passedIndicators).length > 0),
      strategyIndicators: strategy?.strategy_json?.indicators?.length || 0
    });

    if (!strategy || !candleData.length) {
      console.log('⚠️ WebSocket Chart: No strategy or candle data - returning passed indicators');
      return passedIndicators || [];
    }

    try {
      const calculatedIndicators = calculateIndicatorsFromStrategy(candleData, strategy);
      console.log('✅ WebSocket Chart: Indicators calculated', {
        indicatorKeys: Object.keys(calculatedIndicators),
        indicatorCount: Object.keys(calculatedIndicators).length,
        sampleIndicator: Object.keys(calculatedIndicators)[0] ? {
          key: Object.keys(calculatedIndicators)[0],
          dataLength: calculatedIndicators[Object.keys(calculatedIndicators)[0]]?.length || 0
        } : null
      });

      if (onIndicatorsCalculated) {
        onIndicatorsCalculated(calculatedIndicators);
      }
      return calculatedIndicators;
    } catch (error) {
      console.error('❌ Error calculating indicators:', error);
      return passedIndicators || [];
    }
  }, [strategy, candleData, passedIndicators, onIndicatorsCalculated]);

  // Handle data from WebSocket (initial data or real-time updates)
  const handleNewCandle = useCallback((data, isInitialData = false) => {
    if (!mountedRef.current) return;

    if (isInitialData) {
      // Handle initial historical data (array of candles)
      console.log(`📊 WebSocket: Received initial data for ${formattedInstrument} ${chartTimeframe}: ${data.length} candles`);

      setCandleData(data);
      candleBufferRef.current = data;
      setDataStatus('connected');
      setLastUpdate(new Date());
      setUpdateCount(prev => prev + 1);
      setIsInitialLoad(false);

      console.log(`✅ WebSocket Chart: Initial data loaded - ${data.length} candles`);
      console.log(`📊 WebSocket Chart: Latest candle: ${new Date(data[data.length - 1].time * 1000).toISOString()}`);

      // Update last candle timestamp for future catch-up requests
      if (data.length > 0) {
        lastCandleTimestampRef.current = data[data.length - 1].time;
      }

      if (onDataUpdateRef.current) {
        onDataUpdateRef.current(data);
      }
    } else {
      // Handle real-time candle update (single candle)
      const newCandle = data;
      console.log(`📊 WebSocket: Received real-time candle data for ${formattedInstrument} ${chartTimeframe}`);

      setCandleData(prevCandles => {
        console.log(`🔍 WEBSOCKET TRACE: ==================== WebSocket setCandleData CALLED ====================`);
        console.log(`🔍 WEBSOCKET TRACE: Previous candles: ${prevCandles.length}`);
        console.log(`🔍 WEBSOCKET TRACE: New candle: ${new Date(newCandle.time * 1000).toISOString()}`);

        // Implement FIFO buffer management (keep last 1000 candles)
        const maxCandles = 1000;
        let updatedCandles = [...prevCandles];

        // Check if this is an update to the last candle or a new candle
        const lastCandle = updatedCandles[updatedCandles.length - 1];

        if (lastCandle && lastCandle.time === newCandle.time) {
          // Update existing candle
          updatedCandles[updatedCandles.length - 1] = newCandle;
          console.log(`🔄 WebSocket: Updated existing candle at ${new Date(newCandle.time * 1000).toISOString()}`);
        } else {
          // Add new candle
          updatedCandles.push(newCandle);
          console.log(`📈 WebSocket: Added new candle at ${new Date(newCandle.time * 1000).toISOString()}`);

          // Maintain FIFO buffer
          if (updatedCandles.length > maxCandles) {
            updatedCandles = updatedCandles.slice(-maxCandles);
            console.log(`🔄 WebSocket: Trimmed buffer to ${maxCandles} candles`);
          }
        }

        // Sort candles by time to prevent ordering errors
        updatedCandles.sort((a, b) => a.time - b.time);

        // Remove duplicate timestamps to prevent chart errors
        const uniqueCandles = [];
        let lastTime = null;
        for (const candle of updatedCandles) {
          if (candle.time !== lastTime) {
            uniqueCandles.push(candle);
            lastTime = candle.time;
          } else {
            console.warn(`⚠️ WebSocket: Removed duplicate candle with timestamp: ${candle.time} (${new Date(candle.time * 1000).toISOString()})`);
          }
        }

        console.log(`🔍 WEBSOCKET TRACE: Final result: ${uniqueCandles.length} candles`);
        console.log(`🔍 WEBSOCKET TRACE: Latest candle in result: ${new Date(uniqueCandles[uniqueCandles.length - 1].time * 1000).toISOString()}`);
        console.log(`🔍 WEBSOCKET TRACE: ================================================================`);

        return uniqueCandles;
      });

      // Call onDataUpdate after state update - use useEffect to ensure timing
      if (onDataUpdateRef.current) {
        // Create a new array with the updated candle for immediate callback
        const maxCandles = 1000;
        let callbackCandles = [...candleData];
        const lastCandle = callbackCandles[callbackCandles.length - 1];

        if (lastCandle && lastCandle.time === newCandle.time) {
          callbackCandles[callbackCandles.length - 1] = newCandle;
        } else {
          callbackCandles.push(newCandle);
          if (callbackCandles.length > maxCandles) {
            callbackCandles = callbackCandles.slice(-maxCandles);
          }
        }

        onDataUpdateRef.current(callbackCandles);
        console.log(`✅ WebSocket Chart: Called onDataUpdate with ${callbackCandles.length} candles`);
      }

      // Update last candle timestamp for future catch-up requests
      lastCandleTimestampRef.current = newCandle.time;

      // Update status
      setLastUpdate(new Date());
      setUpdateCount(prev => prev + 1);
    }

    setDataStatus('connected');
    setError(null);

  }, [chartTimeframe, formattedInstrument]); // Remove onDataUpdate to prevent unnecessary recreations

  // Update handleNewCandle ref when it changes
  useEffect(() => {
    handleNewCandleRef.current = handleNewCandle;
  }, [handleNewCandle]);

  // Prepare for initial candle data from WebSocket service
  const fetchInitialData = useCallback(async () => {
    if (skipInitialFetch || !mountedRef.current) return;

    try {
      setDataStatus('loading');
      console.log(`📡 WebSocket Chart: Fetching initial data for ${formattedInstrument} ${chartTimeframe}`);

      // Import the polygon service dynamically to avoid SSR issues
      const { default: polygonApiService } = await import('../services/polygonApiService');

      // Fetch initial 1000 candles as fallback
      const result = await polygonApiService.getCandles(formattedInstrument, chartTimeframe, 1000);

      if (result.status === 'success' && result.candles && result.candles.length > 0) {
        console.log(`✅ WebSocket Chart: Fetched ${result.candles.length} initial candles`);
        setCandleData(result.candles);
        candleBufferRef.current = result.candles;

        // Update last candle timestamp for catch-up requests
        if (result.candles.length > 0) {
          lastCandleTimestampRef.current = result.candles[result.candles.length - 1].time;
        }

        setDataStatus('connected');
        setIsInitialLoad(false);

        // Call onDataUpdate if provided
        if (onDataUpdateRef.current) {
          onDataUpdateRef.current(result.candles);
        }
      } else {
        console.warn(`⚠️ WebSocket Chart: Failed to fetch initial data: ${result.message || 'No data'}`);
        setDataStatus('connecting'); // Still try WebSocket
      }

    } catch (error) {
      console.error('❌ WebSocket Chart: Error fetching initial data:', error);
      if (mountedRef.current) {
        setError(error.message);
        setDataStatus('error');
      }
    }
  }, [formattedInstrument, chartTimeframe, skipInitialFetch]);

  // Setup WebSocket subscription - using refs to prevent recreations
  const setupWebSocketSubscription = useCallback(async () => {
    console.log(`🔍 WebSocket Chart: setupWebSocketSubscription called with:`, {
      realTimeEnabled,
      formattedInstrument,
      chartTimeframe,
      enableRealTime,
      enablePolling
    });

    if (!realTimeEnabled || !formattedInstrument || !chartTimeframe) {
      console.log(`🔍 WebSocket Chart: Skipping subscription setup - missing requirements:`, {
        realTimeEnabled,
        formattedInstrument,
        chartTimeframe
      });
      return;
    }

    const targetSubscription = `${formattedInstrument}_${chartTimeframe}`;

    // Check if already subscribed to the same subscription (including across component remounts)
    const persistentKey = `ws_subscription_${targetSubscription}`;
    const lastSubscriptionTime = typeof sessionStorage !== 'undefined' ? sessionStorage.getItem(persistentKey) : null;
    const now = Date.now();

    // If we subscribed to the same thing within the last 5 seconds, skip it
    if (lastSubscriptionTime && (now - parseInt(lastSubscriptionTime)) < 5000) {
      console.log(`⚡ WebSocket Chart: Recently subscribed to ${targetSubscription} (${Math.round((now - parseInt(lastSubscriptionTime))/1000)}s ago) - skipping re-subscription`);
      subscriptionRef.current = targetSubscription;
      setWsConnected(true);
      return;
    }

    // Also check current subscription state
    if (subscriptionRef.current === targetSubscription && wsConnected && subscriptionActiveRef.current) {
      console.log(`⚡ WebSocket Chart: Already subscribed to ${targetSubscription} - skipping re-subscription`);
      return;
    }

    // Prevent multiple simultaneous subscriptions
    if (isSubscribing || subscriptionActiveRef.current) {
      console.log(`⚡ WebSocket Chart: Already subscribing to ${targetSubscription} - skipping duplicate`);
      return;
    }

    // Set subscribing state
    setIsSubscribing(true);

    // Store subscription time
    sessionStorage.setItem(persistentKey, now.toString());

    try {
      console.log(`🔗 WebSocket Chart: Setting up subscription for ${formattedInstrument} ${chartTimeframe}`);

      // Get the last candle timestamp for catch-up (if available)
      const sinceTimestamp = lastCandleTimestampRef.current;
      if (sinceTimestamp) {
        console.log(`🔄 WebSocket Chart: Requesting catch-up data since ${new Date(sinceTimestamp * 1000).toISOString()}`);
      }

      // Create a stable callback that uses the ref
      const stableCallback = (data, isInitialData) => {
        if (handleNewCandleRef.current) {
          handleNewCandleRef.current(data, isInitialData);
        }
      };

      // Subscribe to real-time data with optional catch-up
      await websocketDataService.subscribe(
        formattedInstrument,
        chartTimeframe,
        stableCallback,
        sinceTimestamp
      );

      subscriptionRef.current = targetSubscription;
      subscriptionActiveRef.current = true; // Mark subscription as active
      setWsConnected(true);

      // Store subscription time to prevent rapid re-subscriptions
      if (typeof sessionStorage !== 'undefined') {
        sessionStorage.setItem(persistentKey, now.toString());
      }

      // Notify parent that WebSocket is active
      if (onWebSocketStatusChange) {
        onWebSocketStatusChange(true);
      }

      console.log(`✅ WebSocket Chart: Subscription established for ${formattedInstrument} ${chartTimeframe}`);

    } catch (error) {
      console.error('❌ WebSocket Chart: Error setting up subscription:', error);
      setError(`WebSocket subscription failed: ${error.message}`);
    } finally {
      // Clear subscribing state
      setIsSubscribing(false);
    }
  }, [realTimeEnabled, formattedInstrument, chartTimeframe, wsConnected, isSubscribing]); // Added wsConnected and isSubscribing to dependencies

  // Cleanup WebSocket subscription
  const cleanupWebSocketSubscription = useCallback(async () => {
    // Prevent multiple cleanup attempts
    if (cleanupInProgressRef.current) {
      console.log(`⚡ WebSocket Chart: Cleanup already in progress - skipping`);
      return;
    }

    if (subscriptionRef.current && subscriptionActiveRef.current) {
      const [symbol, timeframe] = subscriptionRef.current.split('_');
      console.log(`🧹 WebSocket Chart: Cleaning up subscription for ${symbol} ${timeframe}`);
      cleanupInProgressRef.current = true;

      try {
        await websocketDataService.unsubscribe(symbol, timeframe);
        console.log(`✅ WebSocket Chart: Successfully unsubscribed from ${subscriptionRef.current}`);
        subscriptionRef.current = null;
        subscriptionActiveRef.current = false;
        setWsConnected(false);

        // Notify parent that WebSocket is inactive
        if (onWebSocketStatusChange) {
          onWebSocketStatusChange(false);
        }
      } catch (error) {
        console.error('❌ WebSocket Chart: Error cleaning up subscription:', error);
      } finally {
        cleanupInProgressRef.current = false;
      }
    } else {
      console.log(`⚡ WebSocket Chart: No active subscription to cleanup`);
    }
  }, []);

  // Page Visibility API support
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;
      pageVisibleRef.current = isVisible;

      console.log(`👁️ WebSocket Chart: Page visibility changed - visible: ${isVisible}`);

      if (isVisible && realTimeEnabled && subscriptionRef.current) {
        // Page became visible - ensure WebSocket is still connected
        console.log(`🔄 WebSocket Chart: Page became visible - checking WebSocket connection`);

        // Request catch-up data since we might have missed updates
        const sinceTimestamp = lastCandleTimestampRef.current;
        if (sinceTimestamp) {
          console.log(`🔄 WebSocket Chart: Requesting catch-up data since ${new Date(sinceTimestamp * 1000).toISOString()}`);
          // Re-establish subscription with catch-up
          setTimeout(() => {
            setupWebSocketSubscription();
          }, 1000);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [realTimeEnabled, setupWebSocketSubscription]);

  // Tab visibility support - save chart state when tab becomes hidden
  useEffect(() => {
    const handleTabVisibilityChange = () => {
      // Save current chart state when component might become hidden
      if (preserveZoom && onZoomChange) {
        try {
          const chartInstance = window.chartInstance || chartInstanceRef.current;
          if (chartInstance) {
            const timeScale = chartInstance.timeScale();
            const currentRange = timeScale.getVisibleLogicalRange();
            if (currentRange) {
              const zoomState = {
                from: currentRange.from,
                to: currentRange.to,
                timestamp: Date.now()
              };
              console.log(`💾 WebSocket Chart: Saving zoom state for tab switch:`, zoomState);
              onZoomChange(zoomState);
            }
          }
        } catch (error) {
          console.warn(`⚠️ WebSocket Chart: Error saving zoom state:`, error);
        }
      }
    };

    // Save state periodically and on potential tab switches
    const saveInterval = setInterval(handleTabVisibilityChange, 2000);

    return () => {
      clearInterval(saveInterval);
      // Save state one final time on cleanup
      handleTabVisibilityChange();
    };
  }, [preserveZoom, onZoomChange]);

  // Initialize component
  useEffect(() => {
    console.log(`🔍 WebSocket Chart: Initialization useEffect triggered with realTimeEnabled: ${realTimeEnabled}`);

    mountedRef.current = true;

    const initialize = async () => {
      // Fetch initial data first
      await fetchInitialData();

      // Then setup WebSocket subscription
      if (realTimeEnabled) {
        await setupWebSocketSubscription();
      }
    };

    initialize();

    // Cleanup on unmount
    return () => {
      console.log(`🔄 WebSocket Chart: Component unmounting - cleaning up`);
      mountedRef.current = false;
      cleanupWebSocketSubscription();
    };
  }, [realTimeEnabled]); // Only depend on realTimeEnabled, not the functions

  // Handle instrument or timeframe changes
  useEffect(() => {
    const handleChange = async () => {
      // Debug logging to see what's triggering this useEffect
      console.log(`🔍 WebSocket Chart: useEffect triggered with:`, {
        formattedInstrument,
        chartTimeframe,
        realTimeEnabled,
        wsConnected,
        currentSubscription: subscriptionRef.current
      });

      // Check if this is actually a change or just a refresh
      const targetSubscription = `${formattedInstrument}_${chartTimeframe}`;
      if (subscriptionRef.current === targetSubscription && wsConnected && realTimeEnabled) {
        console.log(`⚡ WebSocket Chart: No actual change detected for ${targetSubscription} - skipping re-subscription`);
        return;
      }

      // Cleanup existing subscription only if there's an actual change
      if (subscriptionRef.current && subscriptionRef.current !== targetSubscription && subscriptionActiveRef.current) {
        console.log(`🔄 WebSocket Chart: Instrument/timeframe changed from ${subscriptionRef.current} to ${targetSubscription}`);
        await cleanupWebSocketSubscription();
      } else if (subscriptionRef.current === targetSubscription) {
        console.log(`⚡ WebSocket Chart: Same subscription ${targetSubscription} - no cleanup needed`);
      }

      // Setup new subscription
      if (realTimeEnabled) {
        await setupWebSocketSubscription();
      }
    };

    handleChange();
  }, [formattedInstrument, chartTimeframe, realTimeEnabled, wsConnected]); // Added wsConnected to dependencies

  // Update candle data when initial data changes
  useEffect(() => {
    if (initialCandleData && initialCandleData.length > 0) {
      setCandleData(initialCandleData);
      candleBufferRef.current = initialCandleData;
      setIsInitialLoad(false);
    }
  }, [initialCandleData]);

  return (
    <div className="relative w-full h-full">
      {/* Connection status indicator */}
      {realTimeEnabled && (
        <div className="absolute top-4 left-4 z-10 flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-xs text-gray-600">
            {wsConnected ? 'WebSocket Connected' : 'WebSocket Disconnected'}
          </span>
        </div>
      )}

      {/* Error indicator */}
      {error && (
        <div className="absolute top-4 right-4 z-10 bg-red-600 bg-opacity-90 rounded-lg px-3 py-2">
          <span className="text-white text-xs">⚠️ {error}</span>
        </div>
      )}

      {/* Chart component */}
      <OptimizedTradingChart
        candleData={candleData}
        indicators={indicators}
        trades={trades}
        timeframe={displayTimeframe}
        chartTimeframe={chartTimeframe}
        instrument={instrument}
        marketStatus={marketStatus}
        strategyInfo={strategyInfo}
        strategy={strategy}
        isRealTimeUpdate={!isInitialLoad}
        hideDisplayPills={hideDisplayPills}
        hideStrategyPills={hideStrategyPills}
        // Pass through zoom persistence props
        preserveZoom={preserveZoom}
        onZoomChange={onZoomChange}
        initialZoomState={initialZoomState}
        {...otherProps}
      />

      {/* Debug indicator data */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ display: 'none' }}>
          <pre>
            Indicators Debug: {JSON.stringify({
              indicatorKeys: indicators ? Object.keys(indicators) : [],
              candleDataLength: candleData.length,
              hasStrategy: !!strategy
            }, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default WebSocketTradingChart;
