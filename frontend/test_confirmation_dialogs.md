# Frontend Confirmation Dialogs Test Guide

## 🧪 Testing the New Confirmation Dialogs

### **What Was Added:**

1. **Confirmation Dialog Component** (`/components/ConfirmationDialog.js`)
   - Reusable confirmation dialog with different types (pause, stop)
   - Animated with Framer Motion
   - Shows specific warnings for each action type

2. **Pause Confirmation Dialog**
   - ⚠️ **24-Hour Auto-Stop Warning**
   - Explains that bot will automatically stop after 24 hours
   - Shows that positions will be closed immediately

3. **Stop Confirmation Dialog**
   - ⚠️ **Permanent Action Warning**
   - Explains that action cannot be undone
   - Shows that bot will be permanently deleted

### **Updated Pages:**

1. **Trade-Bot Header** (`/components/TradeBotHeader.js`)
   - Pause and Stop buttons now show confirmation dialogs
   - Loading states during actions

2. **Trade-Bots List** (`/pages/trade-bots.js`)
   - Pause and Stop buttons in bot cards show confirmations
   - Resume and Start buttons work immediately (no confirmation needed)

3. **Individual Trade-Bot Page** (`/pages/trade-bots/[id].js`)
   - Header buttons use confirmation dialogs
   - Consistent behavior across all pages

4. **Paused State Display** (`/components/TradeBotTabs.js`)
   - Shows 24-hour auto-stop warning when bot is paused
   - Updated both overlay and banner displays

### **Testing Steps:**

#### **1. Test Pause Confirmation:**
1. Go to any running trade-bot
2. Click the **Pause** button (yellow button with pause icon)
3. **Expected**: Confirmation dialog appears with:
   - Title: "Pause Trading Bot"
   - 24-hour auto-stop warning in yellow box
   - "Pause Bot" and "Cancel" buttons
4. Click "Cancel" → Dialog closes, no action taken
5. Click "Pause Bot" → Dialog closes, bot pauses

#### **2. Test Stop Confirmation:**
1. Go to any running or paused trade-bot
2. Click the **Stop** button (red button with square icon)
3. **Expected**: Confirmation dialog appears with:
   - Title: "Stop Trading Bot"
   - Permanent action warning in red box
   - "Stop Bot" and "Cancel" buttons
4. Click "Cancel" → Dialog closes, no action taken
5. Click "Stop Bot" → Dialog closes, bot stops permanently

#### **3. Test Paused State Display:**
1. Pause a trade-bot (using confirmation dialog)
2. **Expected**: Paused overlay shows:
   - "Bot Paused" title
   - "Auto-stop in 24 hours" warning
   - Yellow warning box with clock icon

#### **4. Test Resume (No Confirmation):**
1. Go to a paused trade-bot
2. Click the **Resume** button (green button with play icon)
3. **Expected**: Bot resumes immediately (no confirmation dialog)

#### **5. Test Loading States:**
1. Click any action button
2. **Expected**: Button shows loading spinner during API call
3. Dialog buttons also show loading state

### **Visual Design:**

- **Pause Dialog**: Yellow theme with clock icon
- **Stop Dialog**: Red theme with warning triangle
- **Backdrop**: Blurred background
- **Animation**: Smooth fade and scale transitions
- **Responsive**: Works on mobile and desktop

### **Error Handling:**

- If API call fails, dialog closes and error is shown
- Loading states prevent multiple clicks
- Dialog can be closed by clicking backdrop or Cancel button

### **Accessibility:**

- Proper focus management
- Keyboard navigation support
- Screen reader friendly
- High contrast colors

## 🎯 **Expected User Experience:**

1. **Clear Intent**: Users understand the consequences of their actions
2. **Prevent Accidents**: No accidental pauses or stops
3. **Informed Decisions**: 24-hour timeout clearly communicated
4. **Smooth Workflow**: Quick actions (resume/start) don't require confirmation
5. **Professional Feel**: Consistent with OrynTrade's design system
