# Frontend Timing Debug Guide

This document helps debug the frontend polling timing to ensure it matches the backend exactly.

## 🔍 **Debug Steps**

### **1. Check Console Logs**

When you load a 1m chart, you should see these logs:

```
🔍 Frontend getWaitTime: timeframe=1m, timeframeMinutes=1, waitSeconds=30
🔍 Frontend getPollingInterval: timeframe=1m, timeframeMinutes=1, intervalMs=60000
🔄 Frontend: Starting polling every 60 seconds with 30s wait for 1m
🔍 Frontend Debug: chartTimeframe=1m, pollingInterval=60000ms, waitTime=30000ms
🚀 Frontend: Starting first poll for 1m
⏰ Frontend: Period completed for 1m, waiting 30s for Polygon processing...
🔄 Frontend: Setting up interval to repeat every 60s for 1m
⏳ Frontend: Waited 30s for Polygon to process 1m candle - now fetching
```

### **2. Expected Timing for 1m**

- **Polling Interval**: 60,000ms (60 seconds) ✅
- **Wait Time**: 30,000ms (30 seconds) ✅
- **Total Cycle**: 60s period + 30s wait = 90s between fetches ✅

### **3. Expected Timing for 3m**

- **Polling Interval**: 180,000ms (180 seconds) ✅
- **Wait Time**: 90,000ms (90 seconds) ✅
- **Total Cycle**: 180s period + 90s wait = 270s between fetches ✅

### **4. Expected Timing for 5m**

- **Polling Interval**: 300,000ms (300 seconds) ✅
- **Wait Time**: 120,000ms (120 seconds) ✅
- **Total Cycle**: 300s period + 120s wait = 420s between fetches ✅

## 🐛 **If Still Seeing 2-Minute Updates for 1m**

### **Possible Issues:**

1. **Wrong Timeframe Passed**: Check if `chartTimeframe` is actually "1m"
2. **Cached Component**: Try hard refresh (Ctrl+F5)
3. **Multiple Intervals**: Check if multiple polling intervals are running
4. **Backend Override**: Check if backend is somehow overriding frontend

### **Debug Commands in Browser Console:**

```javascript
// Check current timeframe
console.log('Current timeframe:', chartTimeframe);

// Check timing calculations
const timeframeMinutes = {'1m': 1, '3m': 3, '5m': 5}['1m'];
const waitSeconds = timeframeMinutes <= 1 ? 30 : (timeframeMinutes <= 3 ? 90 : 120);
const intervalMs = timeframeMinutes * 60 * 1000;
console.log('Expected for 1m:', {timeframeMinutes, waitSeconds, intervalMs});

// Check if multiple intervals are running
console.log('Active intervals:', Object.keys(window).filter(k => k.includes('interval')));
```

## 🔧 **Manual Verification**

### **Test 1m Timing Manually:**

1. Open browser dev tools
2. Go to trade-bot details page with 1m strategy
3. Watch console logs for timing messages
4. Verify these exact values:
   - `timeframeMinutes=1`
   - `waitSeconds=30`
   - `intervalMs=60000`
   - `pollingInterval=60000ms`
   - `waitTime=30000ms`

### **Expected Behavior:**

```
Time 0s:    🚀 Start first poll
Time 30s:   ⏳ Fetch after 30s wait
Time 60s:   ⏰ Next period completed, wait 30s
Time 90s:   ⏳ Fetch after 30s wait
Time 120s:  ⏰ Next period completed, wait 30s
Time 150s:  ⏳ Fetch after 30s wait
```

**Result**: Fetches every 90 seconds (60s period + 30s wait)

## 🎯 **If Problem Persists**

### **Check These:**

1. **Component Props**: Verify `chartTimeframe` prop is "1m"
2. **Multiple Components**: Ensure only one PolygonTradingChart is mounted
3. **Interval Cleanup**: Check if old intervals are being cleared properly
4. **Browser Cache**: Try incognito mode
5. **React StrictMode**: Check if running in development with StrictMode (causes double mounting)

### **Force Debug Mode:**

Add this to the component to force debug output:

```javascript
useEffect(() => {
  console.log('🔍 FORCE DEBUG:', {
    chartTimeframe,
    enablePolling,
    pollingInterval: getPollingInterval(chartTimeframe),
    waitTime: getWaitTime(chartTimeframe)
  });
}, [chartTimeframe, enablePolling]);
```

## 🚀 **Expected Final Result**

For 1m timeframe:
- **Period**: 1 minute
- **Wait**: 30 seconds
- **Fetch Frequency**: Every 90 seconds
- **Console**: Clear timing logs showing 60s intervals with 30s waits

The key is that we should see **60-second intervals** with **30-second waits**, not 120-second intervals!
