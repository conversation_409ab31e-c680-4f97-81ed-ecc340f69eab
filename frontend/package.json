{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "next build"}, "dependencies": {"@heroicons/react": "^2.2.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "axios": "^1.7.9", "chart.js": "^4.4.8", "chartjs-plugin-zoom": "^2.2.0", "date-fns": "^4.1.0", "firebase": "^11.5.0", "framer-motion": "^12.0.11", "lightweight-charts": "^4.2.3", "lucide-react": "^0.525.0", "micro": "^10.0.1", "next": "^15.2.4", "nodemailer": "^6.10.0", "react": "18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-toastify": "^11.0.5", "react-tsparticles": "^2.12.2", "recharts": "^2.15.4", "stripe": "^18.2.1", "tsparticles": "^3.8.1", "tsparticles-engine": "^2.12.0", "tsparticles-slim": "^2.12.0"}, "devDependencies": {"@babel/eslint-parser": "^7.26.8", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "^15.2.4", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5"}}