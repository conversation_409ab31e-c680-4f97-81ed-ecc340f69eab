apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: strategy-controller-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.oryntrade.com  # Replace with your domain
    secretName: strategy-controller-tls
  rules:
  - host: api.oryntrade.com  # Replace with your domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: strategy-controller-service
            port:
              number: 80
