# Exit Code Implementation Test Guide

## 🎯 **What Was Implemented**

### **1. Exit Code System**
- Added `ExitCodes` class with specific codes for different shutdown scenarios
- Business logic shutdowns (codes 2-7) prevent pod restart
- System errors (code 1) trigger automatic restart

### **2. Enhanced Error Handling**
- **Margin Errors**: Exit with code 5 (no restart)
- **Authentication Errors**: Exit with code 4 (no restart)
- **Configuration Errors**: Exit with code 3 (no restart)
- **Strategy Validation Errors**: Exit with code 6 (no restart)
- **System Errors**: Exit with code 1 (restart)

### **3. Strategy Controller Integration**
- Added `/strategy-shutdown` endpoint
- Trade-bots notify Strategy Controller before business logic shutdowns
- Strategy Controller marks strategies as stopped to prevent recreation

### **4. Kubernetes Configuration**
- Updated deployment spec with `restartPolicy: OnFailure`
- Only exit code 1 (system errors) trigger pod restart
- Exit codes 2-7 (business logic) result in completed pods

## 🧪 **Testing the Implementation**

### **Step 1: Deploy Updated Services**

```bash
# Deploy Strategy Controller with new shutdown endpoint
cd strategy-controller-service
./deploy-to-k8s.sh

# Deploy Trade-Bot with exit code handling
cd ../trade-bot-service
./deploy-to-k8s.sh
```

### **Step 2: Test Business Logic Shutdown (Should NOT Restart)**

```bash
# Create a trade-bot with insufficient margin
# This should exit with code 5 and NOT restart

# 1. Deploy a trade-bot through frontend
# 2. Monitor the pod
kubectl get pods -l app=trade-bot-strategy -w

# 3. Check logs for exit code
kubectl logs <trade-bot-pod-name>

# Expected behavior:
# - Pod shows "Completed" status
# - No new pod created
# - Logs show "Exiting with code 5"
```

### **Step 3: Test System Error (Should Restart)**

```bash
# Simulate a system error
kubectl exec -it <trade-bot-pod-name> -- python -c "
import sys
sys.exit(1)  # System error
"

# Expected behavior:
# - Pod restarts automatically
# - RESTARTS column increments
# - Pod continues running
```

### **Step 4: Test Different Exit Codes**

```bash
# Test margin error (code 5)
kubectl run test-margin --image=us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest \
  --env="TEST_TYPE=margin_error" \
  --command -- python test_exit_codes.py

# Test auth error (code 4)
kubectl run test-auth --image=us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest \
  --env="TEST_TYPE=auth_error" \
  --command -- python test_exit_codes.py

# Test system error (code 1)
kubectl run test-system --image=us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest \
  --env="TEST_TYPE=system_error" \
  --command -- python test_exit_codes.py

# Check results
kubectl get pods
```

## 📊 **Expected Results**

| Test Case | Exit Code | Pod Status | Restart Count | Notes |
|-----------|-----------|------------|---------------|-------|
| **Margin Error** | 5 | Completed | 0 | ✅ No restart |
| **Auth Error** | 4 | Completed | 0 | ✅ No restart |
| **Config Error** | 3 | Completed | 0 | ✅ No restart |
| **Strategy Error** | 6 | Completed | 0 | ✅ No restart |
| **System Error** | 1 | Running | 1+ | ✅ Restarts |
| **Success** | 0 | Completed | 0 | ✅ No restart |

## 🔍 **Verification Commands**

### **Check Pod Status:**
```bash
kubectl get pods -l app=trade-bot-strategy
# Look for STATUS and RESTARTS columns
```

### **Check Pod Events:**
```bash
kubectl describe pod <pod-name>
# Look for restart events and exit codes
```

### **Check Logs:**
```bash
kubectl logs <pod-name>
# Look for exit code messages
```

### **Check Strategy Controller Logs:**
```bash
kubectl logs -l app=strategy-controller
# Look for shutdown notifications
```

## 🎯 **Real-World Test Scenarios**

### **Scenario 1: Insufficient Margin**
1. Create trade-bot with small account balance
2. Set high risk percentage
3. Bot should detect insufficient margin and exit with code 5
4. Pod should show "Completed" status
5. No new pod should be created

### **Scenario 2: Invalid OANDA Credentials**
1. Create trade-bot with invalid API key
2. Bot should fail authentication and exit with code 4
3. Pod should show "Completed" status
4. No new pod should be created

### **Scenario 3: Missing Environment Variables**
1. Create trade-bot without USER_ID or STRATEGY_ID
2. Bot should exit with code 3 (configuration error)
3. Pod should show "Completed" status
4. No new pod should be created

### **Scenario 4: Network/System Issues**
1. Simulate network failure or system crash
2. Bot should exit with code 1 (system error)
3. Pod should restart automatically
4. RESTARTS count should increment

## 🚀 **Success Criteria**

### **✅ Business Logic Shutdowns:**
- Exit codes 2-7 result in "Completed" pods
- No automatic restart occurs
- Strategy Controller receives shutdown notification
- Firestore updated with stop reason

### **✅ System Error Handling:**
- Exit code 1 triggers automatic restart
- Pod continues running after restart
- RESTARTS count increments
- No shutdown notification sent

### **✅ User Experience:**
- Clear error messages in logs
- Proper status updates in frontend
- No infinite restart loops
- Graceful error handling

## 🛠️ **Troubleshooting**

### **If Business Logic Errors Still Restart:**
1. Check deployment `restartPolicy` is set to `OnFailure`
2. Verify exit codes are being used correctly
3. Check Kubernetes version compatibility

### **If System Errors Don't Restart:**
1. Verify exit code 1 is being used for system errors
2. Check deployment configuration
3. Review pod events for restart attempts

### **If Shutdown Notifications Fail:**
1. Check Strategy Controller endpoint is accessible
2. Verify authentication tokens
3. Review network connectivity between pods

## 📝 **Implementation Summary**

This implementation provides:
- ✅ **Intelligent restart behavior** based on failure type
- ✅ **Prevention of infinite restart loops** for business logic errors
- ✅ **Automatic recovery** for genuine system failures
- ✅ **Clear error reporting** and user feedback
- ✅ **Production-ready reliability** with proper error handling

The system now distinguishes between "should restart" (system failures) and "should not restart" (business logic decisions), providing a much better user experience and preventing resource waste from infinite restart loops.
