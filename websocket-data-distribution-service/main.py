#!/usr/bin/env python3
"""
WebSocket Data Distribution Service

This service acts as a bridge between the frontend and the existing Polygon WebSocket service.
It handles WebSocket connections from the frontend, manages subscriptions via pub/sub,
and distributes real-time candle data.

Architecture:
Frontend → WebSocket → This Service → Pub/Sub → Polygon WebSocket Service
Frontend ← WebSocket ← This Service ← Pub/Sub ← Polygon WebSocket Service
"""

import asyncio
import logging
import os
import signal
import sys

from dotenv import load_dotenv

# Import our custom modules
from websocket_server import WebSocketServer
from subscription_manager import SubscriptionManager
from candle_processor import CandleProcessor
from pubsub_client import PubSubClient
from polygon_api_client import PolygonAPIClient

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class WebSocketDataDistributionService:
    """
    Main service class that orchestrates all components.
    """
    
    def __init__(self):
        # Configuration
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
        self.websocket_host = os.getenv('WEBSOCKET_HOST', '0.0.0.0')
        # Use Cloud Run's PORT environment variable, fallback to WEBSOCKET_PORT, then 8765
        self.websocket_port = int(os.getenv('PORT', os.getenv('WEBSOCKET_PORT', '8765')))
        self.polygon_api_key = os.getenv('POLYGON_API_KEY')
        
        # Service components
        self.websocket_server = None
        self.subscription_manager = None
        self.candle_processor = None
        self.pubsub_client = None
        self.polygon_client = None
        
        # Service state
        self.running = False
        self.shutdown_event = asyncio.Event()
        
        logger.info(f"🚀 WebSocket Data Distribution Service initializing...")
        logger.info(f"📡 WebSocket server will run on {self.websocket_host}:{self.websocket_port}")
        logger.info(f"🔑 Polygon API key: {'✅ Set' if self.polygon_api_key else '❌ Missing'}")
        logger.info(f"☁️ Google Cloud Project: {self.project_id}")

    async def initialize(self):
        """Initialize all service components."""
        try:
            logger.info("🔧 Initializing service components...")
            
            # Initialize Polygon API client
            if not self.polygon_api_key:
                raise ValueError("POLYGON_API_KEY environment variable is required")
            
            self.polygon_client = PolygonAPIClient(self.polygon_api_key)
            
            # Initialize Pub/Sub client
            self.pubsub_client = PubSubClient(self.project_id)
            await self.pubsub_client.initialize()
            
            # Initialize subscription manager
            self.subscription_manager = SubscriptionManager()
            
            # Initialize candle processor
            self.candle_processor = CandleProcessor(
                polygon_client=self.polygon_client,
                pubsub_client=self.pubsub_client
            )
            
            # Initialize WebSocket server
            self.websocket_server = WebSocketServer(
                host=self.websocket_host,
                port=self.websocket_port,
                subscription_manager=self.subscription_manager,
                candle_processor=self.candle_processor,
                project_id=self.project_id
            )

            # Set WebSocket server reference in candle processor
            self.candle_processor.set_websocket_server(self.websocket_server)
            
            logger.info("✅ All service components initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize service components: {e}")
            raise

    async def _auto_subscribe_to_active_pairs(self):
        """Auto-subscribe to all active pairs from trade-bots (always-running architecture)."""
        try:
            logger.info("🔄 Auto-subscribing to active pairs from trade-bots...")

            # Wait a bit for the pub/sub system to be ready
            await asyncio.sleep(2)

            # Start the pub/sub subscriber to receive minute aggregates from all active pairs
            # The candle processor will automatically cache all 1m candles it receives
            await self.pubsub_client.start_subscriber()

            logger.info("✅ Auto-subscription to active pairs completed - now caching all 1m candles")

        except Exception as e:
            logger.error(f"❌ Error during auto-subscription: {e}")

    async def _cleanup_previous_subscriptions(self):
        """Clean up any previous DDS subscriptions to Polygon WebSocket Service."""
        try:
            logger.info("🧹 Cleaning up previous DDS subscriptions (passive mode)...")

            # Common forex pairs in different formats that DDS might have subscribed to
            common_pairs = [
                'EURUSD', 'EUR/USD',  # Both formats
                'GBPUSD', 'GBP/USD',
                'USDJPY', 'USD/JPY',
                'USDCHF', 'USD/CHF',
                'AUDUSD', 'AUD/USD',
                'USDCAD', 'USD/CAD',
                'NZDUSD', 'NZD/USD'
            ]

            # Different client IDs that DDS might have used
            client_ids = [
                'websocket-data-distribution-cleanup',
                'websocket-data-distribution-EURUSD',
                'websocket-data-distribution-EUR/USD',
                'websocket-data-distribution-service',
                'dds-cleanup'
            ]

            cleanup_count = 0
            for symbol in common_pairs:
                for client_id in client_ids:
                    try:
                        # Send unsubscribe command for this symbol/client combination
                        success = await self.pubsub_client.publish_subscription_command(
                            symbol=symbol,
                            command='unsubscribe',
                            client_id=client_id
                        )

                        if success:
                            cleanup_count += 1
                            logger.info(f"🧹 Sent cleanup unsubscribe for {symbol} (client: {client_id})")
                        else:
                            logger.debug(f"⚠️ Failed to send cleanup unsubscribe for {symbol} (client: {client_id})")

                    except Exception as e:
                        logger.debug(f"⚠️ Error cleaning up {symbol} with {client_id}: {e}")

            logger.info(f"🧹 Sent {cleanup_count} cleanup unsubscribe commands")

            # Wait longer for cleanup commands to be processed
            await asyncio.sleep(5)
            logger.info("✅ DDS subscription cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error during DDS subscription cleanup: {e}")

    async def start(self):
        """Start the service."""
        try:
            logger.info("🚀 Starting WebSocket Data Distribution Service...")

            # Clean up any previous DDS subscriptions (passive mode)
            await self._cleanup_previous_subscriptions()

            # Initialize components
            await self.initialize()

            # Start all components
            tasks = []
            
            # Start WebSocket server
            websocket_task = asyncio.create_task(
                self.websocket_server.start()
            )
            tasks.append(websocket_task)
            
            # Start candle processor
            processor_task = asyncio.create_task(
                self.candle_processor.start()
            )
            tasks.append(processor_task)

            # Auto-subscribe to all active pairs (always-running architecture)
            auto_subscribe_task = asyncio.create_task(
                self._auto_subscribe_to_active_pairs()
            )
            tasks.append(auto_subscribe_task)
            
            # Start pub/sub client
            pubsub_task = asyncio.create_task(
                self.pubsub_client.start()
            )
            tasks.append(pubsub_task)
            
            self.running = True
            logger.info("✅ WebSocket Data Distribution Service started successfully")
            
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
            # Cancel all tasks
            for task in tasks:
                task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"❌ Error starting service: {e}")
            raise
        finally:
            await self.cleanup()

    async def cleanup(self):
        """Clean up resources."""
        logger.info("🧹 Cleaning up service resources...")
        
        try:
            if self.websocket_server:
                await self.websocket_server.stop()
            
            if self.candle_processor:
                await self.candle_processor.stop()
            
            if self.pubsub_client:
                await self.pubsub_client.stop()
            
            logger.info("✅ Service cleanup completed")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

    def handle_shutdown(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"📡 Received shutdown signal {signum}")
        self.running = False
        self.shutdown_event.set()


async def main():
    """Main entry point."""
    service = WebSocketDataDistributionService()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, service.handle_shutdown)
    signal.signal(signal.SIGTERM, service.handle_shutdown)
    
    try:
        await service.start()
    except KeyboardInterrupt:
        logger.info("👋 Service interrupted by user")
    except Exception as e:
        logger.error(f"❌ Service failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
