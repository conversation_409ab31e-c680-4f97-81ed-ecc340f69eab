#!/usr/bin/env python3
"""
Simple WebSocket server test to debug connection issues.
"""

import asyncio
import json
import logging
import websockets

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def handle_client(websocket):
    """Handle a WebSocket client connection."""
    client_ip = websocket.remote_address
    logger.info(f"🔗 New connection from {client_ip}")
    
    try:
        # Send welcome message
        welcome_msg = {
            "type": "welcome",
            "message": "Connected to simple WebSocket server",
            "path": "/"
        }
        await websocket.send(json.dumps(welcome_msg))
        
        # Listen for messages
        async for message in websocket:
            try:
                data = json.loads(message)
                logger.info(f"📨 Received: {data}")
                
                # Echo the message back
                response = {
                    "type": "echo",
                    "original": data,
                    "message": "Message received successfully"
                }
                await websocket.send(json.dumps(response))
                
            except json.JSONDecodeError:
                error_msg = {
                    "type": "error",
                    "message": "Invalid JSON format"
                }
                await websocket.send(json.dumps(error_msg))
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"🔌 Connection closed: {client_ip}")
    except Exception as e:
        logger.error(f"❌ Error handling client {client_ip}: {e}")


async def main():
    """Start the simple WebSocket server."""
    host = "0.0.0.0"
    port = 8766  # Use a different port to avoid conflicts
    
    logger.info(f"🚀 Starting simple WebSocket server on {host}:{port}")
    
    try:
        # Start the server
        server = await websockets.serve(
            handle_client,
            host,
            port,
            ping_interval=20,
            ping_timeout=10
        )
        
        logger.info(f"✅ Simple WebSocket server started on {host}:{port}")
        logger.info(f"🔗 Test with: ws://localhost:{port}")
        
        # Keep the server running
        await server.wait_closed()
        
    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")


if __name__ == "__main__":
    asyncio.run(main())
