#!/bin/bash

# WebSocket Data Distribution Service - Cloud Run Deployment Script
# This script builds and deploys the service to Google Cloud Run

set -e  # Exit on any error

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="websocket-data-distribution-service"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 Starting Cloud Run deployment for ${SERVICE_NAME}"
echo "📍 Project: ${PROJECT_ID}"
echo "🌍 Region: ${REGION}"
echo "🖼️  Image: ${IMAGE_NAME}"

# Check if running in GitHub Actions or local environment
if [ -n "${GITHUB_ACTIONS}" ]; then
    echo "🤖 Running in GitHub Actions - using service account authentication"
    # In GitHub Actions, authentication is handled by google-github-actions/auth
    # Just configure Docker for the registry
    gcloud auth configure-docker gcr.io --quiet
else
    echo "💻 Running locally - checking gcloud authentication..."
    # Check if gcloud is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo "❌ No active gcloud authentication found"
        echo "Please run: gcloud auth login"
        exit 1
    fi

    # Set the project
    echo "📋 Setting gcloud project..."
    gcloud config set project "${PROJECT_ID}"

    # Configure Docker authentication
    echo "🔐 Configuring Docker authentication..."
    gcloud auth configure-docker gcr.io --quiet
fi

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Use your existing build service account
BUILD_SA_EMAIL="build-service-account-872@${PROJECT_ID}.iam.gserviceaccount.com"
echo "📧 Using build service account: ${BUILD_SA_EMAIL}"

# Build the Docker image using build configuration
echo "🏗️  Building Docker image with cloudbuild.yaml..."
gcloud builds submit --config=cloudbuild.yaml .

# Use the same service account for Cloud Run service
SA_EMAIL="build-service-account-872@${PROJECT_ID}.iam.gserviceaccount.com"
echo "✅ Using existing service account for Cloud Run: ${SA_EMAIL}"

# Ensure the service account has necessary permissions for this service
echo "🔑 Ensuring service account has required permissions..."
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SA_EMAIL}" \
    --role="roles/pubsub.editor" \
    --condition=None \
    --quiet

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SA_EMAIL}" \
    --role="roles/secretmanager.secretAccessor" \
    --condition=None \
    --quiet

# Add Firebase Admin permissions
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SA_EMAIL}" \
    --role="roles/firebase.admin" \
    --condition=None \
    --quiet

# Check if the secret exists
echo "🔍 Checking Polygon API key secret..."
if ! gcloud secrets describe polygon-api-key >/dev/null 2>&1; then
    echo "❌ Secret 'polygon-api-key' not found"
    echo "Please create it with: gcloud secrets create polygon-api-key --data-file=<path-to-api-key-file>"
    echo "Or: echo 'YOUR_API_KEY' | gcloud secrets create polygon-api-key --data-file=-"
    exit 1
else
    echo "✅ Polygon API key secret found"
fi

# Deploy to Cloud Run using gcloud (more reliable than kubectl for Cloud Run)
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image=${IMAGE_NAME} \
    --platform=managed \
    --region=${REGION} \
    --service-account=${SA_EMAIL} \
    --allow-unauthenticated \
    --memory=4Gi \
    --cpu=2 \
    --min-instances=1 \
    --max-instances=10 \
    --timeout=3600 \
    --concurrency=1000 \
    --no-cpu-throttling \
    --execution-environment=gen2 \
    --session-affinity \
    --revision-suffix="passive-$(date +%s)" \
    --no-traffic \
    --set-env-vars="GOOGLE_CLOUD_PROJECT=${PROJECT_ID},WEBSOCKET_HOST=0.0.0.0,DDS_MODE=passive" \
    --set-secrets="POLYGON_API_KEY=polygon-api-key:latest"

# Force 100% traffic to the new revision
echo "🔄 Directing 100% traffic to new revision..."
gcloud run services update-traffic ${SERVICE_NAME} \
    --region=${REGION} \
    --to-latest

# Get the service URL
echo "🔗 Getting service URL..."
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format="value(status.url)")

echo ""
echo "✅ Deployment completed successfully!"
echo "🌐 Service URL: ${SERVICE_URL}"
echo "📊 WebSocket endpoint: ${SERVICE_URL/https:/wss:}"
echo ""
echo "🔍 To check logs:"
echo "   gcloud logs tail --follow --resource-type=cloud_run_revision --resource-labels=service_name=${SERVICE_NAME}"
echo ""
echo "📈 To check service status:"
echo "   gcloud run services describe ${SERVICE_NAME} --region=${REGION}"
echo ""
echo "🔧 To update the service:"
echo "   ./deploy-cloudrun.sh"
