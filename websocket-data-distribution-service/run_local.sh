#!/bin/bash

echo "🌐 Setting up WebSocket Data Distribution Service for local development..."

# Set development mode
export DEVELOPMENT_MODE=true

# Check if .env file exists, create from example if not
if [ ! -f .env ]; then
    if [ -f .env.example ]; then
        echo "📋 Creating .env from .env.example..."
        cp .env.example .env
        echo "⚠️  Please edit .env file with your configuration before running again."
        exit 1
    else
        echo "❌ No .env file found. Please create one with your configuration."
        exit 1
    fi
fi

# Load environment variables from .env file
set -a
source .env
set +a

# Check required environment variables
if [ -z "$POLYGON_API_KEY" ]; then
    echo "❌ POLYGON_API_KEY is required. Please set it in your .env file."
    exit 1
fi

# Set default values
export GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT:-"oryntrade"}
export WEBSOCKET_HOST=${WEBSOCKET_HOST:-"0.0.0.0"}
export WEBSOCKET_PORT=${WEBSOCKET_PORT:-"8765"}
export LOG_LEVEL=${LOG_LEVEL:-"INFO"}

# Check if Pub/Sub emulator is running
if [ -z "$PUBSUB_EMULATOR_HOST" ]; then
    echo "🔧 Setting up Pub/Sub emulator..."
    export PUBSUB_EMULATOR_HOST=localhost:8085
    
    # Check if emulator is running
    if ! curl -s http://localhost:8085 > /dev/null 2>&1; then
        echo "❌ Pub/Sub emulator not running on localhost:8085"
        echo "💡 Start it with: gcloud beta emulators pubsub start --port=8085 --host-port=localhost:8085"
        exit 1
    fi
fi

echo "✅ Pub/Sub emulator detected at $PUBSUB_EMULATOR_HOST"

# Install dependencies if needed
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

echo "📦 Activating virtual environment..."
source venv/bin/activate

# Check if dependencies are installed
if ! python -c "import google.cloud.pubsub_v1" 2>/dev/null; then
    echo "📦 Installing dependencies..."
    pip install -r requirements.txt
else
    echo "✅ Dependencies already installed"
fi

# Setup Pub/Sub topics and subscriptions
echo "🔧 Setting up Pub/Sub topics and subscriptions..."
python setup_pubsub_local.py

# Add current directory to PYTHONPATH
export PYTHONPATH=$PYTHONPATH:$(pwd)

echo "🚀 Starting WebSocket Data Distribution Service..."
echo "📡 WebSocket server will be available at ws://$WEBSOCKET_HOST:$WEBSOCKET_PORT"
echo "🔑 Using Polygon API key: ${POLYGON_API_KEY:0:10}..."
echo "☁️  Google Cloud Project: $GOOGLE_CLOUD_PROJECT"
echo ""
echo "💡 Test the service with: python test_client.py"
echo ""

# Run the service
python main.py
