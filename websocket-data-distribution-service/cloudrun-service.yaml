apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: websocket-data-distribution-service
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        # Always-on with minimum 1 instance
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "10"
        # CPU allocation during request processing only
        run.googleapis.com/cpu-throttling: "false"

    spec:
      # Service account with necessary permissions
      serviceAccountName: <EMAIL>
      
      # Container timeout (max for Cloud Run)
      timeoutSeconds: 3600
      
      containerConcurrency: 1000
      
      containers:
      - name: websocket-data-distribution-service
        image: gcr.io/oryntrade/websocket-data-distribution-service:latest
        
        ports:
        - name: http1
          containerPort: 8765
          protocol: TCP
        
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "oryntrade"
        - name: WEBSOCKET_HOST
          value: "0.0.0.0"
        - name: WEBSOCKET_PORT
          value: "8765"
        - name: POLYGON_API_KEY
          valueFrom:
            secretKeyRef:
              name: polygon-api-key
              key: api-key
        
        resources:
          limits:
            # 2 vCPU and 4GB memory for WebSocket handling
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
        
        # Startup probe for initial readiness
        startupProbe:
          tcpSocket:
            port: 8765
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 10
        
        # Liveness probe to restart if unhealthy
        livenessProbe:
          tcpSocket:
            port: 8765
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        
        # Readiness probe for traffic routing
        readinessProbe:
          tcpSocket:
            port: 8765
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 2
