steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'gcr.io/oryntrade/websocket-data-distribution-service:latest', '.']
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'gcr.io/oryntrade/websocket-data-distribution-service:latest']

options:
  logging: CLOUD_LOGGING_ONLY
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET

serviceAccount: 'projects/oryntrade/serviceAccounts/<EMAIL>'
