#!/usr/bin/env python3
"""
Candle Processor for WebSocket Data Distribution Service

Handles the core logic of building full timeframe candles from 1-minute aggregates,
managing candle builders for different symbol/timeframe combinations, and
coordinating with the Polygon API client and Pub/Sub client.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List

from candle_builder import CandleBuilder

logger = logging.getLogger(__name__)


class CandleProcessor:
    """
    Processes minute aggregate data and builds full timeframe candles.
    
    Manages multiple candle builders for different symbol/timeframe combinations
    and coordinates data flow between Pub/Sub and WebSocket clients.
    """
    
    def __init__(self, polygon_client, pubsub_client):
        self.polygon_client = polygon_client
        self.pubsub_client = pubsub_client
        
        # Candle builders for each symbol/timeframe combination
        # Format: {(symbol, timeframe): CandleBuilder}
        self.candle_builders: Dict[tuple, CandleBuilder] = {}
        
        # Active subscriptions
        # Format: {(symbol, timeframe): subscription_info}
        self.active_subscriptions: Dict[tuple, Dict[str, Any]] = {}

        # Global 1m candle cache for all pairs (always-running architecture)
        self.minute_candle_cache: Dict[str, List[Dict]] = {}  # symbol -> list of 1m candles
        self.cache_max_size = 10000  # Keep last 10k 1m candles per symbol

        # DDS is passive - it doesn't manage Polygon WebSocket subscriptions
        # It only receives data that trade-bots have already requested

        # WebSocket server reference (set later)
        self.websocket_server = None

        # State
        self.running = False
        
        # Statistics
        self.candles_processed = 0
        self.candles_built = 0
        self.processing_errors = 0
        
        logger.info("🔧 Candle Processor initialized")

    def set_websocket_server(self, websocket_server):
        """Set the WebSocket server reference for broadcasting candles."""
        self.websocket_server = websocket_server
        logger.info("🌐 WebSocket server reference set")

    async def start(self):
        """Start the candle processor."""
        logger.info("🚀 Starting Candle Processor...")
        
        self.running = True
        
        # Set up message callback for Pub/Sub client
        self.pubsub_client.set_message_callback(self._handle_minute_aggregate)
        
        logger.info("✅ Candle Processor started")

    async def stop(self):
        """Stop the candle processor."""
        logger.info("🛑 Stopping Candle Processor...")
        
        self.running = False
        
        # Clean up candle builders
        self.candle_builders.clear()
        self.active_subscriptions.clear()
        
        logger.info("✅ Candle Processor stopped")

    async def start_subscription(self, symbol: str, timeframe: str, since_timestamp: int = None):
        """
        Start processing for a new symbol/timeframe subscription with optional catch-up.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            timeframe: Timeframe (e.g., '1h', '5m')
            since_timestamp: Optional timestamp for catch-up data (seconds since epoch)
        """
        subscription_key = (symbol, timeframe)

        if subscription_key in self.active_subscriptions:
            logger.info(f"📊 Subscription already active: {symbol} {timeframe}")
            return

        logger.info(f"🆕 Starting new subscription: {symbol} {timeframe}")

        try:
            # Create candle builder
            candle_builder = CandleBuilder(
                symbol=symbol,
                timeframe=timeframe
            )

            # Store candle builder
            self.candle_builders[subscription_key] = candle_builder

            # Store subscription info
            self.active_subscriptions[subscription_key] = {
                'symbol': symbol,
                'timeframe': timeframe,
                'started_at': datetime.now(timezone.utc),
                'candles_built': 0,
                'last_candle_time': None,
                'last_processed_timestamp': None  # Track last processed timestamp for duplicate detection
            }

            # Fetch missing 1m candles for current period
            await self._initialize_candle_builder(symbol, timeframe, candle_builder)

            # Start the pub/sub subscriber if this is the first subscription overall
            # DDS is PASSIVE - it only listens to data already being fetched by trade-bots
            if len(self.active_subscriptions) == 1:
                logger.info("🚀 Starting pub/sub subscriber for first subscription (passive mode)")
                await self.pubsub_client.start_subscriber()

            # DDS does NOT send subscription commands to Polygon WebSocket Service
            # It only receives data that trade-bots have already requested
            logger.info(f"📊 DDS will passively receive {symbol} data if trade-bots are active")

            # Send catch-up data if requested
            if since_timestamp:
                await self._send_catch_up_data(symbol, timeframe, since_timestamp)
            else:
                logger.info(f"📊 No catch-up requested for {symbol} {timeframe} - will send real-time data only")

            logger.info(f"✅ Successfully started subscription: {symbol} {timeframe}")

        except Exception as e:
            logger.error(f"❌ Error starting subscription {symbol} {timeframe}: {e}")
            # Clean up
            if subscription_key in self.candle_builders:
                del self.candle_builders[subscription_key]
            if subscription_key in self.active_subscriptions:
                del self.active_subscriptions[subscription_key]

    async def stop_subscription(self, symbol: str, timeframe: str):
        """
        Stop processing for a symbol/timeframe subscription.
        DDS is passive - it doesn't manage Polygon WebSocket subscriptions.
        It only stops forwarding data to frontend clients.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            timeframe: Timeframe (e.g., '1h', '5m')
        """
        subscription_key = (symbol, timeframe)

        if subscription_key not in self.active_subscriptions:
            logger.info(f"📊 Subscription not active: {symbol} {timeframe}")
            return

        logger.info(f"🛑 Stopping subscription: {symbol} {timeframe}")

        try:
            # Clean up candle builder and subscription info first
            if subscription_key in self.candle_builders:
                del self.candle_builders[subscription_key]

            if subscription_key in self.active_subscriptions:
                del self.active_subscriptions[subscription_key]

            # Check if we need to unsubscribe from real-time data for this symbol
            # Only unsubscribe if there are no more subscriptions for this symbol
            symbol_subscriptions = [key for key in self.active_subscriptions.keys() if key[0] == symbol]

            if len(symbol_subscriptions) == 0:  # No more subscriptions for this symbol
                # DDS is passive - it doesn't control Polygon WebSocket subscriptions
                # It will continue receiving data if trade-bots are still active for this symbol
                logger.info(f"📡 Last frontend subscription for {symbol} removed - will continue receiving data if trade-bots are active")
                logger.info(f"🔄 DDS will continue caching {symbol} data if trade-bots keep it active")
            else:
                logger.info(f"📊 Keeping real-time subscription for {symbol} (other timeframes active)")

            # NEVER stop the pub/sub subscriber - keep it always running for instant reconnection
            # This prevents 10+ second delays when frontend reconnects
            if len(self.active_subscriptions) == 0:
                logger.info("📡 No more active subscriptions but keeping pub/sub subscriber running for instant reconnection")

            logger.info(f"✅ Successfully stopped subscription: {symbol} {timeframe}")

        except Exception as e:
            logger.error(f"❌ Error stopping subscription {symbol} {timeframe}: {e}")

    async def _send_catch_up_data(self, symbol: str, timeframe: str, since_timestamp: int):
        """Send catch-up data by aggregating cached 1m candles since the given timestamp."""
        try:
            logger.info(f"📊 Sending catch-up data for {symbol} {timeframe} since timestamp {since_timestamp}")

            # Get cached 1m candles for this symbol
            if symbol not in self.minute_candle_cache:
                logger.warning(f"⚠️ No cached 1m candles available for {symbol}")
                return

            cached_candles = self.minute_candle_cache[symbol]

            # Filter candles since the given timestamp
            catch_up_candles = []
            for candle in cached_candles:
                candle_time = candle.get('time', candle.get('t', 0))
                # Convert from milliseconds to seconds if needed
                if candle_time > 1000000000000:
                    candle_time = candle_time // 1000

                if candle_time > since_timestamp:
                    catch_up_candles.append(candle)

            if not catch_up_candles:
                logger.info(f"📊 No catch-up candles needed for {symbol} {timeframe}")
                return

            # Aggregate 1m candles into the requested timeframe
            aggregated_candles = self._aggregate_candles_to_timeframe(catch_up_candles, timeframe)

            if aggregated_candles:
                logger.info(f"✅ Aggregated {len(catch_up_candles)} 1m candles into {len(aggregated_candles)} {timeframe} candles")

                # Send catch-up data to WebSocket clients
                if self.websocket_server:
                    await self.websocket_server.broadcast_catch_up_data(symbol, timeframe, aggregated_candles)
                    logger.info(f"📡 Sent catch-up data to WebSocket clients: {symbol} {timeframe}")
                else:
                    logger.warning("⚠️ WebSocket server not available for sending catch-up data")

        except Exception as e:
            logger.error(f"❌ Error sending catch-up data for {symbol} {timeframe}: {e}")

    def _aggregate_candles_to_timeframe(self, minute_candles: List[Dict], timeframe: str) -> List[Dict]:
        """Aggregate 1m candles into the requested timeframe."""
        if not minute_candles:
            return []

        # Parse timeframe (e.g., '5m', '1h', '4h', '1d')
        timeframe_minutes = self._parse_timeframe_to_minutes(timeframe)
        if not timeframe_minutes:
            logger.error(f"❌ Invalid timeframe: {timeframe}")
            return []

        aggregated_candles = []
        current_period_start = None
        current_candle = None

        for candle in sorted(minute_candles, key=lambda x: x.get('time', x.get('t', 0))):
            candle_time = candle.get('time', candle.get('t', 0))
            # Convert from milliseconds to seconds if needed
            if candle_time > 1000000000000:
                candle_time = candle_time // 1000

            # Calculate period start for this candle
            period_start = (candle_time // (timeframe_minutes * 60)) * (timeframe_minutes * 60)

            if current_period_start != period_start:
                # Finalize previous candle if exists
                if current_candle:
                    aggregated_candles.append(current_candle)

                # Start new period
                current_period_start = period_start
                current_candle = {
                    'time': period_start,
                    'open': candle.get('open', candle.get('o', 0)),
                    'high': candle.get('high', candle.get('h', 0)),
                    'low': candle.get('low', candle.get('l', 0)),
                    'close': candle.get('close', candle.get('c', 0)),
                    'volume': candle.get('volume', candle.get('v', 0))
                }
            else:
                # Update existing candle
                if current_candle:
                    current_candle['high'] = max(current_candle['high'], candle.get('high', candle.get('h', 0)))
                    current_candle['low'] = min(current_candle['low'], candle.get('low', candle.get('l', 0)))
                    current_candle['close'] = candle.get('close', candle.get('c', 0))
                    current_candle['volume'] += candle.get('volume', candle.get('v', 0))

        # Add the last candle
        if current_candle:
            aggregated_candles.append(current_candle)

        return aggregated_candles

    def _parse_timeframe_to_minutes(self, timeframe: str) -> int:
        """Parse timeframe string to minutes."""
        timeframe = timeframe.lower()
        if timeframe.endswith('m'):
            return int(timeframe[:-1])
        elif timeframe.endswith('h'):
            return int(timeframe[:-1]) * 60
        elif timeframe.endswith('d'):
            return int(timeframe[:-1]) * 24 * 60
        else:
            return 0

    def _cache_minute_candle(self, symbol: str, candle_data: Dict[str, Any]):
        """Cache a 1m candle for catch-up functionality."""
        if symbol not in self.minute_candle_cache:
            self.minute_candle_cache[symbol] = []

        # Add the candle to the cache
        self.minute_candle_cache[symbol].append(candle_data)

        # Maintain cache size limit (FIFO)
        if len(self.minute_candle_cache[symbol]) > self.cache_max_size:
            self.minute_candle_cache[symbol] = self.minute_candle_cache[symbol][-self.cache_max_size:]

        logger.info(f"📦 Cached 1m candle for {symbol}: {len(self.minute_candle_cache[symbol])} total cached")

    async def _initialize_candle_builder(self, symbol: str, timeframe: str, candle_builder: CandleBuilder):
        """
        Initialize a candle builder by fetching missing 1m candles for the current period.
        
        Args:
            symbol: Forex pair symbol
            timeframe: Timeframe
            candle_builder: CandleBuilder instance
        """
        try:
            logger.info(f"🔧 Initializing candle builder for {symbol} {timeframe}")
            
            # Calculate current period and missing candles
            now = datetime.now(timezone.utc)
            timeframe_minutes = self._get_timeframe_minutes(timeframe)
            
            # Calculate the start of the current period
            period_start = self._calculate_period_start(now, timeframe_minutes)
            
            # Calculate how many minutes into the period we are
            minutes_into_period = int((now - period_start).total_seconds() / 60)
            
            if minutes_into_period > 0:
                logger.info(f"⏰ Mid-period start: {minutes_into_period} minutes into {timeframe} period")
                
                # Fetch the missing 1m candles for this period
                period_end = period_start + timedelta(minutes=minutes_into_period)
                missing_candles = await self.polygon_client.fetch_missing_candles(
                    symbol=symbol,
                    start_time=period_start,
                    end_time=period_end
                )
                
                if missing_candles:
                    # Filter candles to only include those in the current period
                    period_end_timestamp = int(period_end.timestamp())
                    period_start_timestamp = int(period_start.timestamp())

                    filtered_candles = []
                    for candle in missing_candles:
                        candle_time = candle.get('time', candle.get('t', 0))
                        # Convert from milliseconds to seconds if needed
                        if candle_time > 1000000000000:  # If timestamp is in milliseconds
                            candle_time = candle_time // 1000

                        # Only include candles within the current period
                        if period_start_timestamp <= candle_time < period_end_timestamp:
                            filtered_candles.append(candle)

                    logger.info(f"📊 Filtered {len(filtered_candles)} candles from {len(missing_candles)} fetched (only current period)")

                    # Add only the filtered candles to the builder
                    for candle in filtered_candles:
                        candle_builder.add_1m_candle(candle)
                else:
                    logger.warning(f"⚠️ No missing candles found for period {period_start} to {period_end}")
            else:
                logger.info(f"✅ Starting at period boundary - no missing candles needed")
            
        except Exception as e:
            logger.error(f"❌ Error initializing candle builder: {e}")

    async def _handle_minute_aggregate(self, data: Dict[str, Any]):
        """
        Handle incoming minute aggregate data from Pub/Sub.

        Args:
            data: Minute aggregate data from Polygon WebSocket service
        """
        try:
            symbol = data.get('symbol')
            if not symbol:
                logger.warning("⚠️ Received minute aggregate without symbol")
                return

            # Parse timestamp - handle both ISO format and integer timestamps
            timestamp_raw = data.get('timestamp')
            if isinstance(timestamp_raw, str):
                # Parse ISO format timestamp
                from datetime import datetime
                dt = datetime.fromisoformat(timestamp_raw.replace('Z', '+00:00'))
                timestamp = int(dt.timestamp())
            else:
                # Already an integer timestamp
                timestamp = int(timestamp_raw)

            # Convert to our candle format
            candle_data = {
                'time': timestamp,
                'open': float(data['open']),
                'high': float(data['high']),
                'low': float(data['low']),
                'close': float(data['close']),
                'volume': int(data.get('volume', 0))
            }

            # Cache this 1m candle for catch-up functionality (ALWAYS cache in always-running architecture)
            self._cache_minute_candle(symbol, candle_data)

            self.candles_processed += 1

            # Normalize symbol format for comparison (EUR/USD -> EURUSD)
            normalized_symbol = symbol.replace('/', '')

            # Process this candle for active subscriptions (if any)
            symbol_subscriptions = [(sub_symbol, timeframe) for (sub_symbol, timeframe) in self.candle_builders.keys() if sub_symbol == normalized_symbol]

            if not symbol_subscriptions:
                logger.debug(f"📦 Cached 1m candle for {symbol} - no active subscriptions yet")
                return

            logger.info(f"📊 Processing minute aggregate for {normalized_symbol} - {len(symbol_subscriptions)} active subscriptions")

            # Process this candle for all active subscriptions for this symbol
            for (sub_symbol, timeframe), builder in self.candle_builders.items():
                if sub_symbol == normalized_symbol:
                    await self._process_candle_for_timeframe(
                        normalized_symbol, timeframe, candle_data, builder
                    )
            
        except Exception as e:
            self.processing_errors += 1
            logger.error(f"❌ Error handling minute aggregate: {e}")

    async def _process_candle_for_timeframe(self, symbol: str, timeframe: str, 
                                         candle_data: Dict[str, Any], builder: CandleBuilder):
        """
        Process a 1m candle for a specific timeframe.
        
        Args:
            symbol: Forex pair symbol
            timeframe: Timeframe
            candle_data: 1-minute candle data
            builder: CandleBuilder instance
        """
        try:
            # Check for duplicate timestamps to prevent chart errors
            candle_timestamp = candle_data.get('time')
            subscription_key = (symbol, timeframe)

            # Track last processed timestamp for this subscription
            if subscription_key not in self.active_subscriptions:
                logger.warning(f"⚠️ Subscription {symbol} {timeframe} not found in active subscriptions")
                return

            last_timestamp = self.active_subscriptions[subscription_key].get('last_processed_timestamp')

            if last_timestamp and candle_timestamp == last_timestamp:
                logger.warning(f"🔄 Duplicate timestamp detected for {symbol} {timeframe}: {candle_timestamp} - skipping")
                return

            # Update last processed timestamp
            self.active_subscriptions[subscription_key]['last_processed_timestamp'] = candle_timestamp

            # Add candle to builder
            logger.info(f"🔧 About to add 1m candle to builder for {symbol} {timeframe}")
            completed_candle = builder.add_1m_candle(candle_data)
            logger.info(f"🔧 Builder.add_1m_candle returned: {completed_candle is not None} for {symbol} {timeframe}")

            if completed_candle:
                logger.info(f"🕯️ Completed {timeframe} candle for {symbol}")

                # Update statistics
                self.candles_built += 1
                subscription_key = (symbol, timeframe)
                if subscription_key in self.active_subscriptions:
                    self.active_subscriptions[subscription_key]['candles_built'] += 1
                    self.active_subscriptions[subscription_key]['last_candle_time'] = completed_candle['time']

                # Broadcast to WebSocket clients
                logger.info(f"🔧 About to broadcast candle data to WebSocket clients for {symbol} {timeframe}")
                if self.websocket_server:
                    try:
                        await self.websocket_server.broadcast_candle_data(
                            symbol=symbol,
                            timeframe=timeframe,
                            candle_data=completed_candle
                        )
                        logger.info(f"🔧 Successfully broadcasted candle data for {symbol} {timeframe}")
                    except Exception as broadcast_error:
                        logger.error(f"❌ Error broadcasting candle data: {broadcast_error}")
                        import traceback
                        logger.error(f"❌ Broadcast traceback: {traceback.format_exc()}")
                else:
                    logger.warning("⚠️ No WebSocket server reference - cannot broadcast candle")
            else:
                logger.info(f"⏳ No completed candle yet for {symbol} {timeframe} - waiting for more data")
            
        except Exception as e:
            logger.error(f"❌ Error processing candle for {symbol} {timeframe}: {e}")

    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe string to minutes."""
        timeframe_map = {
            '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '2h': 120, '4h': 240, '6h': 360, '8h': 480, '12h': 720,
            '1d': 1440, '1w': 10080
        }
        return timeframe_map.get(timeframe, 60)  # Default to 1 hour

    def _calculate_period_start(self, current_time: datetime, timeframe_minutes: int) -> datetime:
        """Calculate the start of the current period for a given timeframe."""
        # Round down to the nearest period boundary
        total_minutes = current_time.hour * 60 + current_time.minute
        period_minutes = (total_minutes // timeframe_minutes) * timeframe_minutes
        
        return current_time.replace(
            hour=period_minutes // 60,
            minute=period_minutes % 60,
            second=0,
            microsecond=0
        )

    def get_stats(self) -> Dict[str, Any]:
        """Get candle processor statistics."""
        return {
            'running': self.running,
            'active_subscriptions': len(self.active_subscriptions),
            'candles_processed': self.candles_processed,
            'candles_built': self.candles_built,
            'processing_errors': self.processing_errors,
            'subscriptions': [
                {
                    'symbol': info['symbol'],
                    'timeframe': info['timeframe'],
                    'started_at': info['started_at'].isoformat(),
                    'candles_built': info['candles_built'],
                    'last_candle_time': info['last_candle_time']
                }
                for info in self.active_subscriptions.values()
            ]
        }

    def log_stats(self):
        """Log candle processor statistics."""
        stats = self.get_stats()
        logger.info(f"📊 Candle Processor Stats:")
        logger.info(f"   Active Subscriptions: {stats['active_subscriptions']}")
        logger.info(f"   Candles Processed: {stats['candles_processed']}")
        logger.info(f"   Candles Built: {stats['candles_built']}")
        logger.info(f"   Processing Errors: {stats['processing_errors']}")
        
        if stats['subscriptions']:
            logger.info(f"   Active Subscriptions:")
            for sub in stats['subscriptions']:
                logger.info(f"     {sub['symbol']} {sub['timeframe']}: {sub['candles_built']} candles built")
