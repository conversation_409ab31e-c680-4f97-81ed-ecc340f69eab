apiVersion: apps/v1
kind: Deployment
metadata:
  name: websocket-data-distribution-service
  labels:
    app: websocket-data-distribution-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: websocket-data-distribution-service
  template:
    metadata:
      labels:
        app: websocket-data-distribution-service
    spec:
      containers:
      - name: websocket-data-distribution-service
        image: gcr.io/oryntrade/websocket-data-distribution-service:latest
        ports:
        - containerPort: 8765
          name: websocket
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "oryntrade"
        - name: WEBSOCKET_HOST
          value: "0.0.0.0"
        - name: WEBSOCKET_PORT
          value: "8765"
        - name: POLYGON_API_KEY
          valueFrom:
            secretKeyRef:
              name: polygon-api-secret
              key: api-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          tcpSocket:
            port: 8765
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 8765
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
      volumes:
      - name: google-cloud-key
        secret:
          secretName: google-cloud-key
      serviceAccountName: websocket-data-distribution-sa
---
apiVersion: v1
kind: Service
metadata:
  name: websocket-data-distribution-service
  labels:
    app: websocket-data-distribution-service
spec:
  type: LoadBalancer
  ports:
  - port: 8765
    targetPort: 8765
    protocol: TCP
    name: websocket
  selector:
    app: websocket-data-distribution-service
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: websocket-data-distribution-sa
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>
