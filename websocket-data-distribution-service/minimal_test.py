#!/usr/bin/env python3
"""
Minimal WebSocket server to test basic connectivity.
"""

import asyncio
import json
import logging
import websockets

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def handle_client(websocket):
    """Handle a WebSocket client connection."""
    client_ip = websocket.remote_address
    logger.info(f"🔗 New connection from {client_ip}")

    try:
        # Send welcome message
        welcome_msg = {
            "type": "connection_established",
            "message": "Connected to minimal test server",
            "path": "/"
        }
        await websocket.send(json.dumps(welcome_msg))
        logger.info(f"✅ Sent welcome message to {client_ip}")
        
        # Listen for messages
        async for message in websocket:
            try:
                data = json.loads(message)
                logger.info(f"📨 Received from {client_ip}: {data}")
                
                # Echo the message back
                response = {
                    "type": "echo",
                    "original": data,
                    "message": "Message received successfully"
                }
                await websocket.send(json.dumps(response))
                logger.info(f"📤 Sent response to {client_ip}")
                
            except json.JSONDecodeError:
                error_msg = {
                    "type": "error",
                    "message": "Invalid JSON format"
                }
                await websocket.send(json.dumps(error_msg))
                
    except websockets.exceptions.ConnectionClosed:
        logger.info(f"🔌 Connection closed: {client_ip}")
    except Exception as e:
        logger.error(f"❌ Error handling client {client_ip}: {e}")


async def main():
    """Start the minimal WebSocket server."""
    host = "0.0.0.0"
    port = 8768  # Use a different port to avoid conflicts
    
    logger.info(f"🚀 Starting minimal WebSocket server on {host}:{port}")
    
    try:
        # Start the server
        async with websockets.serve(
            handle_client,
            host,
            port,
            ping_interval=20,
            ping_timeout=10
        ):
            logger.info(f"✅ Minimal WebSocket server started on {host}:{port}")
            logger.info(f"🔗 Test with: ws://localhost:{port}")
            
            # Keep the server running
            while True:
                await asyncio.sleep(1)
        
    except Exception as e:
        logger.error(f"❌ Failed to start server: {e}")


if __name__ == "__main__":
    asyncio.run(main())
