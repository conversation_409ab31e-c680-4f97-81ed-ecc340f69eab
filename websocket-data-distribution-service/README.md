# WebSocket Data Distribution Service

A high-performance WebSocket service that distributes real-time forex candle data to frontend clients. This service acts as a bridge between the frontend and the existing Polygon WebSocket service via Google Cloud Pub/Sub.

## 🏗️ Architecture

```
Frontend → WebSocket → Data Distribution Service → Pub/Sub → Polygon WebSocket Service
Frontend ← WebSocket ← Data Distribution Service ← Pub/Sub ← Polygon WebSocket Service
```

## 🔄 Data Flow

1. **Frontend Connection**: Frontend connects to WebSocket server and subscribes to forex pair/timeframe
2. **Subscription Management**: Service manages subscriptions and publishes commands to `polygon.subscription_commands`
3. **Real-time Data**: Service receives minute aggregates from `polygon.minute_aggregates`
4. **Candle Building**: Service builds full timeframe candles from 1-minute aggregates
5. **Data Distribution**: Completed candles are sent to subscribed frontend clients via WebSocket

## 📡 WebSocket API

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8765');
```

### Message Format

#### Subscribe to Symbol/Timeframe
```json
{
  "type": "subscribe",
  "symbol": "EURUSD",
  "timeframe": "1h"
}
```

#### Unsubscribe
```json
{
  "type": "unsubscribe",
  "symbol": "EURUSD",
  "timeframe": "1h"
}
```

#### Ping/Pong
```json
{
  "type": "ping"
}
```

#### Get Status
```json
{
  "type": "get_status"
}
```

### Server Messages

#### Subscription Confirmed
```json
{
  "type": "subscription_confirmed",
  "symbol": "EURUSD",
  "timeframe": "1h",
  "subscription_key": "EURUSD_1h",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### Candle Data
```json
{
  "type": "candle_data",
  "symbol": "EURUSD",
  "timeframe": "1h",
  "candle": {
    "time": 1704067200,
    "open": 1.0950,
    "high": 1.0955,
    "low": 1.0948,
    "close": 1.0952,
    "volume": 1000000
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Google Cloud Project with Pub/Sub enabled
- Polygon.io API key

### Local Development

1. **Clone and setup**:
```bash
cd websocket-data-distribution-service
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Run the service**:
```bash
python main.py
```

### Docker Deployment

1. **Build image**:
```bash
docker build -t websocket-data-distribution-service .
```

2. **Run container**:
```bash
docker run -p 8765:8765 \
  -e GOOGLE_CLOUD_PROJECT=your-project \
  -e POLYGON_API_KEY=your-api-key \
  websocket-data-distribution-service
```

### Kubernetes Deployment

1. **Deploy to cluster**:
```bash
./deploy.sh
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_CLOUD_PROJECT` | Google Cloud project ID | `oryntrade` |
| `POLYGON_API_KEY` | Polygon.io API key | Required |
| `WEBSOCKET_HOST` | WebSocket server host | `0.0.0.0` |
| `WEBSOCKET_PORT` | WebSocket server port | `8765` |
| `LOG_LEVEL` | Logging level | `INFO` |

## 📊 Monitoring

The service provides built-in statistics and monitoring:

- Connection count and management
- Subscription tracking
- Candle processing metrics
- Error rates and performance

## 🔍 Troubleshooting

### Common Issues

1. **Connection refused**: Check if the service is running and port is accessible
2. **Subscription failures**: Verify Polygon API key and Pub/Sub configuration
3. **Missing candles**: Check Polygon WebSocket service status

### Logs

View service logs:
```bash
kubectl logs -l app=websocket-data-distribution-service -f
```

## 🏷️ Dependencies

- **websockets**: WebSocket server implementation
- **google-cloud-pubsub**: Google Cloud Pub/Sub client
- **aiohttp**: Async HTTP client for Polygon API
- **structlog**: Structured logging

## 📝 License

This service is part of the OrynTrade platform.
