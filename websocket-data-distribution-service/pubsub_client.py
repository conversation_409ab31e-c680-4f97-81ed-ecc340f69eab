#!/usr/bin/env python3
"""
Pub/Sub Client for WebSocket Data Distribution Service

Handles communication with Google Cloud Pub/Sub for:
1. Publishing subscription commands to the Polygon WebSocket service
2. Receiving minute aggregate data from the Polygon WebSocket service
3. Managing pub/sub topics and subscriptions
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Callable

from google.cloud import pubsub_v1
from google.api_core import exceptions
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class PubSubClient:
    """
    Handles Pub/Sub communication for the WebSocket Data Distribution Service.
    """
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        
        # Pub/Sub clients
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        
        # Topic and subscription names
        self.subscription_commands_topic = 'polygon.subscription_commands'
        self.minute_aggregates_topic = 'polygon.minute_aggregates'
        self.minute_aggregates_subscription = 'websocket-data-distribution-minute-aggregates-sub'
        
        # Topic paths
        self.subscription_commands_topic_path = self.publisher.topic_path(
            project_id, self.subscription_commands_topic
        )
        self.minute_aggregates_topic_path = self.publisher.topic_path(
            project_id, self.minute_aggregates_topic
        )
        
        # Subscription paths
        self.minute_aggregates_subscription_path = self.subscriber.subscription_path(
            project_id, self.minute_aggregates_subscription
        )
        
        # State
        self.running = False
        self.subscriber_running = False
        self.subscriber_task = None
        self.message_callback: Optional[Callable] = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Statistics
        self.messages_published = 0
        self.messages_received = 0
        self.publish_errors = 0
        self.receive_errors = 0
        
        logger.info(f"📡 Pub/Sub client initialized for project: {project_id}")

    async def initialize(self):
        """Initialize pub/sub topics and subscriptions."""
        try:
            logger.info("🔧 Initializing Pub/Sub infrastructure...")
            
            # Create subscription for minute aggregates if it doesn't exist
            await self._ensure_subscription_exists()
            
            logger.info("✅ Pub/Sub infrastructure initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Pub/Sub infrastructure: {e}")
            raise

    async def _ensure_subscription_exists(self):
        """Ensure the minute aggregates subscription exists."""
        try:
            # Check if subscription exists
            try:
                self.subscriber.get_subscription(
                    request={"subscription": self.minute_aggregates_subscription_path}
                )
                logger.info(f"✅ Subscription already exists: {self.minute_aggregates_subscription}")
                return
            except exceptions.NotFound:
                pass
            
            # Create subscription
            request = {
                "name": self.minute_aggregates_subscription_path,
                "topic": self.minute_aggregates_topic_path,
                "ack_deadline_seconds": 60,
                "message_retention_duration": {"seconds": 3600}  # 1 hour retention
            }
            
            subscription = self.subscriber.create_subscription(request=request)
            logger.info(f"🆕 Created subscription: {subscription.name}")
            
        except Exception as e:
            logger.error(f"❌ Failed to ensure subscription exists: {e}")
            raise

    async def start(self):
        """Start the Pub/Sub client."""
        logger.info("🚀 Starting Pub/Sub client...")

        self.running = True

        # Don't start subscriber automatically - only start when we have active subscriptions
        logger.info("✅ Pub/Sub client started (subscriber will start when needed)")

    async def stop(self):
        """Stop the Pub/Sub client."""
        logger.info("🛑 Stopping Pub/Sub client...")

        self.running = False
        await self.stop_subscriber()

        # Shutdown executor
        self.executor.shutdown(wait=True)

        logger.info("✅ Pub/Sub client stopped")

    async def start_subscriber(self):
        """Start the subscriber when we have active subscriptions."""
        if self.subscriber_running:
            logger.info("📡 Subscriber already running")
            return

        logger.info("🚀 Starting Pub/Sub subscriber for minute aggregates...")
        self.subscriber_running = True
        self.subscriber_task = asyncio.create_task(self._run_subscriber())

    async def stop_subscriber(self):
        """Stop the subscriber when we have no active subscriptions."""
        if not self.subscriber_running:
            return

        logger.info("🛑 Stopping Pub/Sub subscriber...")
        self.subscriber_running = False

        if self.subscriber_task:
            self.subscriber_task.cancel()
            try:
                await self.subscriber_task
            except asyncio.CancelledError:
                pass
            self.subscriber_task = None

        logger.info("✅ Pub/Sub subscriber stopped")

    def set_message_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set the callback function for received messages."""
        self.message_callback = callback
        logger.info("📞 Message callback set")

    async def publish_subscription_command(self, symbol: str, command: str, client_id: str = None) -> bool:
        """
        Publish a subscription command to the Polygon WebSocket service.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            command: 'subscribe' or 'unsubscribe'
            client_id: Optional client identifier
            
        Returns:
            bool: True if published successfully
        """
        try:
            message_data = {
                'command': command,
                'symbol': symbol,
                'trade_bot_id': f'websocket-data-distribution-{client_id or "service"}',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'source': 'websocket-data-distribution-service'
            }
            
            # Encode message
            message_json = json.dumps(message_data).encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(
                self.subscription_commands_topic_path,
                message_json
            )
            
            # Wait for publish to complete
            message_id = future.result(timeout=10.0)
            
            self.messages_published += 1
            logger.info(f"📤 Published subscription command: {command} {symbol} (ID: {message_id})")
            
            return True
            
        except Exception as e:
            self.publish_errors += 1
            logger.error(f"❌ Failed to publish subscription command: {e}")
            return False

    async def _run_subscriber(self):
        """Run the Pub/Sub subscriber."""
        logger.info("👂 Starting Pub/Sub subscriber for minute aggregates...")

        # Configure flow control
        flow_control = pubsub_v1.types.FlowControl(max_messages=100)

        try:
            # Start pulling messages
            streaming_pull_future = self.subscriber.subscribe(
                self.minute_aggregates_subscription_path,
                callback=self._handle_minute_aggregate_message,
                flow_control=flow_control
            )

            logger.info(f"🔄 Listening for minute aggregates on: {self.minute_aggregates_subscription}")

            # Keep the subscriber running while both running and subscriber_running are True
            while self.running and self.subscriber_running:
                await asyncio.sleep(1)

            # Cancel the subscriber
            streaming_pull_future.cancel()
            logger.info("🛑 Pub/Sub subscriber stopped")

        except asyncio.CancelledError:
            logger.info("🛑 Pub/Sub subscriber cancelled")
        except Exception as e:
            logger.error(f"❌ Error in Pub/Sub subscriber: {e}")
        finally:
            self.subscriber_running = False

    def _handle_minute_aggregate_message(self, message):
        """Handle incoming minute aggregate messages."""
        try:
            # Parse the message
            data = json.loads(message.data.decode('utf-8'))
            
            # Get message attributes
            attributes = dict(message.attributes)
            symbol = attributes.get('symbol', 'UNKNOWN')
            
            logger.info(f"📨 Received minute aggregate for {symbol}")

            # Call the message callback if set
            if self.message_callback:
                logger.info(f"🔧 About to execute callback for {symbol}")

                # Try direct callback execution first (simpler and more reliable)
                try:
                    if asyncio.iscoroutinefunction(self.message_callback):
                        # For async callbacks, run in current thread's event loop if available
                        try:
                            loop = asyncio.get_event_loop()
                            if loop.is_running():
                                # If loop is running, use executor
                                future = self.executor.submit(self._safe_callback, data)
                                future.result(timeout=30)
                            else:
                                # If loop is not running, run directly
                                loop.run_until_complete(self.message_callback(data))
                        except RuntimeError:
                            # No event loop, create one
                            asyncio.run(self.message_callback(data))
                    else:
                        # Sync callback - run directly
                        self.message_callback(data)

                    logger.info(f"✅ Callback executed successfully for {symbol}")
                except Exception as e:
                    logger.error(f"❌ Callback execution failed for {symbol}: {e}")
                    import traceback
                    logger.error(f"❌ Callback traceback: {traceback.format_exc()}")
            else:
                logger.warning(f"⚠️ No message callback set for {symbol}")

            # Acknowledge the message
            logger.info(f"🔧 About to acknowledge message for {symbol}")
            message.ack()
            logger.info(f"✅ Message acknowledged for {symbol}")

            self.messages_received += 1
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse minute aggregate message: {e}")
            message.nack()
            self.receive_errors += 1
        except Exception as e:
            logger.error(f"❌ Error handling minute aggregate message: {e}")
            message.nack()
            self.receive_errors += 1

    def _safe_callback(self, data: Dict[str, Any]):
        """Safely execute the message callback."""
        symbol = data.get('symbol', 'UNKNOWN')
        logger.info(f"🔧 Starting callback execution for {symbol}")

        try:
            if self.message_callback:
                # Convert to async if needed
                if asyncio.iscoroutinefunction(self.message_callback):
                    logger.info(f"🔧 Executing async callback for {symbol}")
                    # Run in event loop
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.message_callback(data))
                    loop.close()
                    logger.info(f"✅ Async callback completed for {symbol}")
                else:
                    logger.info(f"🔧 Executing sync callback for {symbol}")
                    self.message_callback(data)
                    logger.info(f"✅ Sync callback completed for {symbol}")
            else:
                logger.warning(f"⚠️ No callback function set for {symbol}")
        except Exception as e:
            logger.error(f"❌ Error in message callback for {symbol}: {e}")
            import traceback
            logger.error(f"❌ Callback traceback: {traceback.format_exc()}")
            raise  # Re-raise to be caught by the future.result() timeout

    def get_stats(self) -> Dict[str, Any]:
        """Get Pub/Sub statistics."""
        return {
            'messages_published': self.messages_published,
            'messages_received': self.messages_received,
            'publish_errors': self.publish_errors,
            'receive_errors': self.receive_errors,
            'running': self.running
        }

    def log_stats(self):
        """Log Pub/Sub statistics."""
        stats = self.get_stats()
        logger.info(f"📊 Pub/Sub Stats:")
        logger.info(f"   Messages Published: {stats['messages_published']}")
        logger.info(f"   Messages Received: {stats['messages_received']}")
        logger.info(f"   Publish Errors: {stats['publish_errors']}")
        logger.info(f"   Receive Errors: {stats['receive_errors']}")
        logger.info(f"   Running: {stats['running']}")
