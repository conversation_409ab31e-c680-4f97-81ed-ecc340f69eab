<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Disconnected</div>
    <div id="messages"></div>
    <button onclick="connect()">Connect</button>
    <button onclick="disconnect()">Disconnect</button>
    <button onclick="sendTest()">Send Test Message</button>

    <script>
        let ws = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');

        function log(message) {
            console.log(message);
            messagesDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function connect() {
            const url = 'ws://localhost:8765';
            log('🔗 Connecting to: ' + url);
            
            ws = new WebSocket(url);
            
            ws.onopen = function(event) {
                log('✅ Connected successfully');
                statusDiv.textContent = 'Connected';
                statusDiv.style.color = 'green';
            };
            
            ws.onmessage = function(event) {
                log('📨 Received: ' + event.data);
            };
            
            ws.onclose = function(event) {
                log('🔌 Connection closed: ' + event.code + ' ' + event.reason);
                statusDiv.textContent = 'Disconnected';
                statusDiv.style.color = 'red';
            };
            
            ws.onerror = function(event) {
                log('❌ WebSocket error: ' + JSON.stringify(event));
                statusDiv.textContent = 'Error';
                statusDiv.style.color = 'red';
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendTest() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'subscribe',
                    symbol: 'EURUSD',
                    timeframe: '1h'
                };
                ws.send(JSON.stringify(message));
                log('📤 Sent: ' + JSON.stringify(message));
            } else {
                log('❌ WebSocket not connected');
            }
        }
    </script>
</body>
</html>
