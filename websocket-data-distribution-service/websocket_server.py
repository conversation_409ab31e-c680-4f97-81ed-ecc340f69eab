#!/usr/bin/env python3
"""
WebSocket Server for Frontend Connections

Handles WebSocket connections from the frontend, processes subscription requests,
and sends real-time candle data to connected clients.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Set, Any

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from firebase_auth import FirebaseAuthenticator

logger = logging.getLogger(__name__)


class WebSocketConnection:
    """Represents a single WebSocket connection from a frontend client."""

    def __init__(self, websocket, connection_id: str, user_info: Dict[str, Any] = None):
        self.websocket = websocket
        self.connection_id = connection_id
        self.user_info = user_info  # Firebase user information
        self.subscriptions: Set[str] = set()  # Set of subscription keys (symbol_timeframe)
        self.connected_at = datetime.now(timezone.utc)
        self.last_ping = datetime.now(timezone.utc)
        
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """Send a message to the client."""
        try:
            message_json = json.dumps(message)
            await self.websocket.send(message_json)
            return True
        except (ConnectionClosed, WebSocketException) as e:
            logger.warning(f"⚠️ Failed to send message to {self.connection_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error sending message to {self.connection_id}: {e}")
            return False
    
    async def close(self):
        """Close the WebSocket connection."""
        try:
            await self.websocket.close()
        except Exception as e:
            logger.warning(f"⚠️ Error closing connection {self.connection_id}: {e}")


class WebSocketServer:
    """
    WebSocket server that handles frontend connections and manages subscriptions.
    """
    
    def __init__(self, host: str, port: int, subscription_manager, candle_processor, project_id: str):
        self.host = host
        self.port = port
        self.subscription_manager = subscription_manager
        self.candle_processor = candle_processor
        self.firebase_auth = FirebaseAuthenticator(project_id)
        
        # Connection management
        self.connections: Dict[str, WebSocketConnection] = {}
        self.running = False
        
        # Message handlers
        self.message_handlers = {
            'subscribe': self._handle_subscribe,
            'unsubscribe': self._handle_unsubscribe,
            'ping': self._handle_ping,
            'get_status': self._handle_get_status
        }
        
        logger.info(f"🌐 WebSocket server initialized for {host}:{port}")

    async def start(self):
        """Start the WebSocket server."""
        logger.info(f"🚀 Starting WebSocket server on {self.host}:{self.port}")

        self.running = True

        try:
            # Create a simple wrapper function like the minimal server
            async def connection_handler(websocket):
                return await self._handle_connection(websocket)

            # Start the WebSocket server using the exact same approach as the working minimal server
            async with websockets.serve(
                connection_handler,
                self.host,
                self.port,
                ping_interval=20,
                ping_timeout=10
            ):
                logger.info(f"✅ WebSocket server started on {self.host}:{self.port}")
                logger.info(f"🔗 Server listening on: ws://{self.host}:{self.port}")

                # Keep the server running
                while self.running:
                    await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"❌ Failed to start WebSocket server: {e}")
            raise

    async def stop(self):
        """Stop the WebSocket server."""
        logger.info("🛑 Stopping WebSocket server...")
        
        self.running = False
        
        # Close all connections
        for connection in list(self.connections.values()):
            await self._disconnect_client(connection.connection_id)
        
        logger.info("✅ WebSocket server stopped")

    async def _handle_connection_simple(self, websocket):
        """Handle a new WebSocket connection using the simple approach that works."""
        connection_id = str(uuid.uuid4())
        client_ip = websocket.remote_address

        logger.info(f"🔗 New WebSocket connection: {connection_id} from {client_ip}")

        # Create connection object
        connection = WebSocketConnection(websocket, connection_id)
        self.connections[connection_id] = connection

        try:
            # Send welcome message
            await connection.send_message({
                'type': 'connection_established',
                'connection_id': connection_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'message': 'Connected to WebSocket Data Distribution Service'
            })

            # Handle messages
            async for message in websocket:
                await self._handle_message(connection, message)

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 WebSocket connection closed: {connection_id}")
        except Exception as e:
            logger.error(f"❌ Error handling connection {connection_id}: {e}")
        finally:
            # Clean up
            await self._disconnect_client(connection_id)

    async def _handle_connection(self, websocket):
        """Handle a new WebSocket connection with Firebase authentication."""
        connection_id = str(uuid.uuid4())
        client_ip = websocket.remote_address

        logger.info(f"🔗 New WebSocket connection: {connection_id} from {client_ip}")

        # Check for development mode bypass via query parameter
        dev_mode = False
        if hasattr(websocket, 'request') and websocket.request and hasattr(websocket.request, 'path'):
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(websocket.request.path)
            query_params = parse_qs(parsed_url.query)
            dev_mode = query_params.get('dev', [''])[0] == 'local-testing'

        if dev_mode:
            logger.warning(f"🔧 DEV MODE: Bypassing authentication for connection {connection_id}")
            user_info = {
                'uid': f'dev-user-{connection_id[:8]}',
                'email': '<EMAIL>',
                'name': 'Local Development User',
                'dev_mode': True
            }
        else:
            # Authenticate the connection using Firebase
            user_info = await self.firebase_auth.authenticate_websocket(websocket)

            if not user_info:
                logger.warning(f"🚫 Authentication failed for connection {connection_id}")
                await websocket.close(code=4001, reason="Authentication required")
                return

        logger.info(f"✅ Authenticated connection {connection_id} for user: {user_info['uid']}")

        # Create connection object with user info
        connection = WebSocketConnection(websocket, connection_id, user_info)
        self.connections[connection_id] = connection

        try:
            # Send welcome message directly to websocket (like minimal server)
            welcome_msg = {
                'type': 'connection_established',
                'connection_id': connection_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'message': 'Connected to WebSocket Data Distribution Service'
            }
            await websocket.send(json.dumps(welcome_msg))
            logger.info(f"✅ Sent welcome message to {client_ip}")

            # Handle messages from this connection
            async for message in websocket:
                await self._handle_message(connection, message)

        except ConnectionClosed:
            logger.info(f"🔌 WebSocket connection closed: {connection_id}")
        except WebSocketException as e:
            logger.warning(f"⚠️ WebSocket error for {connection_id}: {e}")
        except Exception as e:
            logger.error(f"❌ Unexpected error for connection {connection_id}: {e}")
        finally:
            await self._disconnect_client(connection_id)

    async def _handle_message(self, connection: WebSocketConnection, message: str):
        """Handle a message from a WebSocket client."""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            logger.info(f"📨 Received message from {connection.connection_id}: {message_type}")
            
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](connection, data)
            else:
                await connection.send_message({
                    'type': 'error',
                    'message': f'Unknown message type: {message_type}',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
                
        except json.JSONDecodeError:
            logger.error(f"❌ Invalid JSON from {connection.connection_id}: {message}")
            await connection.send_message({
                'type': 'error',
                'message': 'Invalid JSON format',
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
        except Exception as e:
            logger.error(f"❌ Error handling message from {connection.connection_id}: {e}")
            await connection.send_message({
                'type': 'error',
                'message': 'Internal server error',
                'timestamp': datetime.now(timezone.utc).isoformat()
            })

    async def _handle_subscribe(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """Handle subscription request from frontend."""
        try:
            symbol = data.get('symbol')
            timeframe = data.get('timeframe')
            since_timestamp = data.get('sinceTimestamp')  # Optional timestamp for catch-up

            if not symbol or not timeframe:
                await connection.send_message({
                    'type': 'subscription_error',
                    'message': 'Symbol and timeframe are required',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
                return

            subscription_key = f"{symbol}_{timeframe}"

            logger.info(f"📈 Processing subscription request: {connection.connection_id} -> {subscription_key}")
            if since_timestamp:
                logger.info(f"🔄 Client requesting catch-up data since timestamp: {since_timestamp}")

            # Add to connection subscriptions
            connection.subscriptions.add(subscription_key)

            # Register with subscription manager
            await self.subscription_manager.add_subscription(
                connection_id=connection.connection_id,
                symbol=symbol,
                timeframe=timeframe
            )

            # Start candle processing for this subscription (now handles catch-up)
            await self.candle_processor.start_subscription(symbol, timeframe, since_timestamp)
            
            # Send confirmation
            await connection.send_message({
                'type': 'subscription_confirmed',
                'symbol': symbol,
                'timeframe': timeframe,
                'subscription_key': subscription_key,
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
            
            logger.info(f"✅ Subscription confirmed: {connection.connection_id} -> {subscription_key}")
            
        except Exception as e:
            logger.error(f"❌ Error handling subscription: {e}")
            await connection.send_message({
                'type': 'subscription_error',
                'message': 'Failed to process subscription',
                'timestamp': datetime.now(timezone.utc).isoformat()
            })

    async def _handle_unsubscribe(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """Handle unsubscription request from frontend."""
        try:
            symbol = data.get('symbol')
            timeframe = data.get('timeframe')
            
            if not symbol or not timeframe:
                await connection.send_message({
                    'type': 'unsubscription_error',
                    'message': 'Symbol and timeframe are required',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
                return
            
            subscription_key = f"{symbol}_{timeframe}"
            
            logger.info(f"📉 Processing unsubscription request: {connection.connection_id} -> {subscription_key}")
            
            # Remove from connection subscriptions
            connection.subscriptions.discard(subscription_key)
            
            # Unregister with subscription manager
            removed = await self.subscription_manager.remove_subscription(
                connection_id=connection.connection_id,
                symbol=symbol,
                timeframe=timeframe
            )

            # If subscription was removed and no more subscribers, stop candle processing
            if removed:
                subscription_key = f"{symbol}_{timeframe}"
                remaining_subscribers = self.subscription_manager.subscription_counts.get((symbol, timeframe), 0)

                if remaining_subscribers == 0:
                    logger.info(f"🔕 No more subscribers for {subscription_key} - stopping candle processing")
                    await self.candle_processor.stop_subscription(symbol, timeframe)
                else:
                    logger.info(f"📊 {remaining_subscribers} subscribers remaining for {subscription_key}")

            # Send confirmation
            await connection.send_message({
                'type': 'unsubscription_confirmed',
                'symbol': symbol,
                'timeframe': timeframe,
                'subscription_key': subscription_key,
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
            
            logger.info(f"✅ Unsubscription confirmed: {connection.connection_id} -> {subscription_key}")
            
        except Exception as e:
            logger.error(f"❌ Error handling unsubscription: {e}")
            await connection.send_message({
                'type': 'unsubscription_error',
                'message': 'Failed to process unsubscription',
                'timestamp': datetime.now(timezone.utc).isoformat()
            })

    async def _handle_ping(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """Handle ping message from frontend."""
        connection.last_ping = datetime.now(timezone.utc)
        await connection.send_message({
            'type': 'pong',
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

    async def _handle_get_status(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """Handle status request from frontend."""
        await connection.send_message({
            'type': 'status',
            'connection_id': connection.connection_id,
            'connected_at': connection.connected_at.isoformat(),
            'subscriptions': list(connection.subscriptions),
            'total_connections': len(self.connections),
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

    async def _disconnect_client(self, connection_id: str):
        """Disconnect a client and clean up subscriptions."""
        if connection_id not in self.connections:
            return
        
        connection = self.connections[connection_id]
        
        logger.info(f"🔌 Disconnecting client: {connection_id}")
        
        # Remove all subscriptions for this connection
        for subscription_key in list(connection.subscriptions):
            symbol, timeframe = subscription_key.split('_', 1)
            removed = await self.subscription_manager.remove_subscription(
                connection_id=connection_id,
                symbol=symbol,
                timeframe=timeframe
            )

            # If subscription was removed and no more subscribers, stop candle processing
            if removed:
                remaining_subscribers = self.subscription_manager.subscription_counts.get((symbol, timeframe), 0)

                if remaining_subscribers == 0:
                    logger.info(f"🔕 No more subscribers for {subscription_key} - stopping candle processing")
                    await self.candle_processor.stop_subscription(symbol, timeframe)
        
        # Close the connection
        await connection.close()
        
        # Remove from connections
        del self.connections[connection_id]
        
        logger.info(f"✅ Client disconnected: {connection_id}")

    async def broadcast_candle_data(self, symbol: str, timeframe: str, candle_data: Dict[str, Any]):
        """Broadcast candle data to all subscribed clients."""
        subscription_key = f"{symbol}_{timeframe}"
        
        # Find all connections subscribed to this data
        subscribed_connections = [
            conn for conn in self.connections.values()
            if subscription_key in conn.subscriptions
        ]
        
        if not subscribed_connections:
            return
        
        message = {
            'type': 'candle_data',
            'symbol': symbol,
            'timeframe': timeframe,
            'candle': candle_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"📡 Broadcasting candle data to {len(subscribed_connections)} clients: {subscription_key}")
        
        # Send to all subscribed connections
        for connection in subscribed_connections:
            success = await connection.send_message(message)
            if not success:
                # Connection failed, schedule for cleanup
                asyncio.create_task(self._disconnect_client(connection.connection_id))

    async def broadcast_initial_data(self, symbol: str, timeframe: str, candles: list):
        """Broadcast initial historical data to subscribed clients."""
        subscription_key = f"{symbol}_{timeframe}"

        # Find all connections subscribed to this data
        subscribed_connections = [
            conn for conn in self.connections.values()
            if subscription_key in conn.subscriptions
        ]

        if not subscribed_connections:
            logger.info(f"📡 No clients subscribed to {subscription_key} for initial data")
            return

        message = {
            'type': 'initial_data',
            'symbol': symbol,
            'timeframe': timeframe,
            'candles': candles,
            'count': len(candles),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"📡 Broadcasting initial data to {len(subscribed_connections)} clients: {subscription_key} ({len(candles)} candles)")

        # Send to all subscribed connections
        for connection in subscribed_connections:
            success = await connection.send_message(message)
            if success:
                logger.info(f"✅ Sent initial data to {connection.connection_id}: {len(candles)} candles")
            else:
                logger.error(f"❌ Failed to send initial data to {connection.connection_id}")
                # Connection failed, schedule for cleanup
                asyncio.create_task(self._disconnect_client(connection.connection_id))

    async def broadcast_catch_up_data(self, symbol: str, timeframe: str, candles: list):
        """Broadcast catch-up data to subscribed clients."""
        subscription_key = f"{symbol}_{timeframe}"

        # Find all connections subscribed to this data
        subscribed_connections = [
            conn for conn in self.connections.values()
            if subscription_key in conn.subscriptions
        ]

        if not subscribed_connections:
            logger.debug(f"📡 No subscribers for catch-up data: {subscription_key}")
            return

        # Prepare message
        message = {
            'type': 'catch_up_data',
            'symbol': symbol,
            'timeframe': timeframe,
            'candles': candles,
            'count': len(candles),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"📡 Broadcasting catch-up data to {len(subscribed_connections)} clients: {subscription_key} ({len(candles)} candles)")

        # Send to all subscribed connections
        for connection in subscribed_connections:
            success = await connection.send_message(message)
            if success:
                logger.debug(f"✅ Sent catch-up data to {connection.connection_id}")
            else:
                logger.warning(f"⚠️ Failed to send catch-up data to {connection.connection_id}")
                # Connection failed, schedule for cleanup
                asyncio.create_task(self._disconnect_client(connection.connection_id))
