#!/usr/bin/env python3
"""
Polygon API Client for WebSocket Data Distribution Service

Handles fetching missing 1-minute candles from Polygon REST API
to fill gaps in real-time data streams.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any

import aiohttp
from dateutil.parser import parse as parse_date

logger = logging.getLogger(__name__)


class PolygonAPIClient:
    """
    Client for fetching candle data from Polygon.io REST API.
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.polygon.io/v2/aggs/ticker"
        
        # Rate limiting
        self.rate_limit_delay = 12  # 5 requests per minute = 12 seconds between requests
        self.last_request_time = None
        
        # Statistics
        self.requests_made = 0
        self.requests_failed = 0
        self.candles_fetched = 0
        
        logger.info(f"🔑 Polygon API client initialized")

    async def fetch_missing_candles(self, symbol: str, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """
        Fetch missing 1-minute candles for a specific time range.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            start_time: Start time for candle data
            end_time: End time for candle data
            
        Returns:
            List of candle dictionaries
        """
        try:
            logger.info(f"📡 Fetching missing 1m candles for {symbol}: {start_time} to {end_time}")
            # print api key
            logger.info(f"API key: {self.api_key}")
            # Format symbol for Polygon API
            polygon_symbol = self._format_symbol(symbol)
            
            # Convert to timestamps (milliseconds)
            start_timestamp = int(start_time.timestamp() * 1000)
            end_timestamp = int(end_time.timestamp() * 1000)

            # Format dates for URL (still need date format for URL path)
            start_date = start_time.strftime('%Y-%m-%d')
            end_date = end_time.strftime('%Y-%m-%d')

            # Construct API URL
            url = f"{self.base_url}/{polygon_symbol}/range/1/minute/{start_date}/{end_date}"

            # Parameters with timestamp filtering
            params = {
                'apikey': self.api_key,
                'timestamp.gte': start_timestamp,  # Greater than or equal to start
                'timestamp.lt': end_timestamp,     # Less than end
                'limit': 50000,  # Large limit to get all data
                'sort': 'asc'
            }
            
            # Apply rate limiting
            await self._apply_rate_limit()
            
            # Make API request
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    self.requests_made += 1
                    
                    if response.status == 429:
                        logger.warning("⚠️ Rate limited by Polygon API")
                        await asyncio.sleep(60)  # Wait 1 minute
                        return await self.fetch_missing_candles(symbol, start_time, end_time)
                    
                    if response.status != 200:
                        self.requests_failed += 1
                        error_text = await response.text()
                        logger.error(f"❌ Polygon API error {response.status}: {error_text}")
                        return []
                    
                    data = await response.json()
            
            # Process response
            if data.get('status') != 'OK':
                logger.warning(f"⚠️ Polygon API returned status: {data.get('status')}")
                return []
            
            results = data.get('results', [])
            if not results:
                logger.info(f"📊 No candles found for {symbol} in the specified range")
                return []
            
            # Convert to our format
            candles = []
            for candle in results:
                formatted_candle = {
                    'time': int(candle['t'] / 1000),  # Convert to seconds
                    'open': float(candle['o']),
                    'high': float(candle['h']),
                    'low': float(candle['l']),
                    'close': float(candle['c']),
                    'volume': int(candle['v'])
                }
                candles.append(formatted_candle)
            
            self.candles_fetched += len(candles)
            logger.info(f"✅ Fetched {len(candles)} 1m candles for {symbol}")
            
            return candles
            
        except Exception as e:
            self.requests_failed += 1
            logger.error(f"❌ Error fetching missing candles: {e}")
            return []

    async def fetch_recent_candles(self, symbol: str, count: int = 1000) -> List[Dict[str, Any]]:
        """
        Fetch recent 1-minute candles for initialization.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            count: Number of candles to fetch
            
        Returns:
            List of candle dictionaries
        """
        try:
            # Calculate date range (go back enough days to get the required candles)
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=7)  # Go back 7 days to ensure we get enough data
            
            logger.info(f"📡 Fetching recent {count} 1m candles for {symbol}")
            
            candles = await self.fetch_missing_candles(symbol, start_time, end_time)
            
            # Return the most recent candles up to the requested count
            if len(candles) > count:
                candles = candles[-count:]
            
            logger.info(f"✅ Returning {len(candles)} recent candles for {symbol}")
            return candles
            
        except Exception as e:
            logger.error(f"❌ Error fetching recent candles: {e}")
            return []

    def _format_symbol(self, symbol: str) -> str:
        """
        Format symbol for Polygon API.
        
        Args:
            symbol: Symbol like 'EURUSD' or 'EUR/USD'
            
        Returns:
            Formatted symbol for Polygon API (e.g., 'C:EURUSD')
        """
        # Remove any slashes and convert to uppercase
        clean_symbol = symbol.replace('/', '').upper()
        
        # Add Polygon forex prefix
        return f"C:{clean_symbol}"

    async def _apply_rate_limit(self):
        """Apply rate limiting to API requests."""
        if self.last_request_time is not None:
            time_since_last = (datetime.now(timezone.utc) - self.last_request_time).total_seconds()
            if time_since_last < self.rate_limit_delay:
                wait_time = self.rate_limit_delay - time_since_last
                logger.info(f"⏳ Rate limiting: waiting {wait_time:.1f} seconds")
                await asyncio.sleep(wait_time)
        
        self.last_request_time = datetime.now(timezone.utc)

    def get_stats(self) -> Dict[str, Any]:
        """Get API client statistics."""
        return {
            'requests_made': self.requests_made,
            'requests_failed': self.requests_failed,
            'candles_fetched': self.candles_fetched,
            'success_rate': (self.requests_made - self.requests_failed) / max(self.requests_made, 1) * 100
        }

    def log_stats(self):
        """Log API client statistics."""
        stats = self.get_stats()
        logger.info(f"📊 Polygon API Stats:")
        logger.info(f"   Requests Made: {stats['requests_made']}")
        logger.info(f"   Requests Failed: {stats['requests_failed']}")
        logger.info(f"   Candles Fetched: {stats['candles_fetched']}")
        logger.info(f"   Success Rate: {stats['success_rate']:.1f}%")
