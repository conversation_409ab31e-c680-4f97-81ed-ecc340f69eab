#!/usr/bin/env python3
"""
Local Pub/Sub Setup Script

Sets up the required Pub/Sub topics and subscriptions for local development.
"""

import os
from google.cloud import pubsub_v1
from google.api_core import exceptions

# Set up for local emulator
os.environ['PUBSUB_EMULATOR_HOST'] = 'localhost:8085'
project_id = 'oryntrade'

def setup_pubsub():
    """Set up Pub/Sub topics and subscriptions for local development."""
    
    # Initialize clients
    publisher = pubsub_v1.PublisherClient()
    subscriber = pubsub_v1.SubscriberClient()
    
    # Topic and subscription names
    topics = [
        'polygon.subscription_commands',
        'polygon.minute_aggregates'
    ]
    
    subscriptions = [
        {
            'name': 'polygon-subscription-commands-sub',
            'topic': 'polygon.subscription_commands'
        },
        {
            'name': 'websocket-data-distribution-minute-aggregates-sub',
            'topic': 'polygon.minute_aggregates'
        }
    ]
    
    print("🔧 Setting up Pub/Sub for local development...")
    
    # Create topics
    for topic_name in topics:
        topic_path = publisher.topic_path(project_id, topic_name)
        try:
            publisher.create_topic(request={"name": topic_path})
            print(f"✅ Created topic: {topic_name}")
        except exceptions.AlreadyExists:
            print(f"📋 Topic already exists: {topic_name}")
        except Exception as e:
            print(f"❌ Error creating topic {topic_name}: {e}")
    
    # Create subscriptions
    for sub_info in subscriptions:
        subscription_path = subscriber.subscription_path(project_id, sub_info['name'])
        topic_path = publisher.topic_path(project_id, sub_info['topic'])
        
        try:
            request = {
                "name": subscription_path,
                "topic": topic_path,
                "ack_deadline_seconds": 60
            }
            subscriber.create_subscription(request=request)
            print(f"✅ Created subscription: {sub_info['name']}")
        except exceptions.AlreadyExists:
            print(f"📋 Subscription already exists: {sub_info['name']}")
        except Exception as e:
            print(f"❌ Error creating subscription {sub_info['name']}: {e}")
    
    print("🎉 Local Pub/Sub setup completed!")
    print("\n📡 Topics created:")
    for topic in topics:
        print(f"  • {topic}")
    
    print("\n📥 Subscriptions created:")
    for sub in subscriptions:
        print(f"  • {sub['name']} → {sub['topic']}")

if __name__ == "__main__":
    setup_pubsub()
