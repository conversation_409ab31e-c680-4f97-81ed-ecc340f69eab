#!/usr/bin/env python3
"""
Subscription Manager

Manages subscriptions from multiple frontend clients, tracks active subscriptions
per forex pair and timeframe combination, and handles subscription lifecycle.
"""

import logging
from collections import defaultdict
from datetime import datetime, timezone
from typing import Dict, Set, Optional, List, Tuple

logger = logging.getLogger(__name__)


class SubscriptionManager:
    """
    Manages subscriptions from frontend clients.
    
    Tracks which clients are subscribed to which symbol/timeframe combinations
    and manages the lifecycle of subscriptions.
    """
    
    def __init__(self):
        # Track subscriptions by connection
        # Format: {connection_id: {(symbol, timeframe), ...}}
        self.connection_subscriptions: Dict[str, Set[Tuple[str, str]]] = defaultdict(set)
        
        # Track connections by subscription
        # Format: {(symbol, timeframe): {connection_id, ...}}
        self.subscription_connections: Dict[Tuple[str, str], Set[str]] = defaultdict(set)
        
        # Track subscription counts
        # Format: {(symbol, timeframe): count}
        self.subscription_counts: Dict[Tuple[str, str], int] = defaultdict(int)
        
        # Track subscription timestamps
        # Format: {(symbol, timeframe): datetime}
        self.subscription_timestamps: Dict[Tuple[str, str], datetime] = {}
        
        logger.info("📋 Subscription Manager initialized")

    async def add_subscription(self, connection_id: str, symbol: str, timeframe: str) -> bool:
        """
        Add a subscription for a connection.
        
        Args:
            connection_id: Unique identifier for the WebSocket connection
            symbol: Forex pair symbol (e.g., 'EURUSD')
            timeframe: Timeframe (e.g., '1h', '5m')
            
        Returns:
            bool: True if this is a new subscription, False if already existed
        """
        subscription_key = (symbol, timeframe)
        
        # Check if this connection is already subscribed
        if subscription_key in self.connection_subscriptions[connection_id]:
            logger.info(f"📋 Connection {connection_id} already subscribed to {symbol} {timeframe}")
            return False
        
        # Add subscription
        self.connection_subscriptions[connection_id].add(subscription_key)
        self.subscription_connections[subscription_key].add(connection_id)
        self.subscription_counts[subscription_key] += 1
        
        # Track timestamp for new subscriptions
        if subscription_key not in self.subscription_timestamps:
            self.subscription_timestamps[subscription_key] = datetime.now(timezone.utc)
        
        logger.info(f"📈 Added subscription: {connection_id} -> {symbol} {timeframe} "
                   f"(total subscribers: {self.subscription_counts[subscription_key]})")
        
        return True

    async def remove_subscription(self, connection_id: str, symbol: str, timeframe: str) -> bool:
        """
        Remove a subscription for a connection.
        
        Args:
            connection_id: Unique identifier for the WebSocket connection
            symbol: Forex pair symbol (e.g., 'EURUSD')
            timeframe: Timeframe (e.g., '1h', '5m')
            
        Returns:
            bool: True if subscription was removed, False if it didn't exist
        """
        subscription_key = (symbol, timeframe)
        
        # Check if this connection is subscribed
        if subscription_key not in self.connection_subscriptions[connection_id]:
            logger.info(f"📋 Connection {connection_id} not subscribed to {symbol} {timeframe}")
            return False
        
        # Remove subscription
        self.connection_subscriptions[connection_id].discard(subscription_key)
        self.subscription_connections[subscription_key].discard(connection_id)
        self.subscription_counts[subscription_key] -= 1
        
        # Clean up empty entries
        if self.subscription_counts[subscription_key] <= 0:
            del self.subscription_counts[subscription_key]
            del self.subscription_connections[subscription_key]
            if subscription_key in self.subscription_timestamps:
                del self.subscription_timestamps[subscription_key]
        
        # Clean up empty connection entries
        if not self.connection_subscriptions[connection_id]:
            del self.connection_subscriptions[connection_id]
        
        logger.info(f"📉 Removed subscription: {connection_id} -> {symbol} {timeframe} "
                   f"(remaining subscribers: {self.subscription_counts.get(subscription_key, 0)})")
        
        return True

    async def remove_all_subscriptions(self, connection_id: str) -> List[Tuple[str, str]]:
        """
        Remove all subscriptions for a connection.
        
        Args:
            connection_id: Unique identifier for the WebSocket connection
            
        Returns:
            List of (symbol, timeframe) tuples that were removed
        """
        if connection_id not in self.connection_subscriptions:
            return []
        
        removed_subscriptions = []
        
        # Get all subscriptions for this connection
        subscriptions = list(self.connection_subscriptions[connection_id])
        
        # Remove each subscription
        for symbol, timeframe in subscriptions:
            await self.remove_subscription(connection_id, symbol, timeframe)
            removed_subscriptions.append((symbol, timeframe))
        
        logger.info(f"🧹 Removed all subscriptions for connection {connection_id}: {len(removed_subscriptions)} subscriptions")
        
        return removed_subscriptions

    def get_active_subscriptions(self) -> List[Tuple[str, str]]:
        """
        Get all active subscriptions.
        
        Returns:
            List of (symbol, timeframe) tuples that have active subscribers
        """
        return list(self.subscription_counts.keys())

    def get_subscription_count(self, symbol: str, timeframe: str) -> int:
        """
        Get the number of subscribers for a specific subscription.
        
        Args:
            symbol: Forex pair symbol
            timeframe: Timeframe
            
        Returns:
            Number of active subscribers
        """
        subscription_key = (symbol, timeframe)
        return self.subscription_counts.get(subscription_key, 0)

    def get_subscribers(self, symbol: str, timeframe: str) -> Set[str]:
        """
        Get all connection IDs subscribed to a specific symbol/timeframe.
        
        Args:
            symbol: Forex pair symbol
            timeframe: Timeframe
            
        Returns:
            Set of connection IDs
        """
        subscription_key = (symbol, timeframe)
        return self.subscription_connections.get(subscription_key, set()).copy()

    def get_connection_subscriptions(self, connection_id: str) -> Set[Tuple[str, str]]:
        """
        Get all subscriptions for a specific connection.
        
        Args:
            connection_id: Unique identifier for the WebSocket connection
            
        Returns:
            Set of (symbol, timeframe) tuples
        """
        return self.connection_subscriptions.get(connection_id, set()).copy()

    def is_subscribed(self, connection_id: str, symbol: str, timeframe: str) -> bool:
        """
        Check if a connection is subscribed to a specific symbol/timeframe.
        
        Args:
            connection_id: Unique identifier for the WebSocket connection
            symbol: Forex pair symbol
            timeframe: Timeframe
            
        Returns:
            True if subscribed, False otherwise
        """
        subscription_key = (symbol, timeframe)
        return subscription_key in self.connection_subscriptions.get(connection_id, set())

    def has_subscribers(self, symbol: str, timeframe: str) -> bool:
        """
        Check if there are any subscribers for a specific symbol/timeframe.
        
        Args:
            symbol: Forex pair symbol
            timeframe: Timeframe
            
        Returns:
            True if there are subscribers, False otherwise
        """
        subscription_key = (symbol, timeframe)
        return self.subscription_counts.get(subscription_key, 0) > 0

    def get_subscription_timestamp(self, symbol: str, timeframe: str) -> Optional[datetime]:
        """
        Get the timestamp when a subscription was first created.
        
        Args:
            symbol: Forex pair symbol
            timeframe: Timeframe
            
        Returns:
            Datetime when subscription was first created, or None if not found
        """
        subscription_key = (symbol, timeframe)
        return self.subscription_timestamps.get(subscription_key)

    def get_stats(self) -> Dict[str, any]:
        """
        Get subscription statistics.
        
        Returns:
            Dictionary with subscription statistics
        """
        total_connections = len(self.connection_subscriptions)
        total_subscriptions = len(self.subscription_counts)
        total_subscriber_count = sum(self.subscription_counts.values())
        
        # Get most popular subscriptions
        popular_subscriptions = sorted(
            self.subscription_counts.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        return {
            'total_connections': total_connections,
            'total_unique_subscriptions': total_subscriptions,
            'total_subscriber_count': total_subscriber_count,
            'popular_subscriptions': [
                {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'subscriber_count': count
                }
                for (symbol, timeframe), count in popular_subscriptions
            ],
            'active_subscriptions': [
                {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'subscriber_count': count,
                    'created_at': self.subscription_timestamps.get((symbol, timeframe), datetime.now(timezone.utc)).isoformat()
                }
                for (symbol, timeframe), count in self.subscription_counts.items()
            ]
        }

    def log_stats(self):
        """Log current subscription statistics."""
        stats = self.get_stats()
        
        logger.info(f"📊 Subscription Stats:")
        logger.info(f"   Total Connections: {stats['total_connections']}")
        logger.info(f"   Unique Subscriptions: {stats['total_unique_subscriptions']}")
        logger.info(f"   Total Subscribers: {stats['total_subscriber_count']}")
        
        if stats['popular_subscriptions']:
            logger.info(f"   Popular Subscriptions:")
            for sub in stats['popular_subscriptions']:
                logger.info(f"     {sub['symbol']} {sub['timeframe']}: {sub['subscriber_count']} subscribers")
