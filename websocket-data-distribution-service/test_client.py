#!/usr/bin/env python3
"""
Test client for WebSocket Data Distribution Service

Simple test script to verify the WebSocket service is working correctly.
"""

import asyncio
import json
import logging
import websockets
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestClient:
    """Test client for WebSocket Data Distribution Service."""
    
    def __init__(self, url="ws://localhost:8765"):
        self.url = url
        self.websocket = None
        self.running = False
        
    async def connect(self):
        """Connect to the WebSocket service."""
        try:
            logger.info(f"🔗 Connecting to {self.url}")
            self.websocket = await websockets.connect(self.url)
            logger.info("✅ Connected successfully")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect: {e}")
            return False
    
    async def send_message(self, message):
        """Send a message to the server."""
        if self.websocket:
            await self.websocket.send(json.dumps(message))
            logger.info(f"📤 Sent: {message}")
    
    async def listen_for_messages(self):
        """Listen for messages from the server."""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                logger.info(f"📨 Received: {data}")
                
                # Handle different message types
                if data.get('type') == 'candle_data':
                    logger.info(f"🕯️ New candle for {data['symbol']} {data['timeframe']}: {data['candle']}")
                elif data.get('type') == 'subscription_confirmed':
                    logger.info(f"✅ Subscription confirmed for {data['symbol']} {data['timeframe']}")
                elif data.get('type') == 'connection_established':
                    logger.info(f"🎉 Connection established: {data['connection_id']}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("🔌 Connection closed")
        except Exception as e:
            logger.error(f"❌ Error listening for messages: {e}")
    
    async def test_subscription(self, symbol="EURUSD", timeframe="1h"):
        """Test subscribing to a symbol/timeframe."""
        # Subscribe
        subscribe_message = {
            "type": "subscribe",
            "symbol": symbol,
            "timeframe": timeframe
        }
        await self.send_message(subscribe_message)
        
        # Wait a bit for data
        await asyncio.sleep(30)
        
        # Get status
        status_message = {"type": "get_status"}
        await self.send_message(status_message)
        
        # Wait a bit more
        await asyncio.sleep(10)
        
        # Unsubscribe
        unsubscribe_message = {
            "type": "unsubscribe",
            "symbol": symbol,
            "timeframe": timeframe
        }
        await self.send_message(unsubscribe_message)
    
    async def run_test(self):
        """Run the complete test."""
        if not await self.connect():
            return
        
        self.running = True
        
        # Start listening for messages
        listen_task = asyncio.create_task(self.listen_for_messages())
        
        # Wait a moment for connection to stabilize
        await asyncio.sleep(2)
        
        # Test subscription
        await self.test_subscription()
        
        # Wait a bit more to see any final messages
        await asyncio.sleep(5)
        
        # Close connection
        if self.websocket:
            await self.websocket.close()
        
        # Cancel listening task
        listen_task.cancel()
        
        logger.info("🏁 Test completed")


async def main():
    """Main test function."""
    logger.info("🚀 Starting WebSocket Data Distribution Service test")
    
    client = TestClient()
    await client.run_test()


if __name__ == "__main__":
    asyncio.run(main())
