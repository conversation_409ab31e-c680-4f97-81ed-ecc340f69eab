"""
Firebase Authentication module for WebSocket Data Distribution Service.
Handles Firebase ID token validation and user authentication.
"""

import os
import json
import logging
from typing import Optional, Dict, Any
from urllib.parse import parse_qs, urlparse

import firebase_admin
from firebase_admin import auth, credentials

logger = logging.getLogger(__name__)

class FirebaseAuthenticator:
    """Handles Firebase authentication for WebSocket connections."""
    
    def __init__(self, project_id: str):
        """
        Initialize Firebase authenticator.
        
        Args:
            project_id: Google Cloud project ID
        """
        self.project_id = project_id
        self.app = None
        self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK."""
        try:
            # Check if Firebase app is already initialized
            if not firebase_admin._apps:
                # In Cloud Run, use default credentials
                if os.getenv('GOOGLE_CLOUD_PROJECT'):
                    logger.info("🔥 Initializing Firebase with default credentials")
                    cred = credentials.ApplicationDefault()
                    self.app = firebase_admin.initialize_app(cred, {
                        'projectId': self.project_id
                    })
                else:
                    # For local development, use service account key
                    service_account_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
                    if service_account_path and os.path.exists(service_account_path):
                        logger.info("🔥 Initializing Firebase with service account")
                        cred = credentials.Certificate(service_account_path)
                        self.app = firebase_admin.initialize_app(cred)
                    else:
                        logger.warning("⚠️ No Firebase credentials found, using default")
                        self.app = firebase_admin.initialize_app()
            else:
                self.app = firebase_admin.get_app()
                
            logger.info("✅ Firebase Admin SDK initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Firebase: {e}")
            raise
    
    def extract_token_from_websocket(self, websocket) -> Optional[str]:
        """
        Extract Firebase ID token from WebSocket connection.
        
        Supports multiple methods:
        1. Query parameter: ?token=<firebase_id_token>
        2. Authorization header: Authorization: Bearer <firebase_id_token>
        3. Sec-WebSocket-Protocol header (for some clients)
        
        Args:
            websocket: WebSocket connection object
            
        Returns:
            Firebase ID token if found, None otherwise
        """
        try:
            # Method 1: Check query parameters from request object
            if hasattr(websocket, 'request') and websocket.request:
                request = websocket.request

                # Get the path/URI from request
                if hasattr(request, 'path'):
                    websocket_path = request.path
                    parsed_url = urlparse(websocket_path)
                    query_params = parse_qs(parsed_url.query)
                    if 'token' in query_params:
                        token = query_params['token'][0]
                        logger.debug("🔑 Token found in query parameters")
                        return token
            
            # Method 2: Check Authorization header
            if hasattr(websocket, 'request_headers'):
                auth_header = websocket.request_headers.get('Authorization')
                if auth_header and auth_header.startswith('Bearer '):
                    token = auth_header[7:]  # Remove 'Bearer ' prefix
                    logger.debug("🔑 Token found in Authorization header")
                    return token
            
            # Method 3: Check custom header (for some WebSocket clients)
            if hasattr(websocket, 'request_headers'):
                token_header = websocket.request_headers.get('X-Firebase-Token')
                if token_header:
                    logger.debug("🔑 Token found in X-Firebase-Token header")
                    return token_header
            
            logger.debug("🔍 No Firebase token found in WebSocket request")
            return None
            
        except Exception as e:
            logger.error(f"❌ Error extracting token from WebSocket: {e}")
            return None
    
    async def verify_token(self, id_token: str) -> Optional[Dict[str, Any]]:
        """
        Verify Firebase ID token and return user information.

        Args:
            id_token: Firebase ID token to verify

        Returns:
            User information dict if token is valid, None otherwise
        """
        try:
            # Verify the ID token
            decoded_token = auth.verify_id_token(id_token)

            user_info = {
                'uid': decoded_token['uid'],
                'email': decoded_token.get('email'),
                'email_verified': decoded_token.get('email_verified', False),
                'name': decoded_token.get('name'),
                'picture': decoded_token.get('picture'),
                'firebase': {
                    'sign_in_provider': decoded_token.get('firebase', {}).get('sign_in_provider'),
                    'auth_time': decoded_token.get('auth_time'),
                    'exp': decoded_token.get('exp'),
                    'iat': decoded_token.get('iat')
                }
            }

            logger.info(f"✅ Token verified for user: {user_info['uid']} ({user_info.get('email', 'no email')})")
            return user_info

        except auth.InvalidIdTokenError as e:
            error_msg = str(e)
            logger.warning(f"🚫 Invalid Firebase ID token: {error_msg}")

            # Check if this looks like a Firebase emulator token (development mode)
            if 'no "kid" claim' in error_msg or 'algorithm "none"' in error_msg:
                logger.warning("🔧 Detected Firebase emulator token - enabling development mode")
                return self._create_dev_user_from_emulator_token(id_token)

            return None
        except auth.ExpiredIdTokenError as e:
            logger.warning(f"⏰ Expired Firebase ID token: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error verifying Firebase token: {e}")
            return None

    def _create_dev_user_from_emulator_token(self, id_token: str) -> Dict[str, Any]:
        """
        Create a development user from Firebase emulator token.

        Args:
            id_token: Firebase emulator token

        Returns:
            Development user information
        """
        try:
            # Parse the emulator token (it's not signed, so we can decode it directly)
            import base64
            import json

            # Split the JWT token
            parts = id_token.split('.')
            if len(parts) >= 2:
                # Decode the payload (add padding if needed)
                payload = parts[1]
                payload += '=' * (4 - len(payload) % 4)  # Add padding
                decoded_payload = base64.urlsafe_b64decode(payload)
                token_data = json.loads(decoded_payload)

                user_info = {
                    'uid': token_data.get('user_id', f"dev-{token_data.get('sub', 'unknown')}"),
                    'email': token_data.get('email', '<EMAIL>'),
                    'email_verified': token_data.get('email_verified', False),
                    'name': token_data.get('name', 'Development User'),
                    'picture': token_data.get('picture'),
                    'dev_mode': True,
                    'firebase': {
                        'sign_in_provider': token_data.get('firebase', {}).get('sign_in_provider', 'emulator'),
                        'auth_time': token_data.get('auth_time'),
                        'exp': token_data.get('exp'),
                        'iat': token_data.get('iat')
                    }
                }

                logger.info(f"🔧 Created dev user from emulator token: {user_info['uid']} ({user_info['email']})")
                return user_info

        except Exception as e:
            logger.error(f"❌ Error parsing emulator token: {e}")

        # Fallback to generic dev user
        return {
            'uid': 'dev-emulator-user',
            'email': '<EMAIL>',
            'name': 'Development User',
            'dev_mode': True
        }
    
    async def authenticate_websocket(self, websocket) -> Optional[Dict[str, Any]]:
        """
        Authenticate WebSocket connection using Firebase ID token.
        
        Args:
            websocket: WebSocket connection object
            
        Returns:
            User information if authenticated, None otherwise
        """
        try:
            # Extract token from WebSocket request
            id_token = self.extract_token_from_websocket(websocket)
            
            if not id_token:
                logger.warning("🚫 No Firebase token provided in WebSocket connection")
                return None
            
            # Verify the token
            user_info = await self.verify_token(id_token)
            
            if user_info:
                logger.info(f"🔐 WebSocket authenticated for user: {user_info['uid']}")
                return user_info
            else:
                logger.warning("🚫 WebSocket authentication failed")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error authenticating WebSocket: {e}")
            return None
