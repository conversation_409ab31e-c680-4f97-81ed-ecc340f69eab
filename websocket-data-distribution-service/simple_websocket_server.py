#!/usr/bin/env python3
"""
Simple WebSocket server that works with the existing architecture.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Set

import websockets

logger = logging.getLogger(__name__)


class SimpleWebSocketServer:
    """Simple WebSocket server for testing."""
    
    def __init__(self, host="0.0.0.0", port=8767):
        self.host = host
        self.port = port
        self.connections = {}
        self.running = False
        
    async def start(self):
        """Start the WebSocket server."""
        logger.info(f"🚀 Starting simple WebSocket server on {self.host}:{self.port}")
        
        self.running = True
        
        try:
            # Start the WebSocket server
            async with websockets.serve(
                self.handle_connection,
                self.host,
                self.port,
                ping_interval=30,
                ping_timeout=10
            ):
                logger.info(f"✅ WebSocket server started on {self.host}:{self.port}")
                
                # Keep the server running
                while self.running:
                    await asyncio.sleep(1)
                    
        except Exception as e:
            logger.error(f"❌ Failed to start WebSocket server: {e}")
            raise

    async def handle_connection(self, websocket):
        """Handle a new WebSocket connection."""
        connection_id = str(uuid.uuid4())
        client_ip = websocket.remote_address
        
        logger.info(f"🔗 New WebSocket connection: {connection_id} from {client_ip}")
        
        # Store connection
        self.connections[connection_id] = {
            'websocket': websocket,
            'connected_at': datetime.now(timezone.utc),
            'subscriptions': set()
        }
        
        try:
            # Send welcome message
            welcome_msg = {
                'type': 'connection_established',
                'connection_id': connection_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'message': 'Connected to WebSocket Data Distribution Service'
            }
            await websocket.send(json.dumps(welcome_msg))
            
            # Handle messages
            async for message in websocket:
                await self.handle_message(connection_id, message)
                
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 WebSocket connection closed: {connection_id}")
        except Exception as e:
            logger.error(f"❌ Error handling connection {connection_id}: {e}")
        finally:
            # Clean up
            if connection_id in self.connections:
                del self.connections[connection_id]
            logger.info(f"✅ Cleaned up connection: {connection_id}")

    async def handle_message(self, connection_id: str, message: str):
        """Handle a message from a WebSocket client."""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            logger.info(f"📨 Received message from {connection_id}: {message_type}")
            
            connection = self.connections.get(connection_id)
            if not connection:
                return
            
            websocket = connection['websocket']
            
            if message_type == 'subscribe':
                await self.handle_subscribe(connection_id, data, websocket)
            elif message_type == 'unsubscribe':
                await self.handle_unsubscribe(connection_id, data, websocket)
            elif message_type == 'ping':
                await self.handle_ping(connection_id, websocket)
            elif message_type == 'get_status':
                await self.handle_get_status(connection_id, websocket)
            else:
                await self.send_error(websocket, f'Unknown message type: {message_type}')
                
        except json.JSONDecodeError:
            logger.error(f"❌ Invalid JSON from {connection_id}: {message}")
            await self.send_error(connection['websocket'], 'Invalid JSON format')
        except Exception as e:
            logger.error(f"❌ Error handling message from {connection_id}: {e}")

    async def handle_subscribe(self, connection_id: str, data: Dict, websocket):
        """Handle subscription request."""
        symbol = data.get('symbol')
        timeframe = data.get('timeframe')
        
        if not symbol or not timeframe:
            await self.send_error(websocket, 'Symbol and timeframe are required')
            return
        
        subscription_key = f"{symbol}_{timeframe}"
        
        # Add to connection subscriptions
        self.connections[connection_id]['subscriptions'].add(subscription_key)
        
        # Send confirmation
        response = {
            'type': 'subscription_confirmed',
            'symbol': symbol,
            'timeframe': timeframe,
            'subscription_key': subscription_key,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        await websocket.send(json.dumps(response))
        
        logger.info(f"✅ Subscription confirmed: {connection_id} -> {subscription_key}")

    async def handle_unsubscribe(self, connection_id: str, data: Dict, websocket):
        """Handle unsubscription request."""
        symbol = data.get('symbol')
        timeframe = data.get('timeframe')
        
        if not symbol or not timeframe:
            await self.send_error(websocket, 'Symbol and timeframe are required')
            return
        
        subscription_key = f"{symbol}_{timeframe}"
        
        # Remove from connection subscriptions
        self.connections[connection_id]['subscriptions'].discard(subscription_key)
        
        # Send confirmation
        response = {
            'type': 'unsubscription_confirmed',
            'symbol': symbol,
            'timeframe': timeframe,
            'subscription_key': subscription_key,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        await websocket.send(json.dumps(response))
        
        logger.info(f"✅ Unsubscription confirmed: {connection_id} -> {subscription_key}")

    async def handle_ping(self, connection_id: str, websocket):
        """Handle ping message."""
        response = {
            'type': 'pong',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        await websocket.send(json.dumps(response))

    async def handle_get_status(self, connection_id: str, websocket):
        """Handle status request."""
        connection = self.connections[connection_id]
        response = {
            'type': 'status',
            'connection_id': connection_id,
            'connected_at': connection['connected_at'].isoformat(),
            'subscriptions': list(connection['subscriptions']),
            'total_connections': len(self.connections),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        await websocket.send(json.dumps(response))

    async def send_error(self, websocket, message: str):
        """Send error message to client."""
        error_msg = {
            'type': 'error',
            'message': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        try:
            await websocket.send(json.dumps(error_msg))
        except Exception as e:
            logger.error(f"❌ Failed to send error message: {e}")

    async def broadcast_candle_data(self, symbol: str, timeframe: str, candle_data: Dict):
        """Broadcast candle data to subscribed clients."""
        subscription_key = f"{symbol}_{timeframe}"
        
        message = {
            'type': 'candle_data',
            'symbol': symbol,
            'timeframe': timeframe,
            'candle': candle_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        # Find subscribed connections
        subscribed_connections = []
        for conn_id, conn_data in self.connections.items():
            if subscription_key in conn_data['subscriptions']:
                subscribed_connections.append((conn_id, conn_data['websocket']))
        
        if not subscribed_connections:
            return
        
        logger.info(f"📡 Broadcasting candle data to {len(subscribed_connections)} clients: {subscription_key}")
        
        # Send to all subscribed connections
        for conn_id, websocket in subscribed_connections:
            try:
                await websocket.send(json.dumps(message))
            except Exception as e:
                logger.error(f"❌ Failed to send candle data to {conn_id}: {e}")


async def main():
    """Test the simple WebSocket server."""
    logging.basicConfig(level=logging.INFO)
    
    server = SimpleWebSocketServer()
    await server.start()


if __name__ == "__main__":
    asyncio.run(main())
