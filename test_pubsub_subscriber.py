#!/usr/bin/env python3
"""
Test script to subscribe to Polygon minute aggregates via Pub/Sub.
This will help us determine if the issue is with Pub/Sub delivery or trade-bot specific.
"""

import json
import time
from datetime import datetime
from google.cloud import pubsub_v1
from google.api_core import exceptions

class PubSubTestSubscriber:
    def __init__(self, project_id="oryntrade"):
        self.project_id = project_id
        self.subscriber = pubsub_v1.SubscriberClient()
        
        # Pub/Sub configuration
        self.minute_aggregates_topic = "polygon.minute_aggregates"
        self.test_subscription = "polygon-minute-aggregates-test"
        
        # Message tracking
        self.messages_received = 0
        self.last_message_time = None
        self.message_log = []
        
        print(f"🔧 Initialized test subscriber for project: {project_id}")
        
    def setup_subscription(self):
        """Create the test subscription if it doesn't exist."""
        try:
            topic_path = self.subscriber.topic_path(self.project_id, self.minute_aggregates_topic)
            subscription_path = self.subscriber.subscription_path(self.project_id, self.test_subscription)
            
            # Try to create subscription
            try:
                subscription = self.subscriber.create_subscription(
                    request={
                        "name": subscription_path,
                        "topic": topic_path,
                        "ack_deadline_seconds": 60,
                    }
                )
                print(f"✅ Created test subscription: {subscription.name}")
            except exceptions.AlreadyExists:
                print(f"ℹ️ Test subscription already exists: {subscription_path}")
                
        except Exception as e:
            print(f"❌ Error setting up subscription: {e}")
            return False
            
        return True
    
    def message_callback(self, message):
        """Handle incoming minute aggregate messages."""
        try:
            self.messages_received += 1
            current_time = datetime.now()
            
            # Parse message data
            data = json.loads(message.data.decode('utf-8'))
            symbol = data.get('symbol', 'Unknown')
            close_price = data.get('close', 'N/A')
            timestamp = data.get('timestamp', 'N/A')
            
            # Log message details
            log_entry = {
                'message_id': message.message_id,
                'received_at': current_time.isoformat(),
                'symbol': symbol,
                'close': close_price,
                'timestamp': timestamp,
                'sequence': self.messages_received
            }
            self.message_log.append(log_entry)
            
            # Print message info
            print(f"📊 Message #{self.messages_received}: {symbol} - {close_price}")
            print(f"   🕐 Timestamp: {timestamp}")
            print(f"   📨 Message ID: {message.message_id}")
            print(f"   ⏰ Received at: {current_time.strftime('%H:%M:%S')}")
            
            # Check time gap from last message
            if self.last_message_time:
                time_gap = (current_time - self.last_message_time).total_seconds()
                if time_gap > 70:  # More than 70 seconds gap (should be ~60s for minute data)
                    print(f"   ⚠️ Large time gap: {time_gap:.1f} seconds since last message")
                else:
                    print(f"   ✅ Time gap: {time_gap:.1f} seconds")
            
            self.last_message_time = current_time
            
            # Acknowledge the message
            message.ack()
            print(f"   ✅ Message acknowledged\n")
            
        except Exception as e:
            print(f"❌ Error processing message: {e}")
            message.nack()
    
    def start_listening(self):
        """Start listening for messages."""
        if not self.setup_subscription():
            return
            
        subscription_path = self.subscriber.subscription_path(self.project_id, self.test_subscription)
        
        print(f"👂 Starting to listen for messages on: {subscription_path}")
        print(f"🔍 Looking for EUR/USD minute aggregates...")
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Configure flow control
        flow_control = pubsub_v1.types.FlowControl(max_messages=100)
        
        try:
            # Start streaming pull with callback
            streaming_pull_future = self.subscriber.subscribe(
                subscription_path,
                callback=self.message_callback,
                flow_control=flow_control,
            )

            print(f"🔄 Listening for messages... (Press Ctrl+C to stop)")

            # Keep the main thread running
            try:
                streaming_pull_future.result()
            except KeyboardInterrupt:
                streaming_pull_future.cancel()
                print(f"\n🛑 Stopping subscriber...")

        except Exception as e:
            print(f"❌ Error during message pulling: {e}")
        
        finally:
            self.print_summary()
    
    def print_summary(self):
        """Print summary of received messages."""
        print("\n" + "=" * 60)
        print(f"📊 SUMMARY")
        print(f"Total messages received: {self.messages_received}")
        
        if self.message_log:
            print(f"First message: {self.message_log[0]['received_at']}")
            print(f"Last message: {self.message_log[-1]['received_at']}")
            
            # Check for gaps in sequence
            print(f"\n🔍 Message sequence analysis:")
            for i, msg in enumerate(self.message_log):
                print(f"  {i+1:2d}. {msg['timestamp']} - {msg['close']} (ID: {msg['message_id'][:8]}...)")
        
        print("=" * 60)

def main():
    """Main function to run the test subscriber."""
    print("🧪 Pub/Sub Test Subscriber for Polygon Minute Aggregates")
    print("This will help identify if messages are being lost in Pub/Sub delivery")
    print()
    
    # Initialize subscriber
    subscriber = PubSubTestSubscriber()
    
    # Start listening
    try:
        subscriber.start_listening()
    except Exception as e:
        print(f"❌ Fatal error: {e}")

if __name__ == "__main__":
    main()
