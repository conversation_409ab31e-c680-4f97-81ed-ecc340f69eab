#!/bin/bash

# Deploy test script for Polygon WebSocket service
set -e

echo "🚀 Deploying Polygon WebSocket Service (Test Version)"

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="polygon-websocket-ingestion-test"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

# Build the Docker image
echo "🔨 Building Docker image..."
docker build -t ${IMAGE_NAME} .

# Push to Google Container Registry
echo "📤 Pushing to Container Registry..."
docker push ${IMAGE_NAME}

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --memory 1Gi \
    --cpu 1 \
    --timeout 3600 \
    --concurrency 1000 \
    --max-instances 10 \
    --set-env-vars "GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
    --set-env-vars "LOG_LEVEL=DEBUG" \
    --project ${PROJECT_ID}

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --platform managed --region ${REGION} --format 'value(status.url)' --project ${PROJECT_ID})

echo "✅ Deployment complete!"
echo "🔗 Service URL: ${SERVICE_URL}"
echo "🏥 Health check: ${SERVICE_URL}/health"
echo "📊 Metrics: ${SERVICE_URL}/metrics"

# Test the health endpoint
echo "🏥 Testing health endpoint..."
curl -s "${SERVICE_URL}/health" | jq '.'

echo "🎉 Test deployment ready!"
