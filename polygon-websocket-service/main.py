#!/usr/bin/env python3
"""
Polygon WebSocket Ingestion Service

This service connects to Polygon.io WebSocket API and ingests real-time forex data.
It uses Google Cloud Pub/Sub for communication with trade-bots:

1. Subscribes to 'polygon.subscription_commands' for subscription management
2. Publishes to 'polygon.minute_aggregates' for forex data distribution

Architecture:
Trade-Bot → polygon.subscription_commands → WebSocket Service → Polygon.io
                                                    ↓
Trade-Bot ← polygon.minute_aggregates ← WebSocket Service ← Polygon.io
"""

import asyncio
import json
import logging
import os
import signal
import sys
from datetime import datetime, timezone
from typing import Dict, Set

from google.cloud import pubsub_v1
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from pubsub_manager import PubSubManager
from subscription_handler import SubscriptionCommandHandler
from polygon_client_v2 import PolygonWebSocketClientV2
from data_publisher import DataPublisherManager
from monitoring import HealthMonitor, start_health_monitoring
from health_server import start_health_server

# Configure logging
log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
logging.basicConfig(
    level=getattr(logging, log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Enable debug logging for polygon client specifically
polygon_logger = logging.getLogger('polygon_client_v2')
polygon_logger.setLevel(logging.DEBUG)
logger = logging.getLogger(__name__)

class PolygonWebSocketIngestionService:
    """
    Main service class that handles Polygon.io WebSocket ingestion
    and Pub/Sub communication with trade-bots.
    """
    
    def __init__(self):
        # Configuration
        self.polygon_api_key = os.getenv('POLYGON_API_KEY')
        self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')

        # Validate required configuration
        if not self.polygon_api_key:
            logger.warning("⚠️ POLYGON_API_KEY not set - service will start but won't connect to Polygon")

        # Pub/Sub configuration
        self.subscription_commands_topic = 'polygon.subscription_commands'
        self.minute_aggregates_topic = 'polygon.minute_aggregates'
        self.subscription_commands_subscription = 'polygon-subscription-commands-sub'

        # Polygon WebSocket client V2 (official client)
        self.polygon_client = PolygonWebSocketClientV2(
            api_key=self.polygon_api_key or "placeholder",
            on_minute_aggregate_callback=self._handle_minute_aggregate_data,
            on_connection_status_callback=self._handle_connection_status
        )
        
        # State management
        self.active_subscriptions: Set[str] = set()  # Set of forex pairs
        self.running = False

        # Store reference to main event loop for thread-safe async calls
        self.main_loop = None

        # Pub/Sub clients
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        self.pubsub_manager = PubSubManager(self.project_id)

        # Topic paths
        self.subscription_commands_topic_path = self.publisher.topic_path(
            self.project_id, self.subscription_commands_topic
        )
        self.minute_aggregates_topic_path = self.publisher.topic_path(
            self.project_id, self.minute_aggregates_topic
        )
        self.subscription_commands_subscription_path = self.subscriber.subscription_path(
            self.project_id, self.subscription_commands_subscription
        )

        # Subscription command handler
        self.subscription_handler = SubscriptionCommandHandler(
            project_id=self.project_id,
            subscription_path=self.subscription_commands_subscription_path,
            on_subscribe_callback=self._handle_polygon_subscribe,
            on_unsubscribe_callback=self._handle_polygon_unsubscribe
        )

        # Data publisher manager
        self.data_publisher = DataPublisherManager(self.project_id)

        # Health monitor
        self.health_monitor = HealthMonitor(self.project_id, "polygon-websocket-ingestion")

        # Health server
        self.health_server = None

        # Topic paths
        self.subscription_commands_topic_path = self.publisher.topic_path(
            self.project_id, self.subscription_commands_topic
        )
        self.minute_aggregates_topic_path = self.publisher.topic_path(
            self.project_id, self.minute_aggregates_topic
        )
        self.subscription_commands_subscription_path = self.subscriber.subscription_path(
            self.project_id, self.subscription_commands_subscription
        )
        
        logger.info("🚀 Polygon WebSocket Ingestion Service initialized")
        logger.info(f"📡 Project ID: {self.project_id}")
        logger.info(f"🔑 Polygon API Key: {'✅ Set' if self.polygon_api_key else '❌ Missing'}")

    async def start(self):
        """Start the ingestion service."""
        if not self.polygon_api_key:
            logger.error("❌ POLYGON_API_KEY environment variable is required")
            return

        # Store reference to the main event loop for thread-safe async calls
        self.main_loop = asyncio.get_running_loop()
        
        self.running = True
        logger.info("🚀 Starting Polygon WebSocket Ingestion Service...")
        
        try:
            # Start health server (use PORT env var for Cloud Run, fallback to 8081)
            health_port = int(os.getenv('PORT', '8081'))
            self.health_server = await start_health_server(self.health_monitor, health_port)

            # Start health monitoring
            health_task = asyncio.create_task(start_health_monitoring(self.health_monitor))

            # Start Pub/Sub subscription listener
            subscription_task = asyncio.create_task(self._listen_for_subscription_commands())

            # Start Polygon WebSocket connection
            websocket_task = asyncio.create_task(self._connect_to_polygon())

            # Start periodic cleanup task
            cleanup_task = asyncio.create_task(self._periodic_cleanup())

            # Wait for all tasks
            await asyncio.gather(health_task, subscription_task, websocket_task, cleanup_task)
            
        except Exception as e:
            logger.error(f"❌ Error in main service loop: {e}")
        finally:
            await self.stop()

    async def stop(self):
        """Stop the ingestion service gracefully."""
        logger.info("🛑 Stopping Polygon WebSocket Ingestion Service...")
        self.running = False

        # Stop health server
        if self.health_server:
            await self.health_server.stop()
            logger.info("🏥 Health server stopped")

        # Disconnect Polygon WebSocket client
        if self.polygon_client:
            await self.polygon_client.disconnect()
            logger.info("🔌 Polygon WebSocket client disconnected")

    def _handle_polygon_subscribe(self, symbol: str):
        """
        Handle subscription request for a forex pair.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        logger.info(f"🚀 Starting Polygon subscription for: {symbol}")
        self.active_subscriptions.add(symbol)

        # Update health monitor
        self.health_monitor.update_subscription_metrics(
            active_count=len(self.active_subscriptions),
            command_received=True,
            command_processed=True
        )

        # Subscribe via Polygon WebSocket client using thread-safe method
        try:
            # Use the stored main event loop reference
            if self.main_loop and not self.main_loop.is_closed():
                # Schedule the coroutine to run in the main event loop
                asyncio.run_coroutine_threadsafe(
                    self.polygon_client.subscribe_to_symbol(symbol),
                    self.main_loop
                )
                logger.info(f"✅ Scheduled Polygon subscription for {symbol}")
            else:
                logger.error(f"❌ Main event loop not available, cannot subscribe to {symbol}")
        except Exception as e:
            logger.error(f"❌ Error scheduling Polygon subscription for {symbol}: {e}")

    def _handle_polygon_unsubscribe(self, symbol: str):
        """
        Handle unsubscription request for a forex pair.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        logger.info(f"🛑 Stopping Polygon subscription for: {symbol}")
        self.active_subscriptions.discard(symbol)

        # Update health monitor
        self.health_monitor.update_subscription_metrics(
            active_count=len(self.active_subscriptions),
            command_received=True,
            command_processed=True
        )

        # Unsubscribe via Polygon WebSocket client using thread-safe method
        try:
            # Use the stored main event loop reference
            if self.main_loop and not self.main_loop.is_closed():
                # Schedule the coroutine to run in the main event loop
                asyncio.run_coroutine_threadsafe(
                    self.polygon_client.unsubscribe_from_symbol(symbol),
                    self.main_loop
                )
                logger.info(f"✅ Scheduled Polygon unsubscription for {symbol}")
            else:
                logger.error(f"❌ Main event loop not available, cannot unsubscribe from {symbol}")
        except Exception as e:
            logger.error(f"❌ Error scheduling Polygon unsubscription for {symbol}: {e}")

    def _handle_minute_aggregate_data(self, data):
        """
        Handle minute aggregate data received from Polygon.

        Args:
            data: Minute aggregate data
        """
        logger.info(f"📊 Received minute aggregate data for {data['symbol']}: {data['close']}")

        # Update health monitor
        self.health_monitor.record_message_received()

        # Publish to Pub/Sub polygon.minute_aggregates topic
        async def publish_with_monitoring():
            import time
            start_time = time.time()
            try:
                success = await self.data_publisher.publish_minute_aggregate(data)
                processing_time = time.time() - start_time
                self.health_monitor.record_message_published(success=success, processing_time=processing_time)
            except Exception as e:
                processing_time = time.time() - start_time
                self.health_monitor.record_message_published(success=False, processing_time=processing_time)
                self.health_monitor.report_error(e, f"publish_minute_aggregate_{data['symbol']}")
                raise

        # Schedule the async task from synchronous context using stored main loop
        try:
            if self.main_loop and not self.main_loop.is_closed():
                # Schedule the coroutine to run in the main event loop from another thread
                future = asyncio.run_coroutine_threadsafe(publish_with_monitoring(), self.main_loop)
                logger.debug(f"📤 Scheduled publishing task for {data['symbol']}")
            else:
                logger.error("❌ Main event loop not available for publishing data")
        except Exception as e:
            logger.error(f"❌ Error scheduling async task: {e}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")

    def _handle_connection_status(self, connected: bool, reconnection: bool):
        """
        Handle WebSocket connection status changes.

        Args:
            connected: Whether the WebSocket is connected
            reconnection: Whether this is a reconnection
        """
        logger.info(f"🔌 WebSocket connection status: {'Connected' if connected else 'Disconnected'}")

        # Update health monitor
        self.health_monitor.update_websocket_status(connected, reconnection)

        # Log detailed connection stats if available
        if hasattr(self.polygon_client, 'get_connection_stats'):
            stats = self.polygon_client.get_connection_stats()
            logger.info(f"📊 Connection stats: {stats}")

    async def _listen_for_subscription_commands(self):
        """Listen for subscription commands from trade-bots via Pub/Sub."""
        logger.info(f"👂 Listening for subscription commands on: {self.subscription_commands_subscription}")

        # Run the subscription handler in a separate thread
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self.subscription_handler.start_listening)

    async def _connect_to_polygon(self):
        """Connect to Polygon.io WebSocket and handle messages."""
        logger.info("🔗 Starting Polygon WebSocket client...")

        if not self.polygon_api_key:
            logger.warning("⚠️ Skipping Polygon connection - no API key provided")
            return

        try:
            # Start the Polygon WebSocket client
            await self.polygon_client.start()
        except Exception as e:
            logger.error(f"❌ Failed to start Polygon WebSocket client: {e}")
            logger.warning("⚠️ Service will continue without Polygon connection")

    async def _periodic_cleanup(self):
        """Periodically clean up inactive trade-bot subscriptions."""
        logger.info("🧹 Starting periodic cleanup task...")

        while True:
            try:
                # Wait 5 minutes between cleanup cycles
                await asyncio.sleep(300)

                # Clean up inactive trade-bots
                cleaned_count = self.subscription_handler.cleanup_inactive_trade_bots()

                if cleaned_count > 0:
                    logger.info(f"🧹 Periodic cleanup completed: removed {cleaned_count} inactive trade-bots")
                else:
                    logger.debug("🧹 Periodic cleanup completed: no inactive trade-bots found")

            except Exception as e:
                logger.error(f"❌ Error in periodic cleanup: {e}")
                # Continue the loop even if cleanup fails
                await asyncio.sleep(60)  # Wait 1 minute before retrying

def setup_signal_handlers(service):
    """Set up signal handlers for graceful shutdown."""
    def signal_handler(signum, _frame):
        logger.info(f"📡 Received signal {signum}, initiating graceful shutdown...")
        asyncio.create_task(service.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Main entry point."""
    logger.info("🌟 Starting Polygon WebSocket Ingestion Service")
    
    service = PolygonWebSocketIngestionService()
    setup_signal_handlers(service)
    
    try:
        await service.start()
    except KeyboardInterrupt:
        logger.info("👋 Service interrupted by user")
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
