#!/usr/bin/env python3
"""
Subscription Command Handler

This module handles subscription commands from trade-bots via Pub/Sub
and manages Polygon.io WebSocket subscriptions accordingly.
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Set, Callable, Optional

from google.cloud import pubsub_v1
from google.api_core import exceptions

logger = logging.getLogger(__name__)

class SubscriptionCommandHandler:
    """
    Handles subscription commands from trade-bots and manages Polygon.io subscriptions.
    """
    
    def __init__(
        self, 
        project_id: str,
        subscription_path: str,
        on_subscribe_callback: Optional[Callable[[str], None]] = None,
        on_unsubscribe_callback: Optional[Callable[[str], None]] = None
    ):
        self.project_id = project_id
        self.subscription_path = subscription_path
        self.subscriber = pubsub_v1.SubscriberClient()
        
        # Callbacks for subscription changes
        self.on_subscribe_callback = on_subscribe_callback
        self.on_unsubscribe_callback = on_unsubscribe_callback
        
        # Track active subscriptions
        self.active_subscriptions: Dict[str, Set[str]] = {}  # symbol -> set of trade_bot_ids
        self.subscription_counts: Dict[str, int] = {}  # symbol -> count

        # Track last heartbeat from each trade-bot
        self.trade_bot_heartbeats: Dict[str, float] = {}  # trade_bot_id -> timestamp

        # Heartbeat timeout (5 minutes)
        self.heartbeat_timeout_seconds = 300

        # Flow control settings
        self.flow_control = pubsub_v1.types.FlowControl(max_messages=100)
        
        logger.info(f"📨 SubscriptionCommandHandler initialized")
        logger.info(f"📡 Subscription path: {subscription_path}")

    def start_listening(self) -> None:
        """Start listening for subscription commands."""
        logger.info("👂 Starting to listen for subscription commands...")

        # Configure the subscriber
        streaming_pull_future = self.subscriber.subscribe(
            self.subscription_path,
            callback=self._handle_message,
            flow_control=self.flow_control
        )

        logger.info(f"🔄 Listening for messages on {self.subscription_path}")
        logger.info("🔍 DEBUG: Subscription handler is now actively listening...")

        try:
            # Keep the main thread running
            streaming_pull_future.result()
        except KeyboardInterrupt:
            streaming_pull_future.cancel()
            logger.info("👋 Subscription command listener stopped")
        except Exception as e:
            logger.error(f"❌ Error in subscription listener: {e}")
            raise

    def _handle_message(self, message) -> None:
        """
        Handle incoming subscription command messages.

        Args:
            message: Pub/Sub message containing subscription command
        """
        logger.info("🔍 DEBUG: _handle_message called!")
        try:
            # Parse the message
            data = json.loads(message.data.decode('utf-8'))

            logger.info(f"📨 Received subscription command: {data}")
            
            # Validate required fields
            if not self._validate_command(data):
                logger.error(f"❌ Invalid command format: {data}")
                message.ack()
                return
            
            command = data['command']
            symbol = data['symbol']
            trade_bot_id = data['trade_bot_id']
            
            # Update heartbeat for this trade-bot
            self._update_trade_bot_heartbeat(trade_bot_id)

            # Process the command
            if command == 'subscribe':
                self._handle_subscribe_command(symbol, trade_bot_id)
            elif command == 'unsubscribe':
                self._handle_unsubscribe_command(symbol, trade_bot_id)
            elif command == 'heartbeat':
                # Heartbeat command just updates the timestamp (already done above)
                logger.debug(f"💓 Received heartbeat from {trade_bot_id} for {symbol}")
            else:
                logger.error(f"❌ Unknown command: {command}")
            
            # Acknowledge the message
            message.ack()
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse message JSON: {e}")
            message.ack()  # Ack to avoid reprocessing
        except Exception as e:
            logger.error(f"❌ Error handling message: {e}")
            message.nack()  # Nack to retry

    def _validate_command(self, data: dict) -> bool:
        """
        Validate subscription command format.
        
        Args:
            data: Parsed message data
            
        Returns:
            bool: True if command is valid
        """
        required_fields = ['command', 'symbol', 'trade_bot_id']
        
        for field in required_fields:
            if field not in data:
                logger.error(f"❌ Missing required field: {field}")
                return False
        
        if data['command'] not in ['subscribe', 'unsubscribe', 'heartbeat']:
            logger.error(f"❌ Invalid command: {data['command']}")
            return False
        
        return True

    def _normalize_symbol(self, symbol: str) -> str:
        """
        Normalize forex symbol format.

        Converts 'EUR/USD' to 'EURUSD' for Polygon.io compatibility.

        Args:
            symbol: Input symbol (e.g., 'EUR/USD' or 'EURUSD')

        Returns:
            str: Normalized symbol (e.g., 'EURUSD')
        """
        return symbol.replace('/', '').upper()

    def _handle_subscribe_command(self, symbol: str, trade_bot_id: str) -> None:
        """
        Handle subscribe command for a forex pair.

        Args:
            symbol: Forex pair symbol (e.g., 'EUR/USD' or 'EURUSD')
            trade_bot_id: ID of the trade bot requesting subscription
        """
        # Normalize the symbol format
        normalized_symbol = self._normalize_symbol(symbol)
        logger.info(f"📈 Processing subscribe command: {symbol} → {normalized_symbol} for bot {trade_bot_id}")
        
        # Initialize symbol tracking if needed
        if normalized_symbol not in self.active_subscriptions:
            self.active_subscriptions[normalized_symbol] = set()
            self.subscription_counts[normalized_symbol] = 0

        # Add trade bot to the symbol's subscription set
        if trade_bot_id not in self.active_subscriptions[normalized_symbol]:
            self.active_subscriptions[normalized_symbol].add(trade_bot_id)
            self.subscription_counts[normalized_symbol] += 1

            logger.info(f"✅ Added bot {trade_bot_id} to {normalized_symbol} subscriptions")
            logger.info(f"📊 {normalized_symbol} now has {self.subscription_counts[normalized_symbol]} subscribers")

            # If this is the first subscriber for this symbol, start Polygon subscription
            if self.subscription_counts[normalized_symbol] == 1:
                logger.info(f"🚀 Starting Polygon subscription for {normalized_symbol}")
                if self.on_subscribe_callback:
                    self.on_subscribe_callback(normalized_symbol)
        else:
            logger.info(f"ℹ️ Bot {trade_bot_id} already subscribed to {normalized_symbol}")

    def _handle_unsubscribe_command(self, symbol: str, trade_bot_id: str) -> None:
        """
        Handle unsubscribe command for a forex pair.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
            trade_bot_id: ID of the trade bot requesting unsubscription
        """
        logger.info(f"📉 Processing unsubscribe command: {symbol} for bot {trade_bot_id}")
        
        # Check if symbol exists in subscriptions
        if symbol not in self.active_subscriptions:
            logger.warning(f"⚠️ No active subscriptions found for {symbol}")
            return
        
        # Remove trade bot from the symbol's subscription set
        if trade_bot_id in self.active_subscriptions[symbol]:
            self.active_subscriptions[symbol].remove(trade_bot_id)
            self.subscription_counts[symbol] -= 1
            
            logger.info(f"✅ Removed bot {trade_bot_id} from {symbol} subscriptions")
            logger.info(f"📊 {symbol} now has {self.subscription_counts[symbol]} subscribers")
            
            # If no more subscribers for this symbol, stop Polygon subscription
            if self.subscription_counts[symbol] == 0:
                logger.info(f"🛑 Stopping Polygon subscription for {symbol}")
                if self.on_unsubscribe_callback:
                    self.on_unsubscribe_callback(symbol)
                
                # Clean up empty entries
                del self.active_subscriptions[symbol]
                del self.subscription_counts[symbol]
        else:
            logger.warning(f"⚠️ Bot {trade_bot_id} was not subscribed to {symbol}")

    def get_active_subscriptions(self) -> Dict[str, int]:
        """
        Get current active subscriptions summary.

        Returns:
            Dict mapping symbol to subscriber count
        """
        return self.subscription_counts.copy()

    def _update_trade_bot_heartbeat(self, trade_bot_id: str):
        """
        Update the heartbeat timestamp for a trade-bot.

        Args:
            trade_bot_id: ID of the trade bot
        """
        import time
        self.trade_bot_heartbeats[trade_bot_id] = time.time()
        logger.debug(f"💓 Updated heartbeat for trade-bot: {trade_bot_id}")

    def cleanup_inactive_trade_bots(self):
        """
        Clean up subscriptions from trade-bots that haven't sent heartbeats recently.
        This handles cases where trade-bots are forcefully terminated without cleanup.
        """
        import time
        current_time = time.time()
        inactive_bots = []

        # Find inactive trade-bots
        for trade_bot_id, last_heartbeat in self.trade_bot_heartbeats.items():
            if current_time - last_heartbeat > self.heartbeat_timeout_seconds:
                inactive_bots.append(trade_bot_id)

        # Clean up subscriptions for inactive bots
        for trade_bot_id in inactive_bots:
            logger.warning(f"🧹 Cleaning up subscriptions for inactive trade-bot: {trade_bot_id}")

            # Find all symbols this bot was subscribed to
            symbols_to_cleanup = []
            for symbol, bot_set in self.active_subscriptions.items():
                if trade_bot_id in bot_set:
                    symbols_to_cleanup.append(symbol)

            # Unsubscribe from each symbol
            for symbol in symbols_to_cleanup:
                logger.info(f"🧹 Auto-unsubscribing inactive bot {trade_bot_id} from {symbol}")
                self._handle_unsubscribe_command(symbol, trade_bot_id)

            # Remove from heartbeat tracking
            del self.trade_bot_heartbeats[trade_bot_id]

        if inactive_bots:
            logger.info(f"🧹 Cleaned up {len(inactive_bots)} inactive trade-bots: {inactive_bots}")

        return len(inactive_bots)

    def get_subscription_details(self) -> Dict[str, Set[str]]:
        """
        Get detailed subscription information.
        
        Returns:
            Dict mapping symbol to set of trade bot IDs
        """
        return {
            symbol: bots.copy() 
            for symbol, bots in self.active_subscriptions.items()
        }

    def is_symbol_subscribed(self, symbol: str) -> bool:
        """
        Check if a symbol has any active subscriptions.
        
        Args:
            symbol: Forex pair symbol
            
        Returns:
            bool: True if symbol has active subscriptions
        """
        return symbol in self.subscription_counts and self.subscription_counts[symbol] > 0

    def stop_listening(self) -> None:
        """Stop listening for subscription commands."""
        logger.info("🛑 Stopping subscription command listener...")
        # The streaming_pull_future.cancel() is handled in start_listening()

def main():
    """Test function for the subscription handler."""
    import os
    
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
    subscription_name = 'polygon-subscription-commands-sub'
    
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project_id, subscription_name)
    
    def on_subscribe(symbol):
        print(f"🚀 Would start Polygon subscription for: {symbol}")
    
    def on_unsubscribe(symbol):
        print(f"🛑 Would stop Polygon subscription for: {symbol}")
    
    handler = SubscriptionCommandHandler(
        project_id=project_id,
        subscription_path=subscription_path,
        on_subscribe_callback=on_subscribe,
        on_unsubscribe_callback=on_unsubscribe
    )
    
    try:
        handler.start_listening()
    except KeyboardInterrupt:
        print("👋 Test stopped by user")

if __name__ == "__main__":
    main()
