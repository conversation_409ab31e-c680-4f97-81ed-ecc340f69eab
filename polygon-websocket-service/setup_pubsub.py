#!/usr/bin/env python3
"""
Pub/Sub Infrastructure Setup Script

This script sets up all required Pub/Sub topics and subscriptions
for the Polygon WebSocket Ingestion Service.

Usage:
    python setup_pubsub.py [--project-id PROJECT_ID] [--delete]
"""

import argparse
import logging
import os
import sys

from pubsub_manager import PubSubManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_infrastructure(project_id: str) -> bool:
    """
    Set up all Pub/Sub infrastructure for the Polygon WebSocket service.
    
    Args:
        project_id: Google Cloud project ID
        
    Returns:
        bool: True if setup was successful
    """
    logger.info(f"🚀 Setting up Pub/Sub infrastructure for project: {project_id}")
    
    manager = PubSubManager(project_id)
    
    # Set up topics and subscriptions
    success = manager.setup_polygon_topics_and_subscriptions()
    
    if success:
        logger.info("✅ Pub/Sub infrastructure setup completed successfully!")
        
        # Print summary
        print("\n" + "="*60)
        print("📡 POLYGON WEBSOCKET PUB/SUB INFRASTRUCTURE")
        print("="*60)
        print("\n📤 TOPICS CREATED:")
        print("  • polygon.subscription_commands")
        print("    └── For subscription management commands from trade-bots")
        print("  • polygon.minute_aggregates") 
        print("    └── For real-time forex minute aggregate data")
        
        print("\n📥 SUBSCRIPTIONS CREATED:")
        print("  • polygon-subscription-commands-sub")
        print("    └── WebSocket service listens for subscription commands")
        
        print("\n🔄 DATA FLOW:")
        print("  Trade-Bot → polygon.subscription_commands → WebSocket Service")
        print("  Trade-Bot ← polygon.minute_aggregates ← WebSocket Service")
        
        print("\n🎯 NEXT STEPS:")
        print("  1. Start the Polygon WebSocket Ingestion Service")
        print("  2. Configure trade-bots to use the new Pub/Sub topics")
        print("  3. Monitor message flow and service health")
        print("="*60)
        
    else:
        logger.error("❌ Failed to set up Pub/Sub infrastructure")
    
    return success

def cleanup_infrastructure(project_id: str) -> bool:
    """
    Clean up all Pub/Sub infrastructure for the Polygon WebSocket service.
    
    Args:
        project_id: Google Cloud project ID
        
    Returns:
        bool: True if cleanup was successful
    """
    logger.info(f"🧹 Cleaning up Pub/Sub infrastructure for project: {project_id}")
    
    manager = PubSubManager(project_id)
    
    success = True
    
    # Delete subscriptions first (they depend on topics)
    if not manager.delete_subscription("polygon-subscription-commands-sub"):
        success = False
    
    # Delete topics
    if not manager.delete_topic("polygon.subscription_commands"):
        success = False
    
    if not manager.delete_topic("polygon.minute_aggregates"):
        success = False
    
    if success:
        logger.info("✅ Pub/Sub infrastructure cleanup completed successfully!")
    else:
        logger.error("❌ Failed to clean up some Pub/Sub infrastructure")
    
    return success

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Set up Pub/Sub infrastructure for Polygon WebSocket Ingestion Service"
    )
    parser.add_argument(
        "--project-id",
        default=os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade'),
        help="Google Cloud project ID (default: from GOOGLE_CLOUD_PROJECT env var or 'oryntrade')"
    )
    parser.add_argument(
        "--delete",
        action="store_true",
        help="Delete infrastructure instead of creating it"
    )
    
    args = parser.parse_args()
    
    if not args.project_id:
        logger.error("❌ Project ID is required. Set GOOGLE_CLOUD_PROJECT env var or use --project-id")
        sys.exit(1)
    
    try:
        if args.delete:
            success = cleanup_infrastructure(args.project_id)
        else:
            success = setup_infrastructure(args.project_id)
        
        if not success:
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("👋 Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
