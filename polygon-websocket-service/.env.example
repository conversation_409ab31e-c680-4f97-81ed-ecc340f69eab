# Polygon WebSocket Ingestion Service Configuration

# Polygon.io API Configuration
POLYGON_API_KEY=your_polygon_api_key_here

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=oryntrade
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json

# Pub/Sub Topic Configuration
SUBSCRIPTION_COMMANDS_TOPIC=polygon.subscription_commands
MINUTE_AGGREGATES_TOPIC=polygon.minute_aggregates
SUBSCRIPTION_COMMANDS_SUBSCRIPTION=polygon-subscription-commands-sub

# WebSocket Configuration
POLYGON_WS_URL=wss://socket.polygon.io/forex
RECONNECT_DELAY=5
MAX_RECONNECT_ATTEMPTS=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Health Check Configuration
HEALTH_CHECK_PORT=8080
HEALTH_CHECK_INTERVAL=30

# Service Configuration
SERVICE_NAME=polygon-websocket-ingestion
SERVICE_VERSION=1.0.0
