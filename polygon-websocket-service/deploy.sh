#!/bin/bash

# Polygon WebSocket Ingestion Service Deployment Script
# This script builds and deploys the service to Google Cloud Run

set -e  # Exit on any error

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"oryntrade"}
SERVICE_NAME="polygon-websocket-ingestion"
REGION=${REGION:-"us-central1"}
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"
POLYGON_API_KEY=${POLYGON_API_KEY:-""}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Deploying Polygon WebSocket Ingestion Service${NC}"
echo -e "${BLUE}================================================${NC}"

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if [ -z "$PROJECT_ID" ]; then
    echo -e "${RED}❌ GOOGLE_CLOUD_PROJECT environment variable is required${NC}"
    exit 1
fi

# Check if Polygon API key secret exists
echo -e "${YELLOW}🔍 Checking Polygon API key secret...${NC}"
if ! gcloud secrets describe polygon-api-key >/dev/null 2>&1; then
    echo -e "${RED}❌ Secret 'polygon-api-key' not found${NC}"
    echo -e "${RED}Please create it with: gcloud secrets create polygon-api-key --data-file=<path-to-api-key-file>${NC}"
    exit 1
else
    echo -e "${GREEN}✅ Polygon API key secret found${NC}"
fi

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI is not installed${NC}"
    exit 1
fi

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Check if running in GitHub Actions or local environment
if [ -n "${GITHUB_ACTIONS}" ]; then
    echo -e "${BLUE}🤖 Running in GitHub Actions - using service account authentication${NC}"
    # In GitHub Actions, authentication is handled by google-github-actions/auth
    # Just configure Docker for the registry
    gcloud auth configure-docker gcr.io --quiet
else
    echo -e "${YELLOW}💻 Running locally - checking gcloud authentication...${NC}"
    # Check if gcloud is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo -e "${RED}❌ No active gcloud authentication found${NC}"
        echo -e "${RED}Please run: gcloud auth login${NC}"
        exit 1
    fi

    # Set gcloud project
    echo -e "${YELLOW}🔧 Setting gcloud project to ${PROJECT_ID}...${NC}"
    gcloud config set project "$PROJECT_ID"

    # Configure Docker authentication
    echo -e "${YELLOW}🔐 Configuring Docker authentication...${NC}"
    gcloud auth configure-docker gcr.io --quiet
fi

# Enable required APIs
echo -e "${YELLOW}🔌 Enabling required Google Cloud APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable pubsub.googleapis.com
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com

# Create Cloud Build configuration if it doesn't exist
if [ ! -f "cloudbuild.yaml" ]; then
    echo -e "${YELLOW}📝 Creating cloudbuild.yaml...${NC}"
    cat > cloudbuild.yaml << EOF
steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'gcr.io/oryntrade/polygon-websocket-ingestion:latest', '.']
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'gcr.io/oryntrade/polygon-websocket-ingestion:latest']

options:
  logging: CLOUD_LOGGING_ONLY
  defaultLogsBucketBehavior: REGIONAL_USER_OWNED_BUCKET

serviceAccount: 'projects/oryntrade/serviceAccounts/<EMAIL>'
EOF
fi

# Build the Docker image using Cloud Build
echo -e "${YELLOW}🏗️ Building Docker image with Cloud Build...${NC}"
gcloud builds submit --config=cloudbuild.yaml .

# Ensure service account has necessary permissions
echo -e "${YELLOW}🔑 Ensuring service account permissions...${NC}"
SA_EMAIL="build-service-account-872@${PROJECT_ID}.iam.gserviceaccount.com"

gcloud projects add-iam-policy-binding "${PROJECT_ID}" \
    --member="serviceAccount:${SA_EMAIL}" \
    --role="roles/pubsub.editor" \
    --condition=None \
    --quiet

gcloud projects add-iam-policy-binding "${PROJECT_ID}" \
    --member="serviceAccount:${SA_EMAIL}" \
    --role="roles/secretmanager.secretAccessor" \
    --condition=None \
    --quiet

# Set up Pub/Sub infrastructure
echo -e "${YELLOW}📡 Setting up Pub/Sub infrastructure...${NC}"
python setup_pubsub.py --project-id "$PROJECT_ID"

# Deploy to Cloud Run
echo -e "${YELLOW}🚀 Deploying to Cloud Run...${NC}"
gcloud run deploy "$SERVICE_NAME" \
    --image "$IMAGE_NAME" \
    --platform managed \
    --region "$REGION" \
    --service-account="build-service-account-872@${PROJECT_ID}.iam.gserviceaccount.com" \
    --allow-unauthenticated \
    --set-env-vars="GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
    --set-secrets="POLYGON_API_KEY=polygon-api-key:latest" \
    --set-env-vars="LOG_LEVEL=INFO" \
    --memory=2Gi \
    --cpu=1 \
    --timeout=3600 \
    --concurrency=1000 \
    --min-instances=1 \
    --max-instances=10

# Get the service URL
SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" --platform managed --region "$REGION" --format 'value(status.url)')

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}================================================${NC}"
echo -e "${GREEN}🌐 Service URL: ${SERVICE_URL}${NC}"
echo -e "${GREEN}🏥 Health Check: ${SERVICE_URL}/health${NC}"
echo -e "${GREEN}📊 Metrics: ${SERVICE_URL}/metrics${NC}"
echo -e "${GREEN}📈 Status: ${SERVICE_URL}/status${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo -e "${BLUE}1. Configure trade-bots to use MARKET_DATA_PROVIDER=pubsub${NC}"
echo -e "${BLUE}2. Monitor service health at ${SERVICE_URL}/health${NC}"
echo -e "${BLUE}3. Check logs: gcloud logs tail --follow --project=${PROJECT_ID} --resource-type=cloud_run_revision --resource-labels=service_name=${SERVICE_NAME}${NC}"
echo ""
echo -e "${YELLOW}⚠️ Important Notes:${NC}"
echo -e "${YELLOW}• The service runs with minimum 1 instance to maintain WebSocket connections${NC}"
echo -e "${YELLOW}• Monitor costs as this service will run continuously${NC}"
echo -e "${YELLOW}• Check Pub/Sub message quotas and limits${NC}"
