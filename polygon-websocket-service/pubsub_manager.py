#!/usr/bin/env python3
"""
Pub/Sub Topic and Subscription Management

This module handles the creation and management of Google Cloud Pub/Sub topics
and subscriptions for the Polygon WebSocket Ingestion Service.
"""

import logging
from typing import Optional

from google.cloud import pubsub_v1
from google.api_core import exceptions

logger = logging.getLogger(__name__)

class PubSubManager:
    """Manages Pub/Sub topics and subscriptions for the Polygon WebSocket service."""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        
        logger.info(f"📡 PubSubManager initialized for project: {project_id}")

    def create_topic(self, topic_name: str) -> bool:
        """
        Create a Pub/Sub topic if it doesn't exist.
        
        Args:
            topic_name: Name of the topic to create
            
        Returns:
            bool: True if topic was created or already exists, False on error
        """
        topic_path = self.publisher.topic_path(self.project_id, topic_name)
        
        try:
            # Try to get the topic first
            self.publisher.get_topic(request={"topic": topic_path})
            logger.info(f"✅ Topic already exists: {topic_name}")
            return True
            
        except exceptions.NotFound:
            # Topic doesn't exist, create it
            try:
                topic = self.publisher.create_topic(request={"name": topic_path})
                logger.info(f"🆕 Created topic: {topic.name}")
                return True
                
            except Exception as e:
                logger.error(f"❌ Failed to create topic {topic_name}: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error checking topic {topic_name}: {e}")
            return False

    def create_subscription(
        self, 
        topic_name: str, 
        subscription_name: str,
        filter_expression: Optional[str] = None,
        ack_deadline_seconds: int = 60
    ) -> bool:
        """
        Create a Pub/Sub subscription if it doesn't exist.
        
        Args:
            topic_name: Name of the topic to subscribe to
            subscription_name: Name of the subscription to create
            filter_expression: Optional filter expression for message filtering
            ack_deadline_seconds: Acknowledgment deadline in seconds
            
        Returns:
            bool: True if subscription was created or already exists, False on error
        """
        topic_path = self.publisher.topic_path(self.project_id, topic_name)
        subscription_path = self.subscriber.subscription_path(self.project_id, subscription_name)
        
        try:
            # Try to get the subscription first
            self.subscriber.get_subscription(request={"subscription": subscription_path})
            logger.info(f"✅ Subscription already exists: {subscription_name}")
            return True
            
        except exceptions.NotFound:
            # Subscription doesn't exist, create it
            try:
                request = {
                    "name": subscription_path,
                    "topic": topic_path,
                    "ack_deadline_seconds": ack_deadline_seconds
                }
                
                # Add filter if provided
                if filter_expression:
                    request["filter"] = filter_expression
                    logger.info(f"🔍 Creating subscription with filter: {filter_expression}")
                
                subscription = self.subscriber.create_subscription(request=request)
                logger.info(f"🆕 Created subscription: {subscription.name}")
                return True
                
            except Exception as e:
                logger.error(f"❌ Failed to create subscription {subscription_name}: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error checking subscription {subscription_name}: {e}")
            return False

    def setup_polygon_topics_and_subscriptions(self) -> bool:
        """
        Set up all required topics and subscriptions for the Polygon WebSocket service.
        
        Returns:
            bool: True if all topics and subscriptions were set up successfully
        """
        logger.info("🚀 Setting up Polygon WebSocket Pub/Sub infrastructure...")
        
        success = True
        
        # 1. Create polygon.subscription_commands topic
        if not self.create_topic("polygon.subscription_commands"):
            success = False
        
        # 2. Create polygon.minute_aggregates topic  
        if not self.create_topic("polygon.minute_aggregates"):
            success = False
        
        # 3. Create subscription for polygon.subscription_commands
        # This subscription is used by the WebSocket service to receive commands
        if not self.create_subscription(
            topic_name="polygon.subscription_commands",
            subscription_name="polygon-subscription-commands-sub",
            ack_deadline_seconds=30  # Commands should be processed quickly
        ):
            success = False
        
        if success:
            logger.info("✅ All Polygon WebSocket Pub/Sub infrastructure set up successfully!")
        else:
            logger.error("❌ Failed to set up some Pub/Sub infrastructure")
        
        return success

    def delete_topic(self, topic_name: str) -> bool:
        """
        Delete a Pub/Sub topic.
        
        Args:
            topic_name: Name of the topic to delete
            
        Returns:
            bool: True if topic was deleted successfully, False on error
        """
        topic_path = self.publisher.topic_path(self.project_id, topic_name)
        
        try:
            self.publisher.delete_topic(request={"topic": topic_path})
            logger.info(f"🗑️ Deleted topic: {topic_name}")
            return True
            
        except exceptions.NotFound:
            logger.info(f"ℹ️ Topic {topic_name} doesn't exist (already deleted)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete topic {topic_name}: {e}")
            return False

    def delete_subscription(self, subscription_name: str) -> bool:
        """
        Delete a Pub/Sub subscription.
        
        Args:
            subscription_name: Name of the subscription to delete
            
        Returns:
            bool: True if subscription was deleted successfully, False on error
        """
        subscription_path = self.subscriber.subscription_path(self.project_id, subscription_name)
        
        try:
            self.subscriber.delete_subscription(request={"subscription": subscription_path})
            logger.info(f"🗑️ Deleted subscription: {subscription_name}")
            return True
            
        except exceptions.NotFound:
            logger.info(f"ℹ️ Subscription {subscription_name} doesn't exist (already deleted)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete subscription {subscription_name}: {e}")
            return False

def main():
    """Main function for testing topic and subscription creation."""
    import os
    
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
    
    manager = PubSubManager(project_id)
    success = manager.setup_polygon_topics_and_subscriptions()
    
    if success:
        print("✅ Pub/Sub setup completed successfully!")
    else:
        print("❌ Pub/Sub setup failed!")
        exit(1)

if __name__ == "__main__":
    main()
