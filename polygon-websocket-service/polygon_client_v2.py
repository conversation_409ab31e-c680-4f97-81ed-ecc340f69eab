#!/usr/bin/env python3
"""
Polygon WebSocket Client V2 - Using Official Polygon Python Client

This module provides a WebSocket client for Polygon.io using their official
Python client library, which handles connection management, ping/pong, and
reconnections automatically.
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Set, Optional, Callable, List

# Set up logger first
logger = logging.getLogger(__name__)

# Import StreamClient - this is required for the fix to work
try:
    from polygon.streaming import StreamClient
    from polygon.enums import StreamCluster
    logger.info("✅ Successfully imported StreamClient from polygon.streaming")
except ImportError as e:
    logger.error(f"❌ Failed to import StreamClient: {e}")
    logger.error("❌ This indicates the polygon-api-client version doesn't have the streaming module")
    logger.error("❌ The service will not work properly without StreamClient")
    raise ImportError(
        "StreamClient is required for dynamic subscriptions. "
        "Please ensure polygon-api-client>=1.15.3 is installed. "
        f"Import error: {e}"
    )

class PolygonWebSocketClientV2:
    """
    WebSocket client for Polygon.io using their official Python client library.
    
    This client handles:
    - Automatic connection management
    - Built-in ping/pong keep-alive
    - Automatic reconnections
    - Message queuing and processing
    """
    
    def __init__(
        self,
        api_key: str,
        on_minute_aggregate_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        on_connection_status_callback: Optional[Callable[[bool, bool], None]] = None
    ):
        self.api_key = api_key
        self.on_minute_aggregate_callback = on_minute_aggregate_callback
        self.on_connection_status_callback = on_connection_status_callback
        
        # Connection state
        self.connected = False
        self.running = False
        
        # Subscribed symbols
        self.subscribed_symbols: Set[str] = set()
        
        # Message processing
        self.message_queue = asyncio.Queue()

        # Connection statistics
        self.connection_start_time = None
        self.total_disconnections = 0
        self.last_message_time = None

        # Polygon WebSocket client (will be initialized when needed)
        self.polygon_client = None  # Type will be StreamClient or WebSocketClient
        
        logger.info("🔌 PolygonWebSocketClientV2 initialized")
        logger.info(f"🔑 API Key: {'✅ Set' if api_key else '❌ Missing'}")

    async def connect(self):
        """Connect to Polygon.io WebSocket using official client."""
        logger.info("🔗 Connecting to Polygon WebSocket using official client...")

        try:
            # Create Polygon StreamClient for Forex market
            logger.info(f"🔗 Creating StreamClient with API key: {'✅ Set' if self.api_key and self.api_key != 'placeholder' else '❌ Missing/Placeholder'}")

            self.polygon_client = StreamClient(
                api_key=self.api_key,
                cluster=StreamCluster.FOREX,
                on_message=self._handle_polygon_message_sync,
                on_close=self._handle_close,
                on_error=self._handle_error,
                enable_connection_logs=True
            )
            logger.info("✅ StreamClient created successfully")

            # Only start the stream if we have a valid API key
            if self.api_key and self.api_key != "placeholder":
                logger.info("🚀 Starting stream thread...")
                self.polygon_client.start_stream_thread()
                logger.info("✅ Stream thread started")
            else:
                logger.warning("⚠️ Skipping stream start due to missing/placeholder API key")

            self.connected = True
            self.connection_start_time = datetime.now(timezone.utc)

            logger.info("✅ Connected to Polygon WebSocket")
            logger.info(f"📊 Connection established at: {self.connection_start_time.isoformat()}")

            # Notify connection status callback
            if self.on_connection_status_callback:
                self.on_connection_status_callback(True, False)

        except Exception as e:
            logger.error(f"❌ Failed to connect to Polygon WebSocket: {e}")
            logger.error(f"❌ Error details: {type(e).__name__}: {str(e)}")
            self.connected = False
            # Don't raise the exception to prevent service startup failure
            logger.warning("⚠️ Continuing service startup despite Polygon connection failure")

    async def disconnect(self):
        """Disconnect from Polygon.io WebSocket."""
        logger.info("🔌 Disconnecting from Polygon WebSocket...")

        self.running = False
        self.connected = False

        if self.polygon_client:
            # Close the StreamClient
            self.polygon_client.close_stream()
            self.polygon_client = None

        logger.info("✅ Disconnected from Polygon WebSocket")



    def _handle_polygon_message_sync(self, ws, message):
        """
        Handle messages from the Polygon StreamClient (synchronous callback).

        Args:
            ws: WebSocket instance
            message: Message from Polygon WebSocket (raw string)
        """
        try:
            # Update last message time
            self.last_message_time = datetime.now(timezone.utc)

            # Add detailed logging for debugging
            logger.info(f"🔍 RAW MESSAGE RECEIVED: {message}")
            logger.info(f"🔍 MESSAGE TYPE: {type(message)}")

            # Parse the JSON message
            if isinstance(message, bytes):
                # Decode bytes to string first
                message_str = message.decode('utf-8')
                message_data = json.loads(message_str)
                logger.info(f"🔍 DECODED BYTES TO JSON: {message_data}")
            elif isinstance(message, str):
                message_data = json.loads(message)
                logger.info(f"🔍 PARSED STRING TO JSON: {message_data}")
            else:
                message_data = message
                logger.info(f"🔍 NON-STRING/BYTES MESSAGE: {message_data}")

            # Process the message
            self._process_stream_message(message_data)

        except Exception as e:
            logger.error(f"❌ Error handling Polygon stream message: {e}")
            logger.error(f"❌ Raw message that caused error: {message}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")

    def _handle_close(self, ws, close_status_code, close_message):
        """Handle WebSocket close event."""
        logger.warning(f"🔌 WebSocket closed: {close_status_code} - {close_message}")
        self.connected = False

        # Notify connection status callback
        if self.on_connection_status_callback:
            self.on_connection_status_callback(False, False)

    def _handle_error(self, ws, exception):
        """Handle WebSocket error event."""
        logger.error(f"❌ WebSocket error: {exception}")
        self.connected = False

        # Notify connection status callback
        if self.on_connection_status_callback:
            self.on_connection_status_callback(False, False)



    def _process_stream_message(self, message):
        """
        Process a message from the Polygon StreamClient.

        Args:
            message: Message from Polygon WebSocket (parsed JSON)
        """
        try:
            logger.debug(f"🔍 Raw stream message: {message}")

            # Check if this is a list of messages
            if isinstance(message, list):
                for msg in message:
                    self._process_single_message(msg)
            else:
                self._process_single_message(message)

        except Exception as e:
            logger.error(f"❌ Error processing stream message: {e}")

    def _process_single_message(self, msg):
        """
        Process a single message from the stream.

        Args:
            msg: Single message object (dict)
        """
        try:
            # Convert message to our expected format
            message_data = self._convert_stream_message(msg)

            if message_data and self.on_minute_aggregate_callback:
                logger.info(f"📊 Processing message for {message_data.get('symbol', 'unknown')}: {message_data.get('close', 'N/A')}")
                self.on_minute_aggregate_callback(message_data)

        except Exception as e:
            logger.error(f"❌ Error processing single message: {e}")

    def _convert_stream_message(self, msg) -> Optional[Dict[str, Any]]:
        """
        Convert Polygon stream message to our expected format.

        Args:
            msg: Polygon stream message

        Returns:
            Converted message data or None if not applicable
        """
        try:
            logger.debug(f"🔍 Converting message: {msg}")
            logger.debug(f"🔍 Message type: {type(msg)}")

            # Handle dictionary messages
            if isinstance(msg, dict):
                event_type = msg.get('ev', '')

                # Check if this is a Currency Aggregate (CA) message
                if event_type == 'CA':
                    symbol = msg.get('pair', '')

                    # Convert Polygon symbol format back to our format (EUR/USD -> EURUSD)
                    if '/' in symbol:
                        symbol = symbol.replace('/', '')

                    # Convert Unix timestamp (milliseconds) to ISO format
                    start_timestamp_ms = msg.get('s', 0)
                    if start_timestamp_ms:
                        # Convert from milliseconds to seconds and create datetime
                        timestamp_dt = datetime.fromtimestamp(start_timestamp_ms / 1000, tz=timezone.utc)
                        timestamp_iso = timestamp_dt.isoformat()
                    else:
                        timestamp_iso = datetime.now(timezone.utc).isoformat()

                    converted_data = {
                        'symbol': symbol,
                        'open': msg.get('o', 0),
                        'high': msg.get('h', 0),
                        'low': msg.get('l', 0),
                        'close': msg.get('c', 0),
                        'volume': msg.get('v', 0),
                        'timestamp': timestamp_iso,
                        'vwap': msg.get('vw', 0)
                    }

                    logger.debug(f"🔄 Converted CA message: {converted_data}")
                    return converted_data
                else:
                    logger.debug(f"🔍 Ignoring non-CA message: {event_type}")

            return None

        except Exception as e:
            logger.error(f"❌ Error converting stream message: {e}")
            logger.error(f"❌ Message details: {msg}")
            return None



    async def start(self):
        """Start the WebSocket client."""
        self.running = True

        try:
            await self.connect()
            logger.info("✅ StreamClient started and ready for subscriptions")
        except Exception as e:
            logger.error(f"❌ Error in WebSocket client: {e}")
            raise

    async def subscribe_to_symbol(self, symbol: str):
        """
        Subscribe to minute aggregates for a forex pair.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        if not self.connected or not self.polygon_client:
            logger.warning(f"⚠️ Cannot subscribe to {symbol}: not connected")
            return

        # Check if we have a valid API key
        if not self.api_key or self.api_key == "placeholder":
            logger.warning(f"⚠️ Cannot subscribe to {symbol}: invalid API key")
            return

        # Convert symbol to Polygon forex format (e.g., EURUSD -> EUR/USD)
        if len(symbol) == 6:  # Standard forex pair like EURUSD
            forex_symbol = f"{symbol[:3]}/{symbol[3:]}"  # EURUSD -> EUR/USD
        else:
            forex_symbol = symbol  # Use as-is if not standard format

        try:
            # Use StreamClient's dynamic subscription method
            logger.info(f"📈 Attempting to subscribe to {forex_symbol}...")
            logger.info(f"🔍 StreamClient object: {self.polygon_client}")
            logger.info(f"🔍 StreamClient methods: {[method for method in dir(self.polygon_client) if 'subscribe' in method.lower()]}")

            # Try manual WebSocket subscription with correct format
            try:
                # Format should be CA.EUR/USD (not CA.C:EUR/USD)
                correct_symbol = f"CA.{forex_symbol}"
                logger.info(f"🔍 Trying manual subscription to: {correct_symbol}")

                # Access the underlying WebSocket connection
                if hasattr(self.polygon_client, 'WS') and self.polygon_client.WS:
                    import json
                    subscription_message = {
                        "action": "subscribe",
                        "params": correct_symbol
                    }
                    message_json = json.dumps(subscription_message)
                    logger.info(f"🔍 Sending manual subscription: {message_json}")

                    # Send the subscription message directly
                    self.polygon_client.WS.send(message_json)
                    logger.info(f"✅ Manual subscription sent for: {correct_symbol}")
                else:
                    logger.warning("⚠️ No WebSocket connection found")

            except Exception as e:
                logger.error(f"❌ Manual subscription failed: {e}")
                import traceback
                logger.error(f"❌ Full traceback: {traceback.format_exc()}")

            # Also try the high-level method as backup
            try:
                result = self.polygon_client.subscribe_forex_minute_aggregates([forex_symbol])
                logger.info(f"🔍 High-level subscription result: {result}")
            except Exception as e:
                logger.error(f"❌ High-level subscription failed: {e}")

            # Add to subscribed symbols after successful subscription
            self.subscribed_symbols.add(symbol)

            logger.info(f"📈 Subscribed to minute aggregates for: {symbol} (as {forex_symbol})")

        except Exception as e:
            logger.error(f"❌ Failed to subscribe to {symbol}: {e}")
            logger.error(f"❌ Error type: {type(e).__name__}")
            import traceback
            logger.error(f"❌ Full traceback: {traceback.format_exc()}")
            # Don't raise to prevent service disruption
            logger.warning(f"⚠️ Continuing despite subscription failure for {symbol}")

    async def unsubscribe_from_symbol(self, symbol: str):
        """
        Unsubscribe from minute aggregates for a forex pair.

        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        if not self.connected or not self.polygon_client:
            logger.warning(f"⚠️ Cannot unsubscribe from {symbol}: not connected")
            return

        if symbol not in self.subscribed_symbols:
            logger.warning(f"⚠️ Not subscribed to {symbol}")
            return

        # Convert symbol to Polygon forex format
        if len(symbol) == 6:
            forex_symbol = f"{symbol[:3]}/{symbol[3:]}"
        else:
            forex_symbol = symbol

        try:
            # Use StreamClient's dynamic unsubscription method
            self.polygon_client.unsubscribe_forex_minute_aggregates([forex_symbol])

            # Remove from subscribed symbols after successful unsubscription
            self.subscribed_symbols.discard(symbol)

            logger.info(f"📉 Unsubscribed from minute aggregates for: {symbol}")

        except Exception as e:
            logger.error(f"❌ Failed to unsubscribe from {symbol}: {e}")
            raise















    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        connection_duration = None
        if self.connection_start_time and self.connected:
            connection_duration = (datetime.now(timezone.utc) - self.connection_start_time).total_seconds()

        seconds_since_last_message = None
        if self.last_message_time:
            seconds_since_last_message = (datetime.now(timezone.utc) - self.last_message_time).total_seconds()

        return {
            "connected": self.connected,
            "running": self.running,
            "connection_start_time": self.connection_start_time.isoformat() if self.connection_start_time else None,
            "connection_duration_seconds": connection_duration,
            "last_message_time": self.last_message_time.isoformat() if self.last_message_time else None,
            "seconds_since_last_message": seconds_since_last_message,
            "total_disconnections": self.total_disconnections,
            "subscribed_symbols": list(self.subscribed_symbols)
        }


# Test function for the new Polygon WebSocket client
async def main():
    """Test function for the Polygon WebSocket client V2."""
    import os

    api_key = os.getenv('POLYGON_API_KEY')
    if not api_key:
        print("❌ POLYGON_API_KEY environment variable is required")
        return

    def handle_message(data):
        print(f"📊 Received forex data: {data}")

    def handle_connection(connected, reconnected):
        status = "Connected" if connected else "Disconnected"
        reconnect_info = " (reconnected)" if reconnected else ""
        print(f"🔌 Connection status: {status}{reconnect_info}")

    # Create client
    client = PolygonWebSocketClientV2(
        api_key=api_key,
        on_minute_aggregate_callback=handle_message,
        on_connection_status_callback=handle_connection
    )

    try:
        print("🚀 Starting Polygon WebSocket client V2...")

        # Start the client
        await client.start()

        # Subscribe to EUR/USD
        await client.subscribe_to_symbol("EURUSD")

        # Keep running for a while
        await asyncio.sleep(60)

    except KeyboardInterrupt:
        print("👋 Test stopped by user")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
