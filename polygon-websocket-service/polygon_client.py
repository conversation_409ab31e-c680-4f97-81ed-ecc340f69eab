#!/usr/bin/env python3
"""
Polygon.io WebSocket Client

This module handles the WebSocket connection to Polygon.io and manages
forex minute aggregate subscriptions.
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Set, Callable, Optional, Dict, Any

import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

logger = logging.getLogger(__name__)

class PolygonWebSocketClient:
    """
    WebSocket client for connecting to Polygon.io and receiving forex data.
    """
    
    def __init__(
        self,
        api_key: str,
        on_minute_aggregate_callback: Optional[Callable[[Dict[str, Any]], None]] = None,
        on_connection_status_callback: Optional[Callable[[bool, bool], None]] = None
    ):
        self.api_key = api_key
        self.ws_url = "wss://socket.polygon.io/forex"
        self.websocket = None
        self.connected = False
        self.running = False
        
        # Subscribed symbols
        self.subscribed_symbols: Set[str] = set()
        
        # Callbacks
        self.on_minute_aggregate_callback = on_minute_aggregate_callback
        self.on_connection_status_callback = on_connection_status_callback
        
        # Reconnection settings
        self.reconnect_delay = 5
        self.max_reconnect_attempts = 10
        self.reconnect_attempts = 0

        # Keep-alive settings
        self.ping_interval = 30  # Send ping every 30 seconds
        self.ping_timeout = 10   # Wait 10 seconds for pong response
        self.ping_task = None

        # Connection refresh settings (prevent stale connections)
        self.connection_refresh_hours = 12  # Refresh connection every 12 hours
        self.refresh_task = None

        # Connection statistics
        self.connection_start_time = None
        self.total_disconnections = 0
        self.last_message_time = None
        
        logger.info("🔌 PolygonWebSocketClient initialized")
        logger.info(f"🔑 API Key: {'✅ Set' if api_key else '❌ Missing'}")

    async def connect(self):
        """Connect to Polygon.io WebSocket."""
        logger.info(f"🔗 Connecting to Polygon WebSocket: {self.ws_url}")

        try:
            # Connect with ping/pong settings for keep-alive and shorter timeout
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    self.ws_url,
                    ping_interval=self.ping_interval,
                    ping_timeout=self.ping_timeout
                ),
                timeout=30.0  # 30 second connection timeout
            )
            self.connected = True
            self.reconnect_attempts = 0
            self.connection_start_time = datetime.now(timezone.utc)

            logger.info("✅ Connected to Polygon WebSocket")
            logger.info(f"💓 Keep-alive enabled: ping every {self.ping_interval}s, timeout {self.ping_timeout}s")
            logger.info(f"📊 Connection established at: {self.connection_start_time.isoformat()}")

            # Notify connection status callback
            if self.on_connection_status_callback:
                self.on_connection_status_callback(True, self.reconnect_attempts > 0)

            # Authenticate
            await self._authenticate()

            # Start connection refresh task
            self._start_connection_refresh_task()

            # Start message handling
            await self._handle_messages()
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Polygon WebSocket: {e}")
            self.connected = False
            
            if self.running and self.reconnect_attempts < self.max_reconnect_attempts:
                await self._reconnect()

    async def disconnect(self):
        """Disconnect from Polygon.io WebSocket."""
        logger.info("🔌 Disconnecting from Polygon WebSocket...")
        
        self.running = False
        self.connected = False

        # Cancel refresh task
        if self.refresh_task:
            self.refresh_task.cancel()
            self.refresh_task = None

        if self.websocket:
            await self.websocket.close()
            self.websocket = None
        
        logger.info("✅ Disconnected from Polygon WebSocket")

    async def start(self):
        """Start the WebSocket client."""
        self.running = True
        
        while self.running:
            try:
                await self.connect()
            except Exception as e:
                logger.error(f"❌ Error in WebSocket client: {e}")
                
                if self.running:
                    await asyncio.sleep(self.reconnect_delay)

    async def _authenticate(self):
        """Authenticate with Polygon.io WebSocket."""
        auth_message = {
            "action": "auth",
            "params": self.api_key
        }
        
        logger.info("🔐 Authenticating with Polygon WebSocket...")
        await self._send_message(auth_message)

    async def _handle_messages(self):
        """Handle incoming messages from Polygon WebSocket."""
        logger.info("👂 Starting to listen for Polygon messages...")
        
        try:
            async for message in self.websocket:
                if not self.running:
                    break
                
                try:
                    # Update last message time for health monitoring
                    self.last_message_time = datetime.now(timezone.utc)

                    # Debug: Log raw message received
                    logger.info(f"📨 Raw message received from Polygon: {message[:200]}...")

                    data = json.loads(message)

                    # Log message type(s) - handle both single objects and arrays
                    if isinstance(data, list):
                        message_types = [msg.get('ev', 'unknown') for msg in data if isinstance(msg, dict)]
                        logger.info(f"📊 Parsed message types: {message_types}")
                    else:
                        logger.info(f"📊 Parsed message type: {data.get('ev', 'unknown')}")

                    await self._process_message(data)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Failed to parse message JSON: {e}")
                except Exception as e:
                    logger.error(f"❌ Error processing message: {e}")
                    
        except ConnectionClosed as e:
            self.total_disconnections += 1
            connection_duration = None
            if self.connection_start_time:
                connection_duration = (datetime.now(timezone.utc) - self.connection_start_time).total_seconds()

            logger.warning(f"⚠️ WebSocket connection closed: {e}")
            logger.info(f"🔍 Connection close details: code={getattr(e, 'code', 'unknown')}, reason={getattr(e, 'reason', 'unknown')}")
            logger.info(f"📊 Connection stats: duration={connection_duration:.1f}s, total_disconnections={self.total_disconnections}")
            self.connected = False

            # Notify connection status callback
            if self.on_connection_status_callback:
                self.on_connection_status_callback(False, False)

            if self.running:
                await self._reconnect()
        except Exception as e:
            logger.error(f"❌ Error in message handling: {e}")
            self.connected = False
            
            if self.running:
                await self._reconnect()

    async def _process_message(self, data):
        """
        Process incoming message from Polygon WebSocket.

        Args:
            data: Parsed message data (can be dict or list)
        """
        # Polygon sends messages as a list of objects
        if isinstance(data, list):
            for message in data:
                await self._process_single_message(message)
        else:
            await self._process_single_message(data)

    async def _process_single_message(self, data: Dict[str, Any]):
        """
        Process a single message from Polygon WebSocket.

        Args:
            data: Single message data
        """
        message_type = data.get("ev")  # Event type

        if message_type == "status":
            await self._handle_status_message(data)
        elif message_type == "CA":  # Minute aggregate
            await self._handle_minute_aggregate(data)
        else:
            logger.debug(f"📨 Received message: {data}")

    async def _handle_status_message(self, data: Dict[str, Any]):
        """
        Handle status messages from Polygon WebSocket.

        Args:
            data: Status message data
        """
        status = data.get("status")
        message = data.get("message", "")

        if status == "auth_success":
            logger.info("✅ Authentication successful")
            # Resubscribe to previously subscribed symbols after successful authentication
            await self._resubscribe_to_symbols()
        elif status == "connected":
            logger.info(f"✅ Connection status: {message}")
        elif status == "success":
            logger.info(f"✅ Operation successful: {message}")
        else:
            logger.warning(f"⚠️ Status message: {status} - {message}")

    async def _handle_minute_aggregate(self, data: Dict[str, Any]):
        """
        Handle minute aggregate data from Polygon WebSocket.
        
        Args:
            data: Minute aggregate data
        """
        try:
            # Extract relevant fields from Polygon's minute aggregate format
            symbol = data.get("pair", "").replace("C:", "")  # Remove currency prefix
            
            if not symbol:
                logger.warning("⚠️ Received minute aggregate without symbol")
                return
            
            # Convert Polygon format to our standard format
            minute_aggregate = {
                "symbol": symbol,
                "timestamp": datetime.fromtimestamp(data.get("s", 0) / 1000, tz=timezone.utc).isoformat(),
                "open": data.get("o", 0),
                "high": data.get("h", 0),
                "low": data.get("l", 0),
                "close": data.get("c", 0),
                "volume": data.get("v", 0),
                "vwap": data.get("vw", 0),
                "transactions": data.get("n", 0)
            }
            
            logger.info(f"📊 Received minute aggregate for {symbol}: {minute_aggregate['close']}")
            
            # Call the callback if provided
            if self.on_minute_aggregate_callback:
                self.on_minute_aggregate_callback(minute_aggregate)
                
        except Exception as e:
            logger.error(f"❌ Error processing minute aggregate: {e}")

    async def subscribe_to_symbol(self, symbol: str):
        """
        Subscribe to minute aggregates for a forex pair.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        if not self.connected:
            logger.warning(f"⚠️ Cannot subscribe to {symbol}: not connected")
            return
        
        # Convert symbol to Polygon forex format (e.g., EURUSD -> CA.EUR/USD)
        # Polygon forex WebSocket expects CA.{from}/{to} format for Currency Aggregates
        if len(symbol) == 6:  # Standard forex pair like EURUSD
            forex_symbol = f"CA.{symbol[:3]}/{symbol[3:]}"  # EURUSD -> CA.EUR/USD
        else:
            forex_symbol = f"CA.{symbol}"  # Add CA. prefix if not standard format

        subscribe_message = {
            "action": "subscribe",
            "params": forex_symbol  # For forex: CA.EUR/USD format
        }
        
        logger.info(f"📈 Subscribing to minute aggregates for: {symbol}")
        await self._send_message(subscribe_message)
        
        self.subscribed_symbols.add(symbol)

    async def unsubscribe_from_symbol(self, symbol: str):
        """
        Unsubscribe from minute aggregates for a forex pair.
        
        Args:
            symbol: Forex pair symbol (e.g., 'EURUSD')
        """
        if not self.connected:
            logger.warning(f"⚠️ Cannot unsubscribe from {symbol}: not connected")
            return
        
        # Convert symbol to Polygon forex format (e.g., EURUSD -> CA.EUR/USD)
        if len(symbol) == 6:  # Standard forex pair like EURUSD
            forex_symbol = f"CA.{symbol[:3]}/{symbol[3:]}"  # EURUSD -> CA.EUR/USD
        else:
            forex_symbol = f"CA.{symbol}"  # Add CA. prefix if not standard format

        unsubscribe_message = {
            "action": "unsubscribe",
            "params": forex_symbol  # For forex: CA.EUR/USD format
        }
        
        logger.info(f"📉 Unsubscribing from minute aggregates for: {symbol}")
        await self._send_message(unsubscribe_message)
        
        self.subscribed_symbols.discard(symbol)

    async def _send_message(self, message: Dict[str, Any]):
        """
        Send a message to Polygon WebSocket.
        
        Args:
            message: Message to send
        """
        if not self.websocket or not self.connected:
            logger.warning("⚠️ Cannot send message: not connected")
            return
        
        try:
            message_json = json.dumps(message)
            logger.info(f"📤 Sending message to Polygon: {message}")
            await self.websocket.send(message_json)
            logger.info(f"✅ Message sent successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to send message: {e}")

    async def _resubscribe_to_symbols(self):
        """Resubscribe to all previously subscribed symbols after reconnection."""
        if not self.subscribed_symbols:
            logger.info("📭 No symbols to resubscribe to")
            return

        logger.info(f"🔄 Resubscribing to {len(self.subscribed_symbols)} symbols after reconnection: {list(self.subscribed_symbols)}")

        # Resubscribe to each symbol
        for symbol in self.subscribed_symbols.copy():  # Use copy to avoid modification during iteration
            try:
                # Convert symbol to Polygon forex format (e.g., EURUSD -> CA.EUR/USD)
                if len(symbol) == 6:  # Standard forex pair like EURUSD
                    forex_symbol = f"CA.{symbol[:3]}/{symbol[3:]}"  # EURUSD -> CA.EUR/USD
                else:
                    forex_symbol = f"CA.{symbol}"  # Add CA. prefix if not standard format

                subscribe_message = {
                    "action": "subscribe",
                    "params": forex_symbol
                }

                logger.info(f"🔄 Resubscribing to minute aggregates for: {symbol} (as {forex_symbol})")
                await self._send_message(subscribe_message)

            except Exception as e:
                logger.error(f"❌ Failed to resubscribe to {symbol}: {e}")

        logger.info("✅ Resubscription process completed")

    async def _reconnect(self):
        """Attempt to reconnect to Polygon WebSocket."""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"❌ Max reconnection attempts ({self.max_reconnect_attempts}) reached")
            self.running = False
            return

        self.reconnect_attempts += 1

        # Improved backoff strategy: cap at 60 seconds max delay
        base_delay = min(self.reconnect_delay * (2 ** (self.reconnect_attempts - 1)), 60)

        # Add some jitter to avoid thundering herd
        import random
        jitter = random.uniform(0.8, 1.2)
        delay = base_delay * jitter

        logger.info(f"🔄 Reconnection attempt {self.reconnect_attempts}/{self.max_reconnect_attempts} in {delay:.1f}s...")
        logger.info(f"💡 Using improved backoff: base={base_delay}s, jitter={jitter:.2f}")
        await asyncio.sleep(delay)

    def get_subscribed_symbols(self) -> Set[str]:
        """Get currently subscribed symbols."""
        return self.subscribed_symbols.copy()

    def is_connected(self) -> bool:
        """Check if WebSocket is connected."""
        return self.connected

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get detailed connection statistics."""
        current_time = datetime.now(timezone.utc)

        connection_duration = None
        if self.connection_start_time and self.connected:
            connection_duration = (current_time - self.connection_start_time).total_seconds()

        time_since_last_message = None
        if self.last_message_time:
            time_since_last_message = (current_time - self.last_message_time).total_seconds()

        return {
            "connected": self.connected,
            "running": self.running,
            "connection_start_time": self.connection_start_time.isoformat() if self.connection_start_time else None,
            "connection_duration_seconds": connection_duration,
            "last_message_time": self.last_message_time.isoformat() if self.last_message_time else None,
            "seconds_since_last_message": time_since_last_message,
            "total_disconnections": self.total_disconnections,
            "reconnect_attempts": self.reconnect_attempts,
            "max_reconnect_attempts": self.max_reconnect_attempts,
            "subscribed_symbols": list(self.subscribed_symbols),
            "ping_interval": self.ping_interval,
            "ping_timeout": self.ping_timeout
        }

    def _start_connection_refresh_task(self):
        """Start the connection refresh task to prevent stale connections."""
        if self.refresh_task:
            self.refresh_task.cancel()

        self.refresh_task = asyncio.create_task(self._connection_refresh_worker())
        logger.info(f"🔄 Started connection refresh task (refresh every {self.connection_refresh_hours} hours)")

    async def _connection_refresh_worker(self):
        """Worker task that refreshes the connection periodically."""
        while self.running and self.connected:
            try:
                # Wait for the refresh interval
                refresh_seconds = self.connection_refresh_hours * 3600
                await asyncio.sleep(refresh_seconds)

                if not self.running or not self.connected:
                    break

                # Check connection age
                if self.connection_start_time:
                    connection_age = (datetime.now(timezone.utc) - self.connection_start_time).total_seconds()
                    connection_hours = connection_age / 3600

                    if connection_hours >= self.connection_refresh_hours:
                        logger.info(f"🔄 Connection is {connection_hours:.1f} hours old, refreshing...")
                        await self._refresh_connection()
                    else:
                        logger.debug(f"🔄 Connection age: {connection_hours:.1f} hours (refresh at {self.connection_refresh_hours} hours)")

            except asyncio.CancelledError:
                logger.info("🔄 Connection refresh task cancelled")
                break
            except Exception as e:
                logger.error(f"❌ Error in connection refresh worker: {e}")
                # Continue the loop even if there's an error
                await asyncio.sleep(300)  # Wait 5 minutes before retrying

    async def _refresh_connection(self):
        """Refresh the WebSocket connection by disconnecting and reconnecting."""
        try:
            logger.info("🔄 Refreshing WebSocket connection to prevent staleness...")

            # Store current subscriptions
            current_subscriptions = self.subscribed_symbols.copy()

            # Disconnect current connection
            if self.websocket:
                await self.websocket.close()
                self.connected = False

            # Wait a moment
            await asyncio.sleep(2)

            # Reconnect
            await self.connect()

            # Resubscribe to all symbols
            if current_subscriptions:
                logger.info(f"🔄 Resubscribing to {len(current_subscriptions)} symbols after refresh...")
                await self._resubscribe_to_symbols()

            logger.info("✅ Connection refresh completed successfully")

        except Exception as e:
            logger.error(f"❌ Error during connection refresh: {e}")
            # If refresh fails, let the normal reconnection logic handle it

async def main():
    """Test function for the Polygon WebSocket client."""
    import os
    
    api_key = os.getenv('POLYGON_API_KEY')
    if not api_key:
        print("❌ POLYGON_API_KEY environment variable is required")
        return
    
    def on_minute_aggregate(data):
        print(f"📊 Received minute aggregate: {data}")
    
    client = PolygonWebSocketClient(api_key, on_minute_aggregate)
    
    try:
        # Start the client
        await client.start()
    except KeyboardInterrupt:
        print("👋 Test stopped by user")
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
