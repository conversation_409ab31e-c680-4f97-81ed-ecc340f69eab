# Docker Compose for Polygon WebSocket Ingestion Service
# For local development and testing

version: '3.8'

services:
  polygon-websocket-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: polygon-websocket-ingestion
    ports:
      - "8081:8081"  # Health check endpoint
    environment:
      - GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT:-oryntrade}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - HEALTH_CHECK_PORT=8081
      # For local development with emulator
      - PUBSUB_EMULATOR_HOST=pubsub-emulator:8085
    volumes:
      # Mount Google Cloud credentials for local development
      - ${GOOGLE_APPLICATION_CREDENTIALS:-~/.config/gcloud/application_default_credentials.json}:/app/credentials.json:ro
      # Mount source code for development (optional)
      - .:/app
    environment:
      - <PERSON>O<PERSON><PERSON>_APPLICATION_CREDENTIALS=/app/credentials.json
    depends_on:
      - pubsub-emulator
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Google Cloud Pub/Sub Emulator for local development
  pubsub-emulator:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    container_name: pubsub-emulator
    ports:
      - "8085:8085"
    command: >
      sh -c "
        gcloud beta emulators pubsub start --host-port=0.0.0.0:8085 --project=${GOOGLE_CLOUD_PROJECT:-oryntrade}
      "
    environment:
      - CLOUDSDK_CORE_PROJECT=${GOOGLE_CLOUD_PROJECT:-oryntrade}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

networks:
  default:
    name: polygon-websocket-network
