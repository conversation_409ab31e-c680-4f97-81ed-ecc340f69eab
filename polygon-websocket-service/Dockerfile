# Polygon WebSocket Ingestion Service Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Upgrade pip and install Python dependencies
RUN pip install --no-cache-dir --upgrade pip==23.3.1 && \
    pip install --no-cache-dir -r requirements.txt && \
    pip show polygon-api-client && \
    python -c "import polygon; print(f'Polygon version: {getattr(polygon, \"__version__\", \"unknown\")}'); import os; print(f'Polygon contents: {os.listdir(os.path.dirname(polygon.__file__))}'); from polygon.streaming import StreamClient; print('StreamClient import successful')"

# Copy application code
COPY . .

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Default configuration
ENV GOOGLE_CLOUD_PROJECT=oryntrade
ENV LOG_LEVEL=INFO
ENV PORT=8080

# Expose port (Cloud Run will set PORT env var)
EXPOSE 8080

# Health check (use PORT env var)
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8080}/health || exit 1

# Run the service
CMD ["python", "main.py"]
