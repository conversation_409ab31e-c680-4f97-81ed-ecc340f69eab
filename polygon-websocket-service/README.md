# Polygon WebSocket Ingestion Service

A high-performance WebSocket ingestion service that connects to Polygon.io and distributes real-time forex data to trade-bots via Google Cloud Pub/Sub.

## 🏗️ Architecture

```
Trade-Bot → polygon.subscription_commands → WebSocket Service → Polygon.io
                                                    ↓
Trade-Bot ← polygon.minute_aggregates ← WebSocket Service ← Polygon.io
```

## 📡 Pub/Sub Topics

### `polygon.subscription_commands`
- **Purpose**: Subscription management commands from trade-bots
- **Publishers**: Trade-Bot Backend
- **Subscribers**: Polygon WebSocket Ingestion Service
- **Message Format**:
```json
{
  "command": "subscribe|unsubscribe",
  "symbol": "EURUSD",
  "trade_bot_id": "bot_123",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### `polygon.minute_aggregates`
- **Purpose**: Real-time forex minute aggregate data
- **Publishers**: Polygon WebSocket Ingestion Service
- **Subscribers**: Trade-Bot Backend (with symbol filtering)
- **Message Format**:
```json
{
  "symbol": "EURUSD",
  "timestamp": "2024-01-01T00:00:00Z",
  "open": 1.0950,
  "high": 1.0955,
  "low": 1.0948,
  "close": 1.0952,
  "volume": 1000000,
  "vwap": 1.0951
}
```

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Google Cloud Project with Pub/Sub enabled
- Polygon.io API key
- Service account with Pub/Sub permissions

### Installation
```bash
cd polygon-websocket-service
pip install -r requirements.txt
```

### Configuration
```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running the Service
```bash
python main.py
```

## 🔧 Configuration

| Environment Variable | Description | Default |
|---------------------|-------------|---------|
| `POLYGON_API_KEY` | Polygon.io API key | Required |
| `GOOGLE_CLOUD_PROJECT` | GCP project ID | `oryntrade` |
| `SUBSCRIPTION_COMMANDS_TOPIC` | Subscription commands topic | `polygon.subscription_commands` |
| `MINUTE_AGGREGATES_TOPIC` | Minute aggregates topic | `polygon.minute_aggregates` |

## 📊 Monitoring

The service provides:
- Health check endpoint on port 8080
- Structured logging with configurable levels
- Metrics for subscription counts and message throughput
- Error tracking and alerting

## 🔄 Data Flow

1. **Subscription Management**:
   - Trade-bot publishes subscription command to `polygon.subscription_commands`
   - WebSocket service receives command and manages Polygon.io subscriptions
   - Service tracks active subscriptions per forex pair

2. **Data Ingestion**:
   - Service maintains WebSocket connection to Polygon.io
   - Receives real-time minute aggregate data
   - Publishes data to `polygon.minute_aggregates` with symbol attributes

3. **Data Distribution**:
   - Trade-bots subscribe to `polygon.minute_aggregates` with symbol filtering
   - Each trade-bot only receives data for their subscribed pairs
   - Automatic cleanup when trade-bots unsubscribe

## 🛡️ Error Handling

- Automatic WebSocket reconnection with exponential backoff
- Pub/Sub message acknowledgment and retry logic
- Graceful shutdown on SIGINT/SIGTERM
- Dead letter queues for failed messages

## 🚀 Deployment

The service is designed to run on Google Cloud Run with:
- Automatic scaling based on Pub/Sub message load
- Health checks for container orchestration
- Environment-based configuration
- Structured logging for Cloud Logging integration
