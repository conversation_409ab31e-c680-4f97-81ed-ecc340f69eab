# Polygon WebSocket Ingestion Service - Deployment Guide

This guide covers deployment options for the Polygon WebSocket Ingestion Service.

## 🏗️ Architecture Overview

```
Trade-Bot → polygon.subscription_commands → WebSocket Service → Polygon.io
                                                    ↓
Trade-Bot ← polygon.minute_aggregates ← WebSocket Service ← Polygon.io
```

## 📋 Prerequisites

### Required
- Google Cloud Project with billing enabled
- Polygon.io API key
- Docker installed
- gcloud CLI installed and authenticated

### APIs to Enable
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable pubsub.googleapis.com
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
```

## 🚀 Production Deployment (Google Cloud Run)

### 1. Quick Deployment
```bash
# Set environment variables
export GOOGLE_CLOUD_PROJECT="your-project-id"
export POLYGON_API_KEY="your-polygon-api-key"

# Run deployment script
./deploy.sh
```

### 2. Manual Deployment Steps

#### Step 1: Set up Pub/Sub Infrastructure
```bash
python setup_pubsub.py --project-id your-project-id
```

#### Step 2: Create Secret for API Key
```bash
echo -n "your-polygon-api-key" | gcloud secrets create polygon-api-key --data-file=-
```

#### Step 3: Build and Deploy
```bash
# Build image
docker build -t gcr.io/your-project-id/polygon-websocket-ingestion .

# Push to registry
docker push gcr.io/your-project-id/polygon-websocket-ingestion

# Deploy to Cloud Run
gcloud run deploy polygon-websocket-ingestion \
    --image gcr.io/your-project-id/polygon-websocket-ingestion \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --set-env-vars="GOOGLE_CLOUD_PROJECT=your-project-id" \
    --set-env-vars="POLYGON_API_KEY=your-polygon-api-key" \
    --memory=1Gi \
    --cpu=1 \
    --timeout=3600 \
    --min-instances=1 \
    --max-instances=10
```

## 🧪 Local Development

### Using Docker Compose
```bash
# Set environment variables
export POLYGON_API_KEY="your-polygon-api-key"
export GOOGLE_CLOUD_PROJECT="your-project-id"

# Start services
./run_local.sh
```

### Direct Python Execution
```bash
# Install dependencies
pip install -r requirements.txt

# Set up Pub/Sub topics (with emulator)
export PUBSUB_EMULATOR_HOST=localhost:8085
python setup_pubsub.py

# Run the service
python main.py
```

## 📊 Monitoring and Health Checks

### Health Endpoints
- **Health Check**: `GET /health` - Comprehensive health status
- **Metrics**: `GET /metrics` - Service metrics and statistics  
- **Status**: `GET /status` - Simple status summary
- **Info**: `GET /` - Service information

### Example Health Check Response
```json
{
  "service": "polygon-websocket-ingestion",
  "timestamp": "2024-01-01T00:00:00Z",
  "status": "healthy",
  "metrics": {
    "websocket_connected": true,
    "active_subscriptions": 5,
    "messages_per_minute": 120.5,
    "uptime_seconds": 3600
  },
  "checks": {
    "websocket_connection": {"status": "pass"},
    "data_flow": {"status": "pass"},
    "pubsub_publishing": {"status": "pass"}
  }
}
```

### Monitoring Commands
```bash
# View logs
gcloud logs tail --follow \
    --project=your-project-id \
    --resource-type=cloud_run_revision \
    --resource-labels=service_name=polygon-websocket-ingestion

# Check service status
curl https://your-service-url/health

# View metrics
curl https://your-service-url/metrics
```

## 🔧 Configuration

### Environment Variables
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `GOOGLE_CLOUD_PROJECT` | GCP project ID | `oryntrade` | Yes |
| `POLYGON_API_KEY` | Polygon.io API key | - | Yes |
| `LOG_LEVEL` | Logging level | `INFO` | No |
| `HEALTH_CHECK_PORT` | Health check port | `8081` | No |

### Pub/Sub Topics
- **polygon.subscription_commands** - Subscription management
- **polygon.minute_aggregates** - Real-time forex data

## 🔄 Trade-Bot Integration

### Configure Trade-Bots
Set the market data provider to use Pub/Sub:
```bash
export MARKET_DATA_PROVIDER=pubsub
```

### Trade-Bot Dockerfile Update
```dockerfile
ENV MARKET_DATA_PROVIDER=pubsub
```

## 🛡️ Security Considerations

### Service Account Permissions
The service requires these IAM roles:
- `roles/pubsub.publisher` - Publish to minute aggregates topic
- `roles/pubsub.subscriber` - Subscribe to subscription commands
- `roles/monitoring.metricWriter` - Write custom metrics
- `roles/logging.logWriter` - Write logs

### Network Security
- Service runs on private Google Cloud network
- Only health check endpoint exposed publicly
- WebSocket connection to Polygon.io uses TLS

## 📈 Scaling and Performance

### Resource Allocation
- **Memory**: 1GB (recommended minimum)
- **CPU**: 1 vCPU (can scale based on load)
- **Instances**: 1-10 (auto-scaling enabled)

### Performance Characteristics
- **Latency**: <100ms message processing
- **Throughput**: 1000+ messages/minute
- **Availability**: 99.9% uptime target

## 🚨 Troubleshooting

### Common Issues

#### WebSocket Connection Failures
```bash
# Check Polygon API key
curl "https://api.polygon.io/v1/meta/symbols/currency?apikey=YOUR_KEY"

# Check service logs
gcloud logs read --project=your-project-id --filter="resource.type=cloud_run_revision"
```

#### Pub/Sub Message Delivery Issues
```bash
# Check topic exists
gcloud pubsub topics list --project=your-project-id

# Check subscription exists  
gcloud pubsub subscriptions list --project=your-project-id

# Monitor message flow
gcloud pubsub topics publish polygon.subscription_commands --message='{"command":"subscribe","symbol":"EURUSD","trade_bot_id":"test"}'
```

#### High Error Rates
1. Check health endpoint: `/health`
2. Review error logs in Cloud Logging
3. Verify Polygon API rate limits
4. Check Pub/Sub quotas and limits

### Support
For issues and support:
1. Check service health endpoints
2. Review Cloud Logging for errors
3. Monitor Cloud Monitoring metrics
4. Check Polygon.io API status
