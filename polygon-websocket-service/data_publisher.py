#!/usr/bin/env python3
"""
Data Publisher for Polygon Minute Aggregates

This module handles publishing minute aggregate data received from Polygon.io
to Google Cloud Pub/Sub for distribution to trade-bots.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from google.cloud import pubsub_v1
from google.api_core import exceptions

logger = logging.getLogger(__name__)

class MinuteAggregatePublisher:
    """
    Publisher for minute aggregate data to Google Cloud Pub/Sub.
    """
    
    def __init__(self, project_id: str, topic_name: str = "polygon.minute_aggregates"):
        self.project_id = project_id
        self.topic_name = topic_name
        self.publisher = pubsub_v1.PublisherClient()
        
        # Topic path
        self.topic_path = self.publisher.topic_path(project_id, topic_name)
        
        # Publishing statistics
        self.messages_published = 0
        self.publish_errors = 0
        
        logger.info(f"📤 MinuteAggregatePublisher initialized")
        logger.info(f"📡 Topic path: {self.topic_path}")

    async def publish_minute_aggregate(self, data: Dict[str, Any]) -> bool:
        """
        Publish minute aggregate data to Pub/Sub.
        
        Args:
            data: Minute aggregate data from Polygon
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        try:
            # Validate the data
            if not self._validate_minute_aggregate(data):
                logger.error(f"❌ Invalid minute aggregate data: {data}")
                return False
            
            # Prepare the message
            message_data = json.dumps(data).encode('utf-8')
            
            # Add attributes for filtering
            attributes = {
                'symbol': data['symbol'],
                'timestamp': data['timestamp'],
                'source': 'polygon.io',
                'data_type': 'minute_aggregate'
            }
            
            # Publish the message
            future = self.publisher.publish(
                self.topic_path,
                message_data,
                **attributes
            )
            
            # Wait for the publish to complete (with timeout)
            message_id = future.result(timeout=10.0)
            
            self.messages_published += 1
            logger.info(f"✅ Published minute aggregate for {data['symbol']} (ID: {message_id})")
            
            return True
            
        except Exception as e:
            self.publish_errors += 1
            logger.error(f"❌ Failed to publish minute aggregate: {e}")
            return False

    def _validate_minute_aggregate(self, data: Dict[str, Any]) -> bool:
        """
        Validate minute aggregate data format.
        
        Args:
            data: Minute aggregate data to validate
            
        Returns:
            bool: True if data is valid
        """
        required_fields = [
            'symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume'
        ]
        
        for field in required_fields:
            if field not in data:
                logger.error(f"❌ Missing required field: {field}")
                return False
        
        # Validate symbol format
        symbol = data['symbol']
        if not isinstance(symbol, str) or len(symbol) < 3:
            logger.error(f"❌ Invalid symbol format: {symbol}")
            return False
        
        # Validate timestamp format
        try:
            datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
        except (ValueError, AttributeError) as e:
            logger.error(f"❌ Invalid timestamp format: {data['timestamp']} - {e}")
            return False
        
        # Validate numeric fields
        numeric_fields = ['open', 'high', 'low', 'close', 'volume']
        for field in numeric_fields:
            if not isinstance(data[field], (int, float)):
                logger.error(f"❌ Invalid {field} value: {data[field]}")
                return False
        
        # Validate OHLC logic
        if not (data['low'] <= data['open'] <= data['high'] and 
                data['low'] <= data['close'] <= data['high']):
            logger.warning(f"⚠️ Suspicious OHLC values for {symbol}: O={data['open']}, H={data['high']}, L={data['low']}, C={data['close']}")
        
        return True

    def publish_batch(self, data_list: list) -> Dict[str, int]:
        """
        Publish multiple minute aggregates in batch.
        
        Args:
            data_list: List of minute aggregate data
            
        Returns:
            Dict with success and error counts
        """
        results = {'success': 0, 'errors': 0}
        
        for data in data_list:
            if self.publish_minute_aggregate(data):
                results['success'] += 1
            else:
                results['errors'] += 1
        
        logger.info(f"📊 Batch publish results: {results['success']} success, {results['errors']} errors")
        return results

    def get_statistics(self) -> Dict[str, int]:
        """
        Get publishing statistics.
        
        Returns:
            Dict with publishing statistics
        """
        return {
            'messages_published': self.messages_published,
            'publish_errors': self.publish_errors,
            'success_rate': (
                self.messages_published / (self.messages_published + self.publish_errors)
                if (self.messages_published + self.publish_errors) > 0 else 0
            )
        }

    def reset_statistics(self):
        """Reset publishing statistics."""
        self.messages_published = 0
        self.publish_errors = 0
        logger.info("📊 Publishing statistics reset")

class DataPublisherManager:
    """
    Manager for handling multiple data publishers and routing.
    """
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.publishers = {}
        
        # Initialize minute aggregate publisher
        self.minute_aggregate_publisher = MinuteAggregatePublisher(project_id)
        
        logger.info(f"📤 DataPublisherManager initialized for project: {project_id}")

    async def publish_minute_aggregate(self, data: Dict[str, Any]) -> bool:
        """
        Publish minute aggregate data.
        
        Args:
            data: Minute aggregate data
            
        Returns:
            bool: True if published successfully
        """
        return await self.minute_aggregate_publisher.publish_minute_aggregate(data)

    def get_all_statistics(self) -> Dict[str, Dict[str, int]]:
        """
        Get statistics for all publishers.
        
        Returns:
            Dict with statistics for each publisher
        """
        return {
            'minute_aggregates': self.minute_aggregate_publisher.get_statistics()
        }

    def reset_all_statistics(self):
        """Reset statistics for all publishers."""
        self.minute_aggregate_publisher.reset_statistics()
        logger.info("📊 All publisher statistics reset")

def create_sample_minute_aggregate(symbol: str = "EURUSD") -> Dict[str, Any]:
    """
    Create a sample minute aggregate for testing.
    
    Args:
        symbol: Forex pair symbol
        
    Returns:
        Sample minute aggregate data
    """
    now = datetime.now(timezone.utc)
    
    return {
        "symbol": symbol,
        "timestamp": now.isoformat(),
        "open": 1.0950,
        "high": 1.0955,
        "low": 1.0948,
        "close": 1.0952,
        "volume": 1000000,
        "vwap": 1.0951,
        "transactions": 150
    }

async def main():
    """Test function for the data publisher."""
    import os
    
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
    
    # Create publisher manager
    manager = DataPublisherManager(project_id)
    
    # Create sample data
    sample_data = create_sample_minute_aggregate("EURUSD")
    
    # Publish sample data
    success = await manager.publish_minute_aggregate(sample_data)
    
    if success:
        print("✅ Sample minute aggregate published successfully!")
    else:
        print("❌ Failed to publish sample minute aggregate")
    
    # Print statistics
    stats = manager.get_all_statistics()
    print(f"📊 Statistics: {stats}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
