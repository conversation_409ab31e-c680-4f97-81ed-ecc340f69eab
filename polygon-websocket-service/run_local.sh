#!/bin/bash

# Local Development Script for Polygon WebSocket Ingestion Service
# This script sets up and runs the service locally with Pub/Sub emulator

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Polygon WebSocket Ingestion Service (Local Development)${NC}"
echo -e "${BLUE}================================================================${NC}"

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if [ -z "$POLYGON_API_KEY" ]; then
    echo -e "${RED}❌ POLYGON_API_KEY environment variable is required${NC}"
    echo -e "${YELLOW}💡 Set it with: export POLYGON_API_KEY=your_api_key_here${NC}"
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ docker-compose is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Set default project ID for local development
export GOOGLE_CLOUD_PROJECT=${GOOGLE_CLOUD_PROJECT:-"oryntrade"}
export LOG_LEVEL=${LOG_LEVEL:-"INFO"}

echo -e "${YELLOW}🔧 Configuration:${NC}"
echo -e "${YELLOW}   • Project ID: ${GOOGLE_CLOUD_PROJECT}${NC}"
echo -e "${YELLOW}   • Log Level: ${LOG_LEVEL}${NC}"
echo -e "${YELLOW}   • Polygon API Key: ${POLYGON_API_KEY:0:10}...${NC}"

# Start services with docker-compose
echo -e "${YELLOW}🐳 Starting services with Docker Compose...${NC}"
docker-compose up --build

echo -e "${GREEN}✅ Services started successfully!${NC}"
echo -e "${GREEN}================================${NC}"
echo -e "${GREEN}🌐 Service URL: http://localhost:8080${NC}"
echo -e "${GREEN}🏥 Health Check: http://localhost:8080/health${NC}"
echo -e "${GREEN}📊 Metrics: http://localhost:8080/metrics${NC}"
echo -e "${GREEN}📈 Status: http://localhost:8080/status${NC}"
echo -e "${GREEN}📡 Pub/Sub Emulator: http://localhost:8085${NC}"
echo ""
echo -e "${BLUE}📋 Development Notes:${NC}"
echo -e "${BLUE}• The service uses Pub/Sub emulator for local development${NC}"
echo -e "${BLUE}• Source code is mounted for live reloading${NC}"
echo -e "${BLUE}• Press Ctrl+C to stop all services${NC}"
echo ""
echo -e "${YELLOW}🧪 Testing Commands:${NC}"
echo -e "${YELLOW}• Health check: curl http://localhost:8080/health${NC}"
echo -e "${YELLOW}• Metrics: curl http://localhost:8080/metrics${NC}"
echo -e "${YELLOW}• View logs: docker-compose logs -f polygon-websocket-service${NC}"
