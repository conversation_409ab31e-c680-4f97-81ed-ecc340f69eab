# Google Cloud Run Service Configuration
# For Polygon WebSocket Ingestion Service

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: polygon-websocket-ingestion
  annotations:
    # Cloud Run specific annotations
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    # Scaling configuration
    autoscaling.knative.dev/minScale: "1"
    autoscaling.knative.dev/maxScale: "10"
    # CPU allocation during request processing only
    run.googleapis.com/cpu-throttling: "false"
spec:
  template:
    metadata:
      annotations:
        # Resource allocation
        run.googleapis.com/memory: "1Gi"
        run.googleapis.com/cpu: "1000m"
        # Timeout configuration
        run.googleapis.com/timeout: "3600s"
        # Concurrency
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/execution-environment: gen2
    spec:
      # Service account for Pub/Sub access
      serviceAccountName: polygon-websocket-service@PROJECT_ID.iam.gserviceaccount.com
      containerConcurrency: 1000
      timeoutSeconds: 3600
      containers:
      - name: polygon-websocket-ingestion
        image: gcr.io/PROJECT_ID/polygon-websocket-ingestion:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: POLYGON_API_KEY
          valueFrom:
            secretKeyRef:
              name: polygon-api-key
              key: api-key
        - name: LOG_LEVEL
          value: "INFO"
        - name: HEALTH_CHECK_PORT
          value: "8080"
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
        # Health check configuration
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
  traffic:
  - percent: 100
    latestRevision: true
