#!/usr/bin/env python3
"""
Monitoring and Health Check Module

This module provides health monitoring, metrics collection, and alerting
for the Polygon WebSocket Ingestion Service.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

from google.cloud import monitoring_v3
from google.cloud import error_reporting

logger = logging.getLogger(__name__)

@dataclass
class ServiceMetrics:
    """Service metrics data structure."""
    # Connection metrics
    websocket_connected: bool = False
    websocket_reconnections: int = 0
    last_reconnection_time: Optional[str] = None
    
    # Subscription metrics
    active_subscriptions: int = 0
    subscription_commands_received: int = 0
    subscription_commands_processed: int = 0
    subscription_errors: int = 0
    
    # Data flow metrics
    messages_received_from_polygon: int = 0
    messages_published_to_pubsub: int = 0
    publish_errors: int = 0
    last_message_time: Optional[str] = None
    
    # Performance metrics
    average_message_processing_time: float = 0.0
    messages_per_minute: float = 0.0
    
    # Health status
    service_status: str = "starting"  # starting, healthy, degraded, unhealthy
    last_health_check: Optional[str] = None
    uptime_seconds: int = 0

class HealthMonitor:
    """Health monitoring and metrics collection for the WebSocket service."""
    
    def __init__(self, project_id: str, service_name: str = "polygon-websocket-ingestion"):
        self.project_id = project_id
        self.service_name = service_name
        self.metrics = ServiceMetrics()
        self.start_time = time.time()
        
        # Google Cloud Monitoring client
        try:
            self.monitoring_client = monitoring_v3.MetricServiceClient()
            self.error_client = error_reporting.Client()
            self.monitoring_enabled = True
            logger.info("✅ Google Cloud Monitoring enabled")
        except Exception as e:
            logger.warning(f"⚠️ Google Cloud Monitoring not available: {e}")
            self.monitoring_enabled = False
        
        # Message processing tracking
        self.message_processing_times = []
        self.message_count_window = []
        self.window_size = 60  # 1 minute window
        
        logger.info(f"📊 HealthMonitor initialized for service: {service_name}")

    def update_websocket_status(self, connected: bool, reconnection: bool = False):
        """Update WebSocket connection status."""
        self.metrics.websocket_connected = connected
        
        if reconnection:
            self.metrics.websocket_reconnections += 1
            self.metrics.last_reconnection_time = datetime.now(timezone.utc).isoformat()
            logger.info(f"🔄 WebSocket reconnection #{self.metrics.websocket_reconnections}")

    def update_subscription_metrics(self, active_count: int, command_received: bool = False, 
                                  command_processed: bool = False, error: bool = False):
        """Update subscription-related metrics."""
        self.metrics.active_subscriptions = active_count
        
        if command_received:
            self.metrics.subscription_commands_received += 1
        
        if command_processed:
            self.metrics.subscription_commands_processed += 1
        
        if error:
            self.metrics.subscription_errors += 1

    def record_message_received(self):
        """Record a message received from Polygon."""
        self.metrics.messages_received_from_polygon += 1
        self.metrics.last_message_time = datetime.now(timezone.utc).isoformat()
        
        # Update message count window
        current_time = time.time()
        self.message_count_window.append(current_time)
        
        # Remove old entries (older than window_size seconds)
        cutoff_time = current_time - self.window_size
        self.message_count_window = [t for t in self.message_count_window if t > cutoff_time]
        
        # Calculate messages per minute
        self.metrics.messages_per_minute = len(self.message_count_window) * (60 / self.window_size)

    def record_message_published(self, success: bool = True, processing_time: float = 0.0):
        """Record a message published to Pub/Sub."""
        if success:
            self.metrics.messages_published_to_pubsub += 1
        else:
            self.metrics.publish_errors += 1
        
        # Track processing time
        if processing_time > 0:
            self.message_processing_times.append(processing_time)
            
            # Keep only recent processing times (last 100)
            if len(self.message_processing_times) > 100:
                self.message_processing_times = self.message_processing_times[-100:]
            
            # Calculate average processing time
            self.metrics.average_message_processing_time = sum(self.message_processing_times) / len(self.message_processing_times)

    def update_service_status(self, status: str):
        """Update overall service status."""
        if status != self.metrics.service_status:
            logger.info(f"🔄 Service status changed: {self.metrics.service_status} → {status}")
            self.metrics.service_status = status

    def perform_health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check.
        
        Returns:
            Dict containing health check results
        """
        current_time = datetime.now(timezone.utc)
        self.metrics.last_health_check = current_time.isoformat()
        self.metrics.uptime_seconds = int(time.time() - self.start_time)
        
        # Determine overall health status
        health_status = self._calculate_health_status()
        self.update_service_status(health_status)
        
        health_report = {
            "service": self.service_name,
            "timestamp": current_time.isoformat(),
            "status": health_status,
            "metrics": asdict(self.metrics),
            "checks": self._run_health_checks()
        }
        
        # Log health status
        if health_status == "healthy":
            logger.info(f"✅ Health check passed - Service is {health_status}")
        elif health_status == "degraded":
            logger.warning(f"⚠️ Health check warning - Service is {health_status}")
        else:
            logger.error(f"❌ Health check failed - Service is {health_status}")
        
        return health_report

    def _calculate_health_status(self) -> str:
        """Calculate overall health status based on metrics."""
        # Check critical conditions
        if not self.metrics.websocket_connected:
            return "unhealthy"
        
        # Check error rates
        total_commands = self.metrics.subscription_commands_received
        if total_commands > 0:
            error_rate = self.metrics.subscription_errors / total_commands
            if error_rate > 0.1:  # More than 10% error rate
                return "degraded"
        
        total_publishes = self.metrics.messages_published_to_pubsub + self.metrics.publish_errors
        if total_publishes > 0:
            publish_error_rate = self.metrics.publish_errors / total_publishes
            if publish_error_rate > 0.05:  # More than 5% publish error rate
                return "degraded"
        
        # Check if we're receiving data
        if self.metrics.messages_received_from_polygon == 0 and self.metrics.uptime_seconds > 300:
            return "degraded"  # No messages after 5 minutes
        
        return "healthy"

    def _run_health_checks(self) -> Dict[str, Any]:
        """Run individual health checks."""
        checks = {}
        
        # WebSocket connection check
        checks["websocket_connection"] = {
            "status": "pass" if self.metrics.websocket_connected else "fail",
            "message": "WebSocket connected to Polygon.io" if self.metrics.websocket_connected else "WebSocket disconnected"
        }
        
        # Subscription processing check
        total_commands = self.metrics.subscription_commands_received
        if total_commands > 0:
            success_rate = (total_commands - self.metrics.subscription_errors) / total_commands
            checks["subscription_processing"] = {
                "status": "pass" if success_rate > 0.9 else "warn" if success_rate > 0.8 else "fail",
                "message": f"Subscription command success rate: {success_rate:.2%}",
                "success_rate": success_rate
            }
        
        # Data flow check
        checks["data_flow"] = {
            "status": "pass" if self.metrics.messages_per_minute > 0 else "warn",
            "message": f"Receiving {self.metrics.messages_per_minute:.1f} messages per minute",
            "messages_per_minute": self.metrics.messages_per_minute
        }
        
        # Pub/Sub publishing check
        total_publishes = self.metrics.messages_published_to_pubsub + self.metrics.publish_errors
        if total_publishes > 0:
            publish_success_rate = self.metrics.messages_published_to_pubsub / total_publishes
            checks["pubsub_publishing"] = {
                "status": "pass" if publish_success_rate > 0.95 else "warn" if publish_success_rate > 0.9 else "fail",
                "message": f"Pub/Sub publish success rate: {publish_success_rate:.2%}",
                "success_rate": publish_success_rate
            }
        
        return checks

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of current metrics."""
        return {
            "service": self.service_name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime_seconds": int(time.time() - self.start_time),
            "status": self.metrics.service_status,
            "websocket_connected": self.metrics.websocket_connected,
            "active_subscriptions": self.metrics.active_subscriptions,
            "messages_per_minute": self.metrics.messages_per_minute,
            "total_messages_received": self.metrics.messages_received_from_polygon,
            "total_messages_published": self.metrics.messages_published_to_pubsub,
            "error_rates": {
                "subscription_errors": self.metrics.subscription_errors,
                "publish_errors": self.metrics.publish_errors
            }
        }

    def report_error(self, error: Exception, context: str = ""):
        """Report an error to Google Cloud Error Reporting."""
        try:
            if self.monitoring_enabled and self.error_client:
                self.error_client.report_exception(
                    http_context={"method": "WebSocket", "url": context}
                )
                logger.info(f"📊 Error reported to Google Cloud: {error}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to report error to Google Cloud: {e}")

async def start_health_monitoring(monitor: HealthMonitor, interval: int = 30):
    """
    Start periodic health monitoring.
    
    Args:
        monitor: HealthMonitor instance
        interval: Health check interval in seconds
    """
    logger.info(f"🏥 Starting health monitoring (interval: {interval}s)")
    
    while True:
        try:
            health_report = monitor.perform_health_check()
            
            # Log metrics summary every 5 minutes
            if monitor.metrics.uptime_seconds % 300 == 0:
                summary = monitor.get_metrics_summary()
                logger.info(f"📊 Metrics summary: {json.dumps(summary, indent=2)}")
            
        except Exception as e:
            logger.error(f"❌ Error in health monitoring: {e}")
            monitor.report_error(e, "health_monitoring")
        
        await asyncio.sleep(interval)

def main():
    """Test function for the monitoring module."""
    import os
    
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
    monitor = HealthMonitor(project_id)
    
    # Simulate some metrics
    monitor.update_websocket_status(True)
    monitor.update_subscription_metrics(5, command_received=True, command_processed=True)
    monitor.record_message_received()
    monitor.record_message_published(success=True, processing_time=0.05)
    
    # Perform health check
    health_report = monitor.perform_health_check()
    print(json.dumps(health_report, indent=2))

if __name__ == "__main__":
    main()
