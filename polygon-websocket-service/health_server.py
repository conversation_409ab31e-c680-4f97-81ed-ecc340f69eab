#!/usr/bin/env python3
"""
Health Check HTTP Server

This module provides a simple HTTP server for health checks and metrics
for the Polygon WebSocket Ingestion Service.
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Optional

from aiohttp import web, web_request, web_response
from monitoring import HealthMonitor

logger = logging.getLogger(__name__)

class HealthCheckServer:
    """HTTP server for health checks and metrics."""
    
    def __init__(self, health_monitor: HealthMonitor, port: int = 8081):
        self.health_monitor = health_monitor
        self.port = port
        self.app = web.Application()
        self.runner = None
        self.site = None
        
        # Setup routes
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/metrics', self.metrics)
        self.app.router.add_get('/status', self.status)
        self.app.router.add_get('/', self.root)
        
        logger.info(f"🏥 HealthCheckServer initialized on port {port}")

    async def start(self):
        """Start the health check server."""
        try:
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(self.runner, '0.0.0.0', self.port)
            await self.site.start()
            
            logger.info(f"✅ Health check server started on port {self.port}")
            logger.info(f"🔗 Health endpoints:")
            logger.info(f"   • http://0.0.0.0:{self.port}/health - Health check")
            logger.info(f"   • http://0.0.0.0:{self.port}/metrics - Metrics")
            logger.info(f"   • http://0.0.0.0:{self.port}/status - Status summary")
            
        except Exception as e:
            logger.error(f"❌ Failed to start health check server: {e}")
            raise

    async def stop(self):
        """Stop the health check server."""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
        logger.info("🛑 Health check server stopped")

    async def health_check(self, request: web_request.Request) -> web_response.Response:
        """
        Health check endpoint.
        
        Returns:
            HTTP 200 if healthy, 503 if unhealthy
        """
        try:
            health_report = self.health_monitor.perform_health_check()
            
            # Determine HTTP status code based on health
            if health_report['status'] == 'healthy':
                status_code = 200
            elif health_report['status'] == 'degraded':
                status_code = 200  # Still operational
            else:
                status_code = 503  # Service unavailable
            
            return web.json_response(
                health_report,
                status=status_code
            )
            
        except Exception as e:
            logger.error(f"❌ Error in health check: {e}")
            return web.json_response(
                {
                    'status': 'error',
                    'message': str(e),
                    'timestamp': datetime.now(timezone.utc).isoformat()
                },
                status=500
            )

    async def metrics(self, request: web_request.Request) -> web_response.Response:
        """
        Metrics endpoint.
        
        Returns:
            JSON response with service metrics
        """
        try:
            metrics_summary = self.health_monitor.get_metrics_summary()
            
            return web.json_response(metrics_summary)
            
        except Exception as e:
            logger.error(f"❌ Error getting metrics: {e}")
            return web.json_response(
                {
                    'error': str(e),
                    'timestamp': datetime.now(timezone.utc).isoformat()
                },
                status=500
            )

    async def status(self, request: web_request.Request) -> web_response.Response:
        """
        Simple status endpoint.
        
        Returns:
            Simple status response
        """
        try:
            metrics = self.health_monitor.get_metrics_summary()
            
            status_response = {
                'service': 'polygon-websocket-ingestion',
                'status': metrics['status'],
                'uptime_seconds': metrics['uptime_seconds'],
                'websocket_connected': metrics['websocket_connected'],
                'active_subscriptions': metrics['active_subscriptions'],
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            return web.json_response(status_response)
            
        except Exception as e:
            logger.error(f"❌ Error getting status: {e}")
            return web.json_response(
                {
                    'service': 'polygon-websocket-ingestion',
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now(timezone.utc).isoformat()
                },
                status=500
            )

    async def root(self, request: web_request.Request) -> web_response.Response:
        """
        Root endpoint with service information.
        
        Returns:
            Service information and available endpoints
        """
        service_info = {
            'service': 'Polygon WebSocket Ingestion Service',
            'version': '1.0.0',
            'description': 'Real-time forex data ingestion from Polygon.io via WebSocket',
            'endpoints': {
                '/': 'Service information',
                '/health': 'Health check with detailed status',
                '/metrics': 'Service metrics and statistics',
                '/status': 'Simple status summary'
            },
            'architecture': {
                'input': 'Polygon.io WebSocket API',
                'output': 'Google Cloud Pub/Sub (polygon.minute_aggregates)',
                'commands': 'Google Cloud Pub/Sub (polygon.subscription_commands)'
            },
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        return web.json_response(service_info)

async def start_health_server(health_monitor: HealthMonitor, port: int = 8081) -> HealthCheckServer:
    """
    Start the health check server.
    
    Args:
        health_monitor: HealthMonitor instance
        port: Port to run the server on
        
    Returns:
        HealthCheckServer instance
    """
    server = HealthCheckServer(health_monitor, port)
    await server.start()
    return server

def main():
    """Test function for the health check server."""
    import os
    from monitoring import HealthMonitor
    
    async def test_server():
        project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
        health_monitor = HealthMonitor(project_id)
        
        # Simulate some activity
        health_monitor.update_websocket_status(True)
        health_monitor.update_subscription_metrics(3)
        health_monitor.record_message_received()
        health_monitor.record_message_published(success=True, processing_time=0.05)
        
        # Start server
        server = await start_health_server(health_monitor, 8081)
        
        try:
            print("🏥 Health check server running on http://localhost:8081")
            print("📊 Try these endpoints:")
            print("   • http://localhost:8081/")
            print("   • http://localhost:8081/health")
            print("   • http://localhost:8081/metrics")
            print("   • http://localhost:8081/status")
            print("\nPress Ctrl+C to stop...")
            
            # Keep running
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n👋 Stopping server...")
            await server.stop()
    
    asyncio.run(test_server())

if __name__ == "__main__":
    main()
