#!/usr/bin/env python3
"""
Test Trade-Bot Integration

This script simulates a trade-bot using the new Pub/Sub market data provider
to test the complete data flow.
"""

import asyncio
import os
import sys
import time
from datetime import datetime, timezone

# Add the trade-bot-service to the path
sys.path.append('../trade-bot-service')

from data.pubsub_market_data import PubSubMarketDataProvider

class TestTradeBot:
    """Simple test trade-bot to verify Pub/Sub integration."""
    
    def __init__(self, project_id: str, bot_id: str):
        self.project_id = project_id
        self.bot_id = bot_id
        self.candles_received = 0
        self.start_time = time.time()
        
        # Initialize the Pub/Sub market data provider
        self.market_data_provider = PubSubMarketDataProvider(
            project_id=project_id,
            trade_bot_id=bot_id,
            on_candle_callback=self.on_candle_received
        )
        
        print(f"🤖 Test Trade-Bot initialized: {bot_id}")
        print(f"📡 Project ID: {project_id}")

    def on_candle_received(self, candle_data):
        """Handle incoming candle data."""
        self.candles_received += 1
        
        print(f"📊 [{self.bot_id}] Received candle #{self.candles_received}:")
        print(f"   • Symbol: {candle_data['symbol']}")
        print(f"   • Close: {candle_data['close']}")
        print(f"   • Timestamp: {candle_data['timestamp']}")
        print(f"   • Volume: {candle_data.get('volume', 'N/A')}")
        print()

    async def start_trading(self):
        """Start the trading bot."""
        print(f"🚀 Starting trade-bot: {self.bot_id}")
        
        # Start the market data provider
        self.market_data_provider.start()
        
        # Subscribe to forex pairs
        symbols = ['EURUSD', 'GBPUSD']
        
        for symbol in symbols:
            print(f"📈 Subscribing to {symbol}...")
            self.market_data_provider.subscribe_to_symbol(symbol)
            await asyncio.sleep(1)  # Small delay between subscriptions
        
        print(f"✅ Subscribed to {len(symbols)} symbols")
        print("👂 Listening for real-time data...")
        
        # Run for a specified duration
        duration = 120  # 2 minutes
        print(f"⏱️ Running for {duration} seconds...")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            await asyncio.sleep(10)
            
            # Print statistics every 10 seconds
            stats = self.market_data_provider.get_statistics()
            elapsed = time.time() - self.start_time
            
            print(f"📊 Statistics (after {elapsed:.0f}s):")
            print(f"   • Candles received: {self.candles_received}")
            print(f"   • Messages received: {stats['messages_received']}")
            print(f"   • Subscribed symbols: {stats['subscribed_symbols']}")
            print(f"   • Provider running: {stats['running']}")
            print()
        
        print("⏰ Test duration completed")
        await self.stop_trading()

    async def stop_trading(self):
        """Stop the trading bot."""
        print(f"🛑 Stopping trade-bot: {self.bot_id}")
        
        # Unsubscribe from all symbols
        subscribed_symbols = self.market_data_provider.get_subscribed_symbols()
        for symbol in subscribed_symbols:
            print(f"📉 Unsubscribing from {symbol}...")
            self.market_data_provider.unsubscribe_from_symbol(symbol)
            await asyncio.sleep(0.5)
        
        # Stop the market data provider
        self.market_data_provider.stop()
        
        # Final statistics
        elapsed = time.time() - self.start_time
        print(f"📊 Final Statistics:")
        print(f"   • Total runtime: {elapsed:.1f} seconds")
        print(f"   • Total candles received: {self.candles_received}")
        print(f"   • Average candles per minute: {(self.candles_received / elapsed) * 60:.1f}")
        print()
        print("✅ Trade-bot stopped successfully")

async def main():
    """Main test function."""
    print("🧪 POLYGON WEBSOCKET INTEGRATION TEST")
    print("=" * 50)
    
    # Configuration
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
    bot_id = f"test-bot-{int(time.time())}"
    
    print(f"🔧 Test Configuration:")
    print(f"   • Project ID: {project_id}")
    print(f"   • Bot ID: {bot_id}")
    print(f"   • Pub/Sub Emulator: {os.getenv('PUBSUB_EMULATOR_HOST', 'Not set')}")
    print()
    
    # Create and run test bot
    test_bot = TestTradeBot(project_id, bot_id)
    
    try:
        await test_bot.start_trading()
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        await test_bot.stop_trading()
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        await test_bot.stop_trading()
        raise

if __name__ == "__main__":
    # Set up environment for local testing
    os.environ['PUBSUB_EMULATOR_HOST'] = 'localhost:8085'
    os.environ['GOOGLE_CLOUD_PROJECT'] = 'oryntrade'
    
    print("🌟 Starting Polygon WebSocket Integration Test...")
    print("📋 Prerequisites:")
    print("   ✅ Pub/Sub emulator running on localhost:8085")
    print("   ✅ Polygon WebSocket service running")
    print("   ✅ Pub/Sub topics and subscriptions created")
    print()
    
    asyncio.run(main())
