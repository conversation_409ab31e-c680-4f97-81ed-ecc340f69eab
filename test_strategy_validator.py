#!/usr/bin/env python3
"""
Universal Strategy JSON Validator for Trade Bot

This script validates any strategy JSON to ensure it can be run on the trade bot.
It checks:
- JSON parsing and validation
- Indicator calculations
- Signal detection logic
- Risk management configuration
- Position sizing
- Error handling

Usage:
    python test_strategy_validator.py <strategy_file.json>
    python test_strategy_validator.py --json '<json_string>'
    python test_strategy_validator.py --interactive
"""

import json
import sys
import os
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add the trade-bot-service directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'trade-bot-service'))

from strategies.base_strategy import BaseStrategy

def generate_realistic_test_candles(count=150, start_price=1.1000):
    """Generate realistic test candles that will create various indicator patterns."""
    candles = []
    current_price = start_price
    base_time = datetime.now() - timedelta(minutes=count * 5)

    for i in range(count):
        # Create varied price movement patterns
        cycle = i % 40

        if cycle < 10:
            # Sideways movement
            price_change = 0.00005 * (1 if i % 2 == 0 else -1) * (i % 3 + 1)
        elif cycle < 20:
            # Uptrend with pullbacks
            if i % 5 == 0:
                price_change = -0.0001  # Pullback
            else:
                price_change = 0.0002 + (0.0001 * (i % 3))
        elif cycle < 30:
            # Downtrend with bounces
            if i % 6 == 0:
                price_change = 0.0001  # Bounce
            else:
                price_change = -0.0002 - (0.0001 * (i % 2))
        else:
            # Volatile movement
            price_change = 0.0003 * (1 if i % 2 == 0 else -1) * (i % 4 + 1)

        current_price += price_change

        # Ensure price stays reasonable
        current_price = max(0.8000, min(1.5000, current_price))

        candle_time = base_time + timedelta(minutes=i * 5)

        # Create realistic OHLC
        spread = 0.00005
        high_low_range = abs(price_change) + 0.00010
        candle = {
            "time": candle_time.isoformat() + "Z",
            "open": current_price - spread,
            "high": current_price + high_low_range,
            "low": current_price - high_low_range,
            "close": current_price,
            "volume": 1000 + (i % 500)
        }
        candles.append(candle)

    return candles

def validate_strategy_json(strategy_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    Comprehensive validation of strategy JSON for trade bot compatibility.

    Args:
        strategy_json (Dict[str, Any]): Strategy configuration

    Returns:
        Dict[str, Any]: Validation results
    """
    results = {
        "valid": False,
        "errors": [],
        "warnings": [],
        "components": {
            "json_parsing": False,
            "strategy_initialization": False,
            "indicators": False,
            "signal_detection": False,
            "risk_management": False,
            "position_sizing": False
        },
        "details": {},
        "signals_generated": []
    }

    print("🧪 Universal Strategy JSON Validator")
    print("=" * 60)

    try:
        # Step 1: JSON Structure Validation
        print("📋 Step 1: Validating JSON structure...")
        required_fields = ["name", "instruments", "timeframe", "indicators", "entryRules", "riskManagement"]
        missing_fields = [field for field in required_fields if field not in strategy_json]

        if missing_fields:
            results["errors"].append(f"Missing required fields: {missing_fields}")
            return results

        results["components"]["json_parsing"] = True
        print("   ✅ JSON structure valid")

        # Step 2: Strategy Initialization
        print("📋 Step 2: Initializing strategy...")
        try:
            strategy = BaseStrategy(json.dumps(strategy_json))
            results["components"]["strategy_initialization"] = True
            print("   ✅ Strategy initialized successfully")

            # Capture strategy details
            results["details"]["name"] = strategy.name
            results["details"]["instrument"] = strategy.instrument
            results["details"]["timeframe"] = strategy.timeframe
            results["details"]["risk_percentage"] = strategy.risk_percentage
            results["details"]["stop_loss_method"] = strategy.stop_loss_method

        except Exception as e:
            results["errors"].append(f"Strategy initialization failed: {str(e)}")
            return results

        # Step 3: Indicator Validation
        print("📋 Step 3: Validating indicators...")
        test_candles = generate_realistic_test_candles(150)

        try:
            # Test indicator calculation
            indicators = strategy.calculate_indicators(test_candles)

            if not indicators:
                results["warnings"].append("No indicators calculated")
            else:
                results["components"]["indicators"] = True
                results["details"]["indicators_calculated"] = list(indicators.keys())
                print(f"   ✅ Calculated {len(indicators)} indicator types")

                # Check each indicator has values
                for indicator_name, values in indicators.items():
                    if values and len(values) > 0:
                        valid_values = [v for v in values if v is not None and v != 0.0]
                        print(f"   📊 {indicator_name}: {len(valid_values)} valid values")
                    else:
                        results["warnings"].append(f"Indicator {indicator_name} has no valid values")

        except Exception as e:
            results["errors"].append(f"Indicator calculation failed: {str(e)}")
            return results

        # Step 4: Signal Detection Testing
        print("📋 Step 4: Testing signal detection...")
        try:
            # Test multiple scenarios with different candle sets
            test_scenarios = [
                {"candles": test_candles[:50], "description": "Early data"},
                {"candles": test_candles[:100], "description": "Mid data"},
                {"candles": test_candles, "description": "Full data"}
            ]

            signals_found = False
            for i, scenario in enumerate(test_scenarios):
                market_data = {
                    "candles": scenario["candles"],
                    "indicators": {}
                }

                result = strategy.update(market_data, 10000.0, [])
                signals = result.get("signals", {})

                if signals.get("action"):
                    signals_found = True
                    signal_info = f"{signals['action']} - {signals.get('reason', 'No reason')}"
                    results["signals_generated"].append(signal_info)
                    print(f"   🎯 Signal in scenario {i+1}: {signal_info}")

            if signals_found:
                results["components"]["signal_detection"] = True
                print("   ✅ Signal detection working")
            else:
                results["warnings"].append("No signals generated in test scenarios")
                results["components"]["signal_detection"] = True  # Still valid, just no signals
                print("   ⚠️  No signals generated (may be normal depending on strategy)")

        except Exception as e:
            results["errors"].append(f"Signal detection failed: {str(e)}")
            return results

        # Step 5: Risk Management Validation
        print("📋 Step 5: Validating risk management...")
        try:
            risk_details = {
                "risk_percentage": strategy.risk_percentage,
                "stop_loss_method": strategy.stop_loss_method,
                "stop_loss": strategy.stop_loss,
                "take_profit": strategy.take_profit,
                "risk_reward_ratio": strategy.risk_reward_ratio
            }

            results["details"]["risk_management"] = risk_details
            results["components"]["risk_management"] = True
            print(f"   ✅ Risk management configured: {strategy.stop_loss_method}")
            print(f"   📊 Risk: {strategy.risk_percentage}%, SL: {strategy.stop_loss}, TP: {strategy.take_profit}")

        except Exception as e:
            results["errors"].append(f"Risk management validation failed: {str(e)}")
            return results

        # Step 6: Position Sizing Testing
        print("📋 Step 6: Testing position sizing...")
        try:
            test_accounts = [1000.0, 10000.0, 50000.0]
            test_price = 1.1000

            for balance in test_accounts:
                position_size = strategy.get_position_size(balance, test_price)
                if position_size > 0:
                    print(f"   💰 ${balance:,.0f} account → {position_size:,.0f} units")
                else:
                    results["warnings"].append(f"Zero position size for ${balance} account")

            results["components"]["position_sizing"] = True
            print("   ✅ Position sizing working")

        except Exception as e:
            results["errors"].append(f"Position sizing failed: {str(e)}")
            return results

        # Final validation
        if all(results["components"].values()):
            results["valid"] = True
            print("\n🎉 Strategy validation PASSED!")
        else:
            print("\n❌ Strategy validation FAILED!")

    except Exception as e:
        results["errors"].append(f"Unexpected error: {str(e)}")

    return results

def print_validation_summary(results: Dict[str, Any]):
    """Print a detailed summary of validation results."""
    print("\n" + "=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)

    # Overall status
    status = "✅ PASSED" if results["valid"] else "❌ FAILED"
    print(f"Overall Status: {status}")

    # Component status
    print(f"\n📊 Component Status:")
    for component, status in results["components"].items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {component.replace('_', ' ').title()}")

    # Strategy details
    if results["details"]:
        print(f"\n📋 Strategy Details:")
        for key, value in results["details"].items():
            if key != "risk_management":
                print(f"   • {key.replace('_', ' ').title()}: {value}")

        if "risk_management" in results["details"]:
            print(f"   • Risk Management:")
            for key, value in results["details"]["risk_management"].items():
                print(f"     - {key.replace('_', ' ').title()}: {value}")

    # Signals generated
    if results["signals_generated"]:
        print(f"\n🎯 Signals Generated ({len(results['signals_generated'])}):")
        for i, signal in enumerate(results["signals_generated"], 1):
            print(f"   {i}. {signal}")

    # Warnings
    if results["warnings"]:
        print(f"\n⚠️  Warnings ({len(results['warnings'])}):")
        for warning in results["warnings"]:
            print(f"   • {warning}")

    # Errors
    if results["errors"]:
        print(f"\n❌ Errors ({len(results['errors'])}):")
        for error in results["errors"]:
            print(f"   • {error}")

    # Recommendations
    print(f"\n💡 Recommendations:")
    if results["valid"]:
        print("   ✅ Strategy is ready for trade bot deployment!")
        print("   ✅ All components are working correctly")
        if results["warnings"]:
            print("   ⚠️  Review warnings above for potential improvements")
    else:
        print("   ❌ Fix the errors above before deploying to trade bot")
        print("   📚 Check strategy JSON format and indicator configurations")

def main():
    """Main function to handle command line arguments and run validation."""
    parser = argparse.ArgumentParser(
        description="Universal Strategy JSON Validator for Trade Bot",
        epilog="""
Examples:
  python test_strategy_validator.py strategy.json
  python test_strategy_validator.py --json '{"name":"test",...}'
  python test_strategy_validator.py --interactive
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("file", nargs="?", help="Path to strategy JSON file")
    group.add_argument("--json", help="Strategy JSON string")
    group.add_argument("--interactive", action="store_true", help="Interactive mode")

    args = parser.parse_args()

    strategy_json = None

    try:
        if args.file:
            # Load from file
            with open(args.file, 'r') as f:
                strategy_json = json.load(f)
            print(f"📁 Loaded strategy from: {args.file}")

        elif args.json:
            # Parse JSON string
            strategy_json = json.loads(args.json)
            print("📝 Loaded strategy from JSON string")

        elif args.interactive:
            # Interactive mode
            print("📝 Interactive Mode - Paste your strategy JSON:")
            print("(Press Ctrl+D when finished)")
            json_input = sys.stdin.read()
            strategy_json = json.loads(json_input)
            print("✅ Strategy JSON loaded")

        # Validate the strategy
        results = validate_strategy_json(strategy_json)

        # Print summary
        print_validation_summary(results)

        # Exit with appropriate code
        sys.exit(0 if results["valid"] else 1)

    except FileNotFoundError:
        print(f"❌ Error: File '{args.file}' not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON format - {str(e)}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
