syntax = "proto3";

package tradingplatform;

// Service Definition
service TradingPlatform {
  rpc RegisterUser(UserRegistration) returns (ResponseMessage);
  rpc GetAccountSummary(AccountRequest) returns (AccountSummary);
  rpc GetOpenTrades(AccountRequest) returns (OpenTrades);
  rpc GenerateStrategy(StrategyRequest) returns (StrategyResponse);
  rpc ExecuteTrade(TradeExecutionRequest) returns (TradeExecutionResponse);
}

// User Registration
message UserRegistration {
  string api_key = 1;
  string account_id = 2;
}

// Generic Response
message ResponseMessage {
  string message = 1;
}

// Account Request
message AccountRequest {
  string account_id = 1;
}

// Account Summary
message AccountSummary {
  string balance = 1;
  string margin_available = 2;
  string margin_used = 3;
  int32 open_trade_count = 4;
  string profit_loss = 5;
}

// Open Trades
message OpenTrades {
  repeated Trade trades = 1;
}

message Trade {
  string trade_id = 1;
  string instrument = 2;
  string units = 3;
  string open_price = 4;
  string current_price = 5;
  string profit_loss = 6;
  string time_opened = 7;
}

// Strategy Request
message StrategyRequest {
  repeated string timeframes = 1;
  repeated int32 moving_averages = 2;
  bool use_fibonacci = 3;
  float take_profit = 4;
  float stop_loss = 5;
}

// Strategy Response
message StrategyResponse {
  string strategy_json = 1;
}

// Trade Execution Request
message TradeExecutionRequest {
  string account_id = 1;
  StrategyRequest strategy = 2;
}

// Trade Execution Response
message TradeExecutionResponse {
  string trade_id = 1;
  string status = 2;
  string message = 3;
}
