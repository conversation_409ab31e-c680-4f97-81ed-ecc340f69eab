syntax = "proto3";

package forex;

// Moving Average Types
enum MAType {
  SMA = 0;  // Simple Moving Average
  EMA = 1;  // Exponential Moving Average
  WMA = 2;  // Weighted Moving Average
  HMA = 3;  // Hull Moving Average
  VWMA = 4; // Volume Weighted Moving Average
}

// Trend Indicators
message MovingAverage {
  MAType type = 1;
  int32 period = 2;
}

message BollingerBands {
  int32 period = 1;
  double std_dev_multiplier = 2;
}

message ParabolicSAR {
  double acceleration_factor = 1;
  double max_acceleration = 2;
}

// Momentum Indicators
message RSI {
  int32 period = 1;
}

message MACD {
  int32 fast_period = 1;
  int32 slow_period = 2;
  int32 signal_period = 3;
}

message StochasticOscillator {
  int32 k_period = 1;
  int32 d_period = 2;
}

message CCI {
  int32 period = 1;
}

message Momentum {
  int32 period = 1;
}

message WilliamsR {
  int32 period = 1;
}

// Volatility Indicators
message ATR {
  int32 period = 1;
}

message ADX {
  int32 period = 1;
}

// Volume Indicators
message OBV {
  bool cumulative = 1; // True for cumulative, False for period-based
}

message MFI {
  int32 period = 1;
}

// Composite Structure for All Indicators
message ForexIndicators {
  repeated MovingAverage moving_averages = 1;
  BollingerBands bollinger_bands = 2;
  ParabolicSAR parabolic_sar = 3;
  RSI rsi = 4;
  MACD macd = 5;
  StochasticOscillator stochastic_oscillator = 6;
  CCI cci

