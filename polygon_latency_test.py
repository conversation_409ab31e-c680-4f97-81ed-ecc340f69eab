#!/usr/bin/env python3
"""
Polygon API Latency Test Script

Tests how quickly Polygon provides the latest 1-minute candle at the start of each minute.
Fetches 1000 1m candles and checks when the most recent candle becomes available.
"""

import subprocess
import json
import time
import os
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any

# Configuration
POLYGON_API_KEY = os.getenv('POLYGON_API_KEY', '********************************')
FOREX_PAIR = 'C:EURUSD'  # Default forex pair to test
LIMIT = 1000  # Number of candles to fetch
TEST_DURATION_MINUTES = 60  # How long to run the test (in minutes)

def get_current_minute_timestamp() -> int:
    """Get the timestamp for the current minute (rounded down to the minute)."""
    now = datetime.now(timezone.utc)
    current_minute = now.replace(second=0, microsecond=0)
    return int(current_minute.timestamp() * 1000)  # Polygon uses milliseconds

def get_previous_minute_timestamp() -> int:
    """Get the timestamp for the previous minute."""
    now = datetime.now(timezone.utc)
    previous_minute = now.replace(second=0, microsecond=0) - timedelta(minutes=1)
    return int(previous_minute.timestamp() * 1000)  # Polygon uses milliseconds

def fetch_polygon_candles(symbol: str, limit: int = 1000, debug: bool = False) -> Optional[Dict[str, Any]]:
    """
    Fetch candles from Polygon API using curl.

    Args:
        symbol: Forex pair symbol (e.g., 'C:EURUSD')
        limit: Number of candles to fetch
        debug: Print debug information

    Returns:
        JSON response from Polygon API or None if error
    """
    # Polygon aggregates endpoint for forex - correct format
    url = f"https://api.polygon.io/v2/aggs/ticker/{symbol}/range/1/minute"

    # Get timestamp for 2 days ago to ensure we get recent data (same as trade-bot)
    end_time = datetime.now(timezone.utc)
    start_time = end_time - timedelta(days=2)

    # Convert to YYYY-MM-DD format for Polygon API
    from_date = start_time.strftime('%Y-%m-%d')
    to_date = end_time.strftime('%Y-%m-%d')

    # Build the full URL with date format (same as trade-bot)
    # Use a high limit like the trade-bot and add cache busting
    cache_bust = int(datetime.now().timestamp() * 1000)
    full_url = f"{url}/{from_date}/{to_date}?adjusted=true&sort=asc&limit=50000&_t={cache_bust}&apikey={POLYGON_API_KEY}"

    # Build curl command
    curl_cmd = [
        'curl',
        '-s',  # Silent mode
        '-w', '%{http_code}',  # Write HTTP status code
        '-H', 'Accept: application/json',
        full_url
    ]

    if debug:
        print(f"🔍 Debug: Curl command: {' '.join(curl_cmd[:4])} [URL_WITH_API_KEY]")

    try:
        # Execute curl command
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=30)

        if result.returncode != 0:
            print(f"❌ Curl command failed (return code {result.returncode})")
            print(f"❌ Stderr: {result.stderr}")
            return None

        # Extract HTTP status code (last 3 characters)
        response_body = result.stdout[:-3]
        http_status = result.stdout[-3:]

        if debug:
            print(f"🔍 Debug: HTTP Status: {http_status}")
            print(f"🔍 Debug: Response length: {len(response_body)} chars")
            print(f"🔍 Debug: Response preview: {response_body[:200]}...")

        # Check HTTP status
        if http_status != '200':
            print(f"❌ HTTP Error {http_status}")
            print(f"❌ Response: {response_body}")
            return None

        # Parse JSON response
        if not response_body.strip():
            print("❌ Empty response body")
            return None

        response = json.loads(response_body)
        return response

    except subprocess.TimeoutExpired:
        print("❌ Request timed out")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse JSON response: {e}")
        print(f"❌ Raw response body: {response_body}")
        return None
    except Exception as e:
        print(f"❌ Error fetching data: {e}")
        return None

def get_latest_candle_timestamp(response: Dict[str, Any]) -> Optional[int]:
    """
    Extract the timestamp of the most recent candle from Polygon response.
    
    Args:
        response: Polygon API response
        
    Returns:
        Timestamp of latest candle in milliseconds or None
    """
    try:
        if response.get('status') != 'OK':
            print(f"❌ API Error: {response.get('status')} - {response.get('error', 'Unknown error')}")
            return None
            
        results = response.get('results', [])
        if not results:
            print("❌ No candle data in response")
            return None
            
        # Get the latest candle (should be the last one in the array)
        latest_candle = results[-1]
        return latest_candle.get('t')  # 't' is timestamp in milliseconds
        
    except Exception as e:
        print(f"❌ Error parsing response: {e}")
        return None

def format_timestamp(timestamp_ms: int) -> str:
    """Format timestamp for display."""
    dt = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
    return dt.strftime('%Y-%m-%d %H:%M:%S UTC')

def wait_for_next_minute():
    """Wait until the start of the next minute."""
    now = datetime.now(timezone.utc)
    next_minute = (now.replace(second=0, microsecond=0) + timedelta(minutes=1))
    wait_seconds = (next_minute - now).total_seconds()
    
    print(f"⏳ Waiting {wait_seconds:.1f} seconds for next minute...")
    time.sleep(wait_seconds)

def test_api_connection():
    """Test the API connection before running the full test."""
    print("🔧 Testing API connection...")
    response = fetch_polygon_candles(FOREX_PAIR, 10, debug=True)

    if response:
        latest_ts = get_latest_candle_timestamp(response)
        if latest_ts:
            print(f"✅ API connection successful!")
            print(f"📊 Latest candle: {format_timestamp(latest_ts)}")
            return True
        else:
            print("❌ Could not parse candle data")
            return False
    else:
        print("❌ API connection failed")
        return False

def test_polygon_latency():
    """Main test function that runs the latency test."""
    print("🚀 Starting Polygon API Latency Test")
    print(f"📊 Testing symbol: {FOREX_PAIR}")
    print(f"🔑 API Key: {POLYGON_API_KEY[:10]}..." if POLYGON_API_KEY != 'YOUR_API_KEY_HERE' else "❌ Please set POLYGON_API_KEY environment variable")
    print(f"⏱️  Test duration: {TEST_DURATION_MINUTES} minutes")
    print("-" * 60)

    if POLYGON_API_KEY == 'YOUR_API_KEY_HERE':
        print("❌ Please set your Polygon API key in the POLYGON_API_KEY environment variable")
        return

    # Test API connection first
    if not test_api_connection():
        print("❌ Cannot proceed with latency test due to API connection issues")
        return

    print("\n" + "-" * 60)
    
    test_start_time = datetime.now(timezone.utc)
    test_results = []
    
    for test_round in range(TEST_DURATION_MINUTES):
        print(f"\n🔄 Test Round {test_round + 1}/{TEST_DURATION_MINUTES}")
        
        # Wait for the start of the next minute
        wait_for_next_minute()
        
        # Record when we start checking
        check_start_time = datetime.now(timezone.utc)
        current_minute_ts = get_current_minute_timestamp()
        target_candle_ts = get_previous_minute_timestamp()  # We expect the previous minute's candle
        
        print(f"🎯 Looking for candle: {format_timestamp(target_candle_ts)}")
        print(f"⏰ Started checking at: {check_start_time.strftime('%H:%M:%S.%f')[:-3]} UTC")
        
        # Keep checking until we find the latest candle or timeout
        max_attempts = 120  # 2 minutes max
        attempt = 0
        found_latest = False
        
        while attempt < max_attempts and not found_latest:
            attempt += 1
            check_time = datetime.now(timezone.utc)
            
            # Fetch candles from Polygon (enable debug on first attempt)
            debug_mode = (attempt == 1)
            response = fetch_polygon_candles(FOREX_PAIR, LIMIT, debug=debug_mode)
            
            if response:
                latest_candle_ts = get_latest_candle_timestamp(response)
                
                if latest_candle_ts:
                    # Check if we have the candle we're looking for
                    if latest_candle_ts >= target_candle_ts:
                        elapsed_seconds = (check_time - check_start_time).total_seconds()
                        found_latest = True
                        
                        print(f"✅ Found latest candle after {elapsed_seconds:.2f} seconds!")
                        print(f"📊 Latest candle: {format_timestamp(latest_candle_ts)}")
                        
                        test_results.append({
                            'round': test_round + 1,
                            'target_candle': format_timestamp(target_candle_ts),
                            'found_candle': format_timestamp(latest_candle_ts),
                            'latency_seconds': elapsed_seconds,
                            'attempts': attempt
                        })
                    else:
                        print(f"⏳ Attempt {attempt}: Latest candle is {format_timestamp(latest_candle_ts)} (waiting for {format_timestamp(target_candle_ts)})")
                else:
                    print(f"❌ Attempt {attempt}: Could not get latest candle timestamp")
            else:
                print(f"❌ Attempt {attempt}: Failed to fetch data from Polygon")
            
            if not found_latest:
                time.sleep(1)  # Wait 1 second before next attempt
        
        if not found_latest:
            print(f"⏰ Timeout: Could not find latest candle after {max_attempts} attempts")
            test_results.append({
                'round': test_round + 1,
                'target_candle': format_timestamp(target_candle_ts),
                'found_candle': 'TIMEOUT',
                'latency_seconds': None,
                'attempts': max_attempts
            })
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    successful_tests = [r for r in test_results if r['latency_seconds'] is not None]
    
    if successful_tests:
        latencies = [r['latency_seconds'] for r in successful_tests]
        avg_latency = sum(latencies) / len(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        
        print(f"✅ Successful tests: {len(successful_tests)}/{len(test_results)}")
        print(f"⚡ Average latency: {avg_latency:.2f} seconds")
        print(f"🏃 Fastest response: {min_latency:.2f} seconds")
        print(f"🐌 Slowest response: {max_latency:.2f} seconds")
    else:
        print("❌ No successful tests completed")
    
    print(f"\n📋 Detailed Results:")
    for result in test_results:
        status = f"{result['latency_seconds']:.2f}s" if result['latency_seconds'] else "TIMEOUT"
        print(f"Round {result['round']:2d}: {result['target_candle']} -> {status} ({result['attempts']} attempts)")

if __name__ == "__main__":
    test_polygon_latency()
