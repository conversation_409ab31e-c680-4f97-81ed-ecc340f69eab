#!/bin/bash

# Setup script for GitHub Actions CI/CD
# This script creates the necessary Google Cloud service account and provides instructions for GitHub setup

set -e

PROJECT_ID="oryntrade"
SA_NAME="github-actions-sa"
SA_EMAIL="${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
KEY_FILE="github-actions-key.json"

echo "🚀 Setting up GitHub Actions CI/CD for Oryn Trading Platform"
echo "============================================================"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first:"
    echo "   https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "❌ No active gcloud authentication found"
    echo "Please run: gcloud auth login"
    exit 1
fi

# Set the project
echo "📋 Setting gcloud project to ${PROJECT_ID}..."
gcloud config set project ${PROJECT_ID}

# Check if service account already exists
if gcloud iam service-accounts describe ${SA_EMAIL} &>/dev/null; then
    echo "⚠️ Service account ${SA_EMAIL} already exists"
    read -p "Do you want to recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️ Deleting existing service account..."
        gcloud iam service-accounts delete ${SA_EMAIL} --quiet
    else
        echo "ℹ️ Using existing service account"
    fi
fi

# Create service account if it doesn't exist
if ! gcloud iam service-accounts describe ${SA_EMAIL} &>/dev/null; then
    echo "👤 Creating service account: ${SA_EMAIL}..."
    gcloud iam service-accounts create ${SA_NAME} \
        --display-name="GitHub Actions Service Account" \
        --description="Service account for GitHub Actions CI/CD deployments"
fi

# Grant necessary roles
echo "🔐 Granting IAM roles..."

roles=(
    "roles/container.admin"
    "roles/storage.admin"
    "roles/artifactregistry.admin"
    "roles/cloudbuild.builds.editor"
    "roles/iam.serviceAccountUser"
    "roles/run.admin"
    "roles/pubsub.admin"
    "roles/secretmanager.secretAccessor"
)

for role in "${roles[@]}"; do
    echo "   Adding role: ${role}"
    gcloud projects add-iam-policy-binding ${PROJECT_ID} \
        --member="serviceAccount:${SA_EMAIL}" \
        --role="${role}" \
        --condition=None \
        --quiet
done

# Create and download key
echo "🔑 Creating service account key..."
if [ -f "${KEY_FILE}" ]; then
    echo "⚠️ Key file ${KEY_FILE} already exists"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm "${KEY_FILE}"
    else
        echo "ℹ️ Using existing key file"
    fi
fi

if [ ! -f "${KEY_FILE}" ]; then
    gcloud iam service-accounts keys create ${KEY_FILE} \
        --iam-account=${SA_EMAIL}
fi

# Verify cluster access
echo "🔍 Verifying GKE cluster access..."
if gcloud container clusters describe oryn-trading-cluster --zone us-central1 &>/dev/null; then
    echo "✅ GKE cluster 'oryn-trading-cluster' found"
    
    # Get cluster credentials
    gcloud container clusters get-credentials oryn-trading-cluster \
        --zone us-central1 --project ${PROJECT_ID}
    
    # Test kubectl access
    if kubectl cluster-info &>/dev/null; then
        echo "✅ kubectl access verified"
    else
        echo "⚠️ kubectl access failed - please check cluster configuration"
    fi
else
    echo "❌ GKE cluster 'oryn-trading-cluster' not found"
    echo "Please ensure the cluster exists and you have access to it"
fi

# Verify Artifact Registry
echo "🔍 Verifying Artifact Registry..."
if gcloud artifacts repositories describe oryn-containers --location=us-central1 &>/dev/null; then
    echo "✅ Artifact Registry 'oryn-containers' found"
else
    echo "⚠️ Artifact Registry 'oryn-containers' not found"
    read -p "Do you want to create it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "📦 Creating Artifact Registry repository..."
        gcloud artifacts repositories create oryn-containers \
            --repository-format=docker \
            --location=us-central1 \
            --description="Docker repository for Oryn trading platform"
        echo "✅ Artifact Registry created"
    fi
fi

# Verify Polygon API key secret
echo "🔍 Verifying Polygon API key secret..."
if gcloud secrets describe polygon-api-key &>/dev/null; then
    echo "✅ Polygon API key secret found"
else
    echo "⚠️ Polygon API key secret not found"
    echo "   This is required for websocket services deployment"
    read -p "Do you want to create it now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        read -r -p "Enter your Polygon API key: " -s POLYGON_KEY
        echo
        echo "$POLYGON_KEY" | gcloud secrets create polygon-api-key --data-file=-
        echo "✅ Polygon API key secret created"
    else
        echo "⚠️ You can create it later with:"
        echo "   echo 'YOUR_API_KEY' | gcloud secrets create polygon-api-key --data-file=-"
    fi
fi

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "=============="
echo ""
echo "1. Add the service account key to GitHub Secrets:"
echo "   - Go to your repository → Settings → Secrets and variables → Actions"
echo "   - Click 'New repository secret'"
echo "   - Name: GCP_SA_KEY"
echo "   - Value: Copy the entire contents of ${KEY_FILE}"
echo ""
echo "2. Copy the key content:"
echo "   cat ${KEY_FILE}"
echo ""
echo "3. Commit and push the GitHub Actions workflows:"
echo "   git add .github/"
echo "   git commit -m 'Add GitHub Actions CI/CD workflows'"
echo "   git push origin main"
echo ""
echo "4. Test the workflows:"
echo "   - Make a change to trade-bot-service/ or strategy-controller-service/"
echo "   - Push to main branch"
echo "   - Check GitHub Actions tab for deployment status"
echo ""
echo "🔒 Security Note:"
echo "   - Keep ${KEY_FILE} secure and do not commit it to version control"
echo "   - Consider adding ${KEY_FILE} to .gitignore"
echo ""
echo "📚 For more information, see .github/workflows/README.md"
