import os
import json
import logging
import time
import signal
from kubernetes import client, config
from kubernetes.client.rest import ApiException
import re
from logging_config import structured_logger

class KubernetesClient:
    """
    Client for managing Kubernetes resources for trading strategy instances.
    """

    # Store running local processes by strategy_id
    _running_processes = {}

    # Store IDs of strategies that have been explicitly stopped
    _stopped_strategies = set()

    def __init__(self):
        """
        Initialize the Kubernetes client.
        In cluster, this will use the service account.
        For local development, it will use kubectl config.
        For local development without kubectl config, it will use a dummy configuration.
        """
        use_local_trade_bot = os.getenv('USE_LOCAL_TRADE_BOT', 'false').lower() == 'true'

        try:
            # Try to load in-cluster config (when running in K8s)
            config.load_incluster_config()
            logging.info("Using in-cluster Kubernetes configuration")
        except config.ConfigException:
            try:
                # Fall back to local kubeconfig
                config.load_kube_config()
                logging.info("Using local Kubernetes configuration")
            except config.ConfigException:
                if use_local_trade_bot:
                    # If we're using local trade bot, we don't need a real K8s connection
                    logging.warning("No Kubernetes configuration found, but USE_LOCAL_TRADE_BOT=true, continuing with dummy configuration")
                    # Set up dummy client configuration
                    configuration = client.Configuration()
                    configuration.host = "https://localhost:8080"
                    configuration.verify_ssl = False
                    configuration.api_key = {"authorization": "dummy-token"}
                    client.Configuration.set_default(configuration)
                else:
                    # If we're not using local trade bot, we need a real K8s connection
                    logging.error("No Kubernetes configuration found and USE_LOCAL_TRADE_BOT=false, cannot continue")
                    raise

        self.api = client.CoreV1Api()
        self.apps_v1_api = client.AppsV1Api()
        self.batch_api = client.BatchV1Api()

        # Get namespace or default to 'default'
        self.namespace = os.getenv("K8S_NAMESPACE", "default")
        logging.info(f"Using Kubernetes namespace: {self.namespace}")

    def sanitize_strategy_id(self, strategy_id):
        """
        Sanitize a strategy ID to conform to Kubernetes naming requirements.

        Args:
            strategy_id (str): Original strategy ID

        Returns:
            str: Sanitized strategy ID suitable for Kubernetes resource names
        """
        # Convert to lowercase and replace invalid characters with hyphens
        sanitized_id = strategy_id.lower()
        sanitized_id = re.sub(r'[^a-z0-9\.\-]', '-', sanitized_id)

        # Ensure it starts and ends with an alphanumeric character
        if not sanitized_id[0].isalnum():
            sanitized_id = 'a' + sanitized_id
        if not sanitized_id[-1].isalnum():
            sanitized_id = sanitized_id + 'z'

        # Ensure the name isn't too long (Kubernetes has a 63 character limit)
        if len(sanitized_id) > 58:  # Leave room for "strategy-" prefix
            sanitized_id = sanitized_id[:58]

        return sanitized_id

    def sanitize_label_value(self, value):
        """
        Sanitize a value for use in Kubernetes labels.
        Kubernetes label values must be alphanumeric with hyphens, periods, and underscores.
        They must start and end with alphanumeric characters.
        """
        if not value:
            return "unknown"

        # Convert to string and replace invalid characters
        sanitized = re.sub(r'[^a-zA-Z0-9._-]', '-', str(value))

        # Remove consecutive hyphens
        sanitized = re.sub(r'-+', '-', sanitized)

        # Ensure it starts and ends with alphanumeric
        sanitized = re.sub(r'^[^a-zA-Z0-9]+', '', sanitized)
        sanitized = re.sub(r'[^a-zA-Z0-9]+$', '', sanitized)

        # Ensure it's not empty
        if not sanitized:
            sanitized = "unknown"

        # Limit length to 63 characters
        if len(sanitized) > 63:
            sanitized = sanitized[:63]

        return sanitized

    def create_strategy_pod(self, strategy_id, user_id, strategy_json):
        """
        Create a pod for a specific strategy.
        """
        try:
            # Check if this strategy has been explicitly stopped
            if strategy_id in self.__class__._stopped_strategies:
                logging.info(f"Strategy {strategy_id} was explicitly stopped and will not be restarted")
                return {
                    "status": "error",
                    "message": f"Strategy {strategy_id} was explicitly stopped and will not be restarted"
                }

            # Check if we should run the trade bot locally FIRST, before any Kubernetes API calls
            use_local_trade_bot = os.getenv('USE_LOCAL_TRADE_BOT', 'false').lower() == 'true'

            if use_local_trade_bot:
                # Run the trade bot locally as a subprocess
                logging.info(f"Running trade bot locally for strategy {strategy_id}")
                import subprocess
                import tempfile

                # Determine the path to the trade bot
                trade_bot_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'trade-bot-service')
                if not os.path.exists(trade_bot_path):
                    logging.warning(f"Could not find trade bot at {trade_bot_path}, trying relative path")
                    trade_bot_path = os.path.abspath(os.path.join(os.getcwd(), '../trade-bot-service'))

                logging.info(f"Using trade bot at: {trade_bot_path}")

                # Create a temp file with the strategy JSON in the trade bot directory
                strategy_file = os.path.join(trade_bot_path, f"strategy_{strategy_id}.json")
                with open(strategy_file, 'w') as f:
                    f.write(strategy_json)

                # Set up environment for the subprocess
                env = os.environ.copy()

                # First, source the .env file
                env_file = os.path.join(trade_bot_path, '.env')
                if os.path.exists(env_file):
                    logging.info(f"Reading environment from {env_file}")
                    with open(env_file, 'r') as f:
                        for line in f:
                            if line.strip() and not line.strip().startswith('#') and '=' in line:
                                key, value = line.strip().split('=', 1)
                                env[key] = value.strip()

                # Set critical environment variables
                env['PYTHONPATH'] = f"{env.get('PYTHONPATH', '')}:{trade_bot_path}"
                env['FIRESTORE_EMULATOR_HOST'] = '127.0.0.1:8082'
                env['USE_FIREBASE_EMULATOR'] = 'true'
                env['STRATEGY_ID'] = strategy_id
                env['USER_ID'] = user_id
                env['STRATEGY_JSON_FILE'] = strategy_file
                env['BYPASS_MARKET_IS_CLOSED'] = 'false'
                env['MARKET_DATA_PROVIDER'] = 'pubsub'  # Use Pub/Sub for real-time data
                env['PUBSUB_EMULATOR_HOST'] = 'localhost:8085'  # Connect to Pub/Sub emulator

                # Remove Firebase credentials path when using emulator
                if 'GOOGLE_APPLICATION_CREDENTIALS' in env:
                    del env['GOOGLE_APPLICATION_CREDENTIALS']

                # We don't need to mock OANDA credentials - they'll be retrieved from Firestore emulator
                # Remove any mock credentials in case they were set elsewhere
                for key in ['OANDA_API_KEY', 'OANDA_ACCOUNT_ID']:
                    if key in env:
                        del env[key]

                # Force unbuffered output from Python
                env['PYTHONUNBUFFERED'] = '1'

                # Log the environment
                safe_env = {k: v for k, v in env.items() if 'KEY' not in k.upper() and 'SECRET' not in k.upper()}
                logging.info(f"Environment for trade bot: {safe_env}")

                # Create a wrapper script to run the trade bot
                wrapper_script = os.path.join(trade_bot_path, "run_wrapper.sh")
                with open(wrapper_script, 'w') as f:
                    # Write the script content without f-string for the problematic parts
                    script_content = [
                        "#!/bin/bash",
                        'echo "Starting trade bot from wrapper script"',
                        'echo "Working directory: $(pwd)"',
                        f'echo "Strategy ID: {strategy_id}"',
                        f'echo "User ID: {user_id}"',
                        f'echo "Strategy file: {strategy_file}"',
                        "",
                        "# Source .env file if it exists",
                        "if [ -f .env ]; then",
                        '    echo "Sourcing .env file"',
                        "    set -a",
                        "    source .env",
                        "    set +a",
                        "fi",
                        "",
                        "# Override with our values",
                        f'export STRATEGY_ID="{strategy_id}"',
                        f'export USER_ID="{user_id}"',
                        f'export STRATEGY_JSON_FILE="{strategy_file}"',
                        'export USE_FIREBASE_EMULATOR=true',
                        'export FIRESTORE_EMULATOR_HOST=127.0.0.1:8082',
                        'export BYPASS_MARKET_IS_CLOSED=false',
                        'export OANDA_PRACTICE_MODE=true',
                        "",
                        'echo "Environment variables:"',
                        'echo "STRATEGY_ID=$STRATEGY_ID"',
                        'echo "USER_ID=$USER_ID"',
                        'echo "USE_FIREBASE_EMULATOR=$USE_FIREBASE_EMULATOR"',
                        'echo "FIRESTORE_EMULATOR_HOST=$FIRESTORE_EMULATOR_HOST"',
                        'echo "BYPASS_MARKET_IS_CLOSED=$BYPASS_MARKET_IS_CLOSED"',
                        'echo "OANDA_PRACTICE_MODE=$OANDA_PRACTICE_MODE"',
                        "",
                        "# Create empty credentials file if needed",
                        "if [ ! -f ./firebase-key.json ]; then",
                        '    echo "Creating empty firebase-key.json file"',
                        '    echo "{}" > ./firebase-key.json',
                        "fi",
                        "",
                        "# Verify we can connect to Firestore emulator",
                        'echo "Checking Firestore emulator connection..."',
                        'curl -s http://127.0.0.1:8082/ > /dev/null',
                        'if [ $? -ne 0 ]; then',
                        '    echo "ERROR: Cannot connect to Firestore emulator at 127.0.0.1:8082"',
                        '    echo "Make sure the Firebase emulators are running (firebase emulators:start)"',
                        '    exit 1',
                        'else',
                        '    echo "Successfully connected to Firestore emulator"',
                        'fi',
                        "",
                        "# Create required PubSub topics in the emulator",
                        'echo "Creating PubSub topics in emulator..."',
                        'if [ ! -z "$PUBSUB_EMULATOR_HOST" ]; then',
                        '    # Create the command topic',
                        '    curl -s -X PUT "http://localhost:8085/v1/projects/oryntrade/topics/strategy-commands" -H "Content-Type: application/json" -d "{}" > /dev/null',
                        '    echo "Created PubSub topic: strategy-commands"',
                        '    # Create the execution topic',
                        '    curl -s -X PUT "http://localhost:8085/v1/projects/oryntrade/topics/strategy-execution" -H "Content-Type: application/json" -d "{}" > /dev/null',
                        '    echo "Created PubSub topic: strategy-execution"',
                        'fi',
                        "",
                        "# Add PYTHONPATH if not set",
                        'if [ -z "$PYTHONPATH" ]; then',
                        '    export PYTHONPATH=$(pwd)',
                        '    echo "Set PYTHONPATH=$PYTHONPATH"',
                        'fi',
                        "",
                        "# Install required dependencies",
                        'echo "Installing required dependencies..."',
                        'pip install python-dateutil>=2.8.0 > /dev/null 2>&1',
                        'if [ $? -eq 0 ]; then',
                        '    echo "Dependencies installed successfully"',
                        'else',
                        '    echo "Warning: Failed to install some dependencies, continuing anyway..."',
                        'fi',
                        "",
                        "# Run the trade bot",
                        'echo "Starting Python trade bot..."',
                        "python -u main.py"
                    ]
                    f.write("\n".join(script_content))

                # Make the wrapper script executable
                os.chmod(wrapper_script, 0o755)

                # Start the trade bot as a background process using the wrapper script
                logging.info(f"Running trade bot with wrapper script: {wrapper_script}")
                process = subprocess.Popen(
                    [wrapper_script],
                    cwd=trade_bot_path,
                    env=env,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1  # Line buffered
                )

                # Store the process for later use
                self.__class__._running_processes[strategy_id] = process

                # Start a thread to log the output
                import threading
                def log_output(process):
                    logging.info(f"Starting output logging for PID {process.pid}")
                    while True:
                        line = process.stdout.readline()
                        if not line and process.poll() is not None:
                            logging.info(f"Process {process.pid} exited with code {process.returncode}")
                            break
                        if line:
                            logging.info(f"[Trade Bot] {line.rstrip()}")

                thread = threading.Thread(target=log_output, args=(process,), daemon=True)
                thread.start()

                logging.info(f"Started local trade bot process with PID {process.pid}")
                return {"process_id": process.pid, "status": "created", "local": True}

            # If not using local trade bot, proceed with Kubernetes deployment creation
            # Sanitize strategy_id for use in Kubernetes resources
            deployment_name = f"trade-bot-{self.sanitize_strategy_id(strategy_id)}"

            # First try to delete any existing deployment
            deployment_deleted = False
            try:
                self.apps_v1_api.delete_namespaced_deployment(
                    name=deployment_name,
                    namespace=self.namespace
                )
                logging.info(f"Deleting existing deployment {deployment_name}")
                deployment_deleted = True
            except ApiException as e:
                if e.status != 404:  # Ignore 404 (Not Found) errors
                    logging.warning(f"Error checking existing deployment: {e.status}")

            # If we deleted a deployment, wait for it to be fully removed
            if deployment_deleted:
                logging.info(f"Waiting for deployment {deployment_name} to terminate completely")
                max_retries = 10
                retry_interval = 2  # seconds
                for i in range(max_retries):
                    try:
                        # Check if deployment still exists
                        self.apps_v1_api.read_namespaced_deployment(
                            name=deployment_name,
                            namespace=self.namespace
                        )
                        # If we get here, deployment still exists, wait and retry
                        logging.info(f"Deployment {deployment_name} still terminating, waiting... ({i+1}/{max_retries})")
                        time.sleep(retry_interval)
                    except ApiException as e:
                        if e.status == 404:
                            # Deployment is gone, we can proceed
                            logging.info(f"Deployment {deployment_name} successfully terminated")
                            break
                        else:
                            # Some other error
                            logging.warning(f"Error checking deployment termination: {e.status}")
                            time.sleep(retry_interval)
                else:
                    # We exhausted retries but deployment might still be terminating
                    logging.warning(f"Deployment {deployment_name} still terminating after {max_retries} retries, proceeding anyway")



            # Determine if we're running in emulator mode from environment variables
            use_firebase_emulator = os.getenv('FIRESTORE_EMULATOR_HOST') is not None
            use_pubsub_emulator = os.getenv('PUBSUB_EMULATOR_HOST') is not None

            # Get parsed strategy name
            strategy_data = json.loads(strategy_json)
            strategy_name = strategy_data.get("name", "Unnamed Strategy")

            # Create pod environment variables
            pod_environment = [
                {
                    "name": "STRATEGY_ID",
                    "value": strategy_id
                },
                {
                    "name": "USER_ID",
                    "value": user_id
                },
                {
                    "name": "STRATEGY_JSON",
                    "value": strategy_json
                },
                {
                    "name": "USE_FIREBASE_EMULATOR",
                    "value": str(use_firebase_emulator).lower()
                },
                {
                    "name": "OANDA_PRACTICE_MODE",
                    "value": "true"
                },
                {
                    "name": "BYPASS_MARKET_IS_CLOSED",
                    "value": os.getenv('BYPASS_MARKET_IS_CLOSED', 'false').lower()
                }
            ]

            # Add emulator hosts if we're in emulator mode
            if use_firebase_emulator:
                pod_environment.append({
                    "name": "FIRESTORE_EMULATOR_HOST",
                    "value": os.getenv('FIRESTORE_EMULATOR_HOST')
                })

            if use_pubsub_emulator:
                pod_environment.append({
                    "name": "PUBSUB_EMULATOR_HOST",
                    "value": os.getenv('PUBSUB_EMULATOR_HOST')
                })

            # Add API keys and additional environment variables
            polygon_api_key = os.getenv('POLYGON_API_KEY', "")
            if polygon_api_key:
                pod_environment.append({
                    "name": "POLYGON_API_KEY",
                    "value": polygon_api_key
                })

            # Add production environment variables for trade bot
            additional_env_vars = [
                {
                    "name": "GOOGLE_CLOUD_PROJECT",
                    "value": "oryntrade"
                },
                {
                    "name": "STRATEGY_CONTROLLER_URL",
                    "value": "http://strategy-controller-service:80"
                },
                # WebSocket service configuration removed - starting fresh
                {
                    "name": "USE_ENHANCED_MARKET_DATA",
                    "value": "true"
                },
                {
                    "name": "LOG_LEVEL",
                    "value": "INFO"
                },
                {
                    "name": "ENABLE_HEALTH_API",
                    "value": "true"
                },
                {
                    "name": "HEALTH_API_PORT",
                    "value": "8001"
                },
                {
                    "name": "MARKET_DATA_PROVIDER",
                    "value": "pubsub"
                }
            ]

            pod_environment.extend(additional_env_vars)

            # Use local development image if specified
            image = os.getenv('TRADE_BOT_IMAGE', "us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest")

            # Create a deployment for the strategy (instead of standalone pod)
            deployment_spec = {
                "apiVersion": "apps/v1",
                "kind": "Deployment",
                "metadata": {
                    "name": deployment_name,
                    "labels": {
                        "app": "trade-bot-strategy",
                        "strategy-id": self.sanitize_label_value(strategy_id),
                        "user-id": self.sanitize_label_value(user_id),
                        "strategy-name": self.sanitize_label_value(strategy_name)
                    }
                },
                "spec": {
                    "replicas": 1,
                    "selector": {
                        "matchLabels": {
                            "app": "trade-bot-strategy",
                            "strategy-id": self.sanitize_label_value(strategy_id)
                        }
                    },
                    "template": {
                        "metadata": {
                            "labels": {
                                "app": "trade-bot-strategy",
                                "strategy-id": self.sanitize_label_value(strategy_id),
                                "user-id": self.sanitize_label_value(user_id),
                                "strategy-name": self.sanitize_label_value(strategy_name)
                            }
                        },
                        "spec": {
                            "serviceAccountName": "trade-bot-sa",
                            "terminationGracePeriodSeconds": 120,
                            # Note: Deployments use restartPolicy: Always by default
                            # Exit code handling is done via shutdown notification to Strategy Controller
                            "containers": [
                                {
                                    "name": "trade-bot",
                                    "image": image,
                                    "env": pod_environment,
                                    "ports": [
                                        {
                                            "containerPort": 8001,
                                            "name": "http",
                                            "protocol": "TCP"
                                        }
                                    ],
                                    "readinessProbe": {
                                        "httpGet": {
                                            "path": "/health",
                                            "port": 8001
                                        },
                                        "initialDelaySeconds": 30,
                                        "periodSeconds": 10,
                                        "timeoutSeconds": 5,
                                        "failureThreshold": 3
                                    },
                                    "livenessProbe": {
                                        "httpGet": {
                                            "path": "/health",
                                            "port": 8001
                                        },
                                        "initialDelaySeconds": 60,
                                        "periodSeconds": 30,
                                        "timeoutSeconds": 10,
                                        "failureThreshold": 3
                                    },
                                    "lifecycle": {
                                        "preStop": {
                                            "exec": {
                                                "command": ["/bin/sh", "-c", "echo 'Graceful shutdown initiated' && sleep 10"]
                                            }
                                        }
                                    },
                                    "resources": {
                                        "requests": {
                                            "cpu": "200m",
                                            "memory": "512Mi"
                                        },
                                        "limits": {
                                            "cpu": "1000m",
                                            "memory": "2Gi"
                                        }
                                    },
                                    "volumeMounts": [
                                        {
                                            "name": "firebase-key",
                                            "mountPath": "/app/firebase-key",
                                            "readOnly": True
                                        }
                                    ]
                                }
                            ],
                            "volumes": [
                                {
                                    "name": "firebase-key",
                                    "secret": {
                                        "secretName": "firebase-key",
                                        "items": [
                                            {
                                                "key": "firebase-key.json",
                                                "path": "firebase-key.json"
                                            }
                                        ]
                                    }
                                }
                            ]
                        }
                    }
                }
            }

            # Create the deployment
            api_response = self.apps_v1_api.create_namespaced_deployment(
                namespace=self.namespace,
                body=deployment_spec
            )

            logging.info(f"Created deployment {deployment_name} for user {user_id} with image {image}")
            return {"deployment_name": deployment_name, "status": "created", "local": False}

        except ApiException as e:
            logging.error(f"Error creating pod: {e.status} - {e.reason}")
            return {"error": f"{e.status}: {e.reason}"}
        except Exception as e:
            logging.error(f"Error creating strategy: {str(e)}")
            return {"error": str(e)}

    def get_strategy_pod_status(self, strategy_id):
        """
        Get the status of a strategy pod.

        Args:
            strategy_id (str): ID of the strategy

        Returns:
            dict: Pod status information
        """
        sanitized_id = self.sanitize_strategy_id(strategy_id)
        pod_name = f"strategy-{sanitized_id}"
        try:
            pod = self.api.read_namespaced_pod(
                name=pod_name,
                namespace=self.namespace
            )

            return {
                "status": "success",
                "pod_status": pod.status.phase,
                "start_time": pod.status.start_time.isoformat() if pod.status.start_time else None,
                "pod_ip": pod.status.pod_ip,
                "conditions": [
                    {
                        "type": condition.type,
                        "status": condition.status,
                        "last_transition_time": condition.last_transition_time.isoformat() if condition.last_transition_time else None
                    } for condition in pod.status.conditions
                ] if pod.status.conditions else []
            }
        except ApiException as e:
            logging.error(f"Error getting pod status: {e}")
            return {
                "status": "error",
                "message": f"Failed to get pod status: {e}"
            }

    def delete_strategy_pod(self, strategy_id):
        """
        Delete a strategy deployment or stop a local process.

        Args:
            strategy_id (str): ID of the strategy

        Returns:
            dict: Result with status
        """
        # Check if this is a local process first
        if strategy_id in self.__class__._running_processes:
            process = self.__class__._running_processes[strategy_id]
            try:
                logging.info(f"Stopping local trade bot process for strategy {strategy_id} (PID: {process.pid})")
                # Try to terminate gracefully first
                process.terminate()

                # Wait for up to 5 seconds for process to exit
                for _ in range(5):
                    if process.poll() is not None:
                        break
                    time.sleep(1)

                # If still running, force kill
                if process.poll() is None:
                    logging.warning(f"Process didn't terminate gracefully, sending SIGKILL to PID {process.pid}")
                    os.kill(process.pid, signal.SIGKILL)

                # Remove from the tracking dictionary
                del self.__class__._running_processes[strategy_id]

                # Add to stopped strategies set
                self.__class__._stopped_strategies.add(strategy_id)
                logging.info(f"Added strategy {strategy_id} to stopped strategies set")

                return {
                    "status": "success",
                    "message": f"Local process for strategy {strategy_id} stopped successfully"
                }
            except Exception as e:
                logging.error(f"Error stopping local process: {e}")
                return {
                    "status": "error",
                    "message": f"Failed to stop local process: {e}"
                }

        # If not a local process, try to delete deployment
        sanitized_id = self.sanitize_strategy_id(strategy_id)
        deployment_name = f"trade-bot-{sanitized_id}"
        try:
            self.apps_v1_api.delete_namespaced_deployment(
                name=deployment_name,
                namespace=self.namespace
            )
            logging.info(f"Deployment {deployment_name} deleted successfully")
            return {
                "status": "success",
                "message": f"Strategy deployment {deployment_name} deleted successfully"
            }
        except ApiException as e:
            logging.error(f"Error deleting deployment: {e}")
            return {
                "status": "error",
                "message": f"Failed to delete strategy deployment: {e}"
            }

    def _remove_process_from_tracking(self, strategy_id):
        """
        Remove a process from the tracking dictionary without terminating it.
        This is used when we know the process has already exited or will exit soon.

        Args:
            strategy_id (str): ID of the strategy
        """
        if strategy_id in self.__class__._running_processes:
            logging.info(f"Removing process for strategy {strategy_id} from tracking")
            # Just remove from tracking without trying to terminate
            del self.__class__._running_processes[strategy_id]

            # Add to stopped strategies set
            self.__class__._stopped_strategies.add(strategy_id)
            logging.info(f"Added strategy {strategy_id} to stopped strategies set")

    def delete_strategy_deployment(self, user_id, strategy_id):
        """
        Delete a strategy deployment by user_id and strategy_id.

        Args:
            user_id: The user ID
            strategy_id: The strategy ID

        Returns:
            dict: Result of the deletion operation
        """
        try:
            deployment_name = f"trade-bot-{strategy_id.lower()}"

            # Delete the deployment
            self.apps_v1_api.delete_namespaced_deployment(
                name=deployment_name,
                namespace=self.namespace
            )

            structured_logger.info(
                "Successfully deleted deployment",
                user_id=user_id,
                strategy_id=strategy_id,
                deployment_name=deployment_name
            )

            return {
                "status": "success",
                "message": f"Deployment {deployment_name} deleted successfully"
            }

        except Exception as e:
            structured_logger.error(
                "Failed to delete deployment",
                user_id=user_id,
                strategy_id=strategy_id,
                error=str(e)
            )
            return {
                "status": "error",
                "message": f"Failed to delete deployment: {str(e)}"
            }

    def list_strategy_pods(self, user_id=None):
        """
        List all strategy pods, optionally filtered by user ID.

        Args:
            user_id (str, optional): Filter by user ID

        Returns:
            dict: Result with list of pods
        """
        try:
            # Create label selector
            label_selector = "app=oryn-trade-bot"
            if user_id:
                label_selector += f",userId={user_id}"

            pods = self.api.list_namespaced_pod(
                namespace=self.namespace,
                label_selector=label_selector
            )

            result = []
            for pod in pods.items:
                result.append({
                    "pod_name": pod.metadata.name,
                    "strategy_id": pod.metadata.labels.get("strategyId"),
                    "user_id": pod.metadata.labels.get("userId"),
                    "status": pod.status.phase,
                    "start_time": pod.status.start_time.isoformat() if pod.status.start_time else None
                })

            return {
                "status": "success",
                "pods": result
            }
        except ApiException as e:
            logging.error(f"Error listing pods: {e}")
            return {
                "status": "error",
                "message": f"Failed to list strategy pods: {e}"
            }