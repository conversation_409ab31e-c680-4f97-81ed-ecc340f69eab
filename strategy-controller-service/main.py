import os
import sys
import json
import base64
import logging
import threading
from pathlib import Path
import firebase_admin
from firebase_admin import initialize_app, firestore
from fastapi import FastAPI, Request, HTTPException, Depends
from pydantic import BaseModel
from dotenv import load_dotenv
import uvicorn
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles

# Add config directory to path and load configuration
config_dir = Path(__file__).parent.parent / "config"
sys.path.append(str(config_dir))

try:
    from config_loader import setup_environment, get_config_loader
    # Set up environment variables based on configuration
    config_loader = setup_environment()
    IS_DEVELOPMENT = config_loader.is_development
    print(f"🔧 Configuration loaded: {config_loader.environment_name.upper()} mode")
except ImportError:
    print("⚠️  Config loader not found, using environment variables")
    IS_DEVELOPMENT = os.getenv('DEVELOPMENT_MODE', 'false').lower() == 'true'

# Import monitoring module
from monitoring import init_monitoring, HealthData, AlertData

from kubernetes_client import KubernetesClient
from auth import get_current_user, get_optional_user, security
from logging_config import structured_logger, TimedOperation
from middleware import RequestLoggingMiddleware
from pubsub_worker import start_pubsub_worker
from google.cloud import pubsub_v1

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

# Load environment variables
load_dotenv()

# Set environment for development
os.environ["ENVIRONMENT"] = os.getenv("ENVIRONMENT", "development")
if os.environ["ENVIRONMENT"] == "development":
    logging.info("Running in DEVELOPMENT mode")

# Initialize Firebase Admin SDK (if not already initialized)
if not firebase_admin._apps:
    initialize_app()

db = firestore.client()

# Initialize monitoring service
monitoring_service = init_monitoring(db)
structured_logger.info("Monitoring service initialized successfully")

# Create FastAPI app
app = FastAPI(title="Oryn Strategy Controller Service")

# Add middleware for request logging
app.add_middleware(RequestLoggingMiddleware)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://oryntrade.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create a directory for static files if it doesn't exist
os.makedirs("static", exist_ok=True)

# Mount static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Initialize Kubernetes client
k8s_client = KubernetesClient()

# Global variable to hold the Pub/Sub worker
pubsub_worker = None

# Initialize Pub/Sub publisher
publisher = pubsub_v1.PublisherClient()
project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
command_topic_name = os.getenv('COMMAND_TOPIC_NAME', 'strategy-commands')
command_topic_path = publisher.topic_path(project_id, command_topic_name)

class StrategyRequest(BaseModel):
    user_id: str
    strategy_id: str

class StrategyResponse(BaseModel):
    status: str
    message: str
    pod_name: str = None

class StrategyCommand(BaseModel):
    command: str  # "stop", "pause", "resume"

@app.on_event("startup")
async def startup_event():
    """Initialize the Pub/Sub worker when the service starts."""
    global pubsub_worker

    # Get environment variables
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', 'oryntrade')
    pubsub_subscription = os.getenv('PUBSUB_SUBSCRIPTION', 'strategy-controller-sub')
    pubsub_topic = os.getenv('PUBSUB_TOPIC', 'strategy-commands')

    # Log configuration details
    structured_logger.info(
        "Starting strategy controller service",
        project_id=project_id,
        pubsub_emulator=bool(os.getenv('PUBSUB_EMULATOR_HOST')),
        firestore_emulator=bool(os.getenv('FIRESTORE_EMULATOR_HOST'))
    )

    # Ensure the command topic exists
    try:
        publisher = pubsub_v1.PublisherClient()
        command_topic_path = publisher.topic_path(project_id, "strategy-commands")

        try:
            publisher.get_topic(request={"topic": command_topic_path})
            structured_logger.info("Command topic already exists", topic=command_topic_path)
        except Exception:
            # Topic doesn't exist, create it
            publisher.create_topic(request={"name": command_topic_path})
            structured_logger.info("Created command topic", topic=command_topic_path)
    except Exception as e:
        structured_logger.error("Error ensuring command topic exists", error=str(e))

    # Start the Pub/Sub worker
    try:
        pubsub_worker = start_pubsub_worker(k8s_client, db, pubsub_topic)
        structured_logger.info("PubSub worker started successfully")
    except Exception as e:
        structured_logger.error("Failed to start PubSub worker", error=str(e))

@app.on_event("shutdown")
async def shutdown_event():
    """Stop the Pub/Sub worker when the service shuts down."""
    global pubsub_worker
    if pubsub_worker:
        structured_logger.info("Stopping Pub/Sub worker during service shutdown")
        pubsub_worker.stop()

@app.get("/")
def read_root():
    """Health check endpoint."""
    return {"status": "healthy", "service": "strategy-controller"}

@app.post("/start-strategy")
async def start_strategy(request: StrategyRequest, user_id: str = Depends(get_current_user)):
    """
    Manually start a strategy pod.

    Requires authentication. Only the owner of the strategy can start it.
    """
    # Verify the user has permission to access this strategy
    if user_id != request.user_id:
        structured_logger.warning("Permission denied",
                                 authenticated_user=user_id,
                                 requested_user_id=request.user_id,
                                 strategy_id=request.strategy_id)
        raise HTTPException(status_code=403, detail="You don't have permission to access this strategy")

    try:
        # Start a timed operation for monitoring
        with TimedOperation("start_strategy",
                           user_id=user_id,
                           strategy_id=request.strategy_id):

            # Get strategy from Firestore
            strategy_doc = db.collection("users").document(request.user_id) \
                .collection("submittedStrategies").document(request.strategy_id).get()

            if not strategy_doc.exists:
                structured_logger.warning("Strategy not found",
                                         user_id=request.user_id,
                                         strategy_id=request.strategy_id)
                raise HTTPException(status_code=404, detail="Strategy not found")

            strategy_data = strategy_doc.to_dict()
            strategy_json = strategy_data.get("strategy_json", "{}")

            if not strategy_json:
                structured_logger.warning("Empty strategy JSON",
                                         user_id=request.user_id,
                                         strategy_id=request.strategy_id)
                raise HTTPException(status_code=400, detail="Strategy JSON data is empty")

            # Parse the strategy JSON
            try:
                strategy_config = json.loads(strategy_json)

                # Log risk management configuration if present
                if "risk_management" in strategy_config:
                    structured_logger.info("Risk management configuration found",
                                         user_id=request.user_id,
                                         strategy_id=request.strategy_id,
                                         risk_config=strategy_config["risk_management"])
                else:
                    structured_logger.info("No risk management configuration found, using defaults",
                                         user_id=request.user_id,
                                         strategy_id=request.strategy_id)
            except json.JSONDecodeError as e:
                structured_logger.error("Invalid strategy JSON",
                                      user_id=request.user_id,
                                      strategy_id=request.strategy_id,
                                      error=str(e))
                raise HTTPException(status_code=400, detail=f"Invalid strategy JSON: {str(e)}")

            # Check if this strategy has been explicitly stopped
            if request.strategy_id in k8s_client._stopped_strategies:
                structured_logger.info(
                    "Strategy was explicitly stopped and will not be restarted",
                    strategy_id=request.strategy_id
                )
                raise HTTPException(
                    status_code=400,
                    detail="Strategy was explicitly stopped and must be manually restarted"
                )

            # Create the strategy pod
            structured_logger.info("Creating strategy pod",
                                  user_id=request.user_id,
                                  strategy_id=request.strategy_id)

            result = k8s_client.create_strategy_pod(
                strategy_id=request.strategy_id,
                user_id=request.user_id,
                strategy_data=strategy_config
            )

            if result["status"] == "error":
                structured_logger.error("Failed to create strategy pod",
                                       user_id=request.user_id,
                                       strategy_id=request.strategy_id,
                                       error=result["message"])
                raise HTTPException(status_code=500, detail=result["message"])

            # Update the strategy status in Firestore
            deployment_name = result.get("deployment_name", result.get("pod_name", "unknown"))
            structured_logger.info("Updating strategy status in Firestore",
                                  user_id=request.user_id,
                                  strategy_id=request.strategy_id,
                                  deployment_name=deployment_name)

            strategy_doc.reference.update({
                "status": "running",
                "deployment_name": deployment_name,
                "started_at": datetime.utcnow()
            })

            structured_logger.info("Strategy successfully started",
                                  user_id=request.user_id,
                                  strategy_id=request.strategy_id,
                                  deployment_name=deployment_name)

            return {
                "status": "success",
                "message": "Strategy pod created successfully",
                "pod_name": result["pod_name"]
            }

    except Exception as e:
        structured_logger.error("Error starting strategy",
                               user_id=request.user_id,
                               strategy_id=request.strategy_id,
                               error=str(e),
                               exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error starting strategy: {str(e)}")

@app.post("/stop-strategy")
async def stop_strategy(request: StrategyRequest, user_id: str = Depends(get_current_user)):
    """
    Stop a running strategy pod.

    Requires authentication. Only the owner of the strategy can stop it.
    """
    # Verify the user has permission to access this strategy
    if user_id != request.user_id:
        raise HTTPException(status_code=403, detail="You don't have permission to access this strategy")

    try:
        # Delete the strategy pod
        result = k8s_client.delete_strategy_pod(strategy_id=request.strategy_id)

        if result["status"] == "error":
            raise HTTPException(status_code=500, detail=result["message"])

        # Update the strategy status in Firestore
        db.collection("users").document(request.user_id) \
            .collection("submittedStrategies").document(request.strategy_id).update({
                "status": "stopped",
                "stopped_at": datetime.utcnow()
            })

        return {
            "status": "success",
            "message": "Strategy pod stopped successfully"
        }

    except Exception as e:
        logging.error(f"Error stopping strategy: {e}")
        raise HTTPException(status_code=500, detail=f"Error stopping strategy: {str(e)}")

@app.get("/strategy-status/{user_id}/{strategy_id}")
async def get_strategy_status(user_id: str, strategy_id: str, authenticated_user: str = Depends(get_current_user)):
    """
    Get the status of a strategy pod.

    Requires authentication. Only the owner of the strategy can get its status.
    """
    # Verify the user has permission to access this strategy
    if authenticated_user != user_id:
        raise HTTPException(status_code=403, detail="You don't have permission to access this strategy")

    try:
        # Get pod status from Kubernetes
        pod_status = k8s_client.get_strategy_pod_status(strategy_id=strategy_id)

        if pod_status["status"] == "error":
            # If pod doesn't exist, check Firestore for status
            strategy_doc = db.collection("users").document(user_id) \
                .collection("submittedStrategies").document(strategy_id).get()

            if not strategy_doc.exists:
                raise HTTPException(status_code=404, detail="Strategy not found")

            strategy_data = strategy_doc.to_dict()

            return {
                "status": "success",
                "strategy_status": strategy_data.get("status", "unknown"),
                "pod_status": "not_found",
                "last_updated": strategy_data.get("stopped_at", strategy_data.get("started_at", None)),
                "message": "Strategy pod not found"
            }

        # Get additional info from Firestore
        strategy_doc = db.collection("users").document(user_id) \
            .collection("submittedStrategies").document(strategy_id).get()

        if not strategy_doc.exists:
            return {
                "status": "success",
                "strategy_status": "unknown",
                "pod_status": pod_status["pod_status"],
                "conditions": pod_status.get("conditions", []),
                "message": "Strategy found in Kubernetes but not in Firestore"
            }

        strategy_data = strategy_doc.to_dict()

        return {
            "status": "success",
            "strategy_status": strategy_data.get("status", "unknown"),
            "pod_status": pod_status["pod_status"],
            "start_time": pod_status.get("start_time"),
            "pod_ip": pod_status.get("pod_ip"),
            "conditions": pod_status.get("conditions", []),
            "started_at": strategy_data.get("started_at", None),
            "message": "Strategy status retrieved successfully"
        }

    except Exception as e:
        logging.error(f"Error getting strategy status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting strategy status: {str(e)}")

@app.get("/list-strategies/{user_id}")
async def list_strategies(user_id: str, authenticated_user: str = Depends(get_current_user)):
    """
    List all strategies for a user.

    Requires authentication. Users can only list their own strategies.
    """
    # Verify the user has permission to access these strategies
    if authenticated_user != user_id:
        raise HTTPException(status_code=403, detail="You don't have permission to access these strategies")

    try:
        # Get pods from Kubernetes
        pods = k8s_client.list_strategy_pods(user_id=user_id)

        if pods["status"] == "error":
            raise HTTPException(status_code=500, detail=pods["message"])

        # Get strategies from Firestore
        strategy_docs = db.collection("users").document(user_id) \
            .collection("submittedStrategies").stream()

        strategies = []
        for doc in strategy_docs:
            strategy_data = doc.to_dict()
            strategy_id = doc.id

            # Find pod status if exists
            pod_status = "not_found"
            pod_details = None
            for pod in pods.get("pods", []):
                if pod["strategy_id"] == strategy_id:
                    pod_status = pod["status"]
                    pod_details = pod
                    break

            strategies.append({
                "strategy_id": strategy_id,
                "name": strategy_data.get("name", "Unnamed Strategy"),
                "status": strategy_data.get("status", "unknown"),
                "pod_status": pod_status,
                "pod_details": pod_details,
                "created_at": strategy_data.get("created_at", None),
                "started_at": strategy_data.get("started_at", None),
                "stopped_at": strategy_data.get("stopped_at", None)
            })

        return {
            "status": "success",
            "strategies": strategies
        }

    except Exception as e:
        logging.error(f"Error listing strategies: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing strategies: {str(e)}")

@app.post("/process-strategy")
async def process_strategy(request: Request):
    """
    This endpoint is designed to receive a push message from Pub/Sub.
    When a new strategy is published, it creates a Kubernetes pod to run the strategy.

    This endpoint does not require authentication as it's called by Pub/Sub.
    The strategy data itself needs to come from a trusted source (Firestore).
    """
    try:
        # Parse the request body
        envelope = await request.json()
        if "message" not in envelope or "data" not in envelope["message"]:
            raise HTTPException(status_code=400, detail="Invalid Pub/Sub message format")

        # Decode the base64 data
        data_b64 = envelope["message"]["data"]
        decoded_str = base64.b64decode(data_b64).decode("utf-8")
        message_data = json.loads(decoded_str)

        # Extract strategy details
        if "strategy" not in message_data:
            raise HTTPException(status_code=400, detail="Missing strategy in message")

        strategy = message_data["strategy"]
        user_id = strategy.get("user_id")
        strategy_id = strategy.get("id")

        if not user_id or not strategy_id:
            raise HTTPException(
                status_code=400,
                detail="Missing user_id or strategy_id in message"
            )

        logging.info(f"Processing strategy: {strategy_id} for user: {user_id}")

        # Create the strategy pod
        result = k8s_client.create_strategy_pod(
            strategy_id=strategy_id,
            user_id=user_id,
            strategy_data=strategy
        )

        if result["status"] == "error":
            # Update Firestore with error
            db.collection("users").document(user_id) \
                .collection("submittedStrategies").document(strategy_id).update({
                    "status": "error",
                    "error": result["message"],
                    "error_at": datetime.utcnow()
                })

            raise HTTPException(status_code=500, detail=result["message"])

        # Update Firestore with pod info
        db.collection("users").document(user_id) \
            .collection("submittedStrategies").document(strategy_id).update({
                "status": "running",
                "pod_name": result["pod_name"],
                "started_at": datetime.utcnow()
            })

        return {
            "status": "success",
            "message": f"Strategy pod created successfully for strategy {strategy_id}"
        }

    except Exception as e:
        logging.error(f"Error processing strategy: {e}")
        return {
            "status": "error",
            "message": str(e)
        }

def publish_command(user_id: str, strategy_id: str, command: str, parameters: Dict[str, Any] = None):
    """Publish a command to the strategy command topic."""
    try:
        message_data = {
            "user_id": user_id,
            "strategy_id": strategy_id,
            "command": command,
            "parameters": parameters or {},
            "timestamp": datetime.utcnow().isoformat()
        }

        # Publish the message
        future = publisher.publish(
            command_topic_path,
            json.dumps(message_data).encode("utf-8")
        )
        message_id = future.result(timeout=30)

        structured_logger.info(
            "Command published successfully",
            user_id=user_id,
            strategy_id=strategy_id,
            command=command,
            message_id=message_id
        )

        return {"status": "success", "message_id": message_id}

    except Exception as e:
        structured_logger.error(
            "Failed to publish command",
            user_id=user_id,
            strategy_id=strategy_id,
            command=command,
            error=str(e)
        )
        return {"status": "error", "message": str(e)}

async def validate_user_strategy_access(authenticated_user: str, user_id: str, strategy_id: str) -> bool:
    """
    Validate if the authenticated user has access to the strategy.
    In this simple implementation, users can only access their own strategies.
    """
    # If authenticated user matches the requested user_id, allow access
    if authenticated_user == user_id:
        return True

    # For development purposes, we could allow all access when in dev mode
    if os.getenv("ENVIRONMENT", "").lower() == "development":
        logging.warning(f"Development mode: allowing access for {authenticated_user} to strategy of {user_id}")
        return True

    # Otherwise deny access
    logging.warning(f"Access denied: {authenticated_user} tried to access strategy of {user_id}")
    return False

@app.get("/check-strategy-status/{user_id}/{strategy_id}")
async def check_strategy_status(user_id: str, strategy_id: str, authenticated_user: str = Depends(get_current_user)):
    """
    Check if a strategy is in the stopped strategies set.

    Requires authentication. Only the owner of the strategy can check its status.
    """
    # Verify the user has permission to access this strategy
    if not await validate_user_strategy_access(authenticated_user, user_id, strategy_id):
        raise HTTPException(status_code=403, detail="You don't have permission to access this strategy")

    try:
        # Check if the strategy is in the stopped strategies set
        is_stopped = strategy_id in k8s_client._stopped_strategies

        return {
            "is_stopped": is_stopped,
            "strategy_id": strategy_id
        }
    except Exception as e:
        structured_logger.error("Error checking strategy status",
                              user_id=user_id,
                              strategy_id=strategy_id,
                              error=str(e),
                              exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error checking strategy status: {str(e)}")

@app.post("/reset-strategy-status/{user_id}/{strategy_id}")
async def reset_strategy_status(user_id: str, strategy_id: str, authenticated_user: str = Depends(get_current_user)):
    """
    Reset the stopped status of a strategy, allowing it to be started again.

    Requires authentication. Only the owner of the strategy can reset its status.
    """
    # Verify the user has permission to access this strategy
    if not await validate_user_strategy_access(authenticated_user, user_id, strategy_id):
        raise HTTPException(status_code=403, detail="You don't have permission to access this strategy")

    try:
        # Check if the strategy is in the stopped strategies set
        if strategy_id in k8s_client._stopped_strategies:
            # Remove the strategy from the stopped strategies set
            k8s_client._stopped_strategies.remove(strategy_id)
            structured_logger.info(
                "Reset strategy status, it can now be started again",
                strategy_id=strategy_id
            )
            return {
                "status": "success",
                "message": "Strategy status reset successfully"
            }
        else:
            return {
                "status": "success",
                "message": "Strategy was not in stopped state"
            }
    except Exception as e:
        structured_logger.error("Error resetting strategy status",
                              user_id=user_id,
                              strategy_id=strategy_id,
                              error=str(e),
                              exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error resetting strategy status: {str(e)}")

@app.post("/strategy-shutdown")
async def handle_strategy_shutdown(request: Request):
    """Handle intentional strategy shutdowns from trade-bots."""
    try:
        data = await request.json()
        user_id = data.get("user_id")
        strategy_id = data.get("strategy_id")
        reason = data.get("reason")
        shutdown_type = data.get("shutdown_type", "business_logic")

        if not user_id or not strategy_id:
            raise HTTPException(status_code=400, detail="Missing user_id or strategy_id")

        structured_logger.info("Trade-bot shutdown notification received",
                              user_id=user_id,
                              strategy_id=strategy_id,
                              reason=reason,
                              shutdown_type=shutdown_type)

        if shutdown_type == "business_logic":
            # Mark strategy as intentionally stopped to prevent restart
            k8s_client._stopped_strategies.add(strategy_id)

            # Update Firestore
            try:
                strategy_doc = db.collection("users").document(user_id)\
                    .collection("submittedStrategies").document(strategy_id)

                strategy_doc.update({
                    "status": "stopped",
                    "stop_reason": reason,
                    "stopped_at": datetime.utcnow(),
                    "auto_restart": False,
                    "shutdown_type": shutdown_type
                })

                structured_logger.info("Strategy marked as intentionally stopped",
                                      user_id=user_id,
                                      strategy_id=strategy_id,
                                      reason=reason)

            except Exception as e:
                structured_logger.error("Failed to update Firestore after shutdown notification",
                                       user_id=user_id,
                                       strategy_id=strategy_id,
                                       error=str(e))
                # Don't fail the request if Firestore update fails

            # Delete the Kubernetes deployment to prevent pod restart
            try:
                delete_result = k8s_client.delete_strategy_deployment(user_id, strategy_id)
                structured_logger.info("Deployment deleted after shutdown notification",
                                      user_id=user_id,
                                      strategy_id=strategy_id,
                                      delete_result=delete_result)
            except Exception as e:
                structured_logger.error("Failed to delete deployment after shutdown notification",
                                       user_id=user_id,
                                       strategy_id=strategy_id,
                                       error=str(e))
                # Don't fail the request if deployment deletion fails

            return {"status": "success", "message": "Strategy stopped and deployment deleted"}
        else:
            return {"status": "success", "message": "Shutdown notification received"}

    except Exception as e:
        structured_logger.error("Error handling strategy shutdown notification", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/control-strategy/{user_id}/{strategy_id}")
async def control_strategy(
    user_id: str,
    strategy_id: str,
    command: StrategyCommand,
    authenticated_user: str = Depends(get_current_user)
):
    """Control a running strategy."""
    try:
        # Validate user has access to this strategy
        if not await validate_user_strategy_access(authenticated_user, user_id, strategy_id):
            raise HTTPException(status_code=403, detail="Not authorized to access this strategy")

        # Get strategy document
        strategy_ref = db.collection("users").document(user_id)\
            .collection("submittedStrategies").document(strategy_id)
        strategy_doc = strategy_ref.get()

        if not strategy_doc.exists:
            raise HTTPException(status_code=404, detail="Strategy not found")

        # Publish command to Pub/Sub
        topic_path = publisher.topic_path(project_id, "strategy-commands")
        message = {
            "command": command.command,
            "user_id": user_id,
            "strategy_id": strategy_id
        }
        message_data = json.dumps(message).encode("utf-8")

        try:
            publish_future = publisher.publish(topic_path, message_data)
            publish_future.result()  # Wait for message to be published

            # Update strategy status in Firestore
            strategy_ref.update({
                "status": command.command,
                "lastUpdated": datetime.now(timezone.utc)
            })

            # If this is a stop command, remove the process from tracking and delete deployment
            # Note: We don't add user stops to stopped_strategies set to allow restart
            if command.command == "stop":
                # Get the strategy data
                strategy_data = strategy_doc.to_dict()

                structured_logger.info(
                    "User stop command received - deleting deployment to prevent restart",
                    strategy_id=strategy_id
                )

                # Delete the Kubernetes deployment to prevent pod restart
                try:
                    delete_result = k8s_client.delete_strategy_deployment(user_id, strategy_id)
                    structured_logger.info("Deployment deleted after user stop command",
                                          user_id=user_id,
                                          strategy_id=strategy_id,
                                          delete_result=delete_result)
                except Exception as e:
                    structured_logger.error("Failed to delete deployment after user stop",
                                          user_id=user_id,
                                          strategy_id=strategy_id,
                                          error=str(e))

                # If this is a local process, remove it from tracking
                if strategy_id in k8s_client._running_processes:
                    structured_logger.info(
                        "Removing local process from tracking due to stop command",
                        strategy_id=strategy_id
                    )
                    # Schedule the process for removal after a delay
                    # This gives the trade bot time to receive the command and exit gracefully
                    threading.Timer(10.0, lambda: k8s_client._remove_process_from_tracking(strategy_id)).start()

            return {"status": "success", "message": f"Strategy {command.command} command sent"}

        except Exception as e:
            structured_logger.error(f"Error publishing message: {e}")
            raise HTTPException(status_code=500, detail="Failed to send command")

    except Exception as e:
        structured_logger.error(f"Error in control_strategy: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Monitoring endpoints
@app.post("/monitoring/health")
async def update_health(health_data: HealthData, authenticated_user: str = Depends(get_current_user)):
    """Update health data for a service (authenticated)."""
    return monitoring_service.update_health_data(health_data)

@app.post("/monitoring/trade-bot-health")
async def update_trade_bot_health(health_data: HealthData):
    """Update health data for trade bots (no authentication required)."""
    # Validate that this is actually a trade bot
    if health_data.service != "trade-bot":
        raise HTTPException(status_code=403, detail="This endpoint is only for trade bot health updates")

    # Log the health update
    structured_logger.info(
        "Received trade bot health update",
        strategy_id=health_data.strategy_id,
        user_id=health_data.user_id,
        pod_name=health_data.pod_name,
        status=health_data.status
    )

    return monitoring_service.update_health_data(health_data)

@app.post("/monitoring/alerts")
async def create_alert(alert_data: AlertData, authenticated_user: str = Depends(get_current_user)):
    """Create a new alert (authenticated)."""
    return monitoring_service.create_alert(alert_data)

@app.post("/monitoring/trade-bot-alerts")
async def create_trade_bot_alert(alert_data: AlertData):
    """Create a new alert from trade bots (no authentication required)."""
    # Validate that this is actually a trade bot alert
    if alert_data.service != "trade-bot" and not alert_data.service.startswith("trade-bot-"):
        raise HTTPException(status_code=403, detail="This endpoint is only for trade bot alerts")

    # Log the alert
    structured_logger.info(
        "Received trade bot alert",
        strategy_id=alert_data.strategy_id,
        user_id=alert_data.user_id,
        status=alert_data.status,
        message=alert_data.message
    )

    return monitoring_service.create_alert(alert_data)

@app.post("/monitoring/alerts/{alert_id}/resolve")
async def resolve_alert(alert_id: str, authenticated_user: str = Depends(get_current_user)):
    """Resolve an alert."""
    return monitoring_service.resolve_alert(alert_id)

@app.get("/monitoring/health")
async def get_health_data(authenticated_user: str = Depends(get_current_user)):
    """Get all health data."""
    return monitoring_service.get_health_data()

@app.get("/monitoring/alerts")
async def get_alerts(active_only: bool = True, authenticated_user: str = Depends(get_current_user)):
    """Get all alerts."""
    return {"alerts": monitoring_service.get_alerts(active_only)}

@app.get("/monitoring", response_class=HTMLResponse)
async def monitoring_dashboard(authenticated_user: str = Depends(get_current_user)):
    """Monitoring dashboard."""
    try:
        with open("static/monitoring.html", "r") as f:
            return f.read()
    except FileNotFoundError:
        # Create a simple dashboard if the file doesn't exist
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Oryn Monitoring Dashboard</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                h1 { color: #333; }
                .card { background-color: white; border-radius: 5px; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .healthy { color: green; }
                .unhealthy { color: red; }
                .unknown { color: orange; }
            </style>
        </head>
        <body>
            <h1>Oryn Monitoring Dashboard</h1>
            <div class="card">
                <h2>System Status</h2>
                <div id="system-status">Loading...</div>
            </div>
            <div class="card">
                <h2>Services</h2>
                <div id="services">Loading...</div>
            </div>
            <div class="card">
                <h2>Alerts</h2>
                <div id="alerts">Loading...</div>
            </div>

            <script>
                // Fetch health data
                async function fetchHealthData() {
                    const response = await fetch('/monitoring/health');
                    const data = await response.json();
                    return data;
                }

                // Fetch alerts
                async function fetchAlerts() {
                    const response = await fetch('/monitoring/alerts');
                    const data = await response.json();
                    return data.alerts;
                }

                // Update dashboard
                async function updateDashboard() {
                    try {
                        const healthData = await fetchHealthData();
                        const alerts = await fetchAlerts();

                        // Update system status
                        const systemStatusEl = document.getElementById('system-status');
                        systemStatusEl.innerHTML = `
                            <p class="${healthData.status}">${healthData.status.toUpperCase()}</p>
                            <p>Last updated: ${new Date(healthData.last_updated).toLocaleString()}</p>
                        `;

                        // Update services
                        const servicesEl = document.getElementById('services');
                        let servicesHtml = '';

                        for (const [serviceId, serviceData] of Object.entries(healthData.services || {})) {
                            servicesHtml += `
                                <div class="service">
                                    <h3>${serviceData.service} (${serviceData.pod_name})</h3>
                                    <p class="${serviceData.status}">${serviceData.status.toUpperCase()}</p>
                                    <p>Version: ${serviceData.version}</p>
                                    <p>Uptime: ${formatUptime(serviceData.uptime)}</p>
                                    <p>Last updated: ${new Date(serviceData.timestamp).toLocaleString()}</p>
                                    ${serviceData.strategy_id ? `<p>Strategy ID: ${serviceData.strategy_id}</p>` : ''}
                                    ${serviceData.user_id ? `<p>User ID: ${serviceData.user_id}</p>` : ''}
                                    ${serviceData.error ? `<p class="unhealthy">Error: ${serviceData.error}</p>` : ''}

                                    <h4>Dependencies</h4>
                                    <ul>
                                        ${Object.entries(serviceData.dependencies || {}).map(([depName, depStatus]) => `
                                            <li>
                                                <span>${depName}: </span>
                                                <span class="${depStatus.status}">${depStatus.status.toUpperCase()}</span>
                                                ${depStatus.message ? `<span> - ${depStatus.message}</span>` : ''}
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                                <hr>
                            `;
                        }

                        servicesEl.innerHTML = servicesHtml || '<p>No services found</p>';

                        // Update alerts
                        const alertsEl = document.getElementById('alerts');
                        let alertsHtml = '';

                        for (const alert of alerts) {
                            alertsHtml += `
                                <div class="alert">
                                    <h3>${alert.service}</h3>
                                    <p class="${alert.status}">${alert.status.toUpperCase()}</p>
                                    <p>${alert.message}</p>
                                    <p>Created: ${new Date(alert.timestamp).toLocaleString()}</p>
                                    ${alert.strategy_id ? `<p>Strategy ID: ${alert.strategy_id}</p>` : ''}
                                    ${alert.user_id ? `<p>User ID: ${alert.user_id}</p>` : ''}
                                    <button onclick="resolveAlert('${alert.id}')">Resolve</button>
                                </div>
                                <hr>
                            `;
                        }

                        alertsEl.innerHTML = alertsHtml || '<p>No active alerts</p>';
                    } catch (error) {
                        console.error('Error updating dashboard:', error);
                    }
                }

                // Format uptime
                function formatUptime(seconds) {
                    const days = Math.floor(seconds / 86400);
                    const hours = Math.floor((seconds % 86400) / 3600);
                    const minutes = Math.floor((seconds % 3600) / 60);

                    let result = '';
                    if (days > 0) result += `${days}d `;
                    if (hours > 0 || days > 0) result += `${hours}h `;
                    result += `${minutes}m`;

                    return result;
                }

                // Resolve alert
                async function resolveAlert(alertId) {
                    try {
                        await fetch(`/monitoring/alerts/${alertId}/resolve`, { method: 'POST' });
                        updateDashboard();
                    } catch (error) {
                        console.error('Error resolving alert:', error);
                    }
                }

                // Initial update
                updateDashboard();

                // Update every 30 seconds
                setInterval(updateDashboard, 30000);
            </script>
        </body>
        </html>
        """

        # Save the dashboard for future use
        os.makedirs("static", exist_ok=True)
        with open("static/monitoring.html", "w") as f:
            f.write(html)

        return html

if __name__ == "__main__":
    # Run the FastAPI app
    port = int(os.getenv("PORT", 8080))
    host = os.getenv("HOST", "0.0.0.0")
    workers = int(os.getenv("WORKERS", 1))

    uvicorn.run(
        app,
        host=host,
        port=port,
        workers=workers,
        log_level="info",
        timeout_keep_alive=30
    )