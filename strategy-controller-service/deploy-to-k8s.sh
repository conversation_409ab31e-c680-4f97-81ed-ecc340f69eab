#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Enable GKE auth plugin
export USE_GKE_GCLOUD_AUTH_PLUGIN=True

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="strategy-controller"
REPOSITORY="oryn-containers"
TIMESTAMP=$(date +%s)
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${SERVICE_NAME}:${TIMESTAMP}"

echo "🚀 Starting deployment process for ${SERVICE_NAME}"

# Check if running in GitHub Actions or local environment
if [ -n "${GITHUB_ACTIONS}" ]; then
    echo "🤖 Running in GitHub Actions - using service account authentication"
    # In GitHub Actions, authentication is handled by google-github-actions/auth
    # Just configure Docker for the registry
    gcloud auth configure-docker us-central1-docker.pkg.dev --quiet
else
    echo "💻 Running locally - checking gcloud authentication..."
    # Check if gcloud is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo "❌ No active gcloud authentication found"
        echo "Please run: gcloud auth login"
        exit 1
    fi

    # Set the gcloud project
    echo "📋 Setting gcloud project to oryntrade..."
    gcloud config set project oryntrade

    # Configure Docker authentication for Google Container Registry
    echo "🔐 Configuring Docker authentication..."
    gcloud auth configure-docker us-central1-docker.pkg.dev --quiet
fi

# Build the Docker image for linux/amd64 platform with no cache to ensure latest changes
echo "🏗️ Building Docker image: ${IMAGE_NAME}"
echo "   Using timestamp: ${TIMESTAMP}"
echo "   This ensures all code changes are included in the image"
docker buildx build --platform linux/amd64 \
    --no-cache \
    -t "${IMAGE_NAME}" \
    --push \
    .

echo "✅ Strategy Controller image has been built and pushed to the container registry"

# Apply Kubernetes manifests
echo "🔄 Applying Kubernetes manifests..."
kubectl apply -f deployment.yaml

# Force a rollout to use the new image (if deployment exists)
echo "🔄 Rolling out new deployment..."
kubectl rollout restart deployment/${SERVICE_NAME}

# Wait for rollout to complete
echo "⏳ Waiting for rollout to complete..."
kubectl rollout status deployment/${SERVICE_NAME}

# Verify the deployment is ready
echo "🔍 Verifying deployment status..."
kubectl get deployment ${SERVICE_NAME}

# Wait a moment for the pod to be fully ready
echo "⏳ Waiting for pod to be ready..."
sleep 10

# Verify new code is active by checking for our latest improvements
echo "🧪 Verifying new code is active..."
POD_NAME=$(kubectl get pods -l app="${SERVICE_NAME}" -o jsonpath='{.items[0].metadata.name}')
if [ -n "$POD_NAME" ]; then
    echo "   Pod name: $POD_NAME"

    # Check for PubSub topic configuration (should be strategy-commands)
    echo "   Checking PubSub topic configuration..."
    kubectl logs "$POD_NAME" | grep -i "topic_path.*strategy-commands" && echo "   ✅ PubSub topic correctly set to strategy-commands" || echo "   ⚠️  PubSub topic configuration not found in logs"

    # Check for deployment deletion capability
    echo "   Checking deployment deletion capability..."
    kubectl exec "$POD_NAME" -- grep -q "delete_strategy_deployment" /app/kubernetes_client.py && echo "   ✅ Deployment deletion method available" || echo "   ⚠️  Deployment deletion method not found"

    # Check for command filtering in PubSub worker
    echo "   Checking PubSub command filtering..."
    kubectl exec "$POD_NAME" -- grep -q "Ignoring non-start command" /app/pubsub_worker.py && echo "   ✅ PubSub command filtering active" || echo "   ⚠️  PubSub command filtering not found"

    # Test basic connectivity
    echo "   Testing basic connectivity..."
    kubectl port-forward svc/strategy-controller-service 8080:80 &
    PORT_FORWARD_PID=$!
    sleep 3

    # Test health endpoint
    HEALTH_TEST=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ 2>/dev/null || echo "000")

    # Kill port-forward
    kill $PORT_FORWARD_PID 2>/dev/null || true

    if [ "$HEALTH_TEST" = "200" ]; then
        echo "   ✅ Strategy Controller is responding (HTTP $HEALTH_TEST)"
    else
        echo "   ⚠️  Strategy Controller not responding (HTTP $HEALTH_TEST)"
    fi
else
    echo "   ⚠️  Could not find Strategy Controller pod"
fi

echo "✅ Strategy Controller has been deployed successfully with image: ${IMAGE_NAME}"
echo ""
echo "🎯 Key Features in This Deployment:"
echo "   ✅ Creates Deployments instead of standalone Pods"
echo "   ✅ Complete exit code implementation with deployment deletion"
echo "   ✅ User stops allow restart, business logic stops prevent restart"
echo "   ✅ PubSub topic correctly set to strategy-commands"
echo "   ✅ PubSub worker filters commands (only processes 'start')"
echo "   ✅ Deployment deletion on user stops and business logic shutdowns"
echo ""
echo "🧪 To Test the Complete Implementation:"
echo "   1. Create a trade-bot via frontend"
echo "   2. Verify deployment creation:"
echo "      kubectl get deployments -l app=trade-bot-strategy"
echo "      kubectl get pods -l app=trade-bot-strategy"
echo ""
echo "   3. Test user stop (should allow restart):"
echo "      - Stop bot via frontend"
echo "      - Deployment should be deleted, pod should terminate"
echo "      - User can restart from frontend"
echo ""
echo "   4. Test business logic shutdown (should prevent restart):"
echo "      TRADE_BOT_POD=\$(kubectl get pods -l app=trade-bot-strategy -o jsonpath='{.items[0].metadata.name}')"
echo "      kubectl exec \$TRADE_BOT_POD -- python -c \""
echo "      import sys, os"
echo "      sys.path.append('/app')"
echo "      from main import TradingBot, ExitCodes"
echo "      user_id = os.environ.get('USER_ID')"
echo "      strategy_id = os.environ.get('STRATEGY_ID')"
echo "      bot = TradingBot(user_id, strategy_id)"
echo "      bot.shutdown_with_exit_code(ExitCodes.INSUFFICIENT_MARGIN, 'Test shutdown')"
echo "      \""
echo "      # Should delete deployment and prevent restart"
echo ""
echo "📊 To access the service: kubectl port-forward svc/strategy-controller-service 8080:80"
echo "🔒 Firebase credentials are configured via the firebase-key secret"
echo ""
echo "👉 API Endpoints:"
echo "   - Health check: GET /"
echo "   - Control strategy: POST /control-strategy/{user_id}/{strategy_id}"
echo "   - Check strategy status: GET /check-strategy-status/{user_id}/{strategy_id}"
echo "   - Strategy shutdown notification: POST /strategy-shutdown"
echo "   - Trade-bot health monitoring: POST /monitoring/trade-bot-health"
echo ""
echo "🔄 PubSub Integration:"
echo "   - Topic: projects/oryntrade/topics/strategy-commands"
echo "   - Subscription: projects/oryntrade/subscriptions/strategy-controller-sub"
echo "   - Only 'start' commands create deployments"
echo "   - 'stop' commands are handled via control endpoint + deployment deletion"
echo ""
echo "🎉 Deployment complete! The Strategy Controller is ready for production use."