#!/bin/bash

# Deploy Strategy Controller with Deployment Creation Fix
# This updates the Strategy Controller to create Deployments instead of standalone Pods

set -e

echo "🚀 Deploying Strategy Controller with Deployment Creation Fix..."

# Set variables
PROJECT_ID="oryntrade"
REGION="us-central1"
SERVICE_NAME="control-strategy"

echo "📦 Building and deploying Strategy Controller..."

# Deploy to Cloud Run
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --project $PROJECT_ID \
    --allow-unauthenticated \
    --memory 1Gi \
    --cpu 1 \
    --timeout 3600 \
    --max-instances 10 \
    --set-env-vars="GOOGLE_CLOUD_PROJECT=$PROJECT_ID" \
    --set-env-vars="K8S_NAMESPACE=default" \
    --set-env-vars="USE_LOCAL_TRADE_BOT=false"

echo "✅ Strategy Controller deployed successfully!"
echo "🔧 Key Changes:"
echo "   - Creates Deployments instead of standalone Pods"
echo "   - Trade-bots will now automatically restart on pod deletion"
echo "   - Graceful shutdown and state restoration will work properly"
echo ""
echo "🧪 To test:"
echo "   1. Deploy a trade-bot through the frontend"
echo "   2. Delete the pod: kubectl delete pod <pod-name>"
echo "   3. Watch new pod automatically restart: kubectl get pods -w"
echo ""
echo "📊 The trade-bot should:"
echo "   - Save state during graceful shutdown"
echo "   - Automatically restart with new pod"
echo "   - Restore state and resume trading"
