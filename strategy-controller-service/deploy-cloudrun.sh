#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status

# Configuration
PROJECT_ID="oryntrade"
SERVICE_NAME="control-strategy"
REPOSITORY="oryn-containers"
REGION="us-central1"
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/strategy-controller:latest"

echo "🚀 Starting Cloud Run deployment for ${SERVICE_NAME}"

# Build and push the Docker image
echo "🏗️ Building Docker image for linux/amd64 platform..."
docker buildx build --platform linux/amd64 \
    -t ${IMAGE_NAME} \
    --push \
    .

if [ $? -ne 0 ]; then
    echo "❌ Failed to build and push Docker image"
    exit 1
fi

echo "✅ Docker image built and pushed successfully"

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image=${IMAGE_NAME} \
    --platform=managed \
    --region=${REGION} \
    --allow-unauthenticated \
    --memory=2Gi \
    --cpu=1 \
    --min-instances=0 \
    --max-instances=10 \
    --timeout=3600 \
    --concurrency=1000 \
    --execution-environment=gen2 \
    --set-env-vars="GOOGLE_CLOUD_PROJECT=${PROJECT_ID},USE_LOCAL_TRADE_BOT=false,K8S_NAMESPACE=default,PUBSUB_SUBSCRIPTION=strategy-controller-sub,PUBSUB_TOPIC=strategy-execution,PORT=8080,HOST=0.0.0.0,WORKERS=1" \
    --set-secrets="POLYGON_API_KEY=polygon-api-key:latest,WEBSOCKET_API_KEY=websocket-api-key:latest"

if [ $? -ne 0 ]; then
    echo "❌ Failed to deploy to Cloud Run"
    exit 1
fi

echo "✅ Successfully deployed ${SERVICE_NAME} to Cloud Run"

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format="value(status.url)")
echo "🌐 Service URL: ${SERVICE_URL}"

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update frontend .env.local with the service URL:"
echo "   NEXT_PUBLIC_STRATEGY_CONTROLLER_URL_PRODUCTION=${SERVICE_URL}"
echo "2. Test the service health: curl ${SERVICE_URL}/"
echo "3. Deploy trade-bots to the same environment"
