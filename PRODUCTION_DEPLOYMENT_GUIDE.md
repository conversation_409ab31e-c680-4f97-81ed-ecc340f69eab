# 🚀 Oryn Trading Bot Production Deployment Guide

## 📋 Overview

This document describes the complete production setup for the Oryn Trading Bot system, including how to test and monitor the deployment. The system is designed to automatically create individual trading bot instances for each strategy submitted by users.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │  Strategy        │    │  Trade Bot      │
│   (React App)   │───▶│  Controller      │───▶│  Pods           │
│                 │    │  (Kubernetes)    │    │  (Kubernetes)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Firebase      │    │  Google PubSub   │    │  OANDA API      │
│   (Database)    │    │  (Messaging)     │    │  (Trading)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🎯 System Components

### 1. **Google Kubernetes Engine (GKE) Cluster**
- **Name**: `oryn-trading-cluster`
- **Location**: `us-central1`
- **Purpose**: Hosts the Strategy Controller and dynamically created Trade Bot pods

### 2. **Strategy Controller**
- **Type**: Kubernetes Deployment
- **Image**: `us-central1-docker.pkg.dev/oryntrade/oryn-containers/strategy-controller:latest`
- **Purpose**: 
  - Receives strategy execution requests via PubSub
  - Manages Trade Bot pod creation and lifecycle
  - Provides monitoring and health endpoints

### 3. **Trade Bot Pods**
- **Type**: Dynamically created Kubernetes Pods
- **Image**: `us-central1-docker.pkg.dev/oryntrade/oryn-containers/trade-bot:latest`
- **Purpose**: Execute individual trading strategies

### 4. **Monitoring Service**
- **Location**: Built into Strategy Controller
- **Purpose**: Real-time monitoring of system health and alerts

### 5. **Supporting Services**
- **Firebase Firestore**: Strategy storage and configuration
- **Google PubSub**: Message queue for strategy execution requests
- **Google Container Registry**: Docker image storage

## 🔧 Available Commands

### **Cluster Management**

#### View Cluster Information
```bash
kubectl cluster-info
```
- Shows the Kubernetes cluster endpoint and services

#### Check Cluster Nodes
```bash
kubectl get nodes
```
- Lists all worker nodes in the cluster
- Shows node status, roles, age, and version

### **Strategy Controller Management**

#### Check Strategy Controller Status
```bash
kubectl get deployment strategy-controller
```
- Shows deployment status, ready replicas, and age

#### View Strategy Controller Pods
```bash
kubectl get pods -l app=strategy-controller
```
- **`-l app=strategy-controller`**: Filter pods by label
- Shows pod name, ready status, restarts, and age

#### View Strategy Controller Logs
```bash
kubectl logs -f strategy-controller-<pod-name>
```
- **`-f`**: Follow logs in real-time (like `tail -f`)
- Replace `<pod-name>` with actual pod name from previous command

#### Restart Strategy Controller
```bash
kubectl rollout restart deployment/strategy-controller
```
- Gracefully restarts the Strategy Controller with zero downtime

### **Trade Bot Management**

#### List All Trade Bot Pods
```bash
kubectl get pods -l app=trade-bot
```
- **`-l app=trade-bot`**: Filter to show only trade bot pods
- Each pod represents one running trading strategy

#### View Specific Trade Bot Logs
```bash
kubectl logs trade-bot-<strategy-id>
```
- Replace `<strategy-id>` with the actual trade bot pod name

#### Delete a Trade Bot Pod
```bash
kubectl delete pod trade-bot-<strategy-id>
```
- Stops a specific trading strategy
- **⚠️ Use with caution**: This will stop active trading

### **Monitoring and Health**

#### Access Monitoring Dashboard
```bash
kubectl port-forward svc/strategy-controller-service 8080:80
```
- **`port-forward`**: Creates a tunnel from local machine to Kubernetes service
- **`8080:80`**: Maps local port 8080 to service port 80
- Access dashboard at: http://localhost:8080/monitoring

#### Check System Health
```bash
curl http://localhost:8080/
```
- Basic health check endpoint
- Returns: `{"status": "healthy", "service": "strategy-controller"}`

#### View All Resources
```bash
kubectl get all
```
- Shows all Kubernetes resources (pods, services, deployments)

### **Testing and Debugging**

#### View Recent Events
```bash
kubectl get events --sort-by='.lastTimestamp' | tail -10
```
- **`--sort-by='.lastTimestamp'`**: Sort events by time
- **`tail -10`**: Show last 10 events
- Useful for debugging pod creation issues

#### Describe a Resource (Detailed Info)
```bash
kubectl describe pod <pod-name>
```
- Shows detailed information about a specific pod
- Includes events, resource usage, and configuration

#### Execute Commands in a Pod
```bash
kubectl exec -it <pod-name> -- /bin/bash
```
- **`-it`**: Interactive terminal
- **`--`**: Separates kubectl options from container command
- Opens a shell inside the running pod for debugging

## 🧪 Testing the Production System

### **Step 1: Verify System Status**
```bash
# Check if cluster is accessible
kubectl cluster-info

# Verify Strategy Controller is running
kubectl get pods -l app=strategy-controller

# Check for any trade bot pods
kubectl get pods -l app=trade-bot
```

### **Step 2: Set Up Monitoring**
```bash
# Start port forwarding (run in background)
kubectl port-forward svc/strategy-controller-service 8080:80 &

# Test health endpoint
curl http://localhost:8080/

# Access monitoring dashboard in browser
open http://localhost:8080/monitoring
```

### **Step 3: Test Strategy Execution**
The system is tested by publishing messages to Google PubSub, which triggers the Strategy Controller to create Trade Bot pods.

**Test Message Format:**
```json
{
  "strategy": {
    "id": "test-strategy-id",
    "user_id": "test-user-id",
    "name": "Test Strategy",
    "instruments": "EUR/USD",
    "timeframe": "5m",
    "status": "pending"
  },
  "action": "start",
  "timestamp": "2025-06-28T17:55:14.820948+00:00",
  "source": "manual_test"
}
```

**Publishing Test Message:**
```bash
gcloud pubsub topics publish strategy-execution \
  --message='{"strategy":{"id":"test-id","user_id":"test-user"},"action":"start"}' \
  --project=oryntrade
```

### **Step 4: Monitor Test Results**
```bash
# Watch Strategy Controller logs for message processing
kubectl logs -f -l app=strategy-controller

# Check if trade bot pod was created
kubectl get pods -l app=trade-bot

# View trade bot logs if created
kubectl logs <trade-bot-pod-name>
```

## 💰 Expected Google Cloud Costs

### **Kubernetes Engine (GKE)**
- **Cluster Management Fee**: $0.10/hour (~$73/month)
- **Node Costs**: 
  - e2-medium (2 vCPU, 4GB RAM): ~$25/month per node
  - Typical setup: 1-3 nodes = $25-75/month

### **Container Registry**
- **Storage**: $0.026/GB/month
- **Network Egress**: $0.12/GB (first 1GB free)
- **Estimated**: $5-15/month for image storage

### **Firebase Firestore**
- **Reads**: $0.06 per 100,000 operations
- **Writes**: $0.18 per 100,000 operations
- **Storage**: $0.18/GB/month
- **Estimated**: $5-20/month for typical usage

### **Google PubSub**
- **Message Throughput**: $40/TiB/month
- **Storage**: $0.27/GB/month
- **Estimated**: $5-15/month for typical usage

### **Total Estimated Monthly Cost**
- **Minimum**: ~$110/month (1 node, light usage)
- **Typical**: ~$150-200/month (2-3 nodes, moderate usage)
- **High Usage**: ~$300+/month (multiple nodes, heavy trading)

### **Cost Optimization Tips**
1. **Use Preemptible Nodes**: Save up to 80% on compute costs
2. **Auto-scaling**: Automatically scale down during low usage
3. **Resource Limits**: Set appropriate CPU/memory limits on pods
4. **Clean Up**: Regularly delete unused trade bot pods
5. **Monitoring**: Use the monitoring dashboard to track resource usage

## 🔒 Security Considerations

- All services use Google Cloud IAM for authentication
- Firebase security rules control data access
- Kubernetes RBAC limits pod permissions
- Container images are stored in private registry
- Network policies can be added for additional isolation

## 📞 Support Commands

### **Emergency Stop All Trade Bots**
```bash
kubectl delete pods -l app=trade-bot
```

### **Scale Down Strategy Controller**
```bash
kubectl scale deployment strategy-controller --replicas=0
```

### **Scale Up Strategy Controller**
```bash
kubectl scale deployment strategy-controller --replicas=1
```

### **View Resource Usage**
```bash
kubectl top pods
kubectl top nodes
```

## 🔄 Workflow: From Strategy to Execution

### **Complete Process Flow**

1. **User submits strategy** via frontend
2. **Frontend stores strategy** in Firebase Firestore
3. **Frontend publishes message** to PubSub topic `strategy-execution`
4. **Strategy Controller receives** PubSub message
5. **Strategy Controller fetches** strategy details from Firestore
6. **Strategy Controller creates** new Trade Bot pod in Kubernetes
7. **Trade Bot starts** and begins executing the strategy
8. **Trade Bot reports health** back to monitoring service
9. **User monitors progress** via monitoring dashboard

### **Message Flow Diagram**
```
Frontend ──┐
           │ (1) Store Strategy
           ▼
       Firebase ──┐
                  │ (2) Publish Message
                  ▼
              PubSub ──┐
                       │ (3) Receive Message
                       ▼
           Strategy Controller ──┐
                                │ (4) Create Pod
                                ▼
                         Trade Bot Pod ──┐
                                         │ (5) Execute Trades
                                         ▼
                                    OANDA API
```

## 🚨 Troubleshooting Guide

### **Common Issues and Solutions**

#### **Strategy Controller Not Starting**
```bash
# Check pod status
kubectl get pods -l app=strategy-controller

# View detailed pod information
kubectl describe pod <strategy-controller-pod-name>

# Check logs for errors
kubectl logs <strategy-controller-pod-name>
```

#### **Trade Bot Pod Creation Fails**
```bash
# Check recent events
kubectl get events --sort-by='.lastTimestamp' | tail -20

# Verify service account permissions
kubectl get serviceaccount trade-bot-sa

# Check if image is accessible
kubectl describe pod <failed-pod-name>
```

#### **PubSub Messages Not Processing**
```bash
# Verify PubSub subscription exists
gcloud pubsub subscriptions list --project=oryntrade

# Check Strategy Controller logs for PubSub errors
kubectl logs -f -l app=strategy-controller | grep -i pubsub

# Test PubSub connectivity
gcloud pubsub topics publish strategy-execution --message='{"test":"message"}' --project=oryntrade
```

#### **Monitoring Dashboard Not Accessible**
```bash
# Verify port forwarding is active
ps aux | grep "kubectl port-forward"

# Restart port forwarding if needed
kubectl port-forward svc/strategy-controller-service 8080:80

# Check if service exists
kubectl get service strategy-controller-service
```

## 📊 Monitoring and Alerting

### **Key Metrics to Monitor**

1. **Pod Health**: Number of running vs failed pods
2. **Resource Usage**: CPU and memory consumption
3. **Message Processing**: PubSub message throughput
4. **Trade Execution**: Success/failure rates
5. **API Latency**: Response times from OANDA

### **Setting Up Alerts**

The monitoring service can be configured to send alerts for:
- Pod failures or restarts
- High resource usage
- Trading errors
- API connectivity issues

### **Monitoring Dashboard Features**

Access at `http://localhost:8080/monitoring`:
- Real-time system status
- Service health indicators
- Active alerts and notifications
- Trade bot performance metrics
- Resource utilization graphs

## 🔧 Maintenance Tasks

### **Daily Tasks**
```bash
# Check system health
kubectl get pods --all-namespaces

# Review recent events
kubectl get events --sort-by='.lastTimestamp' | tail -10

# Monitor resource usage
kubectl top pods
```

### **Weekly Tasks**
```bash
# Clean up completed pods
kubectl delete pods --field-selector=status.phase=Succeeded

# Review and resolve alerts
curl http://localhost:8080/monitoring/alerts

# Check for image updates
gcloud container images list --repository=us-central1-docker.pkg.dev/oryntrade/oryn-containers
```

### **Monthly Tasks**
- Review cost reports in Google Cloud Console
- Update container images if needed
- Analyze trading performance metrics
- Review and update resource limits

This production setup provides a scalable, monitored, and cost-effective solution for running multiple trading strategies simultaneously.
